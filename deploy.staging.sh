#!/usr/bin/env sh

# abort on errors
set -e

echo '====================================================================================='
echo '=============================...DEPLOYING STAGING...============================='
echo '====================================================================================='

npm run build:stage

echo '====================================================================================='
echo '=====================================...BUILD...====================================='
echo '====================================================================================='

cp .htaccess dist
cd dist

git init
git add -A
git commit -m 'deploy'
git branch -M master

echo '====================================================================================='
echo '==================================...PUSHING GIT...=================================='
echo '====================================================================================='
git push -f https://<EMAIL>/plesk-git/299admin.git master
cd -

rm -rf dist

green=`tput setaf 2`
reset=`tput sgr0`
now=$(date +"%T")

echo "${green}====================================================================================="
echo "${green}=======================...DEPLOY SUCCESS STAGING AT $now...======================"
echo "${green}=====================================================================================${reset}"
