import { Customer } from "./customer";
import { NewsCategory } from "./newsCategory";
import { Product } from "./product";
import { TelegramGroup } from "./telegramGroup";

export enum CustomerRankLevel {
  Free = 0,
  Vip1 = 1,
  Vip12 = 11,
  Vip2 = 2,
  Vip3 = 3,
}

export enum CustomerRankType {
  Free = "FREE", //miễn phí
  Charge = "CHARGE", //có tính phí
  ChargePs = "CHARGE_PS", //có tính phí
  Reference = "REFERENCE", //có ID môi giới
}

export const CustomerRankTypeTrans = {
  [CustomerRankType.Free]: {
    label: "Miễn phí",
    value: CustomerRankType.Free,
  },
  [CustomerRankType.Charge]: {
    label: "Có thu phí CS",
    value: CustomerRankType.Charge,
  },
  [CustomerRankType.ChargePs]: {
    label: "<PERSON><PERSON> thu phí PS",
    value: CustomerRankType.ChargePs,
  },
  [CustomerRankType.Reference]: {
    label: "Có ID môi giới",
    value: CustomerRankType.Reference,
  },
};

export interface CustomerRank {
  id: number;
  createdAt: number;
  updatedAt: number;
  isDeleted: boolean;
  deletedAt: number;
  name: string;
  description: string; //quyền lợi
  condition: string; //điều kiện
  image: string;
  icon: string;
  level: number;
  position: number;
  customers: Customer[];
  products: Product[];
  telegramGroups: TelegramGroup[];
  newsCategories: NewsCategory[];
  noticeEmail: string;
  type: CustomerRankType;
  isVisible: boolean;
}
