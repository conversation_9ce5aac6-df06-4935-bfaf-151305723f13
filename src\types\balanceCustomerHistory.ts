import { Customer } from "./customer";
import { CustomerRank } from "./customer-rank";
import { Employee } from "./employee";

export interface BalanceCustomerHistory {
  id: number;
  createdAt: number;
  updatedAt: number;
  isDeleted: boolean;
  deletedAt: number;
  beforeTotalCustomer: number; //sl tổng KH trước khi cân bằng
  employee: Employee;
  customer: Customer;
  customerRank: CustomerRank; //hạng KH lúc ghi nhận cân bằng
}
