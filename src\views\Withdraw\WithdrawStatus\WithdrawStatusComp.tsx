import { Tag } from "antd";
import { useTranslation } from "react-i18next";
import clsx from "clsx";

import "./styles/WithdrawStatus.scss";
import { WithdrawStatus, WithdrawStatusTrans } from "types/withdraw";

interface Props {
  status: WithdrawStatus;
}

export const WithdrawStatusComp = ({ status }: Props) => {
  const { t } = useTranslation();

  return (
    <Tag
      className={clsx("withdraw-status m-0", status)}
      color={
        WithdrawStatusTrans[status as keyof typeof WithdrawStatusTrans]?.color
      }
    >
      {t(`withdraw${status}`)}
    </Tag>
  );
};
