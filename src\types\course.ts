import { CourseCategory } from "./courseCategory";
import { Lecture } from "./lecture";

export interface Course {
  id: number;
  createdAt: number;
  updatedAt: number;
  isDeleted: boolean;
  deletedAt: number;
  code: string;
  name: string;
  desc: string;
  image: string;
  price: number;
  finalPrice: number;
  isFree: boolean; //true: mi<PERSON><PERSON> ph<PERSON>
  lectures: Lecture[];
  courseCategory: CourseCategory;
  totalView: number;
  no: number;
  visibleHomePage: boolean;
}
