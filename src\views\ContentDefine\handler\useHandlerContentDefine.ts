import { Form, message } from "antd";
import { contentDefineApi } from "api/content-define.api";
import { useFetchTableData } from "hooks/useFetchTableData";
import { useTranslation } from "react-i18next";
import { ContentDefine } from "types/content-define";
import { QueryParam } from "types/query";

interface Props {
  initQuery?: QueryParam;
}

export function useHandlerContentDefine({
  initQuery = {} as QueryParam,
}: Props) {
  const [form] = Form.useForm<ContentDefine>();
  const { t } = useTranslation();

  const fetch = useFetchTableData<ContentDefine>({
    initQuery,
    queryFunc: async (query) => {
      const res = await contentDefineApi.findAll(query);
      return { data: res.data.contentDefines, total: res.data.total };
    },
    createFunc: async () => {
      await form!.validateFields();
      const data = {
        contentDefine: form!.getFieldsValue(),
      };
      await contentDefineApi.create(data);
      message.success(t("actionSuccessfully"));
    },
    editFunc: async (id) => {
      await form!.validateFields();
      const data = {
        contentDefine: form!.getFieldsValue(),
      };
      await contentDefineApi.update(id, data);
      message.success(t("actionSuccessfully"));
    },
  });

  return { ...fetch, form };
}
