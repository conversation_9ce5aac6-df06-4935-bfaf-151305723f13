// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
import React, { useState } from "react";
import {
  Box,
  Typography,
  FormGroup,
  FormControlLabel,
  Button,
  Stack,
  Divider,
} from "@mui/material";
import { Link, useNavigate } from "react-router-dom";
import AuthSocialButtons from "./AuthSocialButtons";
import CustomFormLabel from "components/CustomerInput/CustomFormLabel";
import CustomTextField from "components/CustomerInput/CustomTextField";
import CustomCheckbox from "components/CustomerInput/CustomCheckbox";
import { Form, Input, message } from "antd";
import { requiredRule } from "utils/validate-rules";
import { useForm } from "antd/es/form/Form";
import { appStore } from "store/appStore";
import { observer } from "mobx-react";
import { userStore } from "store/userStore";
import { permissionStore } from "store/permissionStore";
import { adminRoutes } from "router";
import { settings } from "settings";

interface loginType {
  title?: string;
  subtitle?: any | any[];
  subtext?: any | any[];
}

const AuthRegister = ({ title, subtitle, subtext }: loginType) => {
  const navigation = useNavigate();
  const [form] = useForm<{ phone: string; email: string; password: string }>();
  const [loading, setLoading] = useState(false);

  const handleSubmit = async () => {
    setLoading(true);
    try {
      await form.validateFields();
      const { email, phone, password } = form.getFieldsValue();
    } finally {
      setLoading(false);
    }
  };

  return (
    <Form
      layout="vertical"
      form={form}
      validateTrigger={["onBlur", "onChange"]}
    >
      {title ? (
        <Typography fontWeight="700" variant="h3" mb={1}>
          {title}
        </Typography>
      ) : null}

      {subtext}

      {/* <AuthSocialButtons title="Sign in with" /> */}
      {/* <Box mt={3}>
      <Divider>
        <Typography
          component="span"
          color="textSecondary"
          variant="h6"
          fontWeight="400"
          position="relative"
          px={2}
        >
          or sign in with
        </Typography>
      </Divider>
    </Box> */}

      <Stack className="mt-8">
        <Box>
          <Form.Item label="Phone number" name={"phone"} rules={[requiredRule]}>
            <Input />
          </Form.Item>
        </Box>
        <Box>
          <Form.Item label="Email" name={"email"} rules={[requiredRule]}>
            <Input />
          </Form.Item>
        </Box>
        <Box>
          <Form.Item label="Password" name={"password"} rules={[requiredRule]}>
            <Input.Password />
          </Form.Item>
        </Box>
        {/* <Stack
          justifyContent="space-between"
          direction="row"
          alignItems="center"
          mb={2}
        >
          <FormGroup>
            <FormControlLabel
              control={
                <CustomCheckbox
                  defaultChecked
                  onChange={(e, checked) => {
                    appStore.changeRememberThisDevice(checked);
                  }}
                />
              }
              label="Remeber this Device"
            />
          </FormGroup>
          <Typography
            component={Link}
            to="/auth/forgot-password"
            fontWeight="500"
            sx={{
              textDecoration: "none",
              color: "primary.main",
            }}
          >
            Forgot Password ?
          </Typography>
        </Stack> */}
      </Stack>
      <Box>
        <Button
          color="primary"
          variant="contained"
          size="large"
          fullWidth
          onClick={() => {
            handleSubmit();
          }}
        >
          Sign Up
        </Button>
      </Box>
      {subtitle}
    </Form>
  );
};

export default observer(AuthRegister);
