import { Col, Form, Input, message, Modal, Row } from "antd";
import { Rule } from "antd/lib/form";
import { customerApi } from "api/customer.api";
import { SingleImageUpload } from "components/Upload/SingleImageUpload";
import React, { useEffect, useImperativeHandle, useState } from "react";
import { ModalStatus } from "types/modal";
import { Customer } from "types/customer";

const rules: Rule[] = [{ required: true }];

export interface ResetPasswordModalRef {
  handleCreate: () => void;
  handleUpdate: (customer: Customer) => void;
}
interface ResetPasswordModalProps {
  onClose: () => void;
  onSubmitOk: () => void;
}

export const ResetPasswordModal = React.forwardRef(
  ({ onClose, onSubmitOk }: ResetPasswordModalProps, ref) => {
    const [form] = Form.useForm();
    const [loading, setLoading] = useState(false);
    const [visible, setVisible] = useState(false);
    const [status, setStatus] = useState<ModalStatus>("create");
    const [selectedCustomer, setSelectedCustomer] = useState<Customer>();
    useImperativeHandle<any, ResetPasswordModalRef>(
      ref,
      () => ({
        handleCreate() {
          form.resetFields();
          setVisible(true);
          setStatus("create");
        },
        handleUpdate(customer: Customer) {
          //   form.setFieldsValue({ ...customer });
          setSelectedCustomer(customer);
          setVisible(true);
          setStatus("update");
        },
      }),
      []
    );

    const createData = async () => {
      const valid = await form.validateFields();
      const data = { resetPassword: form.getFieldsValue() };

      setLoading(true);
      try {
        // const res = await resetPasswordApi.create(data);
        message.success("Create ResetPassword successfully!");
        onClose();
        onSubmitOk();
      } finally {
        setLoading(false);
      }
    };

    const updateData = async () => {
      const valid = await form.validateFields();
      const data = form.getFieldsValue();
      setLoading(true);
      try {
        const res = await customerApi.resetPass(
          selectedCustomer?.id || 0,
          data
        );
        message.success("Reset mật khẩu thành công!");
        onClose();
        onSubmitOk();
        setVisible(false);
      } finally {
        setLoading(false);
      }
    };

    return (
      <Modal
        onCancel={() => {
          onClose?.();
          setVisible(false);
        }}
        afterClose={() => {
          form.resetFields();
        }}
        destroyOnClose
        visible={visible}
        title={status == "create" ? "Create ResetPassword" : "Reset mật khẩu"}
        style={{ top: 20 }}
        width={600}
        confirmLoading={loading}
        onOk={() => {
          status == "create" ? createData() : updateData();
        }}
        cancelText="Đóng"
      >
        <Form
          layout="vertical"
          form={form}
          validateTrigger={["onBlur", "onChange"]}
        >
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item label="Mật khẩu" name="password" rules={rules}>
                <Input.Password placeholder="" className="w-full" />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    );
  }
);
