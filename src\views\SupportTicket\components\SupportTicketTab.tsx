import {
  DownloadOutlined,
  ExportOutlined,
  SearchOutlined,
} from "@ant-design/icons";
import {
  Button,
  Input,
  message,
  Popconfirm,
  Space,
  Table,
  Tabs,
  Tag,
} from "antd";
import TextArea from "antd/es/input/TextArea";
import dayjs from "dayjs";
import { useHandleCustomer } from "hooks/useHandleCustomer";
import { debounce } from "lodash";
import React, { useEffect, useMemo, useRef, useState } from "react";

import { formatVND, getTitle } from "utils";
import { unixToFullDate } from "utils/dateFormat";
import { MyTableColumn } from "utils/excel";

import { Pagination } from "components/Pagination";
import {
  SupportTicket,
  SupportTicketCreatedBy,
  SupportTicketStatus,
} from "types/supportTicket";
import { useSupportTicket } from "hooks/useSupportTicket";
import { SupportTicketList } from "./Table/SupportTicketTable";
import { useTranslation } from "react-i18next";
import { TextField } from "@mui/material";
import {
  SupportTicketModal,
  SupportTicketModalRef,
} from "./Modal/SupportTicketModal";
import { handleExport, MyExcelColumn } from "utils/MyExcel";
import { supportTicketApi } from "api/supportTicket.api";
const { ColumnGroup, Column } = Table;
import { ReactComponent as DownloadIcon } from "assets/svgs/download.svg";
import { ReactComponent as SearchIcon } from "assets/svgs/search-icon.svg";

interface PropTypes {
  status: SupportTicketStatus;
  title?: string;
  isFocus?: boolean;
  parentLastUpdate?: number;
  onSubmitOk?: () => void;
  type?: SupportTicketCreatedBy;
  onEditModal?: (record: any) => void;
  pageNumber?: number;
  setPageNumber?: (page: number) => void
  typeTitle?: string
}
export const SupportTicketTab = React.memo(
  ({
    title = "",
    status,
    isFocus,
    parentLastUpdate,
    onSubmitOk,
    type,
    onEditModal,
    setPageNumber,
    pageNumber,
    typeTitle = ""
  }: PropTypes) => {
    const [note, setNote] = useState<string>();
    const [loadingConfirmDeposit, setLoadingConfirmDeposit] = useState(false);
    const [loadingRejectDeposit, setLoadingRejectDeposit] = useState(false);
    const [selectedCustomerId, setSelectedCustomerId] = useState<number>();
    const [lastUpdate, setLastUpdate] = useState(0);
    const { t } = useTranslation();
    const supportTicketModalRef = useRef<SupportTicketModalRef>();
    const [activeTabKey, setActiveTabKey] = useState("1");

    console.log({ childLastUpdate: lastUpdate, status });
    const exportColumns: MyExcelColumn<SupportTicket>[] = [
      {
        width: 30,
        header:
          type === SupportTicketCreatedBy.Customer
            ? t("customer")
            : t("partner"),
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "name",
        render: (record: SupportTicket) => {
          return record.customer?.name || record.partner?.fullName;
        },
      },
      {
        width: 30,
        header: t("department"),
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "department",
        render: (record: SupportTicket) => {
          return t(record.department);
        },
      },
      {
        width: 30,
        header: "Email",
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "nameEn",
        render: (record: SupportTicket) => {
          return record.customer?.email || record.partner?.email;
        },
      },
      {
        width: 20,
        header: t("phoneNumber"),
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "phone",
        render: (record: SupportTicket) => {
          return record.customer?.phone || record.partner?.phone;
        },
      },

      {
        width: 30,
        header: t("title"),
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "waitingTime",
        render: (record: SupportTicket) => {
          return record.title;
        },
      },

      {
        width: 30,
        header: t("content"),
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "content",
        render: (record: SupportTicket) => {
          return t(record.content);
        },
      },
      {
        width: 20,
        header: t("status"),
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "rewardPoint",
        render: (record: SupportTicket) => {
          return t("supportTicket" + record.status);
        },
      },
    ];
    // const customerDepositModalRef = useRef<SupportTicketModal>(null);
    const {
      supportTickets,
      fetchSupportTicket,
      loadingSupportTicket,
      querySupportTicket,
      totalSupportTicket,
    } = useSupportTicket({
      initQuery: {
        page: pageNumber ?? 1,
        limit: 20,
        status: status === SupportTicketStatus.All ? undefined : status,
        createdBy:
          type === SupportTicketCreatedBy.Customer
            ? SupportTicketCreatedBy.Customer
            : SupportTicketCreatedBy.Partner,
      },
    });

    useEffect(() => {
      querySupportTicket.createdBy =
        type === SupportTicketCreatedBy.Customer
          ? SupportTicketCreatedBy.Customer
          : SupportTicketCreatedBy.Partner;
      querySupportTicket.page = 1;
      fetchSupportTicket();
    }, [activeTabKey]);

    useEffect(() => {
      // document.title = getTitle(title);
      fetchSupportTicket();
    }, [querySupportTicket]);

    useEffect(() => {
      if (isFocus && parentLastUpdate != lastUpdate) {
        fetchSupportTicket();
        if (parentLastUpdate) setLastUpdate(parentLastUpdate);
      }
    }, [parentLastUpdate, lastUpdate, isFocus]);

    const debounceSearch = debounce((search) => {
      querySupportTicket.search = search;
      querySupportTicket.page = 1;
      fetchSupportTicket();
    }, 500);

    const handleSearch = (search: string) => {
      querySupportTicket.search = search;
      querySupportTicket.page = 1;
      fetchSupportTicket();
    };

    useEffect(() => {
      querySupportTicket.page = pageNumber ?? 1;
    }, []);

    return (
      <div>
        <div className="filter-container">
          <Space>
            <div className="filter-item">
              <label htmlFor="" className="!text-[#2A3547]">
                {t("search")}
              </label>
              <Input.Search
                onChange={(ev) => {
                  querySupportTicket.search = ev.currentTarget.value;
                  querySupportTicket.page = 1;
                }}
                allowClear
                size="large"
                // label={t("searchTitle")}
                className="w-full search-btn mt-1 "
                // placeholder={t("productName")}
                enterButton={<SearchIcon />}
                onKeyDown={(ev) => {
                  if (ev.code == "Enter") {
                    // fetchSupportTicket();
                  }
                }}
                onSearch={handleSearch}
              />
            </div>

            {/* <div className="filter-item ">
              <Button
                onClick={fetchSupportTicket}
                type="primary"
                icon={<SearchOutlined />}
              >
                {t("search")}
              </Button>
            </div> */}
            <div className="filter-item btn">
              <Popconfirm
                title={t("exportAsk")}
                onConfirm={() =>
                  handleExport({
                    onProgress(percent) { },
                    exportColumns,
                    fileType: "xlsx",
                    dataField: "supportTickets",
                    query: querySupportTicket,
                    api: supportTicketApi.findAll,
                    fileName: `${t("supportTicket")} ${typeTitle.toLowerCase()}`,
                    sheetName: `${t("supportTicket")} ${typeTitle.toLowerCase()}`,
                    // convertContent: true
                  })
                }
                okText={t("exportExcel")}
                cancelText={t("cancel")}
              >
                <Button
                  size="large"
                  type="primary"
                  loading={false}
                  icon={<DownloadIcon />}
                >
                  {t("exportExcel")}
                </Button>
              </Popconfirm>
            </div>
          </Space>
        </div>

        <SupportTicketList
          onEdit={(record) => { onEditModal?.(record) }}
          onRefreshData={() => {
            fetchSupportTicket();
            onSubmitOk?.();
          }}
          dataSource={supportTickets}
          loading={loadingSupportTicket}
          //   loadingDelete={loadingDelete}
          pagination={{
            total: totalSupportTicket,
            defaultPageSize: querySupportTicket.limit,
            currentPage: querySupportTicket.page,
            onChange: ({ page, limit }) => {
              setPageNumber?.(page);
              Object.assign(querySupportTicket, {
                page,
                limit,
              });
              fetchSupportTicket();
            },
          }}
          type={type}
        />
      </div>
    );
  }
);
