// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
import React, { useEffect, useState } from "react";
import { Link } from "react-router-dom";

// mui imports
import {
  Chip,
  List,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  styled,
  Typography,
  useTheme,
} from "@mui/material";
import { observer } from "mobx-react";
import { useTranslation } from "react-i18next";
import { appStore } from "store/appStore";
import { FaCircle } from "react-icons/fa6";
import { SvgIcon } from "components/Common/SvgIcon";
import clsx from "clsx";
import { supportTicketApi } from "api/supportTicket.api";
import { SupportTicketCreatedBy } from "types/supportTicket";

type NavGroup = {
  [x: string]: any;
  id?: string;
  navlabel?: boolean;
  subheader?: string;
  title?: string;
  icon?: any;
  href?: string;
  children?: NavGroup[];
  chip?: string;
  chipColor?: any;
  variant?: string | any;
  external?: boolean;
  level?: number;
  onClick?: React.MouseEvent<HTMLButtonElement, MouseEvent>;
};

interface ItemType {
  item: NavGroup;
  hideMenu?: any;
  onClick: (event: React.MouseEvent<HTMLElement>) => void;
  level?: number | any;
  pathDirect: string;
}

const NavItem = ({ item, level, pathDirect, hideMenu, onClick }: ItemType) => {
  const customizer = appStore.customizer;
  const isSelected = pathDirect === item.href;
  const Icon = item?.icon;
  const theme = useTheme();
  const { t } = useTranslation();
  // const itemIcon =
  //   level > 1 ? (
  //     <Icon stroke={1.5} size="1rem" />
  //   ) : (
  //     <Icon stroke={1.5} size="1.3rem" />
  //   );

  const ListItemStyled = styled(ListItemButton)(() => ({
    whiteSpace: "nowrap",
    // marginBottom: "2px",

    padding: "8px 10px",
    width: hideMenu && "48px",
    height: "48px",
    // marginLeft: hideMenu && "2px",
    borderRadius: `${customizer.borderRadius}px`,
    backgroundColor: level > 1 ? "transparent !important" : "inherit",
    color:
      level > 1 && pathDirect === item?.href
        ? `${theme.palette.primary.main}!important`
        : theme.palette.text.secondary,
    paddingLeft: hideMenu ? "10px" : level > 2 ? `${level * 15}px` : "10px",
    "&:hover": {
      backgroundColor: theme.palette.primary.light,
      color: theme.palette.primary.main,
    },
    "&.Mui-selected": {
      color: "white",
      backgroundColor: theme.palette.primary.main,
      "&:hover": {
        backgroundColor: theme.palette.primary.main,
        color: "white",
      },
    },
  }));

  const listItemProps: {
    component: any;
    href?: string;
    target?: any;
    to?: any;
  } = {
    component: item?.external ? "a" : Link,
    to: item?.href,
    href: item?.external ? item?.href : "",
    target: item?.external ? "_blank" : "",
  };


  const [totalCustomer, setTotalCustomer] = useState(0);
  const [totalPartner, setTotalPartner] = useState(0);


  const isSticketCustomer = item?.href === "/support-ticket/support-ticket-customer";
  const isSticketParnert = item?.href === "/support-ticket/support-ticket-partner";

  useEffect(() => {
    if (isSticketCustomer) {
      const fetchSummary = async () => {
        const res = await supportTicketApi.summaryStatus({ createdBy: SupportTicketCreatedBy.Customer });
        const total = res.data?.find((item: any) => item.status === "NEW")?.total;
        setTotalCustomer(total);
      };

      fetchSummary();
    }
    if (isSticketParnert) {
      const fetchSummary = async () => {
        const res = await supportTicketApi.summaryStatus({ createdBy: SupportTicketCreatedBy.Partner });

        const total = res.data?.find((item: any) => item.status === "NEW")?.total;
        setTotalPartner(total);
      };

      fetchSummary();
    }
  }, [appStore.refreshSticket]);

  return (
    <List component="li" disablePadding key={item?.id && item.title}>
      <ListItemStyled
        {...listItemProps}
        disabled={item?.disabled}
        selected={pathDirect === item?.href}
        onClick={onClick}
      >
        <ListItemIcon
          sx={{
            minWidth: "36px",
            p: "3px 0",
            color:
              level > 1 && pathDirect === item?.href
                ? `${theme.palette.primary.main} !important`
                : "inherit",
            marginLeft: hideMenu && "-2px",
            justifyContent: hideMenu ? "center" : "",
            alignItems: "center",
          }}
        >
          {(level === 1 || !level) && (
            <div
              className={clsx(isSelected && "h-fit invert brightness-0")}
              style={{ width: 24, height: 24 }}
            >
              {Icon}
            </div>
          )}
          {level !== 0 && pathDirect === item?.href && (
            <div className="w-[21px] h-[21px] flex justify-center items-center">
              <FaCircle size={4} />
            </div>
          )}
        </ListItemIcon>
        <ListItemText>
          {!hideMenu && (
            <div className="text-ellipsis overflow-hidden whitespace-nowrap">
              {t(`${item?.title}`)}
            </div>
          )}

          {item?.subtitle && (
            <>
              <br />
              <Typography variant="caption">
                {hideMenu ? "" : item?.subtitle}
              </Typography>
            </>
          )}
        </ListItemText>
        {((!!isSticketCustomer || !!isSticketParnert) || item?.chip || hideMenu) && (
          <Chip
            color={item?.chipColor ?? "warning"}
            variant={item?.variant ? item?.variant : "filled"}
            size="small"
            label={totalCustomer || totalPartner || item?.chip}
            className="ml-1 count-navbar"
          />
        )}
      </ListItemStyled>
    </List>
  );
};

export default observer(NavItem);
