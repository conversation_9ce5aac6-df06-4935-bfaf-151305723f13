"use client";
import * as React from "react";
import { BarChart } from "@mui/x-charts/BarChart";
import { useTheme } from "@mui/material";
import { DatePicker } from "antd";
import dayjs from "dayjs";
import { useTranslation } from "react-i18next";

const BCrumb = [
  {
    to: "/",
    title: "Home",
  },
  {
    title: "SimpleBarChart ",
  },
];
interface BarChartProps {
  data: any[];
  refetch: () => void;
  query: any;
  setQuery: any;
  label: string;
}
export default function SimpleBarChart({
  data,
  refetch,
  query,
  setQuery,
  label,
}: BarChartProps) {
  console.log("Data nhận được", data);
  const uData = [4000, 3000, 2000, 2780, 1890, 2390, 3490];
  const pData = [2400, 1398, 9800, 3908, 4800, 3800, 4300];
  const xLabels = [
    "Week 1",
    "Week 2",
    "Week 3",
    "Week 4",
    "Week 5",
    "Week 6",
    "Week 7",
  ];
  const { t } = useTranslation();
  const parseNumber = (val: string): number => +val?.replace(/\s/g, "");

  const theme = useTheme();
  const primary = theme.palette.primary.main;
  const secondary = theme.palette.secondary.main;
  const chartLabels = data?.map((item) => item.date); // trục X
  const chartData = data?.map((item) => item.totalMoney) || []; // giá trị cột
  React.useEffect(() => {
    refetch();
  }, [query]);
  return (
    <>
      <DatePicker
        picker="month"
        style={{ width: 300 }}
        allowClear
        // value={[
        //   queryCustomer.fromAt
        //     ? dayjs.unix(queryCustomer.fromAt)
        //     : null,
        //   queryCustomer.toAt ? dayjs.unix(queryCustomer.toAt) : null,
        // ]}
        value={query.fromAt ? dayjs.unix(query.fromAt) : null}
        onChange={(value) => {
          console.log("datepicker", value);
          if (value) {
            setQuery({
              ...query,
              fromAt: dayjs(value).startOf("month").unix(),
              toAt: dayjs(value).endOf("month").unix(),
              page: 1,
            });
            refetch();
          } else {
            const updatedQuery = { ...query };
            delete updatedQuery.fromAt;
            delete updatedQuery.toAt;
            updatedQuery.page = 1;
            setQuery(updatedQuery);
          }
        }}
      />
      {/* <BarChart
        title="Chi phí"
        height={300}
        margin={{ left: 100 }}
        borderRadius={6}
        series={[
          {
            data: chartData,
            label: "Tổng tiền",
            id: "totalMoney",
            color: primary,
          },
        ]}
        xAxis={[
          {
            data: chartLabels,
            scaleType: "band",
            categoryGapRatio: 0.8,
            barGapRatio: 0.8,
          } as any,
        ]}
        // yAxis={[
        //   {
        //     valueFormatter: (value: number) =>
        //       new Intl.NumberFormat("vi-VN", {
        //         style: "decimal",
        //       }).format(value),
        //   },
        // ]}
      /> */}
      {chartData.length === 0 ? (
        <div className="h-[300px] flex items-center justify-center text-gray-500">
          {t("noData")}
        </div>
      ) : (
        <BarChart
          height={300}
          margin={{ left: 100 }}
          borderRadius={6}
          series={[
            {
              data: chartData,
              label: label,
              color: primary,
            },
          ]}
          xAxis={[
            {
              data: chartLabels,
              scaleType: "band",
              categoryGapRatio: 0.8,
              barGapRatio: 0.8,
            } as any,
          ]}
        />
      )}
    </>
  );
}
