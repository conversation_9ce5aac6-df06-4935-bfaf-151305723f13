import { Box, Grid2 as Grid, Stack, Typography } from "@mui/material";
import Logo from "components/Logo/Logo";
import { useEffect } from "react";
import { getTitle } from "utils";
import img1 from "assets/svgs/login-bg.svg";
import { Link } from "react-router-dom";
import AuthRegister from "components/Auth/AuthRegister";
import { useTranslation } from "react-i18next";

const RegisterPage = ({ title = "" }) => {
  const { t } = useTranslation();

  useEffect(() => {
    document.title = getTitle(title);
  }, []);

  return (
    <Grid container spacing={0} sx={{ overflowX: "hidden" }}>
      <Grid
        sx={{
          position: "relative",
          "&:before": {
            content: '""',
            background: "radial-gradient(#d2f1df, #d3d7fa, #bad8f4)",
            backgroundSize: "400% 400%",
            animation: "gradient 15s ease infinite",
            position: "absolute",
            height: "100%",
            width: "100%",
            opacity: "0.3",
          },
        }}
        size={{
          xs: 12,
          sm: 12,
          lg: 7,
          xl: 8,
        }}
      >
        <Box position="relative">
          <Box px={3}>
            <Logo />
          </Box>
          <Box
            alignItems="center"
            justifyContent="center"
            height={"calc(100vh - 75px)"}
            sx={{
              display: {
                xs: "none",
                lg: "flex",
              },
            }}
          >
            <img
              src={img1}
              alt="bg"
              style={{
                width: "100%",
                maxWidth: "500px",
              }}
            />
          </Box>
        </Box>
      </Grid>
      <Grid
        display="flex"
        justifyContent="center"
        alignItems="center"
        size={{
          xs: 12,
          sm: 12,
          lg: 5,
          xl: 4,
        }}
      >
        <div className="w-full max-w-[500px]">
          <Box p={4}>
            <AuthRegister
              title={t("welcomeArivi")}
              subtext={
                <Typography variant="subtitle1" color="textSecondary" mb={1}>
                  Register your account now!
                </Typography>
              }
              subtitle={
                <Stack direction="row" spacing={1} mt={3}>
                  <Typography
                    color="textSecondary"
                    variant="h6"
                    fontWeight="400"
                  >
                    Already have an Account?
                  </Typography>
                  <Typography
                    component={Link}
                    to="/login"
                    fontWeight="500"
                    sx={{
                      textDecoration: "none",
                      color: "primary.main",
                    }}
                  >
                    Sign In
                  </Typography>
                </Stack>
              }
            />
          </Box>
        </div>
      </Grid>
    </Grid>
  );
};

export default RegisterPage;
