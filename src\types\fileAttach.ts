import { Course } from "./course";
import { Staff } from "./staff";

export enum FileAttachType {
  Image = "IMAGE",
  Pdf = "PDF",
  Other = "OTHER",
}

export interface FileAttach {
  id: number;
  createdAt: number;
  updatedAt: number;
  deletedBy: string;
  isDeleted: boolean;
  deletedAt: number;
  name: string;
  size: number;
  type: FileAttachType;
  url: string;
  course: Course;
  createdStaff: Staff;
  path: string;
}
