import { request } from "utils/request";
import { AxiosPromise } from "axios";

export const partnerTransactionApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/partnerTransaction",
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/partnerTransaction",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/partnerTransaction/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/partnerTransaction/${id}`,
      method: "delete",
    }),
};
