import { request } from "utils/request";
import { AxiosPromise } from "axios";

export const keywordApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/keyword",
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/keyword",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/keyword/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/keyword/${id}`,
      method: "delete",
    }),
};
