// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
import React from "react";
import {
  Stack,
  Avatar,
  Box,
  Typography,
  <PERSON>lt<PERSON>,
  Fab,
  <PERSON>Field,
  Button,
  Chip,
} from "@mui/material";
import { IconArrowBackUp, IconCircle } from "@tabler/icons-react";
import { SupportTicket, SupportTicketCreatedBy } from "types/supportTicket";
import { settings } from "settings";
import { useTranslation } from "react-i18next";
import { unixToFullDate } from "utils/dateFormat";
import { observer } from "mobx-react";
import { userStore } from "store/userStore";

interface Props {
  supportTickets: SupportTicket[];
  onReply?: () => void;
}

const SupportTicketReply = ({ supportTickets, onReply }: Props) => {
  const { t } = useTranslation();

  const getReplierInfo = (supportTicket: SupportTicket) => {
    if (supportTicket.createdBy == SupportTicketCreatedBy.Admin) {
      return {
        avatar: supportTicket.staff?.avatar,
        name: supportTicket.staff?.fullName,
      };
    } else if (supportTicket.createdBy == SupportTicketCreatedBy.Customer) {
      return {
        avatar: supportTicket.customer?.avatar,
        name: supportTicket.customer?.name,
      };
    } else if (supportTicket.createdBy == SupportTicketCreatedBy.Partner) {
      return {
        avatar: supportTicket.partner?.avatar,
        name: supportTicket.partner?.fullName,
      };
    } else {
      return {
        avatar: settings.defaultAvatar,
        name: "",
      };
    }
  };

  return (
    <Stack spacing={1}>
      {supportTickets.length > 0 ? (
        supportTickets.map((supportTicket) => {
          const { avatar, name } = getReplierInfo(supportTicket);
          console.log({ supportTicket });
          return (
            <Box key={supportTicket.id} boxShadow={1}>
              <Box p={2} sx={{ backgroundColor: "grey.100" }}>
                <Stack direction={"row"} gap={2} alignItems="center">
                  <Avatar alt={name} src={avatar} />
                  <Typography variant="h6">{name}</Typography>
                  <Chip
                    size="small"
                    label={t(supportTicket.createdBy)}
                    color="primary"
                  />
                  <Typography variant="caption" color="textSecondary">
                    <IconCircle
                      size="7"
                      fill=""
                      fillOpacity={"0.1"}
                      strokeOpacity="0.1"
                    />{" "}
                    {unixToFullDate(supportTicket.createdAt)}
                  </Typography>
                </Stack>
                <Box py={2}>
                  <Typography
                    color="textSecondary"
                    className="whitespace-pre-wrap"
                  >
                    {supportTicket.content}
                  </Typography>
                </Box>
              </Box>
            </Box>
          );
        })
      ) : (
        <Typography>{t("noReplyYet")}</Typography>
      )}
    </Stack>
  );
};

export default observer(SupportTicketReply);
