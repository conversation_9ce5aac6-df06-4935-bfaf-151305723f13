import { userStore } from "store/userStore";
import settingsData from "./settings.json";
import user from "../src/assets/images/user.png";
import logo from "./assets/svgs/Logo.svg";

export const settings = {
  logo,
  defaultAvatar: user,
  checkPermission: true,
  version: settingsData.version,
  dateFormat: "DD/MM/YYYY",
  dateFormat2: "DD-MM-YYYY",
  fullDateFormat: "HH:mm, DD/MM/YYYY",
  isDev: import.meta.env.VITE_IS_DEV == "true",
  isProduction: import.meta.env.VITE_IS_PRODUCTION == "true",
  qrUrl: `https://302broker.bmdapp.store/cai-dat`,
  googleMapApiKey: import.meta.env.VITE_GOOGLE_API_KEY,
};
