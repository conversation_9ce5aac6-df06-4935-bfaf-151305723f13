import {
  DownOutlined,
  EditOutlined,
  KeyOutlined,
  LockOutlined,
  UnlockOutlined,
} from "@ant-design/icons";
import { Avatar, Button, Popconfirm, Space, Table, Tag } from "antd";
import Column from "antd/lib/table/Column";
import { IPagination, Pagination } from "components/Pagination";
import { AriviTable } from "components/Table/AriviTable";
import DropdownCell from "components/Table/DropdownCell";
import { useTranslation } from "react-i18next";
import { GrEdit } from "react-icons/gr";
import { HiOutlineTrash } from "react-icons/hi2";
import {
  Partner,
  PartnerStatus,
  PartnerStatusTrans,
  PartnerVerifyStatus,
  PartnerVerifyStatusTrans,
} from "types/partner";
import { formatVND } from "utils";
import { unixToFullDate } from "utils/dateFormat";
import {
  PartnerProfileModal,
  PartnerProfileModalRef,
} from "../Modal/PartnerProfileModal";
import { useRef } from "react";

interface PropsType {
  dataSource: Partner[];
  loading: boolean;
  loadingDelete?: boolean;
  pagination?: IPagination;
  onEdit?: (partner: Partner) => void;
  onReset?: (partner: Partner) => void;

  onDelete?: (partnerId: number) => void;
  onActive?: (partnerId: number) => void;
  onInactive?: (partnerId: number) => void;
  onVerify?: (partnerId: number) => void;
  onUnverify?: (partnerId: number) => void;
  onSubmitOk?: () => void;
  // hasDeletePartnerPermission?: boolean;
  // hasUpdatePartnerPermission?: boolean;
}

export const PartnerList = ({
  dataSource,
  loading,
  loadingDelete,
  pagination,
  onDelete,
  onEdit,
  onActive,
  onInactive,
  onVerify,
  onReset,
  onUnverify,
  onSubmitOk,
}: // hasDeletePartnerPermission,
  // hasUpdatePartnerPermission,
  PropsType) => {
  const { t } = useTranslation();
  const partnerProfileModalRef = useRef<PartnerProfileModalRef>();

  return (
    <div>
      <AriviTable
        scroll={{ x: "max-content", y: "calc(100vh - 310px)" }}
        loading={loading}
        pagination={false}
        rowKey="id"
        dataSource={dataSource}
        size="small"
        className="custom-scrollbar"
      // onChange={}
      >
        <Column
          title={t("code")}
          dataIndex="code"
          key={"code"}
          render={(text, record: Partner) => <span>{record.code}</span>}
        />
        <Column
          title={t("partnerName")}
          dataIndex="fullName"
          key={"name"}
          render={(text, record: Partner) => {
            return (
              <div className="flex items-center gap-2">
                <Avatar
                  src={
                    record.avatar ||
                    "https://i.pinimg.com/736x/0d/64/98/0d64989794b1a4c9d89bff571d3d5842.jpg"
                  }
                  className="!h-[30px] !w-[30px] object-contain"
                />
                <span
                  onClick={() => {
                    onEdit?.(record)
                  }}
                  className="font-semibold cursor-pointer text-primary"
                >
                  {text || "--"}
                </span>
              </div>
            );
          }}
        />
        <Column
          title={t("rank")}
          dataIndex="rank"
          key={"rank"}
          render={(text, record: Partner) => <span>{record.rank?.name}</span>}
        />
        <Column
          title="Email"
          dataIndex="email"
          key={"email"}
          render={(text, record: Partner) => <span>{record.email}</span>}
        />
        <Column
          title={t("phoneNumber")}
          dataIndex="phone"
          key={"phone"}
          render={(text, record: Partner) => <span>{record.phone}</span>}
        />
        <Column
          width={100}
          title={t("balance")}
          align="right"
          dataIndex="balance"
          key={"balance"}
          sorter={(a: Partner, b: Partner) =>
            (a.balance || 0) - (b.balance || 0)
          }
          render={(text, record: Partner) => (
            <span>{formatVND(record.balance)}</span>
          )}
        />

        <Column
          title={t("activeStatus")}
          width={170}
          align="center"
          dataIndex="name"
          key={"name"}
          render={(text, record: Partner) => (
            <Tag color={record.isBlocked ? "red" : "green"}>
              {record.isBlocked ? t("locked") : t("open")}
            </Tag>
          )}
        />

        <Column
          title={t("verifyStatus")}
          dataIndex="name"
          width={170}
          align="center"
          key={"name"}
          render={(text, record: Partner) => (
            <>
              <Tag color={PartnerVerifyStatusTrans[record.verifyStatus]?.color}>
                {t(record.verifyStatus)}
              </Tag>
              {record.verifyStatus === PartnerVerifyStatus.Pending && (
                <div className="mt-2">
                  <Button
                    type="primary"
                    onClick={() => {
                      partnerProfileModalRef.current?.handleUpdate(record);
                    }}
                  >
                    {t("viewProfile")}
                  </Button>
                </div>
              )}
            </>
          )}
        />
        <Column
          width={150}
          title={t("createdAt")}
          dataIndex="createdAt"
          key="customer.createdAt"
          render={(text, record: Partner) => {
            return unixToFullDate(record.createdAt);
          }}
        />
        {/* <Column
            align="right"
            width={200}
            title="Giá dịch vụ"
            dataIndex="price"
            key={"price"}
            render={(text, record: Partner) => (
              <span className="">{formatVND(record.price)}</span>
            )}
          />
          <Column
            align="right"
            width={200}
            title="Số lượng đánh giá"
            dataIndex="numReview"
            key={"numReview"}
            render={(text, record: Partner) => (
              <span className="">{formatVND(record.numReview)}</span>
            )}
          />
          <Column
            align="right"
            width={250}
            title="Số ngày thực hiện dịch vụ dự kiến"
            dataIndex="estimatedDay"
            key={"estimatedDay"}
            render={(text, record: Partner) => (
              <span className="">{formatVND(record.estimatedDay)}</span>
            )}
          />
          <Column
            align="right"
            width={250}
            title="Số lượng ký tự của đánh giá "
            dataIndex="maxLengthCharReview"
            key={"maxLengthCharReview"}
            render={(text, record: Partner) => (
              <span className="">{formatVND(record.maxLengthCharReview)}</span>
            )}
          /> */}
        <Column
          fixed="right"
          width={120}
          align="center"
          title=""
          key="action"
          dataIndex={""}
          render={(text, record: Partner) => (
            //@ts-ignore
            <DropdownCell
              text={t("action")}
              items={[
                {
                  onClick: () => "",
                  // createOrderModalRef.current?.handleUpdate(record),
                  label: (
                    <Button
                      icon={<GrEdit />}
                      type="primary"
                      className="w-full justify-center !flex !items-center gap-2 !font-medium"
                      onClick={() => onEdit?.(record)}
                    >
                      {t("detail")}
                    </Button>
                  ),
                  key: "update",
                  // hidden: !hasUpdatePartnerPermission,
                },
                {
                  onClick: () => "",
                  // createOrderModalRef.current?.handleUpdate(record),
                  label: (
                    <Button
                      //   block
                      //   ghost
                      onClick={() => onReset?.(record)}
                      icon={<KeyOutlined />}
                    >
                      {t("resetPass")}
                    </Button>
                  ),
                  key: "reset",
                },
                {
                  onClick: () => "",
                  // createOrderModalRef.current?.handleUpdate(record),
                  label: (
                    <Popconfirm
                      title={t("confirm?")}
                      onConfirm={() => {
                        if (!record.isBlocked) {
                          onInactive?.(record.id);
                        } else {
                          onActive?.(record.id);
                        }
                      }}
                      okText={t("yes")}
                      cancelText={t("no")}
                    >
                      <Button
                        icon={
                          !record.isBlocked ? (
                            <LockOutlined />
                          ) : (
                            <UnlockOutlined />
                          )
                        }
                        type="primary"
                        className={`w-full justify-center !flex !items-center gap-2 !font-medium ${!record.isBlocked
                          ? "bg-red-500"
                          : "bg-blue-500"
                          }`}
                      >
                        {!record.isBlocked ? t("lock") : t("unlock")}
                      </Button>
                    </Popconfirm>
                  ),
                  key: "updateStatus",
                  // hidden: !hasUpdatePartnerPermission,
                },
                // {
                //   label: (
                //     <Popconfirm
                //       placement="topLeft"
                //       title={
                //         <div>
                //           <h1 className="text-sm">{t("confirm?")}</h1>
                //         </div>
                //       }
                //       onConfirm={() => onDelete?.(record.id)}
                //       okText={t("yes")}
                //       cancelText={t("no")}
                //     >
                //       <Button
                //         loading={loadingDelete}
                //         icon={<HiOutlineTrash className="text-lg" />}
                //         className={`w-full justify-center !flex !items-center gap-2 !text-red-500 !font-medium`}
                //       >
                //         {t("delete")}
                //       </Button>
                //     </Popconfirm>
                //   ),
                //   key: "delete",
                //   // hidden: !hasDeletePartnerPermission,
                // },
              ]}
              trigger={["click"]}
            >
              <a onClick={(e) => e.preventDefault()}>
                <Space>
                  {t("action")}
                  <DownOutlined />
                </Space>
              </a>
            </DropdownCell>
          )}
        />
      </AriviTable>
      <PartnerProfileModal
        ref={partnerProfileModalRef}
        onClose={function (): void {
          throw new Error("Function not implemented.");
        }}
        onSubmitOk={onSubmitOk}
        onVerify={onVerify}
        onUnverify={onUnverify}
      />
      {pagination && <Pagination {...pagination} />}
    </div>
  );
};
