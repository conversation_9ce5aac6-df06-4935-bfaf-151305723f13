import { Role } from "./role";

export interface Staff {
  id: number;
  createdAt: number;
  updatedAt: number;
  isDeleted: boolean;
  code: string;
  username: string;
  password: string;
  fullName: string;
  avatar: string;
  phone: string;
  email: string;
  isBlocked: boolean;
  companyName: string; //đơn vị chủ quản
  googleCredentialJson: string;
  isDev: boolean;
  role: Role;
  position: string;
  profileImage: string;
  visibleOnWeb: boolean;
  // activityLogs: ActivityLog[];
  // oneSignals: OneSignal[]
  viewedNotifications: Notification[];
  language: string;
  // inventories: Inventory[];
}
