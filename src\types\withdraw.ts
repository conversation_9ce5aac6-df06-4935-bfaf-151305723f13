import { Partner } from "./partner";
import { PartnerTransaction } from "./partnerTransaction";
import { Staff } from "./staff";

export enum WithdrawStatus {
  All = "ALL",
  Pending = "PENDING",
  Processing = "PROCESSING",
  Approve = "APPROVE",
  Reject = "REJECT",
}

export const WithdrawStatusTrans = {
  [WithdrawStatus.All]: {
    value: WithdrawStatus.All,
    color: "blue",
    label: WithdrawStatus.All,
  },
  [WithdrawStatus.Pending]: {
    value: WithdrawStatus.Pending,
    color: "yellow",
    label: WithdrawStatus.Pending
  },
  [WithdrawStatus.Processing]: {
    value: WithdrawStatus.Processing,
    color: "orange",
    label: WithdrawStatus.Processing
  },
  [WithdrawStatus.Approve]: {
    value: WithdrawStatus.Approve,
    color: "green",
    label: "PROCESSED"
  },
  [WithdrawStatus.Reject]: {
    value: WithdrawStatus.Reject,
    color: "red",
    label: WithdrawStatus.Reject
  },
};

export interface Withdraw {
  id: number;
  createdAt: number;
  updatedAt: number;
  isDeleted: boolean;
  code: string;
  note: string;
  inspecNote: string;
  amount: number;
  bankName: string;
  status: WithdrawStatus;
  inspecAt: number;
  partner: Partner;
  createdAdmin: Staff;
  inspecStaff: Staff;
  partnerTransactions: PartnerTransaction[];
  bank?: any
}
