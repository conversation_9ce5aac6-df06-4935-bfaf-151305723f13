import {
  DownOutlined,
  EditOutlined,
  LockOutlined,
  UnlockOutlined,
} from "@ant-design/icons";
import { Button, Image, Popconfirm, Space, Table } from "antd";
import Column from "antd/lib/table/Column";
import { IPagination, Pagination } from "components/Pagination";
import { AriviTable } from "components/Table/AriviTable";
import DropdownCell from "components/Table/DropdownCell";
import { useTranslation } from "react-i18next";
import { GrEdit } from "react-icons/gr";
import { HiOutlineTrash } from "react-icons/hi2";
import { Rank } from "types/rank";
import { formatVND } from "utils";
import { unixToFullDate } from "utils/dateFormat";

interface PropsType {
  dataSource: Rank[];
  loading: boolean;
  loadingDelete?: boolean;
  pagination?: IPagination;
  onEdit?: (rank: Rank) => void;
  onDelete?: (rankId: number) => void;
  onActive?: (rankId: number) => void;
  onInactive?: (rankId: number) => void;

  // hasDeleteRankPermission?: boolean;
  // hasUpdateRankPermission?: boolean;
}

export const RankList = ({
  dataSource,
  loading,
  loadingDelete,
  pagination,
  onDelete,
  onEdit,
  onActive,
  onInactive,
}: // hasDeleteRankPermission,
// hasUpdateRankPermission,
PropsType) => {
  const { t } = useTranslation();

  return (
    <div>
      <AriviTable
        loading={loading}
        pagination={false}
        rowKey="id"
        dataSource={dataSource}
        size="small"
        className="custom-scrollbar"
        scroll={{ x: "max-content", y: "calc(100vh - 380px)" }}
        // onChange={}
      >
        <Column
          align="center"
          width={100}
          title={t("image")}
          dataIndex="icon"
          key={"icon"}
          render={(text, record: Rank) => (
            <Image
              src={record.icon}
              preview={false}
              className="!h-[35px] object-contain"
            />
          )}
        />
        <Column
          title={t("rankName")}
          dataIndex="name"
          key={"name"}
          render={(text, record: Rank) => (
            <div>
              <div>
                <span className="font-bold">{t("vietnamese")}</span>:{" "}
                {record.name}
              </div>
              <div>
                <span className="font-bold">{t("english")}</span>:{" "}
                {record.nameEn}
              </div>
            </div>
          )}
        />
        <Column
          title={t("position")}
          dataIndex="name"
          width={100}
          key={"name"}
          align="right"
          render={(text, record: Rank) => <span>{record.position}</span>}
        />
        <Column
          title={t("waitingTime")}
          width={270}
          dataIndex="name"
          key={"name"}
          align="right"
          render={(text, record: Rank) => <span>{record.waitingTime}</span>}
        />
        <Column
          title={t("needMission")}
          width={270}
          dataIndex="name"
          key={"name"}
          align="right"
          render={(text, record: Rank) => <span>{record.needReview}</span>}
        />
        <Column
          title={t("moneyImage")}
          width={150}
          dataIndex="name"
          key={"name"}
          align="right"
          render={(text, record: Rank) => (
            <span>{formatVND(record.rewardPointByImage)}</span>
          )}
        />
        <Column
          title={t("moneyMission")}
          width={150}
          dataIndex="name"
          key={"name"}
          align="right"
          render={(text, record: Rank) => (
            <span>{formatVND(record.rewardPoint)}</span>
          )}
        />
        <Column
          fixed="right"
          width={120}
          align="center"
          title=""
          key="action"
          dataIndex={""}
          render={(text, record: Rank) => (
            //@ts-ignore
            <DropdownCell
              text={t("action")}
              items={[
                {
                  onClick: () => "",
                  // createOrderModalRef.current?.handleUpdate(record),
                  label: (
                    <Button
                      icon={<GrEdit />}
                      type="primary"
                      className="w-full justify-center !flex !items-center gap-2 !font-medium"
                      onClick={() => onEdit?.(record)}
                    >
                      {t("update")}
                    </Button>
                  ),
                  key: "update",
                  // hidden: !hasUpdateRankPermission,
                },

                {
                  label: (
                    <Popconfirm
                      placement="topLeft"
                      title={
                        <div>
                          <h1 className="text-sm">{t("confirm?")}</h1>
                        </div>
                      }
                      onConfirm={() => onDelete?.(record.id)}
                      okText={t("yes")}
                      cancelText={t("no")}
                    >
                      <Button
                        loading={loadingDelete}
                        icon={<HiOutlineTrash className="text-lg" />}
                        className={`w-full justify-center !flex !items-center gap-2 !text-red-500 !font-medium`}
                      >
                        {t("delete")}
                      </Button>
                    </Popconfirm>
                  ),
                  key: "delete",
                  // hidden: !hasDeleteRankPermission,
                },
              ]}
              trigger={["click"]}
            >
              <a onClick={(e) => e.preventDefault()}>
                <Space>
                  {t("action")}
                  <DownOutlined />
                </Space>
              </a>
            </DropdownCell>
          )}
        />
      </AriviTable>
      {pagination && <Pagination {...pagination} />}
    </div>
  );
};
