// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
import React from "react";
import { useTheme } from "@mui/material/styles";
import { Card, CardHeader, CardContent, Divider, Box } from "@mui/material";
import { appStore } from "store/appStore";
import { observer } from "mobx-react";

type Props = {
  title: React.ReactNode;
  footer?: string | any;
  codeModel?: any | any[];
  children: any;
  className?: string;
  titleClassName?: string;
  contentClassName?: string;
  hiddenBorder?: boolean
};

const ParentCard = ({
  title,
  children,
  footer,
  codeModel,
  className,
  titleClassName,
  contentClassName,
  hiddenBorder = false
}: Props) => {
  const customizer = appStore.customizer;

  const theme = useTheme();
  const borderColor = theme.palette.divider;

  return (
    <Card
      sx={{
        padding: 0,
        border: !customizer.isCardShadow ? `1px solid ${borderColor}` : "none",
      }}
      elevation={(customizer.isCardShadow && !hiddenBorder) ? 9 : 0}
      variant={!customizer.isCardShadow ? "outlined" : undefined}
      className={className}
    >
      <CardHeader className={titleClassName} title={title} action={codeModel} />
      <Divider />

      <CardContent className={contentClassName}>{children}</CardContent>
      {footer ? (
        <>
          <Divider />
          <Box p={3}>{footer}</Box>
        </>
      ) : (
        ""
      )}
    </Card>
  );
};

export default observer(ParentCard);
