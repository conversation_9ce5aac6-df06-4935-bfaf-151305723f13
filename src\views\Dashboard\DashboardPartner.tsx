import React, { useEffect, useMemo } from 'react';
import { Row, Col, Select, Typography, Spin } from 'antd';
import ChartProject from './components/customer/ChartProject';
import { observer } from 'mobx-react';
import Map from './components/customer/Map';
import { useTranslation } from 'react-i18next';
import { formatNumber, getTitle } from 'utils';
import DashboardStats, { StatItem } from './components/partner/Stats';
import { ReactComponent as PartnerIcon } from "assets/svgs/sum-partner.svg";
import { ReactComponent as PartnerSuccessIcon } from "assets/svgs/partner-success.svg";
import { ReactComponent as SumMissionIcon } from "assets/svgs/sum-mission.svg";
import { ReactComponent as MissionSystemIcon } from "assets/svgs/mission-system.svg";
import { ReactComponent as MissionAdminIcon } from "assets/svgs/mission-admin.svg";
import { ReactComponent as MissionSuccessIcon } from "assets/svgs/mission-success.svg";
import { ReactComponent as MissionFailIcon } from "assets/svgs/mission-fail.svg";
import { ReactComponent as MissionOvertimeIcon } from "assets/svgs/mission-overtime.svg";
import { ReactComponent as ReviewGuaranteeIcon } from "assets/svgs/review-guarantee.svg";
import TopPartnerList from './components/partner/Top';
import { useDashboard } from 'hooks/useDashboard';
import PieChartCard from './components/partner/PieChartCardProps';
import { colorsLevel } from 'types/dashboard';
import PartnerMap from "./components/partner/Map";
import { ReviewStatus } from 'types/review';

export const ReviewStatusData = {
  [ReviewStatus.Pending]: {
    value: ReviewStatus.Pending,
    icon: <MissionOvertimeIcon />,
    note: "pendingStatusNote",
  },
  [ReviewStatus.SystemPending]: {
    value: ReviewStatus.SystemPending,
    icon: <MissionSystemIcon />,
    note: "systemStatusNote",
  },
  [ReviewStatus.AdminPending]: {
    value: ReviewStatus.AdminPending,
    icon: <MissionAdminIcon />,
    note: "adminStatusNote",
  },
  [ReviewStatus.Complete]: {
    value: ReviewStatus.Complete,
    icon: <MissionSuccessIcon />,
    note: "completeStatusNote",
  },
  [ReviewStatus.Reject]: {
    value: ReviewStatus.Reject,
    icon: <MissionFailIcon />,
    note: "rejectStatusNote",
  },
  [ReviewStatus.Warranty]: {
    value: ReviewStatus.Warranty,
    icon: <ReviewGuaranteeIcon />,
    note: "warrantyStatusNote",
  },
  [ReviewStatus.RequestWarranty]: {
    value: ReviewStatus.RequestWarranty,
    icon: <MissionFailIcon />,
    note: "requestWarrantyNote",
  },
  [ReviewStatus.RejectWarranty]: {
    value: ReviewStatus.RejectWarranty,
    icon: <MissionOvertimeIcon />,
    note: "rejectWarrantyNote",
  },
};





export const DashboardPartner = observer(({ title = "" }) => {
  const { t, i18n } = useTranslation();
  const currentLanguage = i18n.language;

  const {
    loadingTopPartners,
    topPartners,
    fetchTopPartners,
    partnerRank,
    fetchPartnerRank,
    loadingPartnerRank,
    summaryReview,
    fetchSummaryReview,
    loadingSummaryReview,

    partnerCount,
    fetchPartnerCount,
    loadingPartnerCount,

    summaryReviewTotal,
    fetchSummaryReviewTotal,
    loadingSummaryReviewTotal,
  } = useDashboard();

  const memberLevelData = useMemo(() => (partnerRank.map((item) => ({
    value: item.total,
    name: currentLanguage === "vi" ? item.fullname : item.nameEn
  }))), [partnerRank])

  const statsData = useMemo(() => {

    const partner = [
      {
        label: t("totalPartner"),
        value: formatNumber(partnerCount.total ?? 0),
        icon: <PartnerIcon />
      },
      {
        label: t('totalVerifiedPartners'),
        value: formatNumber(partnerCount.totalVerified ?? 0),
        icon: <PartnerSuccessIcon />,
      },
      {
        label: t('totalAssignedTasks'),
        value: formatNumber(summaryReview.reduce((sum, item) => sum + item.total, 0)),
        icon: <SumMissionIcon />,
      },
      {
        label: t('tasksPendingSystemApproval'),
        value: formatNumber((summaryReview.find((item) => item.status === ReviewStatus.SystemPending))?.total ?? 0),
        icon: <MissionSystemIcon />,
      },
      {
        label: t('tasksPendingStaffApproval'),
        value: formatNumber((summaryReview.find((item) => item.status === ReviewStatus.AdminPending))?.total ?? 0),
        icon: <MissionAdminIcon />,
      },
      {
        label: t('completedTasks'),
        value: formatNumber((summaryReview.find((item) => item.status === ReviewStatus.Complete))?.total ?? 0),
        icon: <MissionSuccessIcon />,
      },
      {
        label: t('rejectedTasks'),
        value: formatNumber((summaryReview.find((item) => item.status === ReviewStatus.Reject))?.total ?? 0),
        icon: <MissionFailIcon />,
      },
      {
        label: t('totalWarrantyReviews'),
        value: formatNumber((summaryReview.find((item) => item.status === ReviewStatus.RejectWarranty))?.total ?? 0),
        icon: <ReviewGuaranteeIcon />,
      },
      ,
    ]

    return partner
  }, [partnerCount, summaryReview])

  const partnerStatusData = useMemo(() => {
    return [
      { name: t("missionTrue"), value: summaryReviewTotal?.totalComplete ?? 0 },
      { name: t("missionFalse"), value: summaryReviewTotal?.totalReject ?? 0 },
    ]
  }, [summaryReviewTotal])

  useEffect(() => {
    document.title = getTitle(t(title));
    fetchTopPartners();
    fetchPartnerRank();
    fetchSummaryReview();
    fetchPartnerCount();
    fetchSummaryReviewTotal();
  }, []);

  return (
    <div className='flex flex-col gap-6 dashboard-container'>
      <DashboardStats stats={statsData as any} />

      <Row gutter={[26, 26]} className="!mx-0">
        <Col xs={24} md={12}>
          <TopPartnerList partners={topPartners} loading={loadingTopPartners} title={t("topTenBonusPartner")} moneyIconColor="#FFCD00" />
        </Col>
        <Col xs={24} md={12} className="order-1 md:order-2 flex flex-col gap-8">
          <PieChartCard
            title={t("levelMember")}
            data={memberLevelData}
            colors={Object.values(colorsLevel)}
            height={300}
          />
          <PieChartCard
            title={t("partner")}
            data={partnerStatusData}
            colors={['#3AC977', '#EB4D4D']}
            showLabel
            height={300}
          />
        </Col>
      </Row>
      <PartnerMap />
    </div >
  );
})