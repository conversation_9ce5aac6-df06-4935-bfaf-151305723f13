import {
  DownloadOutlined,
  ExportOutlined,
  SearchOutlined,
} from "@ant-design/icons";
import { Button, Input, message, Popconfirm, Space, Table, Tag } from "antd";
import TextArea from "antd/es/input/TextArea";
import { customerDepositApi } from "api/customerDeposit.api";
import dayjs from "dayjs";
import { useHandleCustomer } from "hooks/useHandleCustomer";
import { debounce } from "lodash";
import React, { useEffect, useMemo, useRef, useState } from "react";
import {
  CustomerDeposit,
  CustomerDepositKeys,
  CustomerDepositStatus,
  CustomerDepositStatusTrans,
} from "types/customerDeposit";
import { formatVND, getTitle } from "utils";
import { unixToFullDate } from "utils/dateFormat";
import { MyTableColumn } from "utils/excel";
import {
  getExportData,
  handleExport,
  MyExcelColumn,
} from "../../utils/MyExcel";
import { Pagination } from "components/Pagination";
import { CustomerDepositModal } from "./CustomerDepositModal";
import { useCustomerDeposit } from "hooks/useCustomerDeposit";
import { useTranslation } from "react-i18next";
import { CustomerDepositList } from "./CustomerDepositList";
import { TextField } from "@mui/material";
const { ColumnGroup, Column } = Table;
import { ReactComponent as PlusIcon } from "assets/svgs/plus-icon.svg";
import { ReactComponent as SearchIcon } from "assets/svgs/search-icon.svg";
import { ReactComponent as DownloadIcon } from "assets/svgs/download.svg";

interface PropTypes {
  status: CustomerDepositStatus;
  title?: string;
  isAgent?: boolean;
  isFocus?: boolean;
  parentLastUpdate?: number;
  onSubmitOk?: () => void;
}
export const CustomerDepositTab = React.memo(
  ({
    title = "",
    status,
    isAgent,
    isFocus,
    parentLastUpdate,
    onSubmitOk,
  }: PropTypes) => {
    const [note, setNote] = useState<string>();
    const [loadingConfirmDeposit, setLoadingConfirmDeposit] = useState(false);
    const [loadingRejectDeposit, setLoadingRejectDeposit] = useState(false);
    const [selectedCustomerId, setSelectedCustomerId] = useState<number>();
    const [lastUpdate, setLastUpdate] = useState<number>(0);
    const { t } = useTranslation();

    console.log({ childLastUpdate: lastUpdate, status });

    const customerDepositModalRef = useRef<CustomerDepositModal>(null);
    const { customerDeposits, fetchData, loading, query, total } =
      useCustomerDeposit({
        initQuery: {
          page: 1,
          limit: 20,
          status: status === CustomerDepositStatus.All ? undefined : status,
        },
      });

    useEffect(() => {
      // document.title = getTitle(title);
      fetchData();
    }, [query]);

    const exportColumns: MyExcelColumn<CustomerDeposit>[] = [
      {
        width: 15,
        header: t("code"),
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "code",
        render: (record: CustomerDeposit) => {
          return record.code || t("notUpdate");
        },
      },

      {
        width: 30,
        header: t("customer"),
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "name",
        // style: { font: { color: { argb: "004e47cc" } } },
        render: (record: CustomerDeposit) => {
          return record.customer?.name || t("notUpdate");
        },
      },
      {
        width: 20,
        header: t("amountRequest"),
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "amount",
        render: (record: CustomerDeposit) => {
          return formatVND(record.amount);
        },
      },

      {
        width: 20,
        header: t("bankInfo"),
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "bankInfo",
        render: (record: CustomerDeposit) => {
          return `${record.bankName} ${
            record.bankNumber ? `: ${record.bankNumber}` : ""
          }`;
        },
      },
      {
        width: 30,

        header: t("note"),
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "note",
        render: (record: CustomerDeposit) => record?.note,
      },
      {
        width: 20,
        header: t("inspector"),
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "inspector",
        render: (record: CustomerDeposit) => {
          return record.status === CustomerDepositStatus.Pending
            ? ""
            : record?.inspecStaff
            ? record?.inspecStaff.username +
              " - " +
              record?.inspecStaff.fullName
            : "";
        },
      },
      {
        width: 20,
        header: t("inspectAt"),
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "inspectAt",
        render: (record: CustomerDeposit) => {
          return unixToFullDate(record.inspecAt);
        },
      },
      {
        width: 30,
        header: t("status"),
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "status",
        render: (record: CustomerDeposit) => {
          return t(record.status);
        },
      },
    ];

    useEffect(() => {
      if (isFocus && parentLastUpdate != lastUpdate) {
        fetchData();
        if (parentLastUpdate) setLastUpdate(parentLastUpdate);
      }
    }, [parentLastUpdate, lastUpdate, isFocus]);

    const handleConfirmDeposit = async (DepositId: number) => {
      try {
        setLoadingConfirmDeposit(true);
        const res = await customerDepositApi.approve(DepositId, {
          note: note,
        });
        message.success(t("actionSuccessfully"));
        setNote("");
        fetchData();
        onSubmitOk?.();
      } catch (error) {
      } finally {
        setLoadingConfirmDeposit(false);
      }
    };
    const handleRejectDeposit = async (DepositId: number) => {
      try {
        setLoadingRejectDeposit(true);
        const res = await customerDepositApi.reject(DepositId, {
          note: note,
        });
        message.success(t("actionSuccessfully"));
        setNote("");
        fetchData();
        onSubmitOk?.();
      } catch (error) {
      } finally {
        setLoadingRejectDeposit(false);
      }
    };
    const debounceSearch = debounce((search) => {
      query.search = search;
      query.page = 1;
      fetchData();
    }, 500);

    const handleSearch = (search: string) => {
      query.search = search;
      query.page = 1;
      fetchData();
    };

    return (
      <div>
        <div className="filter-container">
          <Space>
            <div className="filter-item">
              <label htmlFor="">{t("search")}</label>
              <Input.Search
                allowClear
                onChange={(ev) => {
                  const value = ev.currentTarget.value;
                  if (value) {
                    query.page = 1;
                    query.search = value;
                  } else {
                    query.search = undefined;
                  }
                }}
                size="large"
                className="w-full search-btn mt-1"
                // placeholder={t("productName")}
                enterButton={<SearchIcon />}
                onKeyDown={(ev) => {
                  if (ev.code == "Enter") {
                    fetchData();
                  }
                }}
                onSearch={handleSearch}
              />
              {/* <TextField
                size="small"
                onChange={(ev) => {
                  const value = ev.currentTarget.value;
                  if (value) {
                    query.page = 1;
                    query.search = value;
                  } else {
                    query.search = undefined;
                  }
                }}
                label={t("search")}
              /> */}
            </div>

            <div className="filter-item btn">
              <Button
                className="text-regular"
                type="primary"
                onClick={() => {
                  customerDepositModalRef.current?.handleCreate();
                }}
                icon={<PlusIcon />}
                size="large"
              >
                {t("create")}
              </Button>
            </div>
            {/* <div className="filter-item btn">
              <Button
                onClick={fetchData}
                type="primary"
                icon={<SearchOutlined />}
              >
                {t("search")}
              </Button>
            </div> */}
            <div className="filter-item btn">
              <Popconfirm
                title={t("exportAsk")}
                onConfirm={() =>
                  handleExport({
                    onProgress(percent) {},
                    exportColumns,
                    fileType: "xlsx",
                    dataField: "customerDeposits",
                    query: query,
                    api: customerDepositApi.findAll,
                    fileName: t("customerDepositOrderList"),
                    sheetName: t("customerDepositOrderList"),
                  })
                }
                okText={t("exportExcel")}
                cancelText={t("cancel")}
              >
                <Button
                  className="text-regular"
                  type="primary"
                  loading={false}
                  icon={<DownloadIcon />}
                  size="large"
                >
                  {t("exportExcel")}
                </Button>
              </Popconfirm>
            </div>
          </Space>
        </div>

        <CustomerDepositList
          //   onEdit={(record) =>
          //     customerDepositModalRef.current?.handleUpdate(record)
          //   }
          onRefreshData={() => {
            fetchData();
            onSubmitOk?.();
          }}
          dataSource={customerDeposits}
          loading={loadingConfirmDeposit}
          //   loadingDelete={loadingDelete}
          pagination={{
            total: total,
            defaultPageSize: query.limit,
            currentPage: query.page,
            onChange: ({ page, limit }) => {
              Object.assign(query, {
                page,
                limit,
              });
              fetchData();
            },
          }}
        />

        <CustomerDepositModal
          ref={customerDepositModalRef}
          onClose={function (): void {}}
          onSubmitOk={function (): void {
            fetchData();
            onSubmitOk?.();
          }}
        />
      </div>
    );
  }
);
