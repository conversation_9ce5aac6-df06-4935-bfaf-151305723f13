import { DownloadOutlined, SearchOutlined } from "@ant-design/icons";
import { Button, Input, message, Popconfirm, Space, Table, Tag } from "antd";
import TextArea from "antd/es/input/TextArea";
import { customerWithdrawApi } from "api/customerWithdraw.api";
import dayjs from "dayjs";
import { useHandleCustomer } from "hooks/useHandleCustomer";
import { debounce } from "lodash";
import React, { useEffect, useMemo, useRef, useState } from "react";
import {
  CustomerWithdraw,
  CustomerWithdrawStatus,
  CustomerWithdrawStatusTrans,
} from "types/customerWithdraw";
import { formatVND, getTitle } from "utils";
import { unixToFullDate } from "utils/dateFormat";
import { MyTableColumn } from "utils/excel";
import { getExportData } from "../../utils/MyExcel";
import { Pagination } from "components/Pagination";
import { CustomerWithdrawModal } from "./CustomerWithdrawModal";
import { useCustomerWithdraw } from "hooks/useCustomerWithdraw";
import { useTranslation } from "react-i18next";
import { CustomerWithdrawList } from "./CustomerWithdrawList";
import { TextField } from "@mui/material";
const { ColumnGroup, Column } = Table;
interface PropTypes {
  status: CustomerWithdrawStatus;
  title?: string;
  isAgent?: boolean;
  isFocus?: boolean;
  parentLastUpdate?: number;
  onSubmitOk?: () => void;
}
export const CustomerWithdrawTab = React.memo(
  ({
    title = "",
    status,
    isAgent,
    isFocus,
    parentLastUpdate,
    onSubmitOk,
  }: PropTypes) => {
    const [note, setNote] = useState<string>();
    const [loadingConfirmWithdraw, setLoadingConfirmWithdraw] = useState(false);
    const [loadingRejectWithdraw, setLoadingRejectWithdraw] = useState(false);
    const [selectedCustomerId, setSelectedCustomerId] = useState<number>();
    const [lastUpdate, setLastUpdate] = useState<number>(0);
    const { t } = useTranslation();

    console.log({ childLastUpdate: lastUpdate, status });

    const customerWithdrawModalRef = useRef<CustomerWithdrawModal>(null);
    const { customerWithdraws, fetchData, loading, query, total } =
      useCustomerWithdraw({
        initQuery: {
          page: 1,
          limit: 20,
          status: status === CustomerWithdrawStatus.All ? undefined : status,
        },
      });

    useEffect(() => {
      // document.title = getTitle(title);
      fetchData();
    }, [query]);

    useEffect(() => {
      if (isFocus && parentLastUpdate != lastUpdate) {
        fetchData();
        if (parentLastUpdate) setLastUpdate(parentLastUpdate);
      }
    }, [parentLastUpdate, lastUpdate, isFocus]);

    const handleConfirmWithdraw = async (WithdrawId: number) => {
      try {
        setLoadingConfirmWithdraw(true);
        const res = await customerWithdrawApi.approve(WithdrawId, {
          note: note,
        });
        message.success(t("actionSuccessfully"));
        setNote("");
        fetchData();
        onSubmitOk?.();
      } catch (error) {
      } finally {
        setLoadingConfirmWithdraw(false);
      }
    };
    const handleRejectWithdraw = async (WithdrawId: number) => {
      try {
        setLoadingRejectWithdraw(true);
        const res = await customerWithdrawApi.reject(WithdrawId, {
          note: note,
        });
        message.success(t("actionSuccessfully"));
        setNote("");
        fetchData();
        onSubmitOk?.();
      } catch (error) {
      } finally {
        setLoadingRejectWithdraw(false);
      }
    };
    const debounceSearch = debounce((search) => {
      query.search = search;
      query.page = 1;
      fetchData();
    }, 500);

    return (
      <div>
        <div className="filter-container">
          <Space>
            <div className="filter-item ">
              <TextField
                size="small"
                onChange={(ev) => {
                  const value = ev.currentTarget.value;
                  if (value) {
                    query.page = 1;
                    query.search = value;
                  } else {
                    query.search = undefined;
                  }
                }}
                label={t("search")}
              />
            </div>

            <div className="filter-item">
              <Button
                className=""
                type="primary"
                onClick={() => {
                  customerWithdrawModalRef.current?.handleCreate();
                }}
              >
                {t("withdrawMoney")}
              </Button>
            </div>
            <div className="filter-item ">
              <Button
                onClick={fetchData}
                type="primary"
                icon={<SearchOutlined />}
              >
                {t("search")}
              </Button>
            </div>
          </Space>
        </div>

        <CustomerWithdrawList
          //   onEdit={(record) =>
          //     customerWithdrawModalRef.current?.handleUpdate(record)
          //   }
          onRefreshData={() => {
            fetchData();
            onSubmitOk?.();
          }}
          dataSource={customerWithdraws}
          loading={loadingConfirmWithdraw}
          //   loadingDelete={loadingDelete}
          pagination={{
            total: total,
            defaultPageSize: query.limit,
            currentPage: query.page,
            onChange: ({ page, limit }) => {
              Object.assign(query, {
                page,
                limit,
              });
              fetchData();
            },
          }}
        />

        <CustomerWithdrawModal
          ref={customerWithdrawModalRef}
          onClose={function (): void {}}
          onSubmitOk={function (): void {
            fetchData();
            onSubmitOk?.();
          }}
        />
      </div>
    );
  }
);
