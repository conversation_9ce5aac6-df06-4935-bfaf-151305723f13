import React, { MutableRefObject } from "react";

export type WithCustomRef<T> = {
  myRef: MutableRefObject<any>;
} & T;

export const ActiveStatusTrans = {
  true: {
    value: true,
    label: "Đang hoạt động",
    color: "green",
    oppositeLabel: "Dừng hoạt động",
  },
  false: {
    value: false,
    label: "Ngưng hoạt động",
    color: "red",
    oppositeLabel: "Mở hoạt động",
  },
};
export const BlockedStatusTrans = {
  false: {
    value: false,
    label: "Không khóa",
    color: "green",
    oppositeLabel: "Khóa",
  },
  true: {
    value: true,
    label: "Đã bị khóa",
    color: "red",
    oppositeLabel: "Mở khóa",
  },
};
export const HighLightStatusTrans = {
  true: {
    value: true,
    label: "Nổi bật",
    color: "green",
    oppositeLabel: "Không nổi bật",
  },
  false: {
    value: false,
    label: "Không nổi bật",
    color: "red",
    oppositeLabel: "Nổi bật",
  },
};

export const PublicStatusTrans = {
  true: {
    value: true,
    label: "Công khai",
    color: "green",
    oppositeLabel: "Không công khai",
  },
  false: {
    value: false,
    label: "Không công khai",
    color: "red",
    oppositeLabel: "Công khai",
  },
};
export const VisibleStatusTrans = {
  true: {
    value: true,
    label: "Hiển thị",
    color: "green",
    oppositeLabel: "Ẩn",
  },
  false: {
    value: false,
    label: "Ẩn",
    color: "red",
    oppositeLabel: "Hiển thị",
  },
};
type AType = Record<string, any>;

export const removeSubstringFromKeys = (
  obj: AType,
  substring: string
): AType => {
  return Object.fromEntries(
    Object.entries(obj).map(([key, value]) => [
      key.replace(substring, ""),
      value,
    ])
  );
};

export const addBoldToStringAtIndex = ({
  matchs,
  str,
}: {
  str: string;
  matchs: {
    length: number;
    offset: number;
  }[];
}) => {
  let resultStr = str;

  const sortedMatches = [...matchs].sort((a, b) => b.offset - a.offset);

  for (const { offset, length } of sortedMatches) {
    if (offset < 0 || length <= 0 || offset + length > resultStr.length) {
      continue; // Skip invalid matches
    }

    resultStr =
      resultStr.slice(0, offset) +
      `<b>` +
      resultStr.slice(offset, offset + length) +
      `</b>` +
      resultStr.slice(offset + length);
  }

  return stringToJSX(resultStr);
};

export const stringToJSX = (domString: string) => {
  return createJSX(Array.from(getNodes(domString)));
};

let getNodes = (str: string) =>
  new DOMParser().parseFromString(str, "text/html").body.childNodes;
let createJSX = (nodeArray: any) => {
  return nodeArray.map((node: any) => {
    let attributeObj: any = {};
    const { attributes, localName, childNodes, nodeValue } = node;
    if (attributes) {
      Array.from(attributes).forEach((attribute: any) => {
        if (attribute.name === "style") {
          let styleAttributes = attribute.nodeValue.split(";");
          let styleObj: any = {};
          styleAttributes.forEach((attribute: any) => {
            let [key, value] = attribute.split(":");
            styleObj[key] = value;
          });
          attributeObj[attribute.name] = styleObj;
        } else {
          attributeObj[attribute.name] = attribute.nodeValue;
        }
      });
    }
    return localName
      ? React.createElement(
          localName,
          attributeObj,
          childNodes && Array.isArray(Array.from(childNodes))
            ? createJSX(Array.from(childNodes))
            : []
        )
      : nodeValue;
  });
};
