import {
  Button,
  Col,
  Form,
  Input,
  message,
  Modal,
  Row,
  Tabs,
  Select,
  Image,
} from "antd";
import { useForm } from "antd/lib/form/Form";
import { partnerApi } from "api/partner.api";
import { forwardRef, useImperativeHandle, useState, useEffect } from "react";
import { Partner } from "types/partner";
import { ModalStatus } from "types/modal";
import { SurveyCampaign } from "types/survey";
import {
  emailRule,
  phoneNumberRule,
  requiredRule,
  rules,
} from "utils/validate-rules";
import { InputNumber } from "components/Input/InputNumber";
import { useTranslation } from "react-i18next";
import MissionHistoryTab from "../Tab/MissionHistoryTab";
import PartnerTransactionTab from "../Tab/PartnerTransactionTab";
import { useBank } from "hooks/useBank";
import { debounce } from "lodash";
import { SingleImageUpload } from "components/Upload/SingleImageUpload";

enum ActiveKey {
  Info = "INFO",
  Order = "ORDER",
  Transaction = "TRANSACTION",
}

export interface PartnerModalProps {
  onClose: () => void;
  onSubmitOk: () => void;
  // hasAddPartnerPermission?: boolean;
  // hasUpdatePartnerPermission?: boolean;
}

export interface PartnerModalRef {
  handleUpdate: (partner: Partner) => void;
  handleCreate: () => void;
}

export const PartnerModal = forwardRef(
  (
    {
      onSubmitOk,
    }: // hasAddPartnerPermission,
      // hasUpdatePartnerPermission,
      PartnerModalProps,
    ref
  ) => {
    const [form] = useForm();
    const [loading, setLoading] = useState(false);
    const [visible, setVisible] = useState<boolean>();
    const { t } = useTranslation();
    const [activeKey, setActiveKey] = useState<ActiveKey | string>("INFO");

    const [selectedPartner, setSelectedPartner] = useState<Partner>();
    const [status, setStatus] = useState<ModalStatus>("create");

    // Bank related states and hooks
    const { banks, fetchBank, queryBank } = useBank({
      initQuery: { page: 1, limit: 100 },
    });

    useEffect(() => {
      fetchBank();
    }, []);

    // Debounced search for banks
    const debounceSearchBank = debounce((value) => {
      queryBank.search = value;
      fetchBank();
    }, 300);

    useImperativeHandle(
      ref,
      () => ({
        handleUpdate,
        handleCreate,
      }),
      []
    );

    const handleUpdate = (partner: Partner) => {
      form.setFieldsValue({
        ...partner,
        bankId: partner.bank?.id,
        bankAccountName: partner.bankAccountName,
        bankAccountNumber: partner.bankAccountNumber,
      });
      setSelectedPartner(partner);
      setStatus("update");
      setVisible(true);
    };

    const handleCreate = () => {
      form.resetFields();
      setSelectedPartner(undefined);
      setStatus("create");
      setVisible(true);
    };

    const handleSubmitForm = async () => {
      await form.validateFields();
      const dataForm = form.getFieldsValue();
      if (dataForm.name) {
        dataForm.name = dataForm.name.trim();
      }
      const payload = {
        partner: {
          ...dataForm,
        },
        bankId: dataForm.bankId,
      };

      try {
        setLoading(true);
        switch (status) {
          case "update":
            await partnerApi.update(selectedPartner?.id || 0, payload);
            message.success(t("actionSuccessfully"));
            break;

          //create
          default:
            await partnerApi.create(payload);
            message.success(t("actionSuccessfully"));
            break;
        }
        onSubmitOk();
        handleClose();
      } catch (error) {
      } finally {
        setLoading(false);
        // onSubmitOk();
      }
    };

    const handleClose = () => {
      setVisible(false);
      setActiveKey("INFO");
      form.resetFields();
      setSelectedPartner(undefined);
    };

    return (
      <Modal
        onCancel={() => {
          handleClose();
        }}
        visible={visible}
        centered
        title={
          <h1 className="mb-0 text-lg text-primary font-bold">
            {status == "create" ? t("create") : t("update")}
          </h1>
        }
        confirmLoading={loading}
        destroyOnClose
        width={
          activeKey === ActiveKey.Info
            ? 1200
            : activeKey === ActiveKey.Order
              ? 1700
              : 1200
        }
        onOk={handleSubmitForm}
        afterClose={() => {
          form.resetFields();
        }}
        okText={t("save")}
        cancelText={t("close")}
        footer={
          <>
            <Button onClick={() => setVisible(false)}>{t("close")}</Button>
            {activeKey == ActiveKey.Info ? (
              <Button
                // disabled={selectedSurvey && selectedSurvey?.totalSubmit > 0}
                type="primary"
                onClick={() => handleSubmitForm()}
                loading={loading}
              // hidden={type === "review"}
              >
                {t("save")}
              </Button>
            ) : null}
          </>
        }
      // okButtonProps={{
      //   hidden:
      //     (!hasAddPartnerPermission && status == "create") ||
      //     (!hasUpdatePartnerPermission && status == "update"),
      // }}
      >
        <Tabs
          defaultActiveKey={activeKey}
          type="line"
          onChange={(activeKey) => setActiveKey(activeKey)}
        >
          <Tabs.TabPane tab={t("basicInformation")} key={ActiveKey.Info}>
            {activeKey == ActiveKey.Info && (
              <>
                <Form
                  form={form}
                  layout="vertical"
                  validateTrigger={["onBlur", "onChange"]}
                >
                  <Row gutter={[24, 0]}>
                    <Col span={14} style={{ borderRight: "1px solid #efefef" }}>
                      <Row gutter={[12, 0]}>
                        <Col span={12}>
                          <Form.Item
                            className="!mb-2"
                            name="code"
                            label={t("code")}

                          // rules={[requiredRule]}
                          >
                            <Input placeholder={t("autoGen")} disabled />
                          </Form.Item>
                        </Col>
                        {selectedPartner && (
                          <Col span={12}>
                            <Form.Item
                              className="!mb-2"
                              name="rankContent"
                              label={t("rank")}
                            // rules={[requiredRule, emailRule]}
                            >
                              <Input
                                placeholder={selectedPartner?.rank?.name}
                                disabled
                                className="placeholder:text-gray-700"
                              />
                            </Form.Item>
                          </Col>
                        )}

                        <Col span={12}>
                          <Form.Item
                            className="!mb-2"
                            name="email"
                            label="Email"
                            rules={[requiredRule, emailRule]}
                          >
                            <Input type="email" />
                          </Form.Item>
                        </Col>
                        <Col span={12}>
                          <Form.Item
                            className="!mb-2"
                            name="phone"
                            label={t("phoneNumber")}
                            rules={[
                              { required: true },
                              {
                                pattern: /(84|0[3|5|7|8|9])+([0-9]{8})\b/g,
                                message: t("wrongFormatPhone"),
                              },
                              { max: 50 },
                            ]}
                          >
                            <Input />
                          </Form.Item>
                        </Col>

                        {status == "create" && (
                          <Col span={12}>
                            <Form.Item
                              label={t("password")}
                              name="password"
                              rules={rules}
                            >
                              <Input placeholder="" />
                            </Form.Item>
                          </Col>
                        )}
                        <Col span={12}>
                          <Form.Item
                            className="!mb-2"
                            name="fullName"
                            label={t("partnerName")}
                            rules={[requiredRule]}
                          >
                            <Input />
                          </Form.Item>
                        </Col>
                        {/* Bank Information Section */}
                        <Col span={24}>
                          <div className="mb-4 mt-6">
                            <h3 className="text-base font-semibold text-gray-800 mb-4">
                              {t("bankInformation")}
                            </h3>
                          </div>
                        </Col>

                        <Col span={24}>
                          <Form.Item
                            className="!mb-2"
                            name="bankId"
                            label={t("bankName")}
                            rules={[requiredRule]}
                          >
                            <Select
                              size="large"
                              onClear={() => {
                                queryBank.search = "";
                                fetchBank();
                              }}
                              onSearch={(value) => debounceSearchBank(value)}
                              allowClear
                              showSearch
                              filterOption={false}
                              className="!w-full min-h-[42px]"
                              value={form.getFieldValue("bankId")}
                              options={banks.map((item) => {
                                return {
                                  label: (
                                    <div className="flex items-center justify-between w-full min-w-0">
                                      <span className="truncate flex-1 pr-2">
                                        {item.fullName}
                                      </span>
                                      <Image
                                        preview={false}
                                        className="!h-[20px] w-auto object-contain flex-shrink-0"
                                        src={item.logo}
                                        width={36}
                                      />
                                    </div>
                                  ),
                                  value: item.id,
                                  option: item,
                                };
                              })}
                              placeholder={t("selectBank")}
                            />
                          </Form.Item>
                        </Col>

                        <Col span={12}>
                          <Form.Item
                            className="!mb-2"
                            name="bankAccountNumber"
                            label={t("bankAccountNumber")}
                            rules={[
                              requiredRule,
                              {
                                pattern: /^\d{7,14}$/,
                                message: t("bankAccountNumberValidateMessage"),
                              },
                            ]}
                          >
                            <Input className="min-h-[42px]" />
                          </Form.Item>
                        </Col>

                        <Col span={12}>
                          <Form.Item
                            className="!mb-2"
                            name="bankAccountName"
                            label={t("bankAccountName")}
                            rules={[
                              requiredRule,
                              {
                                pattern: /^[a-zA-ZÀ-Ỹà-ỹĐđ\s]{1,25}$/,
                                message: t("fullNameValidateMessage"),
                              },
                            ]}
                          >
                            <Input className="py-[14px] px-[18px] min-h-[42px]" />
                          </Form.Item>
                        </Col>
                      </Row>
                    </Col>
                    <Col span={10}>
                      <Form.Item shouldUpdate={true}>
                        {() => {
                          return (
                            <Form.Item label={t("avatar")} name="avatar">
                              <SingleImageUpload
                                onUploadOk={(path: string) => {
                                  form.setFieldsValue({
                                    avatar: path,
                                  });
                                }}
                                imageUrl={form.getFieldValue("avatar")}
                              />
                            </Form.Item>
                          );
                        }}
                      </Form.Item>
                    </Col>
                  </Row>
                </Form>
              </>
            )}
          </Tabs.TabPane>
          {status === "update" && (
            <>
              {selectedPartner?.id && (
                <Tabs.TabPane tab={t("missionHistory")} key={ActiveKey.Order}>
                  <MissionHistoryTab partnerId={selectedPartner?.id} />
                </Tabs.TabPane>
              )}
              {selectedPartner?.id && (
                <Tabs.TabPane
                  tab={t("partnerTransaction")}
                  key={ActiveKey.Transaction}
                >
                  <PartnerTransactionTab partnerId={selectedPartner?.id} />
                </Tabs.TabPane>
              )}
            </>
          )}
        </Tabs>
      </Modal>
    );
  }
);
