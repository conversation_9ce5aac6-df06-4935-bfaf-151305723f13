import { request } from "utils/request";
import { AxiosPromise } from "axios";

export const promotionApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/promotion",
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/promotion",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/promotion/${id}`,
      method: "patch",
      data,
    }),
  changeStatus: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/promotion/${id}/status`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/promotion/${id}`,
      method: "delete",
    }),
};
