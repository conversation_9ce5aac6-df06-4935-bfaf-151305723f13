import { ExportOutlined, SearchOutlined } from "@ant-design/icons";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  Collapse,
  DatePicker,
  Input,
  Popconfirm,
  Select,
  Space,
  TablePaginationConfig,
  Tabs,
  message,
} from "antd";
import { customerApi } from "api/customer.api";
import { Pagination } from "components/Pagination";
import { CustomerQuery, useHandleCustomer } from "hooks/useHandleCustomer";
import { useCallback, useEffect, useRef, useState } from "react";
import { useSearchParams } from "react-router-dom";
import { Customer, CustomerStatus, CustomerStatusTrans } from "types/customer";
import { formatVND, getTitle } from "utils";
import { CustomerModal } from "./components/Modal/CustomerModal";
import { CustomerTable } from "./components/Table/CustomerTable";
// import ConfirmExportExcel from "./components/ConfirmExportExcel";
import {
  FilterValue,
  SorterResult,
  TableCurrentDataSource,
} from "antd/lib/table/interface";
import ConfirmExportExcel from "components/ConfirmExportExcel/ConfirmExportExcel";
import { QueryObject } from "types/query";
import { TextField } from "@mui/material";
import {
  ResetPasswordModal,
  ResetPasswordModalRef,
} from "./components/Modal/ResetPasswordModal";
import dayjs from "dayjs";
import { useTranslation } from "react-i18next";
import { unixToFullDate } from "utils/dateFormat";
import { handleExport, MyExcelColumn } from "utils/MyExcel";
import { ReactComponent as DownloadIcon } from "assets/svgs/download.svg"
import { CustomerFilter } from "./components/CustomerFilter";
import { useMediaQuery } from "@mui/system";
import { CustomerTableMobile } from "./components/Table/CusomerTableMobile";
interface IProps {
  title?: string;
  initQuery?: CustomerQuery;
  isGetFromStockOrder?: boolean;
  isFollow?: boolean;
  isFavorite?: boolean;
  isClosed?: boolean;
  delayRender?: number;
}

export const CustomerPage = ({
  title = "",
  initQuery = {
    page: 1,
    limit: 15,
    // status: CustomerStatus.All,
  },
  isGetFromStockOrder = false,
  isFollow = false,
  isFavorite = false,
  isClosed = false,
  delayRender = 0,
}: IProps) => {
  const customerModal = useRef<CustomerModal>();
  const [tabActive, setTabActive] = useState<CustomerStatus>(initQuery.status);
  const [summaryCustomerOfStatus, setSummaryCustomerOfStatus] = useState();
  const [queryParameters, setQueryParameters] = useSearchParams();
  const customerId = queryParameters.get("customerId");
  const [loadingDelete, setLoadingDelete] = useState(false);
  console.log("re-render page");
  const resetModalRef = useRef<ResetPasswordModalRef>();
  const { t } = useTranslation();
  const mdDown = useMediaQuery((theme: any) => theme.breakpoints.down("sm"));

  const {
    customers,
    fetchCustomer,
    queryCustomer,
    totalCustomer,
    loadingCustomer,
    debounceSearchCustomer,
  } = useHandleCustomer({
    initQuery: {
      ...initQuery,
      // status: tabActive !== CustomerStatus.All ? tabActive : undefined,
      // isLiked: isFavorite,
      // isFollow,
    },
  });

  // const handleInitQueryParam = async () => {
  //   if (customerId) {
  //     customerModal.current?.handleUpdate(+customerId),
  //       // queryParameters.delete("id");
  //       setQueryParameters(queryParameters);
  //   }
  // };

  // const handleRemoveQueryParam = () => {
  //   const params = new URLSearchParams(queryParameters);
  //   params.delete("customerId");
  //   setQueryParameters(params.toString());
  // };

  const fetchSummaryCustomer = useCallback(
    async (query: any) => {
      const res = await customerApi.summaryStatus(query);
      if (res.status) {
        setSummaryCustomerOfStatus(() => {
          const summary = res.data.reduce(
            (prev: any, curr: { status: CustomerStatus; total: number }) => {
              prev[curr.status] = curr.total;
              prev.ALL = (prev.ALL || 0) + curr.total;
              return prev;
            },
            { ALL: 0 }
          );
          return summary;
        });
      }
    },
    [queryCustomer]
  );

  const exportColumns: MyExcelColumn<Customer>[] = [
    {
      width: 15,
      header: t("code"),
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "code",
      render: (record: Customer) => {
        return record.code || t("notUpdate");
      },
    },

    {
      width: 30,
      header: t("customerName"),
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "name",
      style: { font: { color: { argb: "004e47cc" } } },
      render: (record: Customer) => {
        return record.name || t("notUpdate");
      },
    },
    {
      width: 20,
      header: t("phoneNumber"),
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "phone",
      render: (record: Customer) => {
        return record.phone || t("notUpdate");
      },
    },

    {
      width: 20,
      header: "Email",
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "email",
      render: (record: Customer) => {
        return record.email || t("notUpdate");
      },
    },
    {
      width: 20,
      header: t("projectCreated"),
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "totalProduct",
      render: (record: Customer) => {
        return formatVND(record.totalProduct || 0);
      },
    },
    {
      width: 20,
      header: t("paidProject"),
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "totalProjectPaymentComplete",
      render: (record: Customer) => {
        return formatVND(record.totalProjectPaymentComplete || 0);
      },
    },
    {
      width: 30,
      header: t("balance"),
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "balance",
      render: (record: Customer) => {
        return formatVND(record.balance);
      },
    },
    {
      width: 30,
      header: t("status"),
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "status",
      render: (record: Customer) => {
        return t(record.status);
      },
    },
    {
      width: 20,
      header: t("createdAt"),
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "createdAt",
      render: (record: Customer) => {
        return unixToFullDate(record.createdAt);
      },
    },
  ];

  useEffect(() => {
    document.title = getTitle(t(title));
    setTimeout(() => {
      // handleInitQueryParam();
      fetchCustomer();
    }, delayRender);
  }, []);

  useEffect(() => {
    // if (!isGetFromStockOrder) {
    //   fetchSummaryCustomer(queryCustomer);
    // }
  }, [queryCustomer, isGetFromStockOrder]);

  const handleRefreshData = useCallback(async () => {
    await fetchCustomer();
    await fetchSummaryCustomer(queryCustomer);
  }, [queryCustomer]);

  const handleActiveCustomer = async (customerId: number) => {
    try {
      setLoadingDelete(true);
      const res = await customerApi.update(customerId, {
        customer: { status: CustomerStatus.Active },
      });
      fetchCustomer();
      message.success(t("actionSuccessfully"));
    } catch (error) {
    } finally {
      setLoadingDelete(false);
    }
  };

  const handleInactiveCustomer = async (customerId: number) => {
    try {
      setLoadingDelete(true);
      const res = await customerApi.update(customerId, {
        customer: { status: CustomerStatus.Blocked },
      });
      fetchCustomer();
      message.success(t("actionSuccessfully"));
    } catch (error) {
    } finally {
      setLoadingDelete(false);
    }
  };

  const handleTableChange = (
    pagination: TablePaginationConfig,
    filters: Record<string, FilterValue | null>,
    sorter: SorterResult<Customer>,
    extra: TableCurrentDataSource<Customer>
  ) => {
    let queryObject: QueryObject[] = [];

    if (sorter?.order) {
      queryObject.push({
        type: "sort",
        field: `${sorter.columnKey}`,
        value: sorter.order == "ascend" ? "ASC" : "DESC",
      });
      queryCustomer.queryObject = JSON.stringify(queryObject);
    } else {
      queryCustomer.queryObject = undefined;
    }
    fetchCustomer();
  };

  // const onChangeTab = (key: CustomerStatus) => {
  //   queryCustomer.status = key !== CustomerStatus.All ? key : undefined;

  //   setTabActive(key as CustomerStatus);
  //   setTimeout(() => {
  //     fetchCustomer();
  //   }, 350);
  // };

  const handleDeleteCustomer = async (customerId: number) => {
    try {
      const res = await customerApi.delete(customerId);
      message.success(t("actionSuccessfully"));
      fetchCustomer();
    } catch (error) { }
  };

  const optionStatus = [
    {
      value: "ALL",
      label: t("ALL")
    },
    {
      value: CustomerStatus.Active,
      label: t(CustomerStatus.Active)
    },
    {
      value: CustomerStatus.Blocked,
      label: t(CustomerStatus.Blocked)
    }
  ]


  return (
    <div>
      {/* <Card bodyStyle={{ padding: "8px 20px" }} style={{ borderRadius: "8px" }}> */}
      <div>
        <CustomerFilter
          debounceSearchCustomer={debounceSearchCustomer}
          queryCustomer={queryCustomer}
          fetchCustomer={fetchCustomer}
          exportColumns={exportColumns}
        />
      </div>

      <CustomerTable
        onReset={(record) => resetModalRef.current?.handleUpdate(record)}
        showStatusColumn={!isGetFromStockOrder}
        showActionColumn={!isGetFromStockOrder}
        onEdit={(r) => {
          console.log(r);
          customerModal.current?.handleUpdate(r);
        }}
        onRefreshData={handleRefreshData}
        dataSource={customers}
        loading={loadingCustomer}
        onActive={handleActiveCustomer}
        onInactive={handleInactiveCustomer}
        isGetFromStockOrder={isGetFromStockOrder}
        isFavorite={isFavorite}
        isFollow={isFollow}
        onDelete={handleDeleteCustomer}
        isClosed={isClosed}
        handleTableChange={handleTableChange}
        loadingDelete={loadingDelete}
      />

      <Pagination
        onChange={(value) => {
          Object.assign(queryCustomer, value);
          handleRefreshData();
        }}
        defaultPageSize={queryCustomer.limit}
        currentPage={queryCustomer.page}
        total={totalCustomer}
      ></Pagination>
      {/* </Card> */}
      <ResetPasswordModal
        ref={resetModalRef}
        onClose={() => { }}
        onSubmitOk={fetchCustomer}
      // hasAddIndustryPermission={hasIndustryAddPermission}
      // hasUpdateIndustryPermission={hasIndustryUpdatePermission}
      />
      <CustomerModal
        onClose={() => { }}
        onSubmit={handleRefreshData}
        ref={customerModal}
      ></CustomerModal>
    </div >
  );
};
