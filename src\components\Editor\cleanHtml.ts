type DeltaOp = {
  insert: string | object;
  attributes?: { [key: string]: any };
};

type Delta = {
  ops: DeltaOp[];
};

export const deltaToCleanHtml = (delta: Delta): string => {
  if (!delta?.ops?.length) return "";

  let html = "";
  let currentLine = "";
  let currentBlock = null;

  for (let i = 0; i < delta.ops.length; i++) {
    const op = delta.ops[i];

    if (typeof op.insert === "string") {
      const lines = op.insert.split("\n");

      for (let j = 0; j < lines.length; j++) {
        const line = lines[j];

        if (line === "" && j < lines.length - 1) {
          // Dòng trống
          if (currentBlock) {
            html += wrapBlock(currentLine, currentBlock);
            currentLine = "";
            currentBlock = null;
          } else {
            html += "<p><br></p>";
          }
        } else {
          const formatted = applyInlineFormats(line, op.attributes || {});

          if (j === lines.length - 1) {
            currentLine += formatted;
          } else {
            html += wrapBlock(currentLine + formatted, currentBlock || op.attributes);
            currentLine = "";
            currentBlock = null;
          }
        }
      }
    } else if (typeof op.insert === "object" && (op.insert as any).image) {
      html += `<p><img src="${(op.insert as any).image}" /></p>`;
    }
  }

  // Append leftover line
  if (currentLine) {
    html += wrapBlock(currentLine, currentBlock);
  }

  // Clean up <p><br></p> sau các block
  html = html
    .replace(/(<\/h[1-6]>)(\s*)<p><br\s*\/?><\/p>/gi, "$1")
    .replace(/(<\/(ul|ol)>)(\s*)<p><br\s*\/?><\/p>/gi, "$1")
    .replace(/<p><br\s*\/?><\/p>(?=\s*<\/div>?)/gi, "")
    .replace(/<p><br\s*\/?><\/p>/gi, "");

  return html.trim();
}

function applyInlineFormats(text: string, attrs: any): string {
  if (!attrs) return text;

  if (attrs.bold) text = `<strong>${text}</strong>`;
  if (attrs.italic) text = `<em>${text}</em>`;
  if (attrs.underline) text = `<u>${text}</u>`;
  if (attrs.strike) text = `<s>${text}</s>`;
  if (attrs.link) text = `<a href="${attrs.link}">${text}</a>`;

  return text;
}

function wrapBlock(content: string, attrs: any): string {
  if (!attrs) return `<p>${content}</p>`;

  if (attrs.header) return `<h${attrs.header}>${content}</h${attrs.header}>`;
  if (attrs.list === "ordered") return `<ol><li>${content}</li></ol>`;
  if (attrs.list === "bullet") return `<ul><li>${content}</li></ul>`;
  if (attrs.code) return `<pre><code>${content}</code></pre>`;
  if (attrs.blockquote) return `<blockquote>${content}</blockquote>`;

  return `<p>${content}</p>`;
}