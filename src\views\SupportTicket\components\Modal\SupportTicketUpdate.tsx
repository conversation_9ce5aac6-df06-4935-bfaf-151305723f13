import React from "react";
import { Spin, Button, Input } from "antd";
import { LeftOutlined, ClockCircleOutlined } from "@ant-design/icons";
import { Stack } from "@mui/material";
import ParentCard from "components/Common/ParentCard";
import SupportTicketReply from "components/SupportTicketReply/SupportTicketReply";
import { SupportTicket, SupportTicketStatus } from "types/supportTicket";
import { FileAttachPayload } from "components/Upload/FileUploadItem";
import { ReactComponent as UploadIcon } from "assets/svgs/upload.svg";
import { $url } from "utils/url";
import { formatFullDateTime } from "utils/date";
import { SupportTicketStatusComp } from "components/SupportTicket/SupportTicketStatusComp";
import { FileUploadMultiple } from "components/Upload/FileUploadMultiple";
import { useMediaQuery } from "@mui/system";
import { ModalStatus } from "types/modal";

export const SupportTicketUpdate = ({
  selectedTicket,
  childTickets,
  loadingChildTickets,
  showReply,
  setShowReply,
  fileList,
  setFileList,
  handleReply,
  setStatus,
  t,
  status,
}: {
  selectedTicket: SupportTicket | undefined;
  childTickets: SupportTicket[];
  loadingChildTickets: boolean;
  showReply: {
    open: boolean;
    reply: string;
    fileAttachPayloads: FileAttachPayload[];
  };
  setShowReply: React.Dispatch<
    React.SetStateAction<{
      open: boolean;
      reply: string;
      fileAttachPayloads: FileAttachPayload[];
    }>
  >;
  fileList: any[];
  setFileList: React.Dispatch<React.SetStateAction<any[]>>;
  handleReply: () => void;
  setStatus: (val: ModalStatus | "list") => void;
  t: (key: string) => string;
  status: string;
}) => {
  const mdDown = useMediaQuery((theme: any) => theme.breakpoints.down("sm"));

  return (
    <ParentCard
      className="support-ticket-update-container"
      hiddenBorder
      title={
        <div className="flex flex-col ">
          <div
            className="flex justify-between"
            style={{
              flexDirection: mdDown ? "column" : "row",
              alignItems: mdDown ? "flex-start" : "center",
              gap: "12px",
            }}
          >
            <div className="flex">
              <LeftOutlined onClick={() => setStatus("list")} />
              <div className="flex gap-2 !leading-6">
                {/* <h5 className="semibold">{t("title")}:</h5> */}
                <h5 className="whitespace-pre-wrap semibold">
                  {selectedTicket?.title}

                </h5>
              </div>
            </div>

            <div className="flex flex-row items-center gap-[20px]">
              <div className="flex flex-row items-center gap-[4px]">
                <ClockCircleOutlined
                  style={{ color: "#BDC7D5", fontSize: 18 }}
                />
                <div className="text-regular">
                  {formatFullDateTime(selectedTicket?.createdAt || 0)}
                </div>
              </div>
              <SupportTicketStatusComp
                status={selectedTicket?.status as SupportTicketStatus}
              />
            </div>
          </div>

        </div>
      }
    >
      <Spin spinning={loadingChildTickets}>
        <SupportTicketReply supportTickets={childTickets} />
      </Spin>

      <div className="flex flex-col gap-6">
        <div>
          <div className="title-content mt-4">
            <span className="text-gray-1 text-normal-semibold">{t("content")}</span>
            <span className="mark text-[red]">*</span>
          </div>
          <Input.TextArea
            className="mt-2"
            rows={8}
            size="large"
            onChange={(e) =>
              setShowReply({ ...showReply, reply: e.target.value })
            }
            value={showReply.reply}
          />
        </div>

        <div className="item-reply">
          <span className="text-gray-1 text-normal-semibold">
            {t("fileAttachShort")}
          </span>
          <FileUploadMultiple
            maxFileSize={1024 * 1024 * 10}
            acceptType="image/jpeg,image/jpg,image/png,application/pdf"
            fileList={fileList}
            variant="detailed"
            maxFile={5}
            draggerContent={
              <div className="upload-file">
                <UploadIcon />
                <span className="text-regular text-gray-1">
                  {t("acceptTypeUpload")}
                </span>
              </div>
            }
            id="supportTicketFileAttachReply"
            onUploadOk={(list) => {
              const fileAttachPayloads: FileAttachPayload[] = list.map(
                (item) => ({
                  url: item.url || $url(item.response?.data?.path),
                  name: item.name,
                  size: item.size,
                  uid: item.uid,
                  type: item.type,
                  path: item.response?.data?.path,
                  destination: item.response?.data?.destination,
                })
              );
              setFileList(list);
              setShowReply({ ...showReply, fileAttachPayloads });
            }}
            onDelete={setFileList}
          />
        </div>

        <div className="flex justify-end">
          <Button
            type="primary"
            onClick={() => status === "update" && handleReply()}
            className="h-[42px] w-full md:w-[170px] btn-request"
            disabled={
              !showReply?.reply.trim()
            }
          >
            {t("submitReq")}
          </Button>
        </div>
      </div>
    </ParentCard>
  );
};
