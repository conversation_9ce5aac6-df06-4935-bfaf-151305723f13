import { BankApi } from "api/bank.api";
import { debounce } from "lodash";
import { useCallback, useState } from "react";
import { Bank } from "types/online-payment";
import { QueryParam } from "types/query";

export interface BankQuery extends QueryParam {
  storeId?: number;
}

interface BankProps {
  initQuery: BankQuery;
}

export const useBank = ({ initQuery }: BankProps) => {
  const [data, setData] = useState<Bank[]>([]);
  const [query, setQuery] = useState<BankQuery>(initQuery);
  const [loading, setLoading] = useState<boolean>(false);
  const [total, setTotal] = useState(0);

  const fetchBank = useCallback(
    async (newQuery?: BankQuery) => {
      try {
        setLoading(true);
        const { data } = await BankApi.findAll(newQuery ?? query);
        setTotal(data.total);
        setData(data.banks);
      } finally {
        setLoading(false);
      }
    },
    [query]
  );

  const debounceFetchBank = useCallback(() => {
    debounce(() => fetchBank(), 1000);
  }, []);

  return {
    banks: data,
    totalBank: total,
    fetchBank,
    queryBank: query,
    setQueryBank: setQuery,
    loadingBank: loading,
    debounceFetchBank,
  };
};
