import { Col, Form, Input, message, Modal, Row } from "antd";
import { UploadFile } from "antd/lib";
import { useForm } from "antd/lib/form/Form";
import { rankApi } from "api/rank.api";
import { InputNumber } from "components/Input/InputNumber";
import { SingleImageUpload } from "components/Upload/SingleImageUpload";
import { forwardRef, useImperativeHandle, useState } from "react";
import { useTranslation } from "react-i18next";
import { ModalStatus } from "types/modal";
import { Rank } from "types/rank";
import { requiredRule } from "utils/validate-rules";

export interface RankModalProps {
  onClose: () => void;
  onSubmitOk: () => void;
  // hasAddRankPermission?: boolean;
  // hasUpdateRankPermission?: boolean;
}

export interface RankModalRef {
  handleUpdate: (rank: Rank) => void;
  handleCreate: () => void;
}

export const RankModal = forwardRef(
  (
    {
      onSubmitOk,
    }: // hasAddRankPermission,
      // hasUpdateRankPermission,
      RankModalProps,
    ref
  ) => {
    const [form] = useForm();
    const [loading, setLoading] = useState(false);
    const [visible, setVisible] = useState<boolean>();
    const { t } = useTranslation();
    const [fileList, setFileList] = useState<UploadFile[]>([]);
    const onChangeFileList = (fileList: UploadFile[]): void => {
      setFileList(fileList);
    };
    const [selectedRank, setSelectedRank] = useState<Rank>();
    const [status, setStatus] = useState<ModalStatus>("create");

    useImperativeHandle(
      ref,
      () => ({
        handleUpdate,
        handleCreate,
      }),
      []
    );

    const handleUpdate = (rank: Rank) => {
      form.setFieldsValue({
        ...rank,
      });
      // setFileList(Rank?.fileAttaches)
      setSelectedRank(rank);
      setStatus("update");
      setVisible(true);
    };

    const handleCreate = () => {
      setStatus("create");
      setVisible(true);
    };

    const handleSubmitForm = async () => {
      await form.validateFields();
      console.log(fileList);
      const dataForm = form.getFieldsValue();
      if (dataForm.name) {
        dataForm.name = dataForm.name.trim();
      }
      const payload = {
        rank: {
          ...dataForm,
        },
        productId: dataForm.productId,
        customerId: dataForm.customerId,
      };

      try {
        setLoading(true);
        switch (status) {
          case "update":
            await rankApi.update(selectedRank?.id || 0, payload);
            message.success(t("actionSuccessfully"));
            break;

          //create
          default:
            await rankApi.create(payload);
            message.success(t("actionSuccessfully"));
            break;
        }
        setVisible(false);
        onSubmitOk();
        setFileList([]);
      } catch (error) {
      } finally {
        setLoading(false);
        // onSubmitOk();
      }
    };

    return (
      <Modal
        onCancel={() => {
          setVisible(false);
          setFileList([]);
        }}
        visible={visible}
        centered
        title={
          <h1 className="mb-0 text-lg text-primary font-bold">
            {status == "create" ? t("create") : t("update")}
          </h1>
        }
        confirmLoading={loading}
        destroyOnClose
        width={800}
        onOk={handleSubmitForm}
        afterClose={() => {
          form.resetFields();
        }}

      // okButtonProps={{
      //   hidden:
      //     (!hasAddRankPermission && status == "create") ||
      //     (!hasUpdateRankPermission && status == "update"),
      // }}
      >
        <Form
          form={form}
          layout="vertical"
          validateTrigger={["onBlur", "onChange"]}
        >
          <Row gutter={[12, 0]}>
            <Col span={24}>
              <Form.Item shouldUpdate={true}>
                {() => {
                  return (
                    <Form.Item
                      label={t("image")}
                      name="icon"
                      rules={[requiredRule]}
                    >
                      <SingleImageUpload
                        onUploadOk={(path: string) => {
                          form.setFieldsValue({
                            icon: path,
                          });
                        }}
                        imageUrl={form.getFieldValue("icon")}
                      />
                    </Form.Item>
                  );
                }}
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item
                className="!mb-2"
                name="name"
                label={t("rankName")}
                rules={[requiredRule]}
              >
                <Input placeholder={t("enterName")} size="small" />
              </Form.Item>
            </Col>

            <Col span={24}>
              <Form.Item
                className="!mb-2"
                name="nameEn"
                label={t("rankNameEn")}
                rules={[requiredRule]}
              >
                <Input placeholder={t("enterName")} size="small" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                className="!mb-2"
                name="position"
                label={t("position")}
                rules={[requiredRule]}
              >
                <InputNumber />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                className="!mb-2"
                name="waitingTime"
                label={t("waitingTime")}
                rules={[requiredRule]}
              >
                <InputNumber />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                className="!mb-2"
                name="needReview"
                label={t("needMission")}
                rules={[requiredRule]}
              >
                <InputNumber />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                className="!mb-2"
                name="rewardPoint"
                label={t("moneyMission")}
                rules={[requiredRule]}
              >
                <InputNumber />
              </Form.Item>
            </Col>

            <Col span={24}>
              <Form.Item
                className="!mb-2"
                name="rewardPointByImage"
                label={t("moneyImage")}
                rules={[requiredRule]}
              >
                <InputNumber />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    );
  }
);
