import { projectLogApi } from "api/projectLog.api";
import { useState } from "react";
import { ProjectLog } from "types/projectLog";
import { QueryParam } from "types/query";

export interface ProjectLogQuery extends QueryParam {}

interface UseProjectLogProps {
  initQuery: ProjectLogQuery;
}

export const useProjectLog = ({ initQuery }: UseProjectLogProps) => {
  const [data, setData] = useState<ProjectLog[]>([]);
  const [total, setTotal] = useState(0);
  const [query, setQuery] = useState<ProjectLogQuery>(initQuery);
  const [loading, setLoading] = useState(false);

  const fetchData = async (newQuery?: ProjectLogQuery) => {
    setLoading(true);
    try {
      const { data } = await projectLogApi.findAll({ ...query, ...newQuery });

      setData(data.projectLogs);
      setTotal(data.total);
    } finally {
      setLoading(false);
    }
  };

  return {
    projectLogs: data,
    totalProjectLog: total,
    fetchProjectLog: fetchData,
    loadingProjectLog: loading,
    setQueryProjectLog: setQuery,
    queryProjectLog: query,
  };
};
