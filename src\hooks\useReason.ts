import { reasonApi } from "api/reason.api";
import { useState } from "react";
import { Reason } from "types/reason";
import { QueryParam } from "types/query";

export interface ReasonQuery extends QueryParam {}

interface UseReasonProps {
  initQuery: ReasonQuery;
}

export const useReason = ({ initQuery }: UseReasonProps) => {
  const [data, setData] = useState<Reason[]>([]);
  const [total, setTotal] = useState(0);
  const [query, setQuery] = useState<ReasonQuery>(initQuery);
  const [loading, setLoading] = useState(false);

  const fetchData = async () => {
    setLoading(true);
    try {
      const { data } = await reasonApi.findAll(query);

      setData(data.reasons);
      setTotal(data.total);
    } finally {
      setLoading(false);
    }
  };

  return {
    reasons: data,
    totalReason: total,
    fetchReason: fetchData,
    loadingReason: loading,
    setQueryReason: setQuery,
    queryReason: query,
  };
};
