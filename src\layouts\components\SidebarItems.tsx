// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
import { Box, List, useMediaQuery } from "@mui/material";
import { observer } from "mobx-react";
import { useLocation } from "react-router-dom";
import { appStore } from "store/appStore";
import NavCollapse from "./NavCollapse";
import NavGroup from "./NavGroup";
import NavItem from "./NavItem";
import { useMemo } from "react";
import { permissionStore } from "store/permissionStore";
import { settings } from "settings";
import { uniqueId } from "lodash";
import { IconAperture } from "@tabler/icons-react";
import { toJS } from "mobx";

interface MenuitemsType {
  [x: string]: any;
  id?: string;
  navlabel?: boolean;
  subheader?: string;
  title?: string;
  icon?: any;
  href?: string;
  children?: MenuitemsType[];
  chip?: string;
  chipColor?: string;
  variant?: string;
  external?: boolean;
}

const Menuitems: MenuitemsType[] = [
  {
    navlabel: true,
    subheader: "Home",
  },
  {
    id: uniqueId(),
    title: "Modern",
    icon: IconAperture,
    href: "/dashboards/modern",
    chip: "New",
    chipColor: "primary",
  },
];

const SidebarItems = () => {
  const { pathname } = useLocation();
  const pathDirect = pathname;
  const pathWithoutLastPart = pathname.slice(0, pathname.lastIndexOf("/"));
  const customizer = appStore.customizer;
  const lgUp = useMediaQuery((theme: any) => theme.breakpoints.up("lg"));
  const hideMenu: any = lgUp
    ? customizer.isCollapse && !customizer.isSidebarHover
    : "";

  const menuItems: MenuitemsType[] = useMemo(() => {
    return permissionStore.accessRoutes
      .filter((route) => {
        if (route.hidden) return false;
        if ((!route.hidden && route.isAccess) || !settings.checkPermission || route.isHeader) {
          return true;
        }
        return false;
      })
      .map((route) => {
        if (route.children) {
          return {
            id: uniqueId(),
            title: route.title,
            href: route.path,
            icon: route.icon,
            children: route.children.map((child) => ({
              id: uniqueId(),
              title: child.title,
              href: route.path + "/" + child.path,
              icon: child.icon,
            })),
          };
        }
        return {
          id: uniqueId(),
          title: route.title,
          href: route.path,
          icon: route.icon,
          subheader: route.subheader,
        };
      })
      .map((it) => toJS(it)) as MenuitemsType[];
  }, [permissionStore.accessRoutes]);
  return (
    <Box sx={{ px: 3 }}>
      <List sx={{ pt: 0 }} className="sidebarNav" >
        {menuItems.map((item) => {
          // {/********SubHeader**********/}
          
          if (item.subheader) {
            if (!hideMenu) {
              return (
                <NavGroup
                  isHideDivider={
                    menuItems
                      .filter((it) => it.subheader)
                      .findIndex((it) => it.subheader == item.subheader) == 0
                  }
                  item={item}
                  hideMenu={hideMenu}
                  key={item.subheader}
                />
              );
            } else {
              return;
            }

            // {/********If Sub Menu**********/}
            /* eslint no-else-return: "off" */
          } else if (item.children) {
            return (
              <NavCollapse
                menu={item}
                pathDirect={pathDirect}
                hideMenu={hideMenu}
                pathWithoutLastPart={pathWithoutLastPart}
                level={1}
                key={item.id}
                onClick={() => appStore.toggleMobileSidebar()}
              />
            );

            // {/********If Sub No Menu**********/}
          } else {
            return (
              <NavItem
                item={item}
                key={item.id}
                level={0}
                pathDirect={pathDirect}
                hideMenu={hideMenu}
                onClick={() => appStore.toggleMobileSidebar()}
              />
            );
          }
        })}
      </List>
    </Box>
  );
};
export default observer(SidebarItems);
