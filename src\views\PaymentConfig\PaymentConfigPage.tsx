import {
  ExportOutlined,
  ImportOutlined,
  PlusOutlined,
  SearchOutlined,
} from "@ant-design/icons";
import { Button, Card, Input, message, Popconfirm, Space } from "antd";
import { paymentConfigApi } from "api/paymentConfig.api";
import { useRank } from "hooks/useRank";
import { useEffect, useMemo, useRef, useState } from "react";
import { PermissionNames } from "router";
import { permissionStore } from "store/permissionStore";
import { PaymentConfig } from "types/paymentConfig";
import { formatVND, getTitle } from "utils";
import { checkRole } from "utils/auth";
import { unixToFullDate } from "utils/dateFormat";
import { handleExport, MyExcelColumn } from "../../utils/MyExcel";
import {
  PaymentConfigModal,
  PaymentConfigModalRef,
} from "./components/Modal/PaymentConfigModal";
import { PaymentConfigList } from "./components/Table/PaymentConfigList";
import { TextField } from "@mui/material";
import { useTranslation } from "react-i18next";
import { usePaymentConfig } from "hooks/usePaymentConfig";

export const PaymentConfigPage = ({ title = "" }) => {
  const paymentConfigModalRef = useRef<PaymentConfigModalRef>();
  const [openImport, setOpenImport] = useState(false);
  const { t } = useTranslation();

  const [loadingDelete, setLoadingDelete] = useState(false);
  const {
    paymentConfigs,
    fetchPaymentConfig,
    loadingPaymentConfig,
    queryPaymentConfig,
    totalPaymentConfig,
  } = usePaymentConfig({
    initQuery: {
      page: 1,
      limit: 20,
    },
  });
  // const hasPaymentConfigAddPermission = checkRole(
  //   PermissionNames.consumerPaymentConfigAdd,
  //   permissionStore.permissions
  // );
  // const hasPaymentConfigUpdatePermission = checkRole(
  //   PermissionNames.consumerIndustryEdit,
  //   permissionStore.permissions
  // );
  // const hasIndustryDeletePermission = checkRole(
  //   PermissionNames.consumerIndustryDelete,
  //   permissionStore.permissions
  // );
  useEffect(() => {
    document.title = getTitle(t(title));
    fetchPaymentConfig();
  }, []);
  const exportColumns: MyExcelColumn<PaymentConfig>[] = [
    {
      width: 30,
      header: t("PaymentConfigName"),
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "name",
      // style: { font: { color: { argb: "004e47cc" } } },
      render: (record: PaymentConfig) => {
        return record.name;
      },
    },
    {
      width: 30,
      header: t("paymentConfigNameEn"),
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "nameEn",
      // style: { font: { color: { argb: "004e47cc" } } },
      render: (record: PaymentConfig) => {
        return record.nameEn;
      },
    },
    {
      width: 20,
      header: t("createdAt"),
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "createdAt",
      render: (record: PaymentConfig) => {
        return unixToFullDate(record.createdAt);
      },
    },
  ];
  const handleDeletePaymentConfig = async (paymentConfigId: number) => {
    try {
      setLoadingDelete(true);
      const res = await paymentConfigApi.delete(paymentConfigId);
      fetchPaymentConfig();
      message.success(t("actionSuccessfully"));
    } catch (error) {
    } finally {
      setLoadingDelete(false);
    }
  };

  return (
    <Card bodyStyle={{ padding: "8px 20px" }} style={{ borderRadius: "8px" }}>
      <section className="box">
        <div className="filter-container">
          <Space wrap>
            <div className="filter-item ">
              <label htmlFor="">{t("search")}</label>
              <Input
                allowClear
                onChange={(ev) => {
                  if (ev.currentTarget.value) {
                    queryPaymentConfig.search = ev.currentTarget.value;
                  } else {
                    queryPaymentConfig.search = undefined;
                  }
                  queryPaymentConfig.page = 1;
                  fetchPaymentConfig();
                }}
                onKeyDown={(ev) => {
                  if (ev.code == "Enter") {
                    fetchPaymentConfig();
                  }
                }}
                size="middle"
                placeholder={t("paymentMethod")}
              />
              {/* <TextField
                size="small"
                onChange={(ev) => {
                  const value = ev.currentTarget.value;
                  if (value) {
                    queryPaymentConfig.page = 1;
                    queryPaymentConfig.search = value;
                  } else {
                    queryPaymentConfig.search = undefined;
                    fetchPaymentConfig();
                  }
                }}
                onKeyDown={(ev) => {
                  if (ev.code == "Enter") {
                    fetchPaymentConfig();
                  }
                }}
                label={t("paymentMethod")}
              /> */}
            </div>

            <div className="filter-item btn">
              <Button
                onClick={() => fetchPaymentConfig()}
                type="primary"
                icon={<SearchOutlined />}
              >
                {t("search")}
              </Button>
            </div>
            {/* {hasPaymentConfigAddPermission && ( */}
            {/* <div className="filter-item ">
              <Button
                onClick={() => {
                  paymentConfigModalRef.current?.handleCreate();
                }}
                icon={<PlusOutlined />}
                type="primary"
              >
                {t("create")}
              </Button>
            </div> */}
            {/* )} */}
            {/* <div className="filter-item btn">
                    <Button
                      onClick={() => {
                        setOpenImport(true);
                      }}
                      type="primary"
                      icon={<PlusOutlined />}
                    >
                      Nhập excel
                    </Button>
                  </div> */}
            {/* <div className="filter-item btn">
                      <Button
                        onClick={() => {
                          importModal.current?.open();
                        }}
                        type="primary"
                        icon={<ImportOutlined />}
                      >
                        Nhập excel
                      </Button>
                    </div> */}

            <div className="filter-item btn">
              <Popconfirm
                title={t("exportAsk")}
                onConfirm={() =>
                  handleExport({
                    onProgress(percent) {},
                    exportColumns,
                    fileType: "xlsx",
                    dataField: "paymentConfigs",
                    query: queryPaymentConfig,
                    api: paymentConfigApi.findAll,
                    fileName: t("paymentMethod"),
                    sheetName: t("paymentMethod"),
                  })
                }
                okText={t("exportExcel")}
                cancelText={t("cancel")}
              >
                <Button
                  type="primary"
                  loading={false}
                  icon={<ExportOutlined />}
                >
                  {t("exportExcel")}
                </Button>
              </Popconfirm>
            </div>
          </Space>
        </div>

        <PaymentConfigList
          onEdit={(record) =>
            paymentConfigModalRef.current?.handleUpdate(record)
          }
          dataSource={paymentConfigs}
          loading={loadingPaymentConfig}
          loadingDelete={loadingDelete}
          pagination={{
            total: totalPaymentConfig,
            defaultPageSize: queryPaymentConfig.limit,
            currentPage: queryPaymentConfig.page,
            onChange: ({ page, limit }) => {
              Object.assign(queryPaymentConfig, {
                page,
                limit,
              });
              fetchPaymentConfig();
            },
          }}
          onDelete={handleDeletePaymentConfig}
          onSubmitOk={fetchPaymentConfig}

          // onActive={handleActivePaymentConfig}
          // onInactive={handleInactivePaymentConfig}

          // hasDeletePaymentConfigPermission={hasPaymentConfigDeletePermission}
          // hasUpdatePaymentConfigPermission={hasPaymentConfigUpdatePermission}
        />
      </section>

      <PaymentConfigModal
        ref={paymentConfigModalRef}
        onClose={function (): void {
          throw new Error("Function not implemented.");
        }}
        onSubmitOk={fetchPaymentConfig}
        // hasAddIndustryPermission={hasIndustryAddPermission}
        // hasUpdateIndustryPermission={hasIndustryUpdatePermission}
      />
    </Card>
  );
};
