import { reviewApi } from "api/review.api";
import { useState } from "react";
import { Review } from "types/review";
import { QueryParam } from "types/query";

export interface ReviewQuery extends QueryParam { }

interface UseReviewProps {
  initQuery: ReviewQuery;
}

export const useReview = ({ initQuery }: UseReviewProps) => {
  const [data, setData] = useState<Review[]>([]);
  const [total, setTotal] = useState(0);
  const [query, setQuery] = useState<ReviewQuery>(initQuery);
  const [loading, setLoading] = useState(false);
  const [totalContentEmpty, setTotalContentEmpty] = useState(0);

  const [historyData, setHistoryData] = useState<any[]>([]);
  const [loadingHistory, setLoadingHistory] = useState(false);
  const [historyQuery, setHistoryQuery] = useState<ReviewQuery>(initQuery);
  const [totalHistory, setTotalHistory] = useState(0);

  const fetchData = async (newQuery?: ReviewQuery) => {
    setLoading(true);
    try {
      const { data } = await reviewApi.findAll({ ...query, ...newQuery });
      console.log(data.reviews);
      setData(data.reviews);
      setTotal(data.total);
      setTotalContentEmpty(data.totalContentEmpty);

      return data.reviews;
    } finally {
      setLoading(false);
    }
  };

  const fetchHistory = async (newQuery?: ReviewQuery) => {
    setLoadingHistory(true);
    try {
      const { data } = await reviewApi.reviewHistory({ ...historyQuery, ...newQuery });
      setHistoryData(data.reviews || []);
      setTotalHistory(data.total || 0);
      return data.reviews;
    } finally {
      setLoadingHistory(false);
    }
  };


  const fetchDataReject = async (newQuery?: ReviewQuery) => {
    setLoading(true);
    try {
      const { data } = await reviewApi.findReject({ ...query, ...newQuery });
      console.log(data.reviews);
      setData(data.reviews);
      setTotal(data.total);
      setTotalContentEmpty(data.totalContentEmpty);

      return data.reviews;
    } finally {
      setLoading(false);
    }
  };


  return {
    reviews: data,
    totalReview: total,
    fetchReview: fetchData,
    loadingReview: loading,
    setQueryReview: setQuery,
    queryReview: query,
    totalContentEmpty,

    reviewHistory: historyData,
    fetchReviewHistory: fetchHistory,
    loadingReviewHistory: loadingHistory,
    queryReviewHistory: historyQuery,
    setQueryReviewHistory: setHistoryQuery,
    totalReviewHistory: totalHistory,

    fetchDataReject
  };
};
