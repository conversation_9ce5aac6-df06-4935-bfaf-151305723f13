import { Col, Form, Input, message, Modal, Row } from "antd";
import { UploadFile } from "antd/lib";
import { useForm } from "antd/lib/form/Form";
import { paymentConfigApi } from "api/paymentConfig.api";
import { withdrawMethodApi } from "api/withdrawMethod.api";
import { SingleImageUpload } from "components/Upload/SingleImageUpload";
import { forwardRef, useImperativeHandle, useState } from "react";
import { useTranslation } from "react-i18next";
import { ModalStatus } from "types/modal";
import { PaymentConfig } from "types/paymentConfig";
import { WithdrawMethod } from "types/withdrawMethod";
import { requiredRule } from "utils/validate-rules";

export interface WithdrawConfigModalProps {
  onClose: () => void;
  onSubmitOk: () => void;
  // hasAddWithdrawConfigPermission?: boolean;
  // hasUpdateWithdrawConfigPermission?: boolean;
}

export interface WithdrawConfigModalRef {
  handleUpdate: (withdrawMethod: WithdrawMethod) => void;
  handleCreate: () => void;
}

export const WithdrawConfigModal = forwardRef(
  (
    {
      onSubmitOk,
    }: // hasAddPaymentConfigPermission,
    // hasUpdatePaymentConfigPermission,
    WithdrawConfigModalProps,
    ref
  ) => {
    const [form] = useForm();
    const [loading, setLoading] = useState(false);
    const [visible, setVisible] = useState<boolean>();
    const { t } = useTranslation();
    const [fileList, setFileList] = useState<UploadFile[]>([]);
    const onChangeFileList = (fileList: UploadFile[]): void => {
      setFileList(fileList);
    };
    const [selectedWithdrawMethod, setSelectedWithdrawMethod] =
      useState<WithdrawMethod>();
    const [status, setStatus] = useState<ModalStatus>("create");

    useImperativeHandle(
      ref,
      () => ({
        handleUpdate,
        handleCreate,
      }),
      []
    );

    const handleUpdate = (withdrawMethod: WithdrawMethod) => {
      form.setFieldsValue({
        ...withdrawMethod,
      });
      // setFileList(withdrawMethod?.fileAttaches)
      setSelectedWithdrawMethod(withdrawMethod);
      setStatus("update");
      setVisible(true);
    };

    const handleCreate = () => {
      setStatus("create");
      setVisible(true);
    };

    const handleSubmitForm = async () => {
      await form.validateFields();
      console.log(fileList);
      const dataForm = form.getFieldsValue();
      if (dataForm.name) {
        dataForm.name = dataForm.name.trim();
      }
      const payload = {
        withdrawMethod: {
          ...dataForm,
        },
      };

      try {
        setLoading(true);
        switch (status) {
          case "update":
            await withdrawMethodApi.update(
              selectedWithdrawMethod?.id || 0,
              payload
            );
            message.success(t("actionSuccessfully"));
            break;

          //create
          default:
            await withdrawMethodApi.create(payload);
            message.success(t("actionSuccessfully"));
            break;
        }
        setVisible(false);
        onSubmitOk();
        setFileList([]);
      } catch (error) {
      } finally {
        setLoading(false);
        // onSubmitOk();
      }
    };

    return (
      <Modal
        onCancel={() => {
          setVisible(false);
          setFileList([]);
        }}
        visible={visible}
        centered
        title={
          <h1 className="mb-0 text-lg text-primary font-bold">
            {status == "create" ? t("create") : t("update")}
          </h1>
        }
        confirmLoading={loading}
        destroyOnClose
        width={800}
        onOk={handleSubmitForm}
        afterClose={() => {
          form.resetFields();
        }}

        // okButtonProps={{
        //   hidden:
        //     (!hasAddPaymentConfigPermission && status == "create") ||
        //     (!hasUpdatePaymentConfigPermission && status == "update"),
        // }}
      >
        <Form
          form={form}
          layout="vertical"
          validateTrigger={["onBlur", "onChange"]}
        >
          <Row gutter={[12, 0]}>
            <Col span={24}>
              <Form.Item shouldUpdate={true}>
                {() => {
                  return (
                    <Form.Item label={t("Logo")} name="logo">
                      <SingleImageUpload
                        onUploadOk={(path: string) => {
                          form.setFieldsValue({
                            logo: path,
                          });
                        }}
                        imageUrl={form.getFieldValue("logo")}
                      />
                    </Form.Item>
                  );
                }}
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item
                className="!mb-2"
                name="name"
                label={t("withdrawConfigName")}
                rules={[requiredRule]}
              >
                <Input placeholder={t("enterName")} size="small" />
              </Form.Item>
            </Col>

            <Col span={24}>
              <Form.Item
                className="!mb-2"
                name="nameEn"
                label={t("withdrawConfigNameEn")}
                rules={[requiredRule]}
              >
                <Input placeholder={t("enterName")} size="small" />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    );
  }
);
