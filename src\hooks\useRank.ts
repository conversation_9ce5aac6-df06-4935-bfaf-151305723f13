import { rankApi } from "api/rank.api";
import { useState } from "react";
import { Rank } from "types/rank";
import { QueryParam } from "types/query";

export interface RankQuery extends QueryParam {}

interface UseRankProps {
  initQuery: RankQuery;
}

export const useRank = ({ initQuery }: UseRankProps) => {
  const [data, setData] = useState<Rank[]>([]);
  const [total, setTotal] = useState(0);
  const [query, setQuery] = useState<RankQuery>(initQuery);
  const [loading, setLoading] = useState(false);

  const fetchData = async () => {
    setLoading(true);
    try {
      const { data } = await rankApi.findAll(query);

      setData(data.ranks);
      setTotal(data.total);
    } finally {
      setLoading(false);
    }
  };

  return {
    ranks: data,
    totalRank: total,
    fetchRank: fetchData,
    loadingRank: loading,
    setQueryRank: setQuery,
    queryRank: query,
  };
};
