h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0 !important;
}

h1 {
  &.semibold {
    font-weight: 600;
    font-size: 36px;
    line-height: 120%;
  }
  &.regular {
    font-weight: 400;
  }
}

h2 {
  &.semibold {
    font-weight: 600;
    font-size: 30px;
    line-height: 120%;
  }
  &.regular {
    font-weight: 400;
  }
}

h3 {
  &.semibold {
    font-weight: 600;
    font-size: 24px;
    line-height: 120%;
  }
  &.regular {
    font-weight: 400;
  }
}

h4 {
  &.semibold {
    font-weight: 600;
    font-size: 21px;
    line-height: 130%;
  }
  &.regular {
    font-weight: 400;
  }
}

h5 {
  &.semibold {
    font-weight: 600;
    font-size: 18px;
    line-height: 120%;
  }
  &.regular {
    font-weight: 400;
  }
}

h6 {
  &.semibold {
    font-weight: 600;
    font-size: 16px;
    line-height: 120%;
  }
  &.regular {
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
  }
}

@mixin text-semibold {
  font-weight: 600 !important;
  font-size: 14px !important;
}

@mixin text-normal-semibold {
  font-weight: 600;
  font-size: 14px;
}

@mixin text-regular {
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
}

.text-regular {
  @include text-regular;
}

.text-semibold {
  @include text-semibold;
}

.text-normal-semibold {
  font-weight: 600;
  font-size: 14px;
}

.text-medium {
  font-weight: 500;
  font-size: 14px;
}

.text-bold {
  font-weight: 700;
  font-size: 14px;
}

.text-extra-bold {
  font-weight: 800;
  font-size: 14px;
}

.text-black {
  font-weight: 900;
  font-size: 14px;
}

.text-caption-no-caps-regular {
  font-size: 12px;
  line-height: 140%;
  font-weight: 400;
}

.text-caption-caps-semibold {
  font-size: 12px;
  line-height: 140%;
  font-weight: 600;
}

.text-caption-smaller-text {
  font-size: 10px;
  font-weight: 700;
}

// Color Variables
:root {
  --orange-color: #eb9113;
  --orange-color-500: #fb6514;
  --primary-color: #1a73e8;
  --secondary-color: #2a3547;
  --secondary-color-500: #32343a;
  --third-color: #333b54;
  --gray-1-color: #2a3547;
  --gray-2-color: #5a6a85;
  --gray-3-color: #bdc7d5;
  --gray-4-color: #eaeff4;
  --gray-5-color: #f6f9fc;
  --gray-6-color: #ffffff;
  --gray-7-color: #7c8fac;
  --action-1-color: #41d664;
  --action-2-color: #ffcd00;
  --action-3-color: #ed2012;
  --action-4-color: #4d8cff;
  --action-5-color: #8b2dff;
  --action-6-color: #99bbfb;
  --action-light-1-color: #e9ffee;
  --action-light-2-color: #fffaeb;
  --action-light-3-color: #fff1f1;
  --action-light-4-color: #eff5ff;
  --success-50: #ecfdf3;
  --success-100: #d1fadf;
  --success-200: #a6f4c5;
  --success-300: #6ce9a6;
  --success-400: #32d583;
  --success-500: #12b76a;
  --success-600: #039855;
  --success-700: #027a48;
  --success-800: #05603a;
  --success-900: #054f31;
  --success-950: #053321;
  --warning-50: #fffaeb;
  --warning-100: #fef0c7;
  --warning-200: #fedf89;
  --warning-300: #fec84b;
  --warning-400: #fdb022;
  --warning-500: #f79009;
  --green-light-color: #f6fef9;
  --orange-light-color: #fffaf5;
  --orange-light-border-color: #ffead5;
  --blue-light-color: #f5faff;
  --blue-light-border-color: #e0f2fe;
  --error-color-500: #ed2012;
  --red-light-color: #fffbfa;
  --red-light-border-color: #fee4e2;
  --blue-color-500: #2e90fa;
  --text-red-600: #ed2012;
  --text-green-600: #12b76a;
  --warning-color-300: #fec84b;
  --warning-background-color-25: #fffcf5;
  --warning-border-color: #fef0c7;
}

.text-orange {
  color: var(--orange-color);
}
.text-primary {
  color: var(--primary-color);
}
.text-secondary {
  color: var(--secondary-color);
}
.text-secondary-500 {
  color: var(--secondary-color-500);
}
.text-third {
  color: var(--third-color);
}
.text-gray-1 {
  color: var(--gray-1-color);
}
.text-gray-2 {
  color: var(--gray-2-color);
}
.text-gray-3 {
  color: var(--gray-3-color);
}
.text-gray-4 {
  color: var(--gray-4-color);
}
.text-gray-5 {
  color: var(--gray-5-color);
}
.text-gray-6 {
  color: var(--gray-6-color);
}
.text-gray-7 {
  color: var(--gray-7-color);
}
.text-action-1 {
  color: var(--action-1-color);
}
.text-action-2 {
  color: var(--action-2-color);
}
.text-action-3 {
  color: var(--action-3-color);
}
.text-action-4 {
  color: var(--action-4-color);
}
.text-action-5 {
  color: var(--action-5-color);
}
.text-action-6 {
  color: var(--action-6-color);
}
.text-red-600 {
  color: var(--text-red-600);
}
.text-green-600 {
  color: var(--text-green-600);
}

.text-action-light-1 {
  color: var(--action-light-1-color);
}
.text-action-light-2 {
  color: var(--action-light-2-color);
}
.text-action-light-3 {
  color: var(--action-light-3-color);
}
.text-action-light-4 {
  color: var(--action-light-4-color);
}

.text-success-500 {
  color: var(--success-500);
}

.shadow {
  box-shadow: 0px 0px 2px rgba(145, 158, 171, 0.2),
    0px 12px 24px -4px rgba(145, 158, 171, 0.12);
}

.border-shadow {
  box-shadow: 0px 0px 2px 0px rgba(145, 158, 171, 0.1),
    0px 4px 12px -2px rgba(145, 158, 171, 0.1);
}

.border-shadow-2 {
  box-shadow: 0px 0px 2px 0px rgba(145, 158, 171, 0.2),
    0px 12px 24px -4px rgba(145, 158, 171, 0.12);
}

.green-status-color {
  color: var(--success-500);
  background-color: var(--green-light-color);
  border-color: var(--success-100);
}

.orange-status-color {
  color: var(--orange-color-500);
  background-color: var(--orange-light-color);
  border-color: var(--orange-light-border-color);
}

.blue-status-color {
  color: var(--blue-color-500);
  background-color: var(--blue-light-color);
  border-color: var(--blue-light-border-color);
}

.red-status-color {
  color: var(--error-color-500);
  background-color: var(--red-light-color);
  border-color: var(--red-light-border-color);
}

.gray-status-color {
  color: var(--gray-2-color);
  background-color: var(--gray-1-color);
  border-color: var(--gray-4-color);
}

.yellow-status-color {
  color: var(--warning-color-300);
  background-color: var(--warning-background-color-25);
  border-color: var(--warning-border-color);
}
