import {
  ExportOutlined,
  ImportOutlined,
  PlusOutlined,
  SearchOutlined,
} from "@ant-design/icons";
import { Button, Card, Input, message, Popconfirm, Space } from "antd";
import { faqCategoryApi } from "api/faqCategory.api";
import { useEffect, useMemo, useRef, useState } from "react";
import { PermissionNames } from "router";
import { permissionStore } from "store/permissionStore";
import { getTitle } from "utils";
import { checkRole } from "utils/auth";
import { unixToFullDate } from "utils/dateFormat";
import { handleExport, MyExcelColumn } from "../../utils/MyExcel";
import {
  FaqCategoryModal,
  FaqCategoryModalRef,
} from "./components/Modal/FaqCategoryModal";
import { FaqCategoryList } from "./components/Table/FaqCategoryList";
import { TextField } from "@mui/material";
import { FaqCategory, FaqCategoryType } from "types/faq";
import { useFaqCategory } from "hooks/useFaqCategory";
import { useTranslation } from "react-i18next";
import { ReactComponent as PlusIcon } from "assets/svgs/plus-icon.svg";
import { ReactComponent as SearchIcon } from "assets/svgs/search-icon.svg";

const exportColumns: MyExcelColumn<FaqCategory>[] = [
  {
    header: "Tên FaqCategory (VI)",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "name",
    columnKey: "name",
    render: (record) => record.name,
  },
  {
    header: "Tên FaqCategory (ENG)",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "nameEn",
    columnKey: "nameEn",
    render: (record) => record.nameEn,
  },
  {
    header: "Ngày tạo",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "createdDateTime",
    columnKey: "createdDateTime",
    render: (record) => unixToFullDate(record.createdAt),
  },
];

export const FaqCategoryPage = ({
  title = "",
  type = FaqCategoryType.Customer,
}) => {
  const faqCategoryModalRef = useRef<FaqCategoryModalRef>();
  const [openImport, setOpenImport] = useState(false);
  const { t } = useTranslation();

  const [loadingDelete, setLoadingDelete] = useState(false);
  const {
    faqCategories,
    fetchFaqCategory,
    loadingFaqCategory,
    queryFaqCategory,
    totalFaqCategory,
  } = useFaqCategory({
    initQuery: {
      page: 1,
      limit: 20,
      type,
    },
  });
  // const hasFaqCategoryAddPermission = checkRole(
  //   PermissionNames.consumerFaqCategoryAdd,
  //   permissionStore.permissions
  // );
  // const hasFaqCategoryUpdatePermission = checkRole(
  //   PermissionNames.consumerIndustryEdit,
  //   permissionStore.permissions
  // );
  // const hasIndustryDeletePermission = checkRole(
  //   PermissionNames.consumerIndustryDelete,
  //   permissionStore.permissions
  // );
  useEffect(() => {
    document.title = getTitle(t(title));
    fetchFaqCategory();
  }, []);

  const handleDeleteFaqCategory = async (faqCategoryId: number) => {
    try {
      setLoadingDelete(true);
      const res = await faqCategoryApi.delete(faqCategoryId);
      fetchFaqCategory();
      message.success(t("actionSuccessfully"));
    } catch (error) {
    } finally {
      setLoadingDelete(false);
    }
  };

  const handleSearch = (search: string) => {
    queryFaqCategory.search = search;
    queryFaqCategory.page = 1;
    fetchFaqCategory();
  };

  return (
    // <Card bodyStyle={{ padding: "8px 20px" }} style={{ borderRadius: "8px" }}>
    <div>
      <section className="box">
        <div className="filter-container">
          <Space wrap>
            {/* <div className="filter-item">
              <label htmlFor="">{t("search")}</label>
              <Input.Search
                allowClear
                onChange={(ev) => {
                  if (ev.currentTarget.value) {
                    queryFaqCategory.search = ev.currentTarget.value;
                  } else {
                    queryFaqCategory.search = undefined;
                  }
                  queryFaqCategory.page = 1;
                }}
                onKeyDown={(ev) => {
                  if (ev.code == "Enter") {
                  }
                }}
                size="large"
                className="w-full search-btn mt-1"
                enterButton={<SearchIcon />}
                onSearch={handleSearch}
              />
            </div> */}

            {/* <div className="filter-item btn ">
              <Button
                onClick={() => fetchFaqCategory()}
                type="primary"
                icon={<SearchOutlined />}
              >
                {t("search")}
              </Button>
            </div> */}
            {/* {hasFaqCategoryAddPermission && ( */}
            <div className="filter-item-btn">
              <Button
                onClick={() => {
                  faqCategoryModalRef.current?.handleCreate();
                }}
                icon={
                  <PlusIcon height={20} width={20} className="text-gray-6" />
                }
                type="primary"
                size="large"
              >
                <span className="text-regular text-action-light-4">
                  {t("create")}
                </span>
              </Button>
            </div>
            {/* )} */}
            {/* <div className="filter-item btn">
                <Button
                  onClick={() => {
                    setOpenImport(true);
                  }}
                  type="primary"
                  icon={<PlusOutlined />}
                >
                  Nhập excel
                </Button>
              </div> */}
            {/* <div className="filter-item btn">
                  <Button
                    onClick={() => {
                      importModal.current?.open();
                    }}
                    type="primary"
                    icon={<ImportOutlined />}
                  >
                    Nhập excel
                  </Button>
                </div> */}

            {/* <div className="filter-item btn">
                <Popconfirm
                  title={`Bạn có muốn xuất file excel`}
                  onConfirm={() =>
                    handleExport({
                      onProgress(percent) {},
                      exportColumns,
                      fileType: "xlsx",
                      dataField: "industries",
                      query: queryFaqCategory,
                      api: FaqCategoryApi.findAll,
                      fileName: "Danh sách loại FAQ",
                      sheetName: "Danh sách loại FAQ",
                    })
                  }
                  okText={"Xuất excel"}
                  cancelText={"Huỷ"}
                >
                  <Button
                    type="primary"
                    loading={false}
                    icon={<ExportOutlined />}
                  >
                    Xuất excel
                  </Button>
                </Popconfirm>
              </div> */}
          </Space>
        </div>

        <FaqCategoryList
          onEdit={(record) => faqCategoryModalRef.current?.handleUpdate(record)}
          dataSource={faqCategories}
          loading={loadingFaqCategory}
          loadingDelete={loadingDelete}
          pagination={
            totalFaqCategory > queryFaqCategory.limit
              ? {
                  total: totalFaqCategory,
                  defaultPageSize: queryFaqCategory.limit,
                  currentPage: queryFaqCategory.page,
                  onChange: ({ page, limit }) => {
                    Object.assign(queryFaqCategory, {
                      page,
                      limit,
                    });
                    fetchFaqCategory();
                  },
                }
              : undefined
          }
          onDelete={handleDeleteFaqCategory}

          // hasDeleteFaqCategoryPermission={hasFaqCategoryDeletePermission}
          // hasUpdateFaqCategoryPermission={hasFaqCategoryUpdatePermission}
        />
      </section>

      <FaqCategoryModal
        ref={faqCategoryModalRef}
        onClose={function (): void {
          throw new Error("Function not implemented.");
        }}
        type={type}
        onSubmitOk={fetchFaqCategory}
        // hasAddIndustryPermission={hasIndustryAddPermission}
        // hasUpdateIndustryPermission={hasIndustryUpdatePermission}
      />
      {/* </Card> */}
    </div>
  );
};
