import React, { useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { settings } from "settings";
import { permissionStore } from "store/permissionStore";
import { getToken } from "utils/auth";

const whileList = ["/login"];

export const useRouter = (isLoaded: boolean) => {
  const location = useLocation();
  const navigate = useNavigate();

  useEffect(() => {
    if (!isLoaded) {
      return;
    }

    const token = getToken();

    if (!token) {
      return navigate("/login");
    }

    if (location.pathname == "/login" && token) {
      return navigate("/");
    }

    if (whileList.includes(location.pathname)) {
      return;
    }

    if (permissionStore.accessRoutes.length) {
      if (location.pathname == "/") {
        if (token) {
          let firstRoute = permissionStore.accessRoutes.find(
            (e) => e.isAccess || !settings.checkPermission
          );

          const route = firstRoute?.children
            ? firstRoute.path + "/" + firstRoute.children[0].path
            : firstRoute?.path;
          navigate(route || "/login");
        } else {
          navigate("/login");
        }
      } else {
        if (
          settings.checkPermission &&
          !permissionStore.permissions.some((p) => p.path == location.pathname)
        ) {
          navigate("/404");
        }
      }
    }

    handleScrollToTop();
  }, [location.pathname, isLoaded]);

  const handleScrollToTop = () => {
    window.scrollTo({
      top: 0,
      left: 0,
      behavior: "smooth",
    });
  };

  return location;
};
