import * as ExcelJS from "exceljs";
import saveAs from "file-saver";

export interface ExcelDataType {
  title?: string;
  required?: boolean;
  positive?: boolean;
  unique?: boolean;
  description?: string[];
  formuleData?: string[];
}

export const handleAddFormuleData = (
  worksheet: any,
  formule: string[],
  columnIndex: number
) => {
  //Cách này chỉ áp dụng cho các bảng có cột từ A->Z
  const alphabet = [
    "A",
    "B",
    "C",
    "D",
    "E",
    "F",
    "G",
    "H",
    "I",
    "J",
    "K",
    "L",
    "M",
    "N",
    "O",
    "P",
    "Q",
    "R",
    "S",
    "T",
    "U",
    "V",
    "W",
    "X",
    "Y",
    "Z",
  ];
  const columnName = alphabet[columnIndex];
  //Thêm danh hộp chọn danh mục
  const formuleData = '"' + formule.join(",") + '"';
  const address = `${columnName}2:${columnName}9999`;
  //@ts-ignore
  worksheet.dataValidations.add(address, {
    allowBlank: true,
    error: "Vui lòng chọn giá trị hợp lệ trong hộp chọn",
    errorTitle: "Giá trị không tồn tại",
    formulae: [formuleData],
    showErrorMessage: true,
    type: "list",
  });
};

export const autoWidth = (worksheet: ExcelJS.Worksheet) => {
  worksheet.columns?.forEach?.(function (column, i) {
    var maxLength = 0;
    //@ts-ignore
    column?.["eachCell"]({ includeEmpty: true }, function (cell) {
      var columnLength = cell.value ? cell.value.toString().length : 20;
      if (columnLength > maxLength) {
        maxLength = columnLength;
      }
    });
    column.width = maxLength < 20 ? 20 : maxLength;
  });
};

const addDemoFileSheet = (
  workbook: ExcelJS.Workbook,
  dataSheet: ExcelDataType[],
  t: (key: string, options?: any) => string
) => {
  const worksheetDemo = workbook.addWorksheet(t("sample"), {
    pageSetup: { fitToPage: true, fitToHeight: 5, fitToWidth: 7 },
  });

  const titleRow = worksheetDemo.getRow(1);
  titleRow.height = 50;
  dataSheet.forEach((cell, index) => {
    if (cell.formuleData) {
      handleAddFormuleData(worksheetDemo, cell.formuleData, index);
    }
    const currCell = titleRow.getCell(index + 1);
    currCell.value = cell.title;
    currCell.font = {
      bold: true,
      color: {
        argb: "ffffff",
      },
    };
    currCell.border = {
      top: {
        style: "thin",
        color: {
          argb: "000000",
        },
      },
      left: {
        style: "thin",
        color: {
          argb: "000000",
        },
      },
      right: {
        style: "thin",
        color: {
          argb: "000000",
        },
      },
      bottom: {
        style: "thin",
        color: {
          argb: "000000",
        },
      },
    };
    currCell.alignment = {
      vertical: "middle",
      horizontal: "center",
    };
    currCell.fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "fe7b52" },
    };
  });

  autoWidth(worksheetDemo);
};

const handleDemoSheet = (
  workbook: ExcelJS.Workbook,
  dataSheet: ExcelDataType[],
  t: (key: string, options?: any) => string
) =>
  new Promise((resolve, reject) => {
    const worksheetDemo = workbook.addWorksheet(t("instruction"), {
      pageSetup: { fitToPage: true, fitToHeight: 5, fitToWidth: 7 },
    });
    const title = t("samplePartnerList");

    const headingRow = worksheetDemo.getRow(1);

    headingRow.getCell(1).value = title;
    headingRow.getCell(1).alignment = { horizontal: "center" };
    headingRow.getCell(1).font = { size: 15, bold: true };
    headingRow.getCell(1).border = {
      bottom: {
        style: "thin",
        color: {
          argb: "000000",
        },
      },
    };

    worksheetDemo.mergeCells(1, 1, 1, dataSheet.length);

    const titleRow = worksheetDemo.getRow(2);
    titleRow.height = 50;
    const conditionRow = worksheetDemo.getRow(3);
    conditionRow.height = 50;
    const descRow = worksheetDemo.getRow(4);
    descRow.height = 50;
    for (let i = 0; i < dataSheet.length; i++) {
      const title = dataSheet[i].title;
      const isRequired = dataSheet[i].required;
      titleRow.getCell(i + 1).value = title;

      conditionRow.getCell(i + 1).value = isRequired
        ? t("required")
        : t("optional");
      titleRow.getCell(i + 1).font = { bold: true, color: { argb: "ffffff" } };
      conditionRow.getCell(i + 1).font = {
        bold: isRequired,
        color: isRequired ? { argb: "ff0505" } : { argb: "000000" },
      };
      titleRow.getCell(i + 1).alignment = { horizontal: "center" };
      conditionRow.getCell(i + 1).alignment = { horizontal: "center" };
      descRow.getCell(i + 1).alignment = { horizontal: "center" };

      titleRow.getCell(i + 1).border = {
        bottom: {
          style: "thin",
          color: {
            argb: "000000",
          },
        },
      };
      conditionRow.getCell(i + 1).border = {
        bottom: {
          style: "thin",
          color: {
            argb: "000000",
          },
        },
      };

      titleRow.getCell(i + 1).fill = {
        type: "pattern",
        pattern: "solid",
        fgColor: { argb: "fe7b52" },
      };
      conditionRow.getCell(i + 1).fill = {
        type: "pattern",
        pattern: "solid",
        fgColor: { argb: "dddddd" },
      };
      titleRow.getCell(i + 1).alignment = {
        vertical: "middle",
        horizontal: "center",
      };
      conditionRow.getCell(i + 1).alignment = {
        vertical: "middle",
        horizontal: "center",
      };
      descRow.getCell(i + 1).alignment = {
        vertical: "middle",
        horizontal: "center",
      };
      const desc = dataSheet[i].description;

      desc?.forEach((value: string, index) => {
        const newDesc = worksheetDemo.getRow(4 + index);
        const currCell = newDesc.getCell(i + 1);
        currCell.value = value;
        currCell.alignment = {
          horizontal: "center",
          vertical: "middle",
        };
      });
    }
    autoWidth(worksheetDemo);
    return resolve(true);
  });

export const handleAddSheet = <T>(
  workbook: ExcelJS.Workbook,
  title: string,
  data: T[],
  heading: Partial<ExcelJS.Column>[]
) => {
  //Khai báo sheet
  const sheet = workbook.addWorksheet(title, {
    pageSetup: { fitToPage: true, fitToHeight: 5, fitToWidth: 7 },
  });
  //Khai báo giá trị của từng column
  sheet.columns = heading;
  sheet.state = "veryHidden";
  // Khai báo heading
  const headingRow = sheet.getRow(1);
  headingRow.getCell(1).value = title;
  headingRow.getCell(1).alignment = { horizontal: "center" };
  headingRow.getCell(1).font = { size: 15, bold: true };
  headingRow.getCell(1).border = {
    bottom: {
      style: "thin",
      color: {
        argb: "000000",
      },
    },
  };
  //Merge Cell heading
  sheet.mergeCells(1, 1, 1, heading.length);

  //Khai báo title
  const titleRow = sheet.getRow(2);
  heading.forEach((title, index) => {
    titleRow.getCell(index + 1).value = title.header as string;
    titleRow.getCell(index + 1).font = {
      bold: true,
    };
    titleRow.getCell(index + 1).alignment = {
      horizontal: "center",
    };
  });

  //Add từng row
  sheet.insertRows(3, data);

  //Fit width
  autoWidth(sheet);
};

const unitSheetHeading: Partial<ExcelJS.Column>[] = [
  {
    header: "Loại",
    key: "id",
    alignment: {
      horizontal: "center",
    },
  },
  { header: "Loại", key: "name" },
];

const partnerExportDemo = async (t: (key: string, options?: any) => string) => {
  //Khai bao file
  const workbook = new ExcelJS.Workbook();

  const data: ExcelDataType[] = [
    {
      title: "Email (*)",
      description: [t("requireEmail")],
      required: true,
    },
    {
      title: `${t("phoneNumber")} (*)`,
      description: [t("requirePhoneNum")],
      required: true,
    },
    {
      title: `${t("password")} (*)`,
      description: [t("requirePass")],
      required: true,
    },
    {
      title: `${t("partnerName")} (*)`,
      description: [t("requirePartnerName")],
      required: true,
    },
  ];
  addDemoFileSheet(workbook, data, t);

  handleDemoSheet(workbook, data, t);

  workbook.xlsx.writeBuffer().then(function (buffer: any) {
    saveAs(
      new Blob([buffer], { type: "application/octet-stream" }),
      `${t("samplePartnerList")}.xlsx`
    );
  });
};

export default partnerExportDemo;
