import { projectApi } from "api/project.api";
import { debounce } from "lodash";
import { useEffect, useRef, useState } from "react";
import { Project } from "types/project";
import { QueryParam } from "types/query";

export interface ProjectQuery extends QueryParam {
  // customerId: number;
}

interface UseProjectProps {
  initQuery: ProjectQuery;
}

export const useProject = ({ initQuery }: UseProjectProps) => {
  const [data, setData] = useState<Project[]>([]);
  const [summaryData, setSummaryData] = useState<{
    total: number;
    totalProcessing: number;
    totalPaused: number;
    totalPrice: number;
  }>();
  const [total, setTotal] = useState(0);
  const [query, setQuery] = useState<ProjectQuery>(initQuery);
  const [loading, setLoading] = useState(false);
  const [loadingSummary, setLoadingSummary] = useState(false);
  const [hasMoreData, setHasMoreData] = useState(true);
  const countFetch = useRef(0);

  useEffect(() => {
    setHasMoreData(data.length < total);
  }, [data]);

  const fetchData = async (newQuery?: ProjectQuery) => {
    setLoading(true);
    try {
      countFetch.current++;
      let currentFetch = countFetch.current;

      const { data } = await projectApi.findAll({ ...query, ...newQuery });

      if (currentFetch == countFetch.current) {
        setData(data.projects);
        setTotal(data.total);
      }
    } finally {
      setLoading(false);
    }
  };

  const fetchMore = async (newQuery?: ProjectQuery) => {
    if (loading) return;

    const mergedQuery = { ...query, ...newQuery };
    const nextPage = Math.floor(data.length / mergedQuery.limit) + 1;

    const isAllDataFetched = data.length >= total;
    if (isAllDataFetched) return;

    setLoading(true);
    try {
      const { data: moreData } = await projectApi.findAll({
        ...mergedQuery,
        page: nextPage,
      });

      setQuery((prev) => ({
        ...prev,
        ...newQuery,
        page: nextPage,
      }));

      setData((prev) => [...prev, ...moreData.projects]);
      setTotal(moreData.total);
    } finally {
      setLoading(false);
    }
  };


  const fetchSummary = async () => {
    setLoadingSummary(true);
    try {
      const { data } = await projectApi.findSummary(query);
      setSummaryData(data);
    } finally {
      setLoadingSummary(false);
    }
  };

  const debounceSearchProject = debounce((search: string) => {
    query.search = search;
    fetchData();
  }, 400);

  return {
    projects: data,
    totalProject: total,
    fetchProject: fetchData,
    loadingProject: loading,
    setQueryProject: setQuery,
    queryProject: query,
    fetchSummary,
    summaryData,
    debounceSearchProject,
    fetchMore,
    hasMoreData,
  };
};
