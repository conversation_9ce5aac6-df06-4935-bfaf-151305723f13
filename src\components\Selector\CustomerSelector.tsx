import { Select } from "antd";
import { DefaultOptionType } from "antd/es/select";
import { SelectProps } from "antd/lib";
import { useHandleCustomer } from "hooks/useHandleCustomer";
import { usePartner } from "hooks/usePartner";
import { debounce, uniqBy } from "lodash";
import {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
} from "react";
import { Customer } from "types/customer";
import { QueryParams2 } from "types/query";

type CustomFormItemProps = {
  value?: number;
  initQuery?: QueryParams2;
  disabled?: boolean;
  selectedPartner?: Customer[];
  multiple?: boolean;
  onChange?: (id: number | any) => void;
  selectProps?: SelectProps;
  initOptionItem?: Customer | Customer[];
  valueIsOption?: boolean;
  allowClear?: boolean;
  placeholder?: string;
};

export interface CustomerSelector {
  refresh(): void;
}

/**
 * A demo that changes it's number value on click.
 * @param value initialValue passed by parent Form.Item.
 * @param onChange a callback for Form.Item to read its child component's value.
 * @constructor
 */
export const CustomerSelector = forwardRef(
  (
    {
      value,
      onChange,
      initQuery,
      disabled,
      multiple = false,
      selectedPartner,
      initOptionItem,
      valueIsOption,
      selectProps,
      allowClear = true,
      placeholder,
    }: CustomFormItemProps,
    ref
  ) => {
    const {
      customers,
      totalCustomer: total,
      loadingCustomer: loading,
      fetchCustomer: fetchData,
      queryCustomer: query,
    } = useHandleCustomer({
      initQuery: {
        page: 1,
        limit: 50,
        ...initQuery,
      },
    });

    useImperativeHandle<any, CustomerSelector>(
      ref,
      () => ({
        refresh() {
          fetchData();
        },
      }),
      []
    );

    useEffect(() => {
      fetchData();
    }, [selectedPartner]);

    const debounceSearch = useCallback(
      debounce((keyword) => {
        query.search = keyword;
        fetchData();
      }, 300),
      [query]
    );

    const options = useMemo(() => {
      let data = [...customers];
      if (initOptionItem) {
        if ((initOptionItem as Customer[])?.length) {
          data = data.concat(initOptionItem);
        } else {
          data.push(initOptionItem as Customer);
        }
      }

      return uniqBy(data, (data) => data.id);
    }, [customers, initOptionItem]);

    return (
      <Select<any, DefaultOptionType>
        mode={multiple ? "multiple" : undefined}
        disabled={disabled}
        loading={loading}
        style={{ width: "100%", minWidth: 250 }}
        allowClear={allowClear}
        onSearch={debounceSearch}
        showSearch
        size="large"
        placeholder={placeholder}
        filterOption={false}
        value={value}
        onChange={(v, opts) => {
          if (v === undefined || v.length == 0) {
            debounceSearch(v);
          }
          if (valueIsOption) {
            if (opts instanceof Array) {
              onChange?.(opts.map((v) => v.item));
            } else {
              onChange?.(opts?.item);
            }
          } else {
            onChange?.(v);
          }
        }}
        {...selectProps}
      >
        {options?.map((item) => (
          <Select.Option item={item} value={item.id} key={item.id}>
            <span className="text-blue-500 ">{item.phone}</span> - {item.email}{" "}
            - {item.name}
          </Select.Option>
        ))}
      </Select>
    );
  }
);
