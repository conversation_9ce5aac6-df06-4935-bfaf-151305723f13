import { UploadOutlined, ExpandOutlined, CompressOutlined } from "@ant-design/icons";
import { But<PERSON>, Spin } from "antd";
import { Sources } from "quill";
import React, { memo, useEffect, useMemo, useRef, useState, useCallback } from "react";
import ReactQuill from "react-quill";
import "react-quill/dist/quill.snow.css";
import { $url } from "utils/url";
import { UploadImageModal } from "./components/UploadImageModal";
import { useTranslation } from "react-i18next";
import "./styles/RichTextEditorV2.scss";

const modules = {
  toolbar: [
    [{ header: [1, 2, 3, 4, 5, 6, false] }],
    ["bold", "italic", "underline", "strike"], // toggled buttons
    ["blockquote", "code-block"],
    ["link", "formula"],
    [
      { list: "ordered" },
      { list: "bullet" },
      { list: "check" },
      { indent: "-1" },
      { indent: "+1" },
    ],
    [{ direction: "rtl" }],
    [{ script: "sub" }, { script: "super" }],
    [{ size: ["small", false, "large", "huge"] }],
    [{ font: [] }],
    [{ align: [] }],
    [{ color: [] }, { background: [] }],
    ["clean"],
  ],
};

const formats = [
  "header",
  "bold",
  "italic",
  "underline",
  "strike",
  "blockquote",
  "list",
  "bullet",
  "indent",
  "link",
  "image",
  "align",
  "size",
  "color",
  "direction",
  "font",
  "code-block",
  "script",
  "clean"
];

interface IRichTextEditorV2 extends Partial<ReactQuill> {
  onChange: (content: string) => void;
  content: string;
  label?: React.ReactNode;
  minHeight?: number;
  maxHeight?: number;
  hideUpload?: boolean;
  hideExpand?: boolean; // Thêm option để ẩn nút expand
}

export const RichTextEditorV2 = memo(function RichTextEditorV2({
  onChange,
  content = "",
  label,
  minHeight = 500,
  maxHeight = 500,
  hideUpload = false,
  hideExpand = false,
  ...reactQuillProps
}: IRichTextEditorV2) {
  const [visibleUploadModal, setVisibleUploadModal] = useState(false);
  const [cursorPosition, setCursorPosition] = useState(0);
  const [loadingRender, setLoadingRender] = useState(true);
  const [isExpanded, setIsExpanded] = useState(false);
  const { t } = useTranslation();

  const editorRef = useRef<ReactQuill>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const containers = document.getElementsByClassName(
      "ql-container"
    ) as HTMLCollectionOf<HTMLDivElement>;

    for (let i = 0; i < containers.length; i++) {
      const element = containers[i];
      if (!isExpanded) {
        // element.style.maxHeight = `${maxHeight}px`;
        // element.style.minHeight = `${minHeight}px`;
        element.style.overflowY = "hidden";
      }
    }

    setTimeout(() => {
      setLoadingRender(false);
    }, 100);
  }, [minHeight, maxHeight, isExpanded]);

  // Handle ESC key to exit fullscreen
  useEffect(() => {
    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isExpanded) {
        setIsExpanded(false);
      }
    };

    if (isExpanded) {
      document.addEventListener('keydown', handleEscKey);
      document.body.style.overflow = 'hidden'; // Prevent background scroll
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.removeEventListener('keydown', handleEscKey);
      document.body.style.overflow = 'unset';
    };
  }, [isExpanded]);

  const toggleExpand = useCallback(() => {
    setIsExpanded(!isExpanded);
  }, [isExpanded]);

  const onEditorChange = (content: string) => {
    onChange(content);
  };

  const reactQuillComponent = useMemo(
    () => (
      <ReactQuill
        theme="snow"
        className={`${isExpanded ? 'rich-text-editor__editor--expanded' : ''}`}
        value={content}
        onChange={onEditorChange}
        onChangeSelection={(
          selection: ReactQuill.Range,
          source: Sources,
          editor: ReactQuill.UnprivilegedEditor
        ) => {
          if (selection) {
            setCursorPosition(selection?.index || 0);
          }
        }}
        modules={modules}
        formats={formats}
        ref={editorRef}
        {...reactQuillProps}
      />
    ),
    [content, isExpanded, onEditorChange, reactQuillProps]
  );

  return (
    <div className="rich-text-editor">
      <div className={`rich-text-editor__container ${isExpanded ? 'rich-text-editor__container--expanded' : ''}`}>
        <div className="rich-text-editor__header">
          <div className="rich-text-editor__header-label">{label}</div>
          {!hideUpload && (
            <div className="rich-text-editor__header-actions">
              <Button
                icon={<UploadOutlined />}
                onClick={() => setVisibleUploadModal(true)}
                type="primary"
              >
                {t("uploadImg")}
              </Button>
            </div>
          )}
        </div>

        <div style={{ position: 'relative' }}>
          {!hideExpand && (
            <Button
              className="rich-text-editor__expand-btn"
              icon={isExpanded ? <CompressOutlined /> : <ExpandOutlined />}
              onClick={toggleExpand}
              type="text"
              size="small"
              title={isExpanded ? t("collapse") : t("expand")}
            />
          )}

          {loadingRender ? <Spin /> : reactQuillComponent}
        </div>
        {/* {isExpanded && (
          <div className="rich-text-editor__hint">
            Nhấn ESC để thoát chế độ toàn màn hình
          </div>
        )} */}
      </div>

      {visibleUploadModal && (
        <UploadImageModal
          onClose={() => setVisibleUploadModal(false)}
          onSubmitOk={(path) => {
            setVisibleUploadModal(false);
            editorRef.current?.editor?.insertEmbed(
              cursorPosition,
              "image",
              `${$url(path)}`
            );
          }}
          visible={visibleUploadModal}
        />
      )}
    </div>
  );
});

export default RichTextEditorV2;