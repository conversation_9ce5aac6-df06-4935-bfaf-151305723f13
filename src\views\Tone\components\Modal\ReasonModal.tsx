import { Col, Form, Input, message, Modal, Row } from "antd";
import { UploadFile } from "antd/lib";
import { useForm } from "antd/lib/form/Form";
import { toneApi } from "api/tone.api";
import { forwardRef, useImperativeHandle, useState } from "react";
import { useTranslation } from "react-i18next";
import { ModalStatus } from "types/modal";
import { Tone } from "types/tone";
import { requiredRule } from "utils/validate-rules";

export interface ToneModalProps {
  onClose: () => void;
  onSubmitOk: () => void;
}

export interface ToneModalRef {
  handleUpdate: (tone: Tone) => void;
  handleCreate: () => void;
}

export const ToneModal = forwardRef(
  ({ onSubmitOk }: ToneModalProps, ref) => {
    const [form] = useForm();
    const [loading, setLoading] = useState(false);
    const [visible, setVisible] = useState<boolean>();
    const { t } = useTranslation();
    const [fileList, setFileList] = useState<UploadFile[]>([]);
    const [selectedTone, setSelectedTone] = useState<Tone>();
    const [status, setStatus] = useState<ModalStatus>("create");

    useImperativeHandle(ref, () => ({
      handleUpdate,
      handleCreate,
    }));

    const handleUpdate = (tone: Tone) => {
      form.setFieldsValue({
        ...tone,
      });
      setSelectedTone(tone);
      setStatus("update");
      setVisible(true);
    };

    const handleCreate = () => {
      setStatus("create");
      setVisible(true);
    };

    const handleSubmitForm = async () => {
      await form.validateFields();
      const dataForm = form.getFieldsValue();
      if (dataForm.name) {
        dataForm.name = dataForm.name.trim();
      }
      const payload = {
        tone: {
          ...dataForm,
        },
      };

      try {
        setLoading(true);
        switch (status) {
          case "update":
            await toneApi.update(selectedTone?.id || 0, payload);
            message.success(t("actionSuccessfully"));
            break;
          default:
            await toneApi.create(payload);
            message.success(t("actionSuccessfully"));
            break;
        }
        setVisible(false);
        onSubmitOk();
        setFileList([]);
      } catch (error) {
      } finally {
        setLoading(false);
      }
    };

    return (
      <Modal
        onCancel={() => {
          setVisible(false);
          setFileList([]);
        }}
        visible={visible}
        centered
        title={
          <h1 className="mb-0 text-lg text-primary font-bold">
            {status === "create" ? t("create") : t("update")}
          </h1>
        }
        confirmLoading={loading}
        destroyOnClose
        width={800}
        onOk={handleSubmitForm}
        afterClose={() => {
          form.resetFields();
        }}
      >
        <Form
          form={form}
          layout="vertical"
          validateTrigger={["onBlur", "onChange"]}
        >
          <Row gutter={[12, 0]}>
            <Col span={24}>
              <Form.Item
                className="!mb-2"
                name="name"
                label={t("content")}
                rules={[requiredRule]}
              >
                <Input placeholder={t("enterName")} size="small" />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    );
  }
);
