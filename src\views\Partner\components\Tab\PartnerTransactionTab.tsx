// SurveyActivityLogTab.js
import {
  Button,
  Input,
  message,
  Popconfirm,
  Progress,
  Select,
  Space,
  Table,
  Tag,
} from "antd";
import { FormInstance } from "antd/es/form";
import Column from "antd/lib/table/Column";

import { unixToFullDate } from "utils/dateFormat";
import { useEffect, useRef, useState } from "react";
import { Pagination } from "components/Pagination";
import { useProject } from "hooks/useProject";
import { Project, ProjectStatus, ProjectStatusTrans } from "types/project";
import { useTranslation } from "react-i18next";
import { AriviTable } from "components/Table/AriviTable";
import { PaymentStatus, PaymentStatusTrans } from "types/payment";
import { useHandleCustomer } from "hooks/useHandleCustomer";
import {
  DownOutlined,
  LockOutlined,
  PlusOutlined,
  SearchOutlined,
  StopOutlined,
  UnlockOutlined,
} from "@ant-design/icons";
import { projectApi } from "api/project.api";
import { formatVND } from "utils";
import { useSearchParams } from "react-router-dom";
import {
  ProjectModal,
  ProjectModalRef,
} from "views/ProjectListPage/ProjectModal";
import DropdownCell from "components/Table/DropdownCell";
import { useCustomerTransaction } from "hooks/useCustomerTransaction";
import {
  CustomerTransaction,
  CustomerTransactionTypeTrans,
} from "types/customerTransaction";
import { usePartnerTransaction } from "hooks/usePartnerTransaction";
import { PartnerTransaction } from "types/partnerTransaction";

interface PropTypes {
  partnerId?: number;
}
const PartnerTransactionTab = ({ partnerId }: PropTypes) => {
  const {
    partnerTransactions,
    fetchData,
    loading,
    query,
    total,
    setQuery,
    //   f,
  } = usePartnerTransaction({
    initQuery: {
      page: 1,
      limit: 5,
      partnerId: partnerId ? partnerId : undefined,
    },
  });
  const { t } = useTranslation();

  useEffect(() => {
    fetchData();
  }, []);

  return (
    <div>
      <AriviTable
        pagination={false}
        rowKey="id"
        dataSource={partnerTransactions}
        scroll={{ x: "max-content" }}
        loading={loading}
      >
        {/* <Column
              width={50}
              title={t("stt")}
              dataIndex={"stt"}
              align="center"
              render={(_, __, index) => {
                if (queryProject.page == 1) {
                  return index + 1;
                }
                return (queryProject.page - 1) * 10 + index + 1;
              }}
            /> */}
        {/* <Column title={t("code")} dataIndex="code" key="code" /> */}
        <Column
          //   width={300}
          title={t("code")}
          dataIndex="code"
          key="code"
          render={(address, record: PartnerTransaction) => {
            return <>{record.code}</>;
          }}
        />
        {/* <Column
              width={150}
              title={t("customer")}
              dataIndex="customer"
              key="customer"
              render={(address, record: Project) => {
                return (
                  <div className="whitespace-pre-wrap">{record.customer?.name}</div>
                );
              }}
            /> */}
        {/* <Column
          width={250}
          title={t("type")}
          dataIndex="type"
          key="type"
          align="center"
          render={(type, record: PartnerTransaction) => {
            return (
              <div className="whitespace-pre-wrap">
                <Tag color={PartnerTransactionTypeTrans[record.type]?.color}>
                  {t(record.type)}
                </Tag>
              </div>
            );
          }}
        /> */}
        <Column
          title={t("content")}
          dataIndex="type"
          key="type"
          render={(type, record: PartnerTransaction) => {
            return t(record.type);
          }}
        />
        <Column
          align="right"
          width={150}
          title={t("beforeChange")}
          dataIndex="beforeChange"
          key={"beforeChange"}
          render={(text, record: PartnerTransaction) => (
            <span className="font-medium text-primary">
              {formatVND(record?.beforeChange || 0)}
            </span>
          )}
        />
        <Column
          align="right"
          width={150}
          title={t("moneyChange")}
          dataIndex="change"
          key={"change"}
          render={(text, record: PartnerTransaction) => {
            return (
              <span
                className={`font-medium ${
                  record.change >= 0 ? "text-green-500" : "text-red-500"
                }`}
              >
                {formatVND(record?.change || 0)}
              </span>
            );
          }}
        />

        <Column
          align="right"
          width={130}
          title={t("afterChange")}
          dataIndex="afterChange"
          key={"afterChange"}
          render={(text, record: PartnerTransaction) => (
            <span className="font-medium text-primary">
              {formatVND(record?.afterChange || 0)}
            </span>
          )}
        />
        <Column
          title={t("createdAt")}
          dataIndex="createdAt"
          key="createdAt"
          render={(createdAt) => {
            return unixToFullDate(createdAt);
          }}
        />
      </AriviTable>

      <Pagination
        defaultPageSize={query.limit}
        currentPage={query.page}
        total={total}
        onChange={({ limit, page }) => {
          query.page = page;
          query.limit = limit;
          setQuery({ ...query });
          fetchData();
        }}
      />
    </div>
  );
};

export default PartnerTransactionTab;
