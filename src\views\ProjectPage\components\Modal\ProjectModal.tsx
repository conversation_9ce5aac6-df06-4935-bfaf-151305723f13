import {
  Button,
  Col,
  Form,
  Input,
  message,
  Modal,
  Radio,
  Row,
  Select,
  Switch,
  Tag,
  Tooltip,
} from "antd";
import TextArea from "antd/es/input/TextArea";
import { Table, UploadFile } from "antd/lib";
import { useForm, useWatch } from "antd/lib/form/Form";
import { fileAttachApi } from "api/fileAttach.api";
import { projectApi } from "api/project.api";
import { InputNumber } from "components/Input/InputNumber";
import { useHandleCustomer } from "hooks/useHandleCustomer";
import { useProduct } from "hooks/useProduct";
import {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from "react";
import { useTranslation } from "react-i18next";
import { ModalStatus } from "types/modal";
import { Project } from "types/project";
import { requiredRule } from "utils/validate-rules";
import { GoogleLocationModal } from "./GoogleLocationModal";
import { InfoCircleOutlined } from "@ant-design/icons";
import { divide } from "lodash";
import { geminiApi } from "api/gemini.api";
import { ColumnsType } from "antd/es/table";
import { useReview } from "hooks/useReview";
import { Review } from "types/review";
import { useKeyword } from "hooks/useKeyword";
import { Keyword } from "types/keyword";
import { FileUploadMultiple } from "components/Upload/FileUploadMultiple";
import { $url } from "utils/url";
import { FileAttachPayload } from "components/Upload/FileUploadItem";
import { FileAttachType } from "types/fileAttach";
import { usePromotion } from "hooks/usePromotion";
// import "../../styles/googleLocationModal.scss";
export interface ProjectModalProps {
  onClose: () => void;
  onSubmitOk: () => void;
  // hasAddProjectPermission?: boolean;
  // hasUpdateProjectPermission?: boolean;
}
interface KeywordItem {
  name: string;
  position: number;
}

export interface ProjectModalRef {
  handleUpdate: (project: Project) => void;
  handleCreate: () => void;
}
const colors = [
  "#ff9f43",
  "#ff4757",
  "#1e90ff",
  "#2ed573",
  "#eccc68",
  "#8e44ad",
];

export const ProjectModal = forwardRef(
  (
    {
      onSubmitOk,
    }: // hasAddProjectPermission,
    // hasUpdateProjectPermission,
    ProjectModalProps,
    ref
  ) => {
    const {
      reviews: reviewData,
      fetchReview,
      loadingReview: loadingReviews,
      queryReview,
      totalReview,
    } = useReview({
      initQuery: {
        page: 1,
        limit: 100,
      },
    });
    const {
      keywords: keywordData,
      fetchKeyword,
      loadingKeyword: loadingKeywords,
      queryKeyword,
      totalKeyword,
    } = useKeyword({
      initQuery: {
        page: 1,
        limit: 50,
      },
    });
    const {
      customers,
      fetchCustomer,
      queryCustomer,
      loadingCustomer,
      debounceSearchCustomer,
    } = useHandleCustomer({
      initQuery: {
        page: 1,
        limit: 50,
      },
    });
    const {
      products,
      fetchProduct,
      queryProduct,
      loadingProduct,
      debounceSearchProduct,
    } = useProduct({
      initQuery: {
        page: 1,
        limit: 50,
      },
    });
    const { promotions, fetchPromotion, queryPromotion, loadingPromotion } =
      usePromotion({
        initQuery: {
          page: 1,
          limit: 50,
        },
      });

    const [form] = useForm();
    const [loading, setLoading] = useState(false);
    const [loadingKeyword, setLoadingKeyword] = useState(false);
    const [loadingReview, setLoadingReview] = useState(false);
    const [reviews, setReviews] = useState<Partial<Review>[]>([]);
    const [visible, setVisible] = useState<boolean>();
    const { t } = useTranslation();
    const [fileList, setFileList] = useState<UploadFile[]>([]);
    const [selectedProject, setSelectedProject] = useState<Project>();
    const [status, setStatus] = useState<ModalStatus>("create");
    const [addressVisible, setAddressVisible] = useState(false);

    const address = Form.useWatch("address", form);
    const description = Form.useWatch("description", form);
    const productId = Form.useWatch("productId", form);
    const quantity = Form.useWatch("quantity", form);
    const lat = Form.useWatch("lat", form);
    const long = Form.useWatch("long", form);
    const keywords = useWatch("keywords", form) || [];
    const useImage = useWatch("useImage", form);

    const tagColors = useRef<Record<string, string>>({});
    keywords.forEach((tag: string) => {
      if (!tagColors.current[tag]) {
        tagColors.current[tag] =
          colors[Math.floor(Math.random() * colors.length)];
      }
    });

    useEffect(() => {
      fetchCustomer();
      fetchProduct();
      fetchPromotion();
    }, []);
    useImperativeHandle(
      ref,
      () => ({
        handleUpdate,
        handleCreate,
      }),
      []
    );

    const generateKeywords = async () => {
      try {
        setLoadingKeyword(true);
        if (!description) {
          message.error(t("plsFillDes"));
          return;
        }
        const { data } = await geminiApi.getKeywords({
          description,
        });
        const keywords = data.map((item: KeywordItem) => item.name);
        form.setFieldsValue({ keywords });
        // console.log(data);
      } catch (error) {
        console.log(error);
      } finally {
        setLoadingKeyword(false);
      }
    };

    const generateReviews = async () => {
      try {
        setLoadingReview(true);
        const findProduct = products.find(
          (product) => product.id === productId
        );
        if (!description) {
          message.error(t("plsFillDes"));
          return;
        }
        if (!keywords) {
          message.error(t("plsFillKeyword"));
          return;
        }
        if (!findProduct) {
          message.error(t("plsSelectProduct"));
          return;
        }
        if (!quantity) {
          message.error(t("plsFillQuantity"));
          return;
        }
        const { data } = await geminiApi.getReviews({
          description,
          keywords,
          maxContent: findProduct?.maxLengthCharReview,
          numberReviews: +quantity,
        });
        setReviews(data);

        // const keywords = data.map((item: KeywordItem) => item.keyword);
        // form.setFieldsValue({ keywords });
        // console.log(data);
      } catch (error) {
        console.log(error);
      } finally {
        setLoadingReview(false);
      }
    };

    const handleConfirmChange = (
      field: "productId" | "quantity",
      newValue: any
    ) => {
      const prevValue = form.getFieldValue(field);
      Modal.confirm({
        title: t("confirmChange"),
        content: t("reviewLost"),
        okText: t("save"),
        cancelText: t("close"),
        onOk: () => {
          form.setFieldsValue({ [field]: newValue });
          setReviews([]);
        },
        onCancel: () => {
          form.setFieldsValue({ [field]: prevValue });
        },
      });
    };

    const getDataReview = async () => {
      try {
        setLoadingReview(true);
        const data = await fetchReview();
        setReviews(data);

        console.log(data);
      } catch (error) {
      } finally {
        setLoadingReview(false);
      }
    };

    const getDataKeyword = async () => {
      try {
        setLoadingKeyword(true);
        const data = await fetchKeyword();
        const keywords = data.map((item: Keyword) => item.name);
        form.setFieldsValue({ keywords });
        console.log(data);
      } catch (error) {
      } finally {
        setLoadingKeyword(false);
      }
    };

    const handleUpdate = (project: Project) => {
      form.setFieldsValue({
        ...project,
        customerId: project.customer?.id,
        productId: project.product?.id,
        promotionId: project.promotion?.id,
      });
      queryReview.projectId = project.id;
      queryKeyword.projectId = project.id;

      getDataReview();
      getDataKeyword();
      // setReviews(data.)
      if (project.fileAttaches.length > 0) {
        form.setFieldValue("useImage", true);
      } else {
        form.setFieldValue("useImage", false);
      }

      const filePayloads = project?.fileAttaches?.map((item) => {
        return {
          url: item.url,
          name: item.name,
          size: item?.size,
          // uid: item?.uid,
          type: item.type,
          path: item.path,
          id: item.id,
          // destination: item.response?.data?.destination,
        };
      });

      console.log(filePayloads);
      setFileList(project?.fileAttaches as any);
      form.setFieldsValue({
        fileAttachList: filePayloads,
      });
      setSelectedProject(project);
      setStatus("update");
      setVisible(true);
    };

    const handleCreate = () => {
      setStatus("create");
      setVisible(true);
    };

    const getFileData = async () => {
      try {
        const uploadPromises = fileList.map((file) =>
          fileAttachApi.create({
            fileAttach: {
              name: file.name,
              type: file.type,
              url: file.response?.data?.path, // Nếu có URL, cập nhật tại đây
              path: file.response?.data?.path,
              size: file.size,
              desc: "",
            },
          })
        );

        const responses = await Promise.all(uploadPromises);
        console.log("Uploaded files:", responses);

        return responses.map((res) => res.data); // Trả về danh sách file đã upload
      } catch (error) {
        console.error("Error uploading files:", error);
      }
    };

    const handleSubmitForm = async () => {
      await form.validateFields();
      console.log(fileList);
      // const fileData = await getFileData();
      debugger;
      const dataForm = form.getFieldsValue();
      let fileAttachIds: number[] = [];
      let fileAttachList = dataForm.fileAttachList;

      if (fileAttachList && fileAttachList.length > 0) {
        const newFiles = fileAttachList.filter((file: any) => !file.id);
        fileAttachIds = fileAttachList
          .filter((file: any) => file.id)
          .map((file: any) => file.id);

        if (newFiles.length > 0) {
          const results = await Promise.allSettled(
            newFiles.map((file: FileAttachPayload) =>
              fileAttachApi.create({
                fileAttach: {
                  name: file.name,
                  type: file.type.includes("image")
                    ? FileAttachType.Image
                    : file.type.includes("pdf")
                    ? FileAttachType.Pdf
                    : FileAttachType.Other,
                  url: file.url,
                  path: file.path,
                  size: file.size,
                },
              })
            )
          );

          fileAttachIds.push(
            //@ts-ignore

            ...results.map((result) => result.value?.data?.id)
          );
        }
      }
      if (dataForm.name) {
        dataForm.name = dataForm.name.trim();
      }
      console.log(keywords);
      const payload = {
        project: {
          ...dataForm,
        },
        productId: dataForm.productId,
        customerId: dataForm.customerId,
        fileAttachIds,
        keywords: keywords.map((item: string, index: number) => ({
          name: item,
          position: index,
        })),
        reviews,
        promotionId: dataForm.promotionId,
      };

      try {
        setLoading(true);
        switch (status) {
          case "update":
            await projectApi.update(selectedProject?.id || 0, payload);
            message.success(t("actionSuccessfully"));
            break;

          //create
          default:
            await projectApi.create(payload);
            message.success(t("actionSuccessfully"));
            break;
        }
        setVisible(false);
        onSubmitOk();
        setFileList([]);
        setReviews([]);
      } catch (error) {
      } finally {
        setLoading(false);
        // onSubmitOk();
      }
    };
    console.log(reviews);

    const reviewColumns: ColumnsType<Partial<Review>> = useMemo(() => {
      return [
        {
          title: "Review",
          dataIndex: "review",
          key: "review",
          render: (_, record, index) => (
            <Input
              value={record.content}
              onChange={(event) => {
                const newValue = event.target.value; // Lấy giá trị nhập vào
                reviews[index].content = newValue || undefined;
                setReviews([...reviews]);
              }}
            />
          ),
        },
      ];
    }, [reviews]);

    return (
      <Modal
        onCancel={() => {
          setVisible(false);
          setFileList([]);
          setReviews([]);
        }}
        visible={visible}
        centered
        title={
          <h1 className="mb-0 text-lg text-primary font-bold">
            {status == "create" ? t("create") : t("update")}
          </h1>
        }
        confirmLoading={loading}
        destroyOnClose
        width={"95vw"}
        onOk={handleSubmitForm}
        afterClose={() => {
          form.resetFields();
        }}
        okButtonProps={{
          className: status === "update" ? "hidden" : "",
        }}
        cancelText={t("close")}
        okText={t("save")}
        // height={500}
        // okButtonProps={{
        //   hidden:
        //     (!hasAddProjectPermission && status == "create") ||
        //     (!hasUpdateProjectPermission && status == "update"),
        // }}
      >
        <Form
          form={form}
          layout="vertical"
          disabled={status === "update"}
          initialValues={{ isDropSlow: false, useImage: false }}
          validateTrigger={["onBlur", "onChange"]}
        >
          <div className="flex gap-4 flex-col md:flex-row">
            <Row gutter={[12, 0]} className="flex-1">
              <Col span={12}>
                <Form.Item
                  className="!mb-2"
                  name="code"
                  label={t("code")}
                  rules={[requiredRule]}
                >
                  <Input placeholder={t("enterCode")} size="small" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  className="!mb-2"
                  name="name"
                  label={t("projectName")}
                  rules={[requiredRule]}
                >
                  <Input />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  className="!mb-2"
                  name="customerId"
                  label={t("customer")}
                  rules={[
                    {
                      required: true,
                    },
                  ]}
                >
                  <Select
                    showSearch
                    size="large"
                    onSearch={(value) => debounceSearchCustomer(value)}
                    allowClear
                    options={customers.map((item) => {
                      return {
                        label: (
                          <div>
                            <span className="">{item.name}</span>
                          </div>
                        ),
                        value: item.id,
                      };
                    })}
                    onClear={() => {
                      queryCustomer.search = "";
                      fetchCustomer();
                    }}
                    loading={loadingCustomer}
                    filterOption={false}
                    placeholder={t("selectCustomer")}
                  ></Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  className="!mb-2"
                  name="productId"
                  label={t("product")}
                  rules={[
                    {
                      required: true,
                    },
                  ]}
                  noStyle={true}
                ></Form.Item>
                <div className="font-bold text-md mb-2">
                  {t("product")} <span className="font-bold">*</span>
                </div>
                <Select
                  showSearch
                  size="large"
                  className="w-full"
                  onSearch={(value) => debounceSearchProduct(value)}
                  value={productId}
                  onChange={(value) => {
                    if (reviews.length > 0) {
                      handleConfirmChange("productId", value);
                    } else {
                      form.setFieldValue("productId", value);
                    }
                  }}
                  options={products.map((item) => {
                    return {
                      label: (
                        <div>
                          <span className="">{item.name}</span>
                        </div>
                      ),
                      value: item.id,
                    };
                  })}
                  onClear={() => {
                    queryProduct.search = "";
                    fetchProduct();
                  }}
                  loading={loadingProduct}
                  filterOption={false}
                  placeholder={t("selectProduct")}
                ></Select>
              </Col>
              <Col span={12}>
                <Form.Item
                  className="!mb-2"
                  name="quantity"
                  label={t("reviewQuantity")}
                  rules={[requiredRule]}
                  noStyle
                ></Form.Item>
                <div className="font-bold text-md mb-2">
                  {t("reviewQuantity")} <span className="font-bold">*</span>
                </div>
                <InputNumber
                  className="mb-2"
                  size="large"
                  value={quantity}
                  onChange={(e) => {
                    console.log(e);
                    if (reviews.length > 0) {
                      handleConfirmChange("quantity", e.target.value);
                    } else {
                      form.setFieldValue("quantity", e.target.value);
                    }
                  }}
                />
              </Col>
              <Col span={12}>
                <Form.Item
                  className="!mb-2"
                  name="promotionId"
                  label={t("promotion")}
                  rules={[
                    {
                      required: true,
                    },
                  ]}
                >
                  <Select
                    // showSearch
                    className="w-full"
                    size="large"
                    // onSearch={(value) => debounceSearchProduct(value)}
                    // value={productId}
                    // onChange={(value) => {
                    //   if (reviews.length > 0) {
                    //     handleConfirmChange("productId", value);
                    //   } else {
                    //     form.setFieldValue("productId", value);
                    //   }
                    // }}
                    options={promotions.map((item) => {
                      return {
                        label: (
                          <div>
                            <span className="">{`${item.code} - ${item.title}`}</span>
                          </div>
                        ),
                        value: item.id,
                      };
                    })}
                    // onClear={() => {
                    //   queryProduct.search = "";
                    //   fetchProduct();
                    // }}
                    loading={loadingProduct}
                    // filterOption={false}
                    placeholder={t("selectPromotion")}
                  ></Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  className="!mb-2"
                  name="isDropSlow"
                  valuePropName="checked"
                  label={t("dropSlow")}
                  rules={[requiredRule]}
                >
                  <Switch
                    checkedChildren={t("on")}
                    unCheckedChildren={t("off")}
                  />
                </Form.Item>
              </Col>
              <Col span={24}>
                <Form.Item
                  className="!mb-2"
                  name="mapUrl"
                  label={t("mapUrl")}
                  rules={[requiredRule]}
                >
                  <Input />
                </Form.Item>
              </Col>
              <Col span={24}>
                <Form.Item hidden name={"lat"}>
                  <Input />
                </Form.Item>
                <Form.Item hidden name={"placeId"}>
                  <Input />
                </Form.Item>
                <Form.Item hidden name={"long"}>
                  <Input />
                </Form.Item>
                <Form.Item shouldUpdate noStyle>
                  {({ getFieldValue }) => (
                    <Form.Item
                      label={t("position")}
                      className="custom-link"
                      required
                      // rules={[requiredRule]}
                      name={"address"}
                    >
                      <Input
                        size="middle"
                        className=" leading-none custom-input"
                        suffix={
                          getFieldValue("lat") && (
                            <img
                              width={20}
                              className="object-cover leading-none"
                              height={20}
                              src={`/icon/${
                                getFieldValue("lat")
                                  ? "success.png"
                                  : "error.png"
                              }`}
                            />
                          )
                        }
                        // status={getFieldValue("lat") ? undefined : "error"}
                        value={
                          getFieldValue("lat")
                            ? t("positionChoosen")
                            : t("positionUnChoose")
                        }
                        readOnly
                        style={{ height: "100%" }}
                        addonAfter={
                          <div
                            className="h-full px-4 text-primary font-bold cursor-pointer"
                            // type="primary"
                            onClick={() => setAddressVisible(true)}
                          >
                            {/* {merchant?.address ? "Update" : "Chọn vị trí"} */}
                            {t("select")}
                          </div>
                        }
                      />
                    </Form.Item>
                  )}
                </Form.Item>
                {/* <AddressSelect
                // ref={addressSelectRef}
                // form={form}
                ref={addressSelectRef}
                defaultValue={defaultAddress}
                onChange={function (data: any): void {}}
              ></AddressSelect> */}
              </Col>
              <Col span={24}>
                <Form.Item
                  className="!mb-2"
                  name="description"
                  label={t("description")}
                  rules={[requiredRule]}
                >
                  <TextArea />
                </Form.Item>
              </Col>
              <Col span={24}>
                <Form.Item
                  label={
                    <div className="flex gap-4 items-center">
                      <span>
                        <Tooltip title={t("keywordPlaceholder")}>
                          <InfoCircleOutlined style={{ marginRight: 5 }} />
                        </Tooltip>
                        {t("keyword")}
                      </span>
                      <div>
                        <Button
                          type="primary"
                          loading={loadingKeyword}
                          onClick={() => {
                            generateKeywords();
                          }}
                        >
                          {t("genKeywords")}
                        </Button>
                      </div>
                    </div>
                  }
                  name="keywords"
                  // rules={[{ required: true, message: "Vui lòng nhập từ khóa!" }]}
                >
                  <Select
                    mode="tags"
                    style={{ width: "100%" }}
                    placeholder={t("keywordPlaceholder")}
                    tokenSeparators={[","]}
                    tagRender={(props) => {
                      const { label, value, closable, onClose } = props;
                      // console.log(value);
                      // console.log(label);
                      return (
                        <Tag
                          color={tagColors.current[value]}
                          closable={closable}
                          onClose={onClose}
                          style={{ margin: 2, fontSize: 14 }}
                        >
                          {label}
                        </Tag>
                      );
                    }}
                  />
                </Form.Item>
              </Col>
              <Col span={24}>
                <Form.Item
                  name="useImage"
                  label={t("useImage")}
                  rules={[
                    { required: true, message: "Vui lòng chọn một tùy chọn!" },
                  ]}
                >
                  <Radio.Group>
                    <Radio value={true}>{t("on")}</Radio>
                    <Radio value={false}>{t("off")}</Radio>
                  </Radio.Group>
                </Form.Item>
              </Col>
              {useImage && (
                <Col span={24}>
                  <Form.Item shouldUpdate={true} style={{ marginBottom: 0 }}>
                    {() => {
                      return (
                        <Form.Item
                          label={t("uploadAttachments")}
                          style={{ marginBottom: 0 }}
                          name="fileAttachList"
                        >
                          <FileUploadMultiple
                            maxFileSize={1024 * 1024 * 10}
                            fileList={fileList}
                            onUploadOk={(fileList) => {
                              console.log(fileList);

                              const filePayloads = fileList.map((item) =>
                                item.id
                                  ? item // Nếu có id, giữ nguyên object
                                  : {
                                      url:
                                        item.url ||
                                        $url(item.response?.data?.path),
                                      name: item.name,
                                      size: item?.size,
                                      uid: item?.uid,
                                      type: item.type,
                                      path: item.response?.data?.path,
                                      destination:
                                        item.response?.data?.destination,
                                    }
                              );

                              console.log(filePayloads);
                              setFileList(fileList);
                              form.setFieldsValue({
                                fileAttachList: filePayloads,
                              });
                            }}
                            onDelete={setFileList}
                          />
                        </Form.Item>
                      );
                    }}
                  </Form.Item>
                </Col>
              )}
            </Row>
            <div className="flex-1">
              <Button
                type="primary"
                onClick={() => {
                  generateReviews();
                }}
                loading={loadingReview}
              >
                {t("genReviews")}
              </Button>
              <Table
                size="small"
                scroll={{ x: "max-content", y: 500 }}
                columns={reviewColumns}
                dataSource={reviews}
                pagination={false}
                className="w-full my-4"
              />
            </div>
          </div>
        </Form>
        {addressVisible && (
          <GoogleLocationModal
            coord={{ address, lat, lng: long }}
            // merchantLocation={merchantLocation}
            visible={addressVisible}
            onClose={() => {
              setAddressVisible(false);
              // setMerchantLocation(undefined);
            }}
            onSubmitOk={(addressInfo) => {
              // addressSelectRef.current?.updateSelectMatchWithAddress(
              //   addressInfo
              // );
              form.setFieldsValue({
                address: addressInfo.address,
                lat: addressInfo.lat,
                long: addressInfo.lng,
                placeId: addressInfo.placeId,
              });
            }}
          />
        )}
      </Modal>
    );
  }
);
