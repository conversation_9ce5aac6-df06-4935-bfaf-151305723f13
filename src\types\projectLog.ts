import { Partner } from "./partner";
import { Project } from "./project";
import { Review } from "./review";

export enum ProjectLogType {
  DropReview = "DROP_REVIEW",
  RejectReview = "REJECT_REVIEW",
  CompleteReview = "COMPLETE_REVIEW",
  Project = "PROJECT",
  AdminPendingReview = "ADMIN_PENDING_REVIEW",
}

export interface ProjectLog {
  id: number;
  createdAt: number;
  updatedAt: number;
  isDeleted: boolean;
  type: ProjectLogType;
  description: string;
  placeId: string;
  project: Project | null;
  review: Review | null;
  partner: Partner | null;
  customer?: any;
  staff?: string;
}
