import { Company } from "./company";
import { Customer } from "./customer";
import { CustomerRank } from "./customer-rank";
import { Employee } from "./employee";
import { OnlinePayment } from "./online-payment";
import { Product } from "./product";
import { Staff } from "./staff";

export enum OrderCancelBy {
  Customer = "CUSTOMER",
  Staff = "STAFF",
  Employee = "EMPLOYEE",
}
export interface Order {
  id: number;
  createdAt: number;
  updatedAt: number;
  isDeleted: boolean;
  isPrivate: boolean;
  deletedAt: number;
  code: string;
  paymentMethod: PaymentMethod;
  status: OrderStatus;
  moneyFinal: number; //Tiền khách cần trả
  totalMoney: number; //tổng tiền
  moneyProduct: number;
  subMoneyProduct: number;
  cancelBy: OrderCancelBy;
  completedAt: number;
  startAt: number;
  expiredAt: number;
  expiresInDay: number;
  reason: string;
  privateNote: string;
  //custom
  inspecStaff: Staff;
  customer: Customer;
  employee: Employee; //ghi nhận broker tại thời điểm mua gói
  inspecEmployee: Employee;
  onlinePayment: OnlinePayment;
  customerRank: CustomerRank; //rank hiện tại
  subCustomerRank: CustomerRank; //rank hiện tại
  nextCustomerRank: CustomerRank; //rank kế tiếp
  subNextCustomerRank: CustomerRank; //rank kế tiếp (dành riêng cho V1 CS, PS)
  product: Product;
  subProduct: Product;
  company: Company;
  nextRefEmployee: Employee; //yêu cầu đổi broker khi nâng cấp độ
  nav: string;
  accountNumber: string;
  transferContent: string; //nội dung chuyển tiền
  // METHOD
}

export enum PaymentMethod {
  COD = "COD", //Thanh toán khi nhận hàng
  BankTransfer = "BANK_TRANSFER", //Chuyển khoản ngân hàng
}

export const PaymentMethodTrans = {
  [PaymentMethod.COD]: "Thanh toán khi nhận hàng",
  [PaymentMethod.BankTransfer]: "Chuyển khoản ngân hàng",
};

export enum OrderStatus {
  All = "ALL",
  Pending = "PENDING", //Chờ xác nhận
  Complete = "COMPLETE", //đã hoàn tất
  Cancel = "CANCEL", //hủy
}

export const OrderStatusTrans = {
  [OrderStatus.All]: {
    title: "Tất cả",
    color: "purple",
    value: OrderStatus.All,
  },
  [OrderStatus.Pending]: {
    title: "Chờ xác nhận",
    color: "blue",
    value: OrderStatus.Pending,
  },
  [OrderStatus.Complete]: {
    title: "Đã hoàn tất",
    color: "green",
    value: OrderStatus.Complete,
  },
  [OrderStatus.Cancel]: {
    title: "Đã Huỷ",
    color: "red",
    value: OrderStatus.Cancel,
  },
};

export const OrderStatuses = [
  {
    label: OrderStatusTrans[OrderStatus.Complete],
    value: OrderStatus.Complete,
  },
  {
    label: OrderStatusTrans[OrderStatus.Cancel],
    value: OrderStatus.Cancel,
  },
];

export interface OrderDetail {
  id: number;
  createdAt: number;
  updatedAt: number;
  isDeleted: boolean;
  price: number;
  finalPrice: number;
  quantity: number;
  product: Product;
  order: Order;
}
