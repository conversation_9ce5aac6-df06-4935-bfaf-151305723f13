import { formatVND } from "utils";
import { Customer, CustomerStatus, CustomerStatusTrans } from "types/customer";
import { MyExcelColumn } from "utils/MyExcel";
import { unixToFullDate, unixToDate } from "utils/dateFormat";
import { handleCountRemainDays } from "../Table/CustomerTable";

export const exportCustomerColumns: MyExcelColumn<Customer>[] = [
  {
    width: 15,
    header: "Mã KH",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "code",
    render: (record: Customer) => {
      return record.code || "Chưa cập nhật";
    },
  },

  {
    width: 30,
    header: "Tên KH",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "name",
    style: { font: { color: { argb: "004e47cc" } } },
    render: (record: Customer) => {
      return record.name || "Chưa cập nhật";
    },
  },
  {
    width: 20,
    header: "<PERSON><PERSON> điện thoại",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "phone",
    render: (record: Customer) => {
      return record.phone || "Chưa cập nhật";
    },
  },

  {
    width: 20,
    header: "Email",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "email",
    render: (record: Customer) => {
      return record.email || "Chưa cập nhật";
    },
  },
  {
    width: 30,
    header: "Số dư ví (đ)",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "balance",
    render: (record: Customer) => {
      return formatVND(record.balance);
    },
  },
  {
    width: 30,
    header: "Trạng thái",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "status",
    render: (record: Customer) => {
      return CustomerStatusTrans[record.status]?.label;
    },
  },
  {
    width: 20,
    header: "Ngày đăng ký",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "createdAt",
    render: (record: Customer) => {
      return unixToFullDate(record.createdAt);
    },
  },
];
