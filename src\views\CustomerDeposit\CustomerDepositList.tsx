import {
  DownOutlined,
  EditOutlined,
  LockOutlined,
  RetweetOutlined,
  UnlockOutlined,
} from "@ant-design/icons";
import { Button, message, Popconfirm, Space, Table, Tag } from "antd";
import TextArea from "antd/es/input/TextArea";
import Column from "antd/lib/table/Column";
import { customerDepositApi } from "api/customerDeposit.api";
import { IPagination, Pagination } from "components/Pagination";
import { AriviTable } from "components/Table/AriviTable";
import DropdownCell from "components/Table/DropdownCell";
import { useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { GrEdit } from "react-icons/gr";
import { HiOutlineTrash } from "react-icons/hi2";
import {
  CustomerDeposit,
  CustomerDepositStatus,
  CustomerDepositStatusTrans,
} from "types/customerDeposit";
import { PaymentType } from "types/payment";
import { formatVND } from "utils";
import { unixToFullDate } from "utils/dateFormat";

interface PropsType {
  dataSource: CustomerDeposit[];
  loading: boolean;
  loadingDelete?: boolean;
  pagination?: IPagination;
  onEdit?: (customerDeposit: CustomerDeposit) => void;
  onDelete?: (customerDepositId: number) => void;
  onActive?: (customerDepositId: number) => void;
  onInactive?: (customerDepositId: number) => void;
  onRefreshData: () => void;
  // hasDeleteCustomerDepositPermission?: boolean;
  // hasUpdateCustomerDepositPermission?: boolean;
}

export const CustomerDepositList = ({
  dataSource,
  loading,
  loadingDelete,
  pagination,
  onDelete,
  onEdit,
  onActive,
  onInactive,
  onRefreshData,
}: // hasDeleteCustomerDepositPermission,
// hasUpdateCustomerDepositPermission,

PropsType) => {
  const [loadingStatus, setLoadingStatus] = useState(false);
  const noteRef = useRef<string>("");
  const { t } = useTranslation();

  const handleApprove = async (id: number, note: string) => {
    try {
      setLoadingStatus(true);
      console.log({ note });
      const { data } = await customerDepositApi.approve(id, { note });
      message.success(t("actionSuccessfully"));
      noteRef.current = "";
      onRefreshData();
    } catch (error) {
      console.log(error);
    } finally {
      setLoadingStatus(false);
    }
  };

  const handleReject = async (id: number, note: string) => {
    try {
      setLoadingStatus(true);
      const { data } = await customerDepositApi.reject(id, { note });
      message.success(t("actionSuccessfully"));
      noteRef.current = "";

      onRefreshData();
    } catch (error) {
      console.log(error);
    } finally {
      setLoadingStatus(false);
    }
  };

  return (
    <div>
      <AriviTable
        // bordered

        loading={loading}
        pagination={false}
        rowKey="id"
        dataSource={dataSource}
        className="custom-scrollbar"
        scroll={{ x: "max-content", y: "calc(100vh - 400px)" }}

        // onChange={}
      >
        <Column
          title={`${t("code")}`}
          dataIndex="code"
          key="code"
          render={(text, record: CustomerDeposit) => (
            <>
              <div>{record.code}</div>
            </>
          )}
        />
        <Column
          title={`${t("customer")}`}
          dataIndex="customer"
          key="customer"
          render={(text, record: CustomerDeposit) => (
            <>
              <div>
                {t("customerName")}: {record.customer?.name}
              </div>
              <div>Email: {record.customer?.email}</div>
            </>
          )}
        />
        <Column
          align="right"
          width={160}
          title={`${t("amountRequest")}`}
          dataIndex="amountRequest"
          key="amountRequest"
          render={(text, record: CustomerDeposit) => (
            <>
              <div>{formatVND(record.amount)}</div>
            </>
          )}
        />
        <Column
          title={`${t("paymentTypeTable")}`}
          dataIndex="paymentType"
          key="paymentType"
          render={(text, record: CustomerDeposit) => (
            <>
              <div>{t(record.paymentType)}</div>
            </>
          )}
        />
        {/* <Column
          title={`${t("bankInfo")}`}
          dataIndex="bankInfo"
          key="bankInfo"
          render={(text, record: CustomerDeposit) => (
            <>
              {record.bankName ? (
                <div>
                  <div>{record?.bankName}</div>
                  <div>
                    {t("stk")}: {record?.bankNumber}
                  </div>
                </div>
              ) : (
                <div></div>
              )}
            </>
          )}
        /> */}
        <Column
          title={`${t("note")}`}
          dataIndex="note"
          width={200}
          key="note"
          render={(text, record: CustomerDeposit) => (
            <>
              <div>{record.note}</div>
            </>
          )}
        />
        <Column
          title={`${t("inspector")}`}
          dataIndex="inspector"
          width={150}
          key="inspector"
          render={(text, record: CustomerDeposit) => (
            <>
              <div>
                {record.status === CustomerDepositStatus.Pending
                  ? ""
                  : record?.inspecStaff
                  ? record?.inspecStaff.username +
                    " - " +
                    record?.inspecStaff.fullName
                  : t("depositSYSTEM_REVIEW")}
              </div>
            </>
          )}
        />
        <Column
          title={`${t("inspectAt")}`}
          dataIndex="inspectAt"
          width={250}
          key="inspectAt"
          render={(text, record: CustomerDeposit) => (
            <>
              <div>{unixToFullDate(record.inspecAt)}</div>
            </>
          )}
        />
        <Column
          width={100}
          title={t("status")}
          dataIndex="name"
          align="center"
          key={"name"}
          render={(text, record: CustomerDeposit) => (
            <Tag color={CustomerDepositStatusTrans[record.status]?.color}>
              {t(record.status)}
            </Tag>
          )}
        />

        <Column
          title={`${t("inspectNote")}`}
          dataIndex="inspectNote"
          width={150}
          key="inspectNote"
          render={(text, record: CustomerDeposit) => (
            <>
              <div>{record.inspecNote}</div>
            </>
          )}
        />
        <Column
          fixed="right"
          width={120}
          align="center"
          title=""
          key="action"
          dataIndex={""}
          render={(text, record: CustomerDeposit) => (
            <>
              {record.status === CustomerDepositStatus.Pending &&
                (record.paymentType === PaymentType.Manual ||
                  record.paymentType === PaymentType.Bank) && (
                  //@ts-ignore
                  <DropdownCell
                    text={t("action")}
                    items={[
                      {
                        onClick: () => "",
                        // createOrderModalRef.current?.handleUpdate(record),
                        label: (
                          <Popconfirm
                            placement="topLeft"
                            title={
                              <>
                                <h3>{t("approveRequest")}</h3>
                                <TextArea
                                  onChange={(e) => {
                                    noteRef.current = e.target.value;
                                  }}
                                  placeholder={t("enterNote")}
                                />
                              </>
                            }
                            onConfirm={() =>
                              handleApprove(record.id, noteRef.current)
                            }
                            okText={t("save")}
                            cancelText={t("close")}
                          >
                            <Button type="primary">{t("approve")}</Button>
                          </Popconfirm>
                        ),
                        key: "approve",
                        hidden: record.status !== CustomerDepositStatus.Pending,
                      },
                      {
                        onClick: () => "",
                        // createOrderModalRef.current?.handleUpdate(record),
                        label: (
                          <Popconfirm
                            className="w-full "
                            placement="topLeft"
                            title={
                              <>
                                <h3>{t("rejectRequest")}</h3>
                                <TextArea
                                  onChange={(e) => {
                                    noteRef.current = e.target.value;
                                  }}
                                  placeholder={t("enterNote")}
                                />
                              </>
                            }
                            onConfirm={() =>
                              handleReject(record.id, noteRef.current)
                            }
                            okText={t("save")}
                            cancelText={t("close")}
                          >
                            <Button danger>{t("reject")}</Button>
                          </Popconfirm>
                        ),
                        key: "reject",
                        hidden: record.status !== CustomerDepositStatus.Pending,
                      },
                    ]}
                    trigger={["click"]}
                  >
                    <a onClick={(e) => e.preventDefault()}>
                      <Space>
                        {t("action")}
                        <DownOutlined />
                      </Space>
                    </a>
                  </DropdownCell>
                )}
            </>
          )}
        />
      </AriviTable>
      {pagination && <Pagination {...pagination} />}
    </div>
  );
};
