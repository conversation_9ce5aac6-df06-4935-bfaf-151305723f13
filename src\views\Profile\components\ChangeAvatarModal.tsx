import {
  Button,
  Col,
  Form,
  Input,
  message,
  Modal,
  Row,
  Typography,
  Upload,
} from "antd";
import { Rule } from "antd/lib/form";
import { fileAttachApi } from "api/fileAttach.api";
import { SingleImageUpload } from "components/Upload/SingleImageUpload";
import React, { useEffect, useImperativeHandle, useState } from "react";
import { ModalStatus } from "types/modal";
import { FileAttach } from "types/fileAttach";
import uploadImage from "assets/images/uploadAvatarImage.svg";
import { $url } from "utils/url";
import { PreviewImage } from "components/PreviewImage/PreviewImage";
import { authApi } from "api/auth.api";
import { set } from "lodash";
import { userInfo } from "os";
import { userStore } from "store/userStore";
import { useTranslation } from "react-i18next";
import closeIcon from "../../../assets/svgs/icon-close.svg";
import "./changeAvatarModal.scss";
const rules: Rule[] = [{ required: true }];

export interface ChangeAvatarModalRef {
  handleCreate: () => void;
  handleUpdate: (image: any) => void;
}
interface FileAttachModalProps {
  onClose: () => void;
  onSubmitOk: (file: File) => void;
  url: string;
}

export const ChangeAvatarModal = React.forwardRef(
  ({ onClose, onSubmitOk, url }: FileAttachModalProps, ref) => {
    const { t } = useTranslation();
    const [form] = Form.useForm<FileAttach>();
    const [loading, setLoading] = useState(false);
    const [visible, setVisible] = useState(false);
    const [status, setStatus] = useState<ModalStatus>("create");
    const [prevImage, setPrevImage] = useState<any>();
    const handleUpdateAvatar = async () => {
      try {
        setLoading(true);
        const res = await authApi.updateProfile({
          staff: {
            avatar: $url(prevImage),
          },
        });
        userStore.getProfile();
        setVisible(false);
        setPrevImage("");
        message.success("Cập nhật ảnh đại diện thành công!");
      } catch (error) {
        console.log(error);
      } finally {
        setLoading(false);
      }
    };
    useImperativeHandle<any, ChangeAvatarModalRef>(
      ref,
      () => ({
        handleCreate() {
          form.resetFields();
          setVisible(true);
          setStatus("create");
        },
        handleUpdate(avatar: any) {
          // setPrevImage(avatar || "");
          setVisible(true);
          setStatus("update");
        },
      }),
      []
    );

    const createData = async () => {
      const valid = await form.validateFields();
      const data = { fileAttach: form.getFieldsValue() };

      setLoading(true);
      try {
        const res = await fileAttachApi.create(data);
        message.success("Create FileAttach successfully!");
        onClose();
        // onSubmitOk();
      } finally {
        setLoading(false);
      }
    };

    const updateData = async () => {
      const valid = await form.validateFields();
      const data = { fileAttach: form.getFieldsValue() };
      setLoading(true);
      try {
        const res = await fileAttachApi.update(data.fileAttach.id || 0, data);
        message.success("Update FileAttach successfully!");
        onClose();
        // onSubmitOk();
      } finally {
        setLoading(false);
      }
    };

    return (
      <div className="custom-modal">
        <Modal
          centered
          onCancel={() => {
            onClose?.();
            setVisible(false);
          }}
          visible={visible}
          title={""}
          width={522}
          height={513}
          closeIcon={<img src={closeIcon} />}
          footer={null}
          confirmLoading={loading}
          onOk={() => {
            status == "create" ? createData() : updateData();
          }}
          rootClassName="!bg-black"
        >
          <div className="py-[44px] flex flex-col justify-center gap-0 items-center">
            <div className="text-center !w-full text-[18px] font-[600] text-[#2A3547] leading-[120%]">
              {t("changeAvatarContent")}
            </div>
            <Typography.Text
              type="secondary"
              className="!text-[#7C8FAC] text-[14px] font-[400]"
            >
              {t("changeAvatarContent2")}
            </Typography.Text>

            <div className="text-center flex flex-col items-center px-[32px] py-[24px] w-[135px]">
              <Upload.Dragger
                accept="image/*"
                maxCount={1}
                className=""
                beforeUpload={(file) => {
                  if (file.size / 1024 / 1024 > 5) {
                    Modal.error({
                      title: t("fileTooLarge"),
                      content: t("pleaseChooseLessThan5Mb"),
                    });
                    return Upload.LIST_IGNORE;
                  }

                  const formData = new FormData();
                  formData.append("file", file); 

                  fileAttachApi.upload(formData).then((res) => {
                    setPrevImage(res.data.path);
                  });
                  return false;
                }}
                showUploadList={false}
              >
                <div className="flex flex-col items-center gap-2 px-[16px] py-[8px]">
                  {prevImage ? (
                    <img
                      src={$url(prevImage)}
                      width={200}
                      height={200}
                      className="object-contain"
                    />
                  ) : (
                    <>
                      <img src={uploadImage} />
                      <p className="leading-[140%] text-[12px] font-[400] my-0 text-[#5A6A85]">
                        {t("clickOrDragToUpload")}
                      </p>
                    </>
                  )}
                </div>
              </Upload.Dragger>
              <div className="text-center flex flex-col items-center w-[183px]">
                <div className="text-[#7C8FAC] text-[14px] font-[400] mt-[8px] w-full">
                  {t("maxFileSize", { size: 5 })}
                </div>
              </div>
            </div>

            <div className="flex mt-[8px] gap-[12px]">
              <Button
                onClick={() => {
                  setVisible(false);
                }}
                danger
                className="w-[136px] h-[42px] text-[16px] font-[600]"
              >
                {t("cancel")}
              </Button>
              <Button
                loading={loading}
                type="primary"
                // onClick={() => onSubmitOk(prevImage)}
                onClick={() => handleUpdateAvatar()}
                className="w-[136px] h-[42px] !text-[16px] !font-[600]"
              >
                {t("confirm")}
              </Button>
            </div>
          </div>
        </Modal>
      </div>
    );
  }
);
