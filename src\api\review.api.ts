import { request } from "utils/request";
import { AxiosPromise } from "axios";

export const reviewApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/review",
      params,
    }),
  findReject: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/review/reject",
      params,
    }),
  findOne: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/review/${id}`,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/review",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/review/${id}`,
      method: "patch",
      data,
    }),
  warranty: (id: number, data?: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/review/${id}/warranty`,
      method: "patch",
      data,
    }),
  rejectWarranty: (id: number, data?: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/review/${id}/reject/warranty`,
      method: "patch",
      data,
    }),
  approveWarranty: (id: number, data?: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/review/${id}/approve/warranty`,
      method: "patch",
      data,
    }),
  complete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/review/${id}/complete`,
      method: "patch",
    }),
  reject: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/review/${id}/reject`,
      method: "patch",
      data,
    }),
  pending: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/review/${id}/pending`,
      method: "patch",
      // data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/review/${id}`,
      method: "delete",
    }),
  summaryStatus: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/review/summary/status",
      params,
    }),
  reviewHistory: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/review/history",
      params,
    }),
};
