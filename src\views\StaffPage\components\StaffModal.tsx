import { Col, Form, Input, message, Modal, Row, Select, Switch } from "antd";
import { Rule } from "antd/lib/form";
import { useForm } from "antd/lib/form/Form";
import { staffApi } from "api/staff.api";
import { SingleImageUpload } from "components/Upload/SingleImageUpload";
// import { AreaSelector } from "components/AreaSelector/AreaSelector";
import { useRole } from "hooks/useRole";
import { debounce } from "lodash";
import { userInfo } from "os";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import { userStore } from "store/userStore";
import { VisibleStatusTrans } from "types/common";
import { ModalStatus } from "types/modal";
import { Staff } from "types/staff";
import { phoneNumberRule, requiredRule } from "utils/validate-rules";

const rules: Rule[] = [{ required: true, message: "<PERSON><PERSON><PERSON> buộc nhập!" }];
const { Option } = Select;

interface StaffForm extends Staff {
  roleId?: number;
  storeId?: number;
  areaId?: number;
  profile?: string;
}

export const StaffModal = ({
  visible,
  status,
  staff,
  onClose,
  onSubmitOk,
}: {
  visible: boolean;
  status: ModalStatus;
  staff: Partial<Staff>;
  onClose: () => void;
  onSubmitOk: () => void;
}) => {
  const [loading, setLoading] = useState(false);
  const { fetchRole, roles } = useRole();
  const { t } = useTranslation();

  const [form] = useForm<StaffForm>();

  useEffect(() => {
    if (status == "create" && visible) {
      form.resetFields();
    }
  }, [visible, status]);

  useEffect(() => {
    fetchRole({ page: 1, limit: 10 });
  }, []);

  useEffect(() => {
    if (status == "update") {
      form.setFieldsValue({
        ...staff,
        roleId: staff.role?.id,
      });
    }
  }, [staff, status, visible]);

  const handleSubmitForm = async () => {
    await form.validateFields();
    const error = form.getFieldsError();
    console.log(error);

    setLoading(true);
    const { roleId, areaId, ...data } = form.getFieldsValue();
    const dataPost = { staff: { ...data, isBlocked: staff.isBlocked }, roleId };

    console.log(data);

    let res;
    try {
      switch (status) {
        case "update":
          res = await staffApi.update(staff?.id || 0, {
            ...dataPost,
          });
          message.success(t("actionSuccessfully"));

          break;

        default:
          res = await staffApi.create({ ...dataPost });
          message.success(t("actionSuccessfully"));
          break;
      }

      form.resetFields();
      onSubmitOk();
      onClose();
    } finally {
      setLoading(false);
    }
  };

  // const debounceSearchArea = useCallback(
  //   debounce((keyword) => {
  //     query.search = keyword;
  //     fetchData();
  //   }, 300),
  //   []
  // );

  const shouldDisableAdmin = useMemo(
    () => status == "update" && staff?.role?.isAdmin,
    [staff]
  );

  return (
    <Modal
      maskClosable={false}
      onCancel={() => {
        form.resetFields();
        onClose();
      }}
      visible={visible}
      title={
        <h1 className="mb-0 text-lg text-primary font-bold">
          {status == "create" ? t("create") : t("update")}
        </h1>
      }
      style={{ top: 20 }}
      width={700}
      cancelText={t("close")}
      confirmLoading={loading}
      onOk={handleSubmitForm}
      destroyOnClose
    >
      <Form
        layout="vertical"
        form={form}
        validateTrigger={["onBlur", "onChange"]}
        // initialValues={{ areaId: userStore?.info?.area?.id }}
      >
        <Row gutter={16}>
          <Col span={24}>
            <Form.Item shouldUpdate={true} style={{ marginBottom: 0 }}>
              {() => {
                return (
                  <Form.Item
                    style={{ marginBottom: 0 }}
                    label={<div>{t("avatar")}</div>}
                    name="avatar"
                  >
                    <SingleImageUpload
                      onUploadOk={(path: string) => {
                        form.setFieldsValue({
                          avatar: path,
                        });
                      }}
                      imageUrl={form.getFieldValue("avatar")}
                    />
                  </Form.Item>
                );
              }}
            </Form.Item>
          </Col>
          {/* <Col span={12}>
            <Form.Item shouldUpdate={true} style={{ marginBottom: 0 }}>
              {() => {
                return (
                  <Form.Item
                    style={{ marginBottom: 0 }}
                    label={<div>Ảnh profile</div>}
                    name="profileImage"
                  >
                    <SingleImageUpload
                      recommendSize={{ width: 764, height: 1080 }}
                      onUploadOk={(path: string) => {
                        form.setFieldsValue({
                          profileImage: path,
                        });
                      }}
                      imageUrl={form.getFieldValue("profileImage")}
                    />
                  </Form.Item>
                );
              }}
            </Form.Item>
          </Col> */}
          <Col span={12}>
            <Form.Item
              label="Username"
              name="username"
              rules={[
                { required: true },
                {
                  pattern: /^[a-zA-Z0-9]+$/,
                  message: t("wrongFormatUsername"),
                },
                { max: 50 },
              ]}
            >
              <Input disabled={status == "update"} placeholder="" />
            </Form.Item>
          </Col>
          {status == "create" && (
            <Col span={12}>
              <Form.Item
                label={t("password")}
                name="password"
                rules={[requiredRule]}
              >
                <Input placeholder="" />
              </Form.Item>
            </Col>
          )}

          <Col span={12}>
            <Form.Item
              label={t("fullName")}
              name="fullName"
              rules={[requiredRule]}
            >
              <Input placeholder="" />
            </Form.Item>
          </Col>
          {/* <Col span={12}>
            <AreaSelector
              disabled={shouldDisableAdmin}
              hiddenLabel
              form={form}
              fieldName="areaId"
            />
          </Col> */}
          <Col span={12}>
            <Form.Item label="Email" name="email" rules={[requiredRule]}>
              <Input placeholder="" />
            </Form.Item>
          </Col>

          <Col span={12}>
            <Form.Item
              label={t("phoneNumber")}
              name="phone"
              rules={[
                { required: true },
                {
                  pattern: /(84|0[3|5|7|8|9])+([0-9]{8})\b/g,
                  message: t("wrongFormatPhone"),
                },
                { max: 50 },
              ]}
            >
              <Input placeholder="" />
            </Form.Item>
          </Col>

          {/* <Col span={12}>
            <Form.Item label="Chức vụ" name="position">
              <Input placeholder="" />
            </Form.Item>
          </Col> */}

          <Col span={12}>
            <Form.Item label={t("role")} name="roleId" rules={[requiredRule]}>
              <Select
                size="large"
                disabled={shouldDisableAdmin}
                style={{ width: "100%" }}
              >
                {roles?.map((role) => (
                  <Option key={role.id} value={role.id}>
                    {role.name}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          {/* <Col span={12}>
            <Form.Item
              label="Ẩn hiện trên web"
              name="visibleOnWeb"
              rules={rules}
              valuePropName="checked"
            >
              <Switch
                className="new-visible-btn"
                checkedChildren={VisibleStatusTrans.true.label}
                unCheckedChildren={VisibleStatusTrans.false.label}
              ></Switch>
            </Form.Item>
          </Col> */}
        </Row>
      </Form>
    </Modal>
  );
};
