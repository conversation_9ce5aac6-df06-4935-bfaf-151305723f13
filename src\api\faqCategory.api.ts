import { request } from "utils/request";
import { AxiosPromise } from "axios";

export const faqCategoryApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/faqCategory",
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/faqCategory",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/faqCategory/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/faqCategory/${id}`,
      method: "delete",
    }),
};
