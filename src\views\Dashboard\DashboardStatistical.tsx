import React, { useEffect, useMemo, useState } from 'react';
import { Row, Col, Select, Typography, Spin, Card, Table } from 'antd';
import ChartProject from './components/customer/ChartProject';
import { observer } from 'mobx-react';
import Map from './components/customer/Map';
import { useTranslation } from 'react-i18next';
import { formatNumber, formatVND, getTitle } from 'utils';
import DashboardStats, { StatItem } from './components/partner/Stats';
import { ReactComponent as RevenueIcon } from "assets/svgs/total-revenue.svg";
import { ReactComponent as ExpenseIcon } from "assets/svgs/total-expense.svg";
import { ReactComponent as ProfitIcon } from "assets/svgs/total-profit.svg";
import { ReactComponent as WithdrawalNotApprovedIcon } from "assets/svgs/withdrawal-not-approved.svg";
import { ReactComponent as ProjectPendingIcon } from "assets/svgs/project-pending.svg";
import { ReactComponent as BalanceIcon } from "assets/svgs/mission-fail.svg";
import { ReactComponent as UsedBalanceIcon } from "assets/svgs/mission-success.svg";
import { ReactComponent as WithdrawPendingIcon } from "assets/svgs/mission-system.svg";

import TopPartnerList from './components/partner/Top';
import { useDashboard } from 'hooks/useDashboard';
import PieChartCard from './components/partner/PieChartCardProps';
import { DateType } from 'types/dashboard';
import dayjs from 'dayjs';
import { CostProfitChart } from './components/statisticals/LineChart';
import "./DashboardStatistical.scss";
import { useMediaQuery } from '@mui/system';
import { LineProfitChart } from './components/statisticals/LineChartMutiplte';

export const DashboardStatistical = observer(({ title = "" }) => {
  const { t } = useTranslation();

  const mdUp = useMediaQuery((theme: any) => theme.breakpoints.up("md"));

  const {
    topCustomers,
    fetchTopCustomers,
    loadingTopCustomers,

    summaryStatistical,
    fetchSummaryStatistical,
    loadingSummaryStatistical,

    summaryProfit,
    fetchSummaryProfit,
    loadingSummaryProfit,

    topProducts,
    fetchTopProducts,
    loadingTopProducts,

    totalStatistical,
    fetchMoneyPartnerWithdraw,

    summaryDeposit,
    fetchSummaryDeposit,
    loadingSummaryDeposit,

    customerTransaction,
    fetchCustomerTransaction,
    loadingCustomerTransaction,

    customerBalance,
    fetchCustomerBalance,
    loadingCustomerBalance,

    summaryCost,
    fetchSummaryCost,
    loadingSummaryCost,

    summaryProfitSingle,
    fetchSummaryProfitSingle,
    loadingSummaryProfitSingle
  } = useDashboard();

  useEffect(() => {
    fetchTopCustomers();
    fetchSummaryStatistical();
    fetchSummaryProfit();
    fetchTopProducts();
    fetchMoneyPartnerWithdraw();
    fetchSummaryDeposit();
    fetchCustomerTransaction();
    fetchCustomerBalance();
  }, []);

  const statistical = useMemo(() => {
    const moneyPaymentComplete = summaryStatistical?.project?.moneyPaymentComplete ?? 0;
    const moneyPaymentPending = summaryStatistical?.project?.moneyPaymentPending ?? 0
    const price = totalStatistical?.totalDeposit ?? 0;
    const totalCost = totalStatistical?.totalRewardPoint ?? 0;
    const totalProfit = totalStatistical?.totalProfit ?? 0;

    const totalBalance = totalStatistical?.totalBalance ?? 0;
    const totalProjectTransaction = totalStatistical?.totalProjectTransaction ?? 0;
    const totalWithdraw = totalStatistical?.totalWithdraw ?? 0;

    return {
      project: [
        {
          label: t("requestWithdrawalNotApproved"),
          value: formatVND(moneyPaymentComplete.toFixed(0)),
          icon: <WithdrawalNotApprovedIcon />
        },
        {
          label: t("projectWaitting"),
          value: formatVND(moneyPaymentPending.toFixed(0)),
          icon: <ProjectPendingIcon />
        }
      ],
      review: [
        {
          label: t("totalRevenue"),
          value: formatVND(price.toFixed(0)),
          icon: <RevenueIcon />
        },
        {
          label: t("notYet"),
          value: formatVND(totalBalance.toFixed(0)),
          icon: <BalanceIcon />
        },
        {
          label: t("used"),
          value: formatVND(totalProjectTransaction.toFixed(0)),
          icon: <UsedBalanceIcon />
        },
        {
          label: t("totalCost"),
          value: formatVND(totalCost.toFixed(0)),
          icon: <ExpenseIcon />
        },
        {
          label: t("totalProfit"),
          value: formatVND(totalProfit.toFixed(0)),
          icon: <ProfitIcon />
        },
        {
          label: t("unpaidResquest"),
          value: formatVND(totalWithdraw.toFixed(0)),
          icon: <WithdrawPendingIcon />
        }
      ],
      partner: [
        {
          name: t("revenue"),
          value: (price.toFixed(0)),
        },
        {
          name: t("cost"),
          value: (totalCost.toFixed(0)),
        },
        {
          name: t("profit"),
          value: (totalProfit.toFixed(0)),
        }
      ]
    }
  }, [summaryStatistical, totalStatistical])


  const timeOptions = [
    {
      value: DateType.Day,
      label: t("dayNumber", { number: 30 }),
      fromAt: dayjs().subtract(30, "day").startOf("day").unix(),
      toAt: dayjs().endOf("day").unix(),
    },
    {
      value: DateType.Month,
      label: t("monthNumber", { number: 12 }),
      fromAt: dayjs().subtract(12, "month").startOf("month").unix(),
      toAt: dayjs().endOf("day").unix(),
    },
    {
      value: DateType.Year,
      label: t("yearNumber", { number: 5 }),
      fromAt: dayjs().subtract(5, "year").startOf("year").unix(),
      toAt: dayjs().endOf("day").unix(),
    },
  ];

  const timeDayOptions = [
    {
      value: DateType.Day,
      label: t("month"),
      fromAt: dayjs().startOf("month").startOf("day").unix(),
      toAt: dayjs().endOf("day").unix(),
      type: "DAY"
    },
    {
      value: DateType.Month,
      label: t("year"),
      fromAt: dayjs().subtract(12, "month").startOf("month").unix(),
      toAt: dayjs().endOf("day").unix(),
      type: "MONTH"
    },
    {
      value: DateType.Year,
      label: t("all"),
      fromAt: 0,
      toAt: dayjs().endOf("day").unix(),
      type: "YEAR"
    },
  ];



  const [timeSelect, setTimeSelect] = useState(timeOptions[1]);
  const [timeSelectDeposit, setTimeSelectDeposit] = useState(timeDayOptions[0]);
  const [timeSelectTransaction, setTimeSelectTransaction] = useState(timeDayOptions[0]);
  const [timeSelectBalance, setTimeSelectBalance] = useState(timeDayOptions[0]);
  const [timeSelectCost, setTimeSelectCost] = useState(timeDayOptions[0]);
  const [timeSelectProfit, setTimeSelectProfitt] = useState(timeDayOptions[0]);


  useEffect(() => {
    fetchSummaryProfit({ type: timeSelect.value, fromAt: timeSelect.fromAt, toAt: timeSelect.toAt })
  }, [timeSelect]);

  useEffect(() => {
    fetchSummaryDeposit({ type: timeSelectDeposit.value, fromAt: timeSelectDeposit.fromAt, toAt: timeSelectDeposit.toAt })
  }, [timeSelectDeposit]);

  useEffect(() => {
    fetchCustomerTransaction({ type: timeSelectTransaction.value, fromAt: timeSelectTransaction.fromAt, toAt: timeSelectTransaction.toAt })
  }, [timeSelectTransaction]);

  useEffect(() => {
    fetchCustomerBalance({ type: timeSelectBalance.value, fromAt: timeSelectBalance.fromAt, toAt: timeSelectBalance.toAt })
  }, [timeSelectBalance]);

  useEffect(() => {
    fetchSummaryCost({ type: timeSelectCost.value, fromAt: timeSelectCost.fromAt, toAt: timeSelectCost.toAt })
  }, [timeSelectCost]);

  useEffect(() => {
    fetchSummaryProfitSingle({ type: timeSelectProfit.value, fromAt: timeSelectProfit.fromAt, toAt: timeSelectProfit.toAt })
  }, [timeSelectProfit]);



  const data = useMemo(() => {
    return summaryProfit.map((item) => ({
      date: item.date,
      profit: Number(item.profit ?? 0).toFixed(0),
      cost: Number(item.price ?? 0).toFixed(0),
    }));
  }, [summaryProfit]);

  const dataDeposit = useMemo(() => {
    return summaryDeposit.map((item) => ({
      date: item.date,
      totalMoney: Number(item.totalMoney ?? 0).toFixed(0),
    }));
  }, [summaryDeposit]);

  const dataTransaction = useMemo(() => {
    return customerTransaction.map((item) => ({
      date: item.date,
      totalMoney: Number(-item.totalMoney).toFixed(0),
    }));
  }, [customerTransaction]);

  const dataBalance = useMemo(() => {
    return customerBalance.map((item) => ({
      date: item.date,
      totalMoney: Number(item.totalAfterChange ?? 0).toFixed(0),
    }));
  }, [customerBalance]);

  const dataCost = useMemo(() => {
    return summaryCost.map((item) => ({
      date: item.date,
      totalMoney: Number(item.total ?? 0).toFixed(0),
    }));
  }, [summaryCost]);

  const dataProfit = useMemo(() => {
    return summaryProfitSingle.map((item) => ({
      date: item.date,
      totalMoney: Number(item.total ?? 0).toFixed(0),
    }));
  }, [summaryProfitSingle]);



  const dataSource = useMemo(() => {
    const sorted = [...topProducts].sort((a, b) => b.totalMoney - a.totalMoney);

    return sorted.map((item, i) => ({
      key: i + 1,
      code: item.code,
      name: item.name,
      price: `${formatVND(item.totalMoney ?? 0)}₫`,
      numberOfUses: formatNumber(item.total)
    }))
  }, [topProducts]);

  const columns = [
    {
      title: "STT",
      dataIndex: "key",
      key: "key",
      width: 70,
      align: "center" as const,
      render: (value: number) => (
        <div
          style={{
            width: 32,
            height: 32,
            borderRadius: "50%",
            backgroundColor: "#EAEFF4",
            color: "#1D1F2C",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            fontWeight: 500,
            fontSize: 12
          }}
        >
          {value}
        </div>
      ),
    },
    {
      title: t("code"),
      dataIndex: "code",
      key: "code",
    },
    {
      title: t("numberOfUses"),
      dataIndex: "numberOfUses",
      key: "numberOfUses",
      align: "right" as const,
    },
    {
      title: t("amount"),
      dataIndex: "price",
      key: "price",
      align: "right" as const,
    },
  ];

  return (
    <div className='flex flex-col gap-[27px] dashboard-container'>
      <DashboardStats stats={statistical?.review ?? []} endText="₫" />
      <Row gutter={[24, 24]}>
        <Col xs={{ span: 24, order: 2 }} md={{ span: 16, order: 1 }}>
          <PieChartCard
            data={statistical?.partner ?? []}
            colors={['#1A73E8', '#E94134', '#41D664']}
            showLabel
            height={312}
            maxWidth={450}
            formatMoney
          />
        </Col>
        <Col xs={{ span: 24, order: 0 }} md={{ span: 8, order: 1 }}>
          <DashboardStats stats={statistical?.project ?? []} col height={mdUp ? 163 : 'auto'} gutter={27} endText="₫" />
        </Col>
      </Row>

      <Card>
        <Row justify="space-between" className="mb-[20px]" align="middle">
          <Col span={12} className="font-semibold text-[18px] text-black">
            <h5 className="semibold">
              {t("project")}
            </h5>
          </Col>
          <Col>
            <Select size="large"
              defaultValue={timeSelect.value}
              onChange={(value) => {
                const opt = timeOptions.find(opt => opt.value === value);
                if (opt) setTimeSelect(opt);
              }}
              style={{ width: 151, height: 42 }}
            >
              {timeOptions.map(opt => (
                <Select.Option key={opt.value} value={opt.value}>
                  {opt.label}
                </Select.Option>
              ))}
            </Select>
          </Col>
          <Col span={24} className="pt-2">
            <CostProfitChart data={data} type={timeSelect.value} loading={loadingSummaryProfit} />
          </Col>
        </Row>
      </Card>

      <Card>
        <Row justify="space-between" className="mb-[20px]" align="middle">
          <Col span={12} className="font-semibold text-[18px] text-black">
            <h5 className="semibold">
              {t("revenue")}
            </h5>
          </Col>
          <Col>
            <Select size="large"
              defaultValue={timeSelectDeposit.value}
              onChange={(value) => {
                const opt = timeDayOptions.find(opt => opt.value === value);
                if (opt) setTimeSelectDeposit(opt);
              }}
              style={{ width: 151, height: 42 }}
            >
              {timeDayOptions.map(opt => (
                <Select.Option key={opt.value} value={opt.value}>
                  {opt.label}
                </Select.Option>
              ))}
            </Select>
          </Col>
          <Col span={24} className="pt-2">
            <LineProfitChart
              loading={loadingSummaryDeposit}
              data={dataDeposit}
              type={timeSelectDeposit.type}
              series={[
                {
                  name: t("amount"),
                  data: dataDeposit.map(item => Number(item.totalMoney)),
                  itemStyle: { color: '#1A73E8' },
                }
              ]}
            />

          </Col>
        </Row>
      </Card>

      <Card>
        <Row justify="space-between" className="mb-[20px]" align="middle">
          <Col span={12} className="font-semibold text-[18px] text-black">
            <h5 className="semibold">
              {t("customerPaidProject")}
            </h5>
          </Col>
          <Col>
            <Select size="large"
              defaultValue={timeSelectTransaction.value}
              onChange={(value) => {
                const opt = timeDayOptions.find(opt => opt.value === value);
                if (opt) setTimeSelectTransaction(opt);
              }}
              style={{ width: 151, height: 42 }}
            >
              {timeDayOptions.map(opt => (
                <Select.Option key={opt.value} value={opt.value}>
                  {opt.label}
                </Select.Option>
              ))}
            </Select>
          </Col>
          <Col span={24} className="pt-2">
            <LineProfitChart
              loading={loadingCustomerTransaction}
              data={dataTransaction}
              type={timeSelectTransaction.type}
              series={[
                {
                  name: t("amount"),
                  data: dataTransaction.map(item => Number(item.totalMoney)),
                  itemStyle: { color: '#1A73E8' },
                }
              ]}
            />
          </Col>
        </Row>
      </Card>

      <Card>
        <Row justify="space-between" className="mb-[20px]" align="middle">
          <Col span={12} className="font-semibold text-[18px] text-black">
            <h5 className="semibold">
              {t("cost")}
            </h5>
          </Col>
          <Col>
            <Select size="large"
              defaultValue={timeSelectCost.value}
              onChange={(value) => {
                const opt = timeDayOptions.find(opt => opt.value === value);
                if (opt) setTimeSelectCost(opt);
              }}
              style={{ width: 151, height: 42 }}
            >
              {timeDayOptions.map(opt => (
                <Select.Option key={opt.value} value={opt.value}>
                  {opt.label}
                </Select.Option>
              ))}
            </Select>
          </Col>
          <Col span={24} className="pt-2">
            <LineProfitChart
              loading={loadingSummaryCost}
              data={dataCost}
              type={timeSelectCost.type}
              series={[
                {
                  name: t("amount"),
                  data: dataCost.map(item => Number(item.totalMoney)),
                  itemStyle: { color: '#1A73E8' },
                }
              ]}
            />
          </Col>
        </Row>
      </Card>


      <Card>
        <Row justify="space-between" className="mb-[20px]" align="middle">
          <Col span={12} className="font-semibold text-[18px] text-black">
            <h5 className="semibold">
              {t("profit")}
            </h5>
          </Col>
          <Col>
            <Select size="large"
              defaultValue={timeSelectProfit.value}
              onChange={(value) => {
                const opt = timeDayOptions.find(opt => opt.value === value);
                if (opt) setTimeSelectProfitt(opt);
              }}
              style={{ width: 151, height: 42 }}
            >
              {timeDayOptions.map(opt => (
                <Select.Option key={opt.value} value={opt.value}>
                  {opt.label}
                </Select.Option>
              ))}
            </Select>
          </Col>
          <Col span={24} className="pt-2">
            <LineProfitChart
              loading={loadingSummaryProfitSingle}
              data={dataProfit}
              type={timeSelectProfit.type}
              series={[
                {
                  name: t("amount"),
                  data: dataProfit.map(item => Number(item.totalMoney)),
                  itemStyle: { color: '#1A73E8' },
                }
              ]}
            />
          </Col>
        </Row>
      </Card>

      <Card>
        <Row justify="space-between" className="mb-[20px]" align="middle">
          <Col span={12} className="font-semibold text-[18px] text-black">
            <h5 className="semibold">
              {t("customerWallet")}
            </h5>
          </Col>
          <Col>
            <Select size="large"
              defaultValue={timeSelectBalance.value}
              onChange={(value) => {
                const opt = timeDayOptions.find(opt => opt.value === value);
                if (opt) setTimeSelectBalance(opt);
              }}
              style={{ width: 151, height: 42 }}
            >
              {timeDayOptions.map(opt => (
                <Select.Option key={opt.value} value={opt.value}>
                  {opt.label}
                </Select.Option>
              ))}
            </Select>
          </Col>
          <Col span={24} className="pt-2">
            <LineProfitChart
              loading={loadingCustomerBalance}
              data={dataBalance}
              type={timeSelectBalance.type}
              series={[
                {
                  name: t("amount"),
                  data: dataBalance.map(item => Number(item.totalMoney)),
                  itemStyle: { color: '#1A73E8' },
                }
              ]}
            />
          </Col>
        </Row>
      </Card>


      <Row justify="space-between" gutter={[26, 26]}>
        <Col span={12}>
          <TopPartnerList partners={topCustomers as any[]}
            loading={loadingTopCustomers}
            title={t("topTenHighestPayingCustomer")}
            hideIconTop
            typePartner
          />
        </Col>
        <Col xs={24} md={12} >
          <Card>
            <Row gutter={[21, 21]}>
              <Col span={24}>
                <h5 className="semibold">
                  {t("topTenHighestPayingProject")}
                </h5>
              </Col>
              <Col span={24}>
                <Table
                  dataSource={dataSource}
                  columns={columns}
                  pagination={false}
                  bordered={false}
                  className="custom-table-header"
                />
              </Col>
            </Row>
          </Card>
        </Col>

      </Row>
    </div >
  );
})