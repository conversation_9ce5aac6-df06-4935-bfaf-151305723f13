import { request } from "utils/request";
import { AxiosPromise } from "axios";

export const paymentConfigApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/paymentConfig",
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/paymentConfig",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/paymentConfig/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/paymentConfig/${id}`,
      method: "delete",
    }),
};
