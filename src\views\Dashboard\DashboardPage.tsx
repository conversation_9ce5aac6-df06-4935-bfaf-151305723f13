import { Row, Table } from "antd";
import Column from "antd/es/table/Column";
import { dashboardApi } from "api/dashboard.api";
import SimpleBarChart from "components/CustomChart/BarChart/BarChart";
import BasicPieChart from "components/CustomChart/PieChart/PieChart";
import dayjs from "dayjs";
import { toJS } from "mobx";
import { observer } from "mobx-react";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { userStore } from "store/userStore";
import { formatVND, getTitle } from "utils";
import DashboardMap from "./components/DashboardMap";
import { PanelGroup } from "./components/PanelGroup";
interface PieChartData {
  id: number;
  fullName: string;
  total: number;
  percent: number;
}
export const DashboardPage = observer(({ title = "" }) => {
  const { t } = useTranslation();

  const [pieChartData, setPieChartData] = useState<PieChartData[]>();
  const [top10Product, setTop10Product] = useState<any[]>();
  const [top10Partner, setTop10Partner] = useState<any[]>();
  const [top10Customer, setTop10Customer] = useState<any[]>();
  const [cashFlow, setCashFlow] = useState<any>();
  const [moneyProjectPayment, setMoneyProjectPayment] = useState<any[]>();
  const [moneyPartnerWithdraw, setMoneyPartnerWithdraw] = useState<any[]>();
  const [moneyProjectPaymentQuery, setMoneyProjectPaymentQuery] = useState<any>(
    {
      fromAt: dayjs().startOf("month").unix(),
      toAt: dayjs().endOf("month").unix(),
    }
  );
  const [moneyPartnerWithdrawQuery, setMoneyPartnerWithdrawQuery] =
    useState<any>({
      fromAt: dayjs().startOf("month").unix(),
      toAt: dayjs().endOf("month").unix(),
    });
  const lang = toJS(userStore.info.language);

  const [loading, setLoading] = useState(false);

  useEffect(() => {
    document.title = getTitle(t(title));
  }, []);

  const fetchRank = async () => {
    try {
      const res = await dashboardApi.partnerRank("", lang);
      setPieChartData(res.data);
    } catch (error) {
      console.log("Status project in dashboard", error);
    }
  };
  const topTenProject = async () => {
    try {
      const res = await dashboardApi.topProduct();
      setTop10Product(res.data);
    } catch (error) {
      console.log("Status project in dashboard", error);
    }
  };
  const topPartnerReward = async () => {
    try {
      const res = await dashboardApi.topPartner();
      setTop10Partner(res.data);
    } catch (error) {
      console.log("Status project in dashboard", error);
    }
  };
  const topCustomer = async () => {
    try {
      const res = await dashboardApi.topCustomer();
      setTop10Customer(res.data);
    } catch (error) {
      console.log("Status project in dashboard", error);
    }
  };
  const fetchCashFlow = async () => {
    try {
      const res = await dashboardApi.cashFlow();
      setCashFlow(res.data);
    } catch (error) {
      console.log("Status project in dashboard", error);
    }
  };
  const fetchMoneyProjectPayment = async () => {
    try {
      const res = await dashboardApi.moneyProjectPayment(
        moneyProjectPaymentQuery
      );
      setMoneyProjectPayment(res.data);
    } catch (error) {
      console.log("Status project in dashboard", error);
    }
  };
  const fetchMoneyParterWithdraw = async () => {
    try {
      const res = await dashboardApi.moneyPartnerWithdraw(
        moneyPartnerWithdrawQuery
      );
      setMoneyPartnerWithdraw(res.data);
    } catch (error) {
      console.log("Status project in dashboard", error);
    }
  };
  useEffect(() => {
    fetchRank();
    topTenProject();
    topPartnerReward();
    topCustomer();
    fetchCashFlow();
    fetchMoneyProjectPayment();
    fetchMoneyParterWithdraw();
  }, []);
  useEffect(() => {
    fetchRank();
  }, [lang]);

  const parseNumber = (val: string): number => +val?.replace(/\s/g, "");
  return (
    <div>
      {/* <PanelGroup /> */}

      <Row gutter={24} className="!mx-0">
        <div className="w-full grid grid-cols-1 md:grid-cols-2 gap-8 flex-wrap my-8">
          <div className="w-full shadow-md rounded-md p-4">
            <h2>{t("projectPaymentChart")}</h2>
            <div className="w-full">
              <SimpleBarChart
                data={moneyProjectPayment! || []}
                refetch={fetchMoneyProjectPayment}
                query={moneyProjectPaymentQuery}
                setQuery={setMoneyProjectPaymentQuery}
                label="VND"
              />
            </div>
          </div>
          <div className="w-full shadow-md rounded-md p-4">
            <h2>{t("partnerWithdrawalChart")}</h2>
            <div className="w-full">
              <SimpleBarChart
                data={moneyPartnerWithdraw! || []}
                refetch={fetchMoneyParterWithdraw}
                query={moneyPartnerWithdrawQuery}
                setQuery={setMoneyPartnerWithdrawQuery}
                label="VND"
              />
            </div>
          </div>
        </div>
        <div className="w-full rounded-md p-4 shadow-md my-4">
          <h2>{t("numberOfPartnerByLevel")}</h2>
          <BasicPieChart data={pieChartData!} type={"NUMBER_OF_PARTNER"} />
        </div>
        <div className="w-full grid grid-cols-1  md:grid-cols-2 gap-8">
          <div className="w-full shadow rounded-md p-2">
            <h2>{t("topTenBonusPartner")}</h2>
            <Table
              className=""
              pagination={false}
              rowKey="id"
              dataSource={top10Partner!}
              scroll={{ x: "max-content" }}
              loading={loading}
              components={{
                header: {
                  cell: (props: any) => (
                    <th
                      {...props}
                      style={{
                        ...props.style,
                        background: "black !important",
                        fontWeight: "bold",
                        borderBottom: "1px solid #d9d9d9",
                      }}
                    />
                  ),
                },
              }}
            >
              <Column
                //   width={300}
                title={t("partnerName")}
                dataIndex="fullname"
                align="left"
                key="fullname"
              />
              <Column
                //   width={300}
                title={`${t("totalPrice")} (VND)`}
                dataIndex="totalRewardPoint"
                align="right"
                key="totalRewardPoint"
                render={(totalRewardPoint, record) => {
                  return (
                    <>
                      {formatVND(
                        Number(parseNumber(totalRewardPoint || "")) || 0
                      )}{" "}
                    </>
                  );
                }}
              />
            </Table>
          </div>
          <div className="w-full shadow rounded-md p-2">
            <h2>{t("topTenHighestPayingCustomer")}</h2>
            <Table
              pagination={false}
              rowKey="id"
              dataSource={top10Customer!}
              scroll={{ x: "max-content" }}
              loading={loading}
              components={{
                header: {
                  cell: (props: any) => (
                    <th
                      {...props}
                      style={{
                        ...props.style,
                        background: "black !important",
                        fontWeight: "bold",
                        borderBottom: "1px solid #d9d9d9",
                      }}
                    />
                  ),
                },
              }}
            >
              <Column
                //   width={300}
                title={t("customerName")}
                align="left"
                dataIndex="name"
                key="name"
              />
              <Column
                //   width={300}
                title={`${t("totalPrice")} (VND)`}
                dataIndex="totalPay"
                key="totalPay"
                align="right"
                render={(totalPay, record) => {
                  return <>{formatVND(totalPay || 0)}</>;
                }}
              />
            </Table>
          </div>
          <div className="w-full shadow rounded-md p-2">
            <h2>{t("topTenHighestPayingProject")}</h2>
            <Table
              pagination={false}
              rowKey="id"
              dataSource={top10Product!}
              scroll={{ x: "max-content" }}
              loading={loading}
              components={{
                header: {
                  cell: (props: any) => (
                    <th
                      {...props}
                      style={{
                        ...props.style,
                        background: "black !important",
                        fontWeight: "bold",
                        borderBottom: "1px solid #d9d9d9",
                      }}
                    />
                  ),
                },
              }}
            >
              <Column
                //   width={300}
                title={t("code")}
                dataIndex="code"
                align="left"
                key="code"
                render={(address, record) => {
                  return <>{record.code}</>;
                }}
              />
              <Column
                //   width={300}
                title={t("productName")}
                dataIndex="name"
                align="start"
                key="name"
              />
              <Column
                //   width={300}
                title={`${t("totalPrice")} (VND)`}
                dataIndex="totalMoney"
                align="right"
                key="totalMoney"
                render={(totalMoney, record) => {
                  return <>{formatVND(totalMoney)}</>;
                }}
              />
            </Table>
          </div>
        </div>
        <DashboardMap />
      </Row>
    </div>
  );
});
