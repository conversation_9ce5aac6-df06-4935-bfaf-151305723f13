{"project": "Project", "partner": "Partner", "customer": "Customer", "role": "Role", "profile": "Profile", "product": "Service package", "staff": "Staff", "create": "Create", "exportExcel": "Export excel", "staffList": "Workforce list", "search": "Search", "actionSuccessfully": "Operation successful", "productName": "Service package name", "code": "Code", "projectName": "Project name", "status": "Status", "action": "Operation", "update": "Update", "stop": "Stop working", "reopen": "Reopen", "createdAt": "Created at", "updatedAt": "Updated at", "NEW": "Not processed yet", "PROCESSING": "Processing", "COMPLETE": "Complete", "PAUSE": "Pause", "CANCEL": "Canceled", "confirm?": "Confirm?", "yes": "Yes", "no": "No", "delete": "Delete", "faqCategory": "FAQ category", "faq": "FAQ", "faqList": "FAQ List", "enterCode": "Enter code", "selectCustomer": "Select customer", "selectProduct": "Select service package", "reviewQuantity": "Number of reviews", "dropSlow": "Drop slowly", "on": "On", "off": "Off", "mapUrl": "Link on google map", "description": "Description", "save": "Save", "close": "Close", "ACTIVE": "Active", "INACTIVE": "Inactive", "price": "Unit price", "estimatedDay": "Estimated number of service days", "maxLengthCharReview": "Number of characters of the review", "autoGen": "The system will automatically generate the code.", "partnerName": "Partner Name", "exportAsk": "Do you want to export excel file?", "partnerList": "List of partners", "cancel": "Cancel", "phoneNumber": "Phone number", "locked": "Locked", "open": "Open", "resetPass": "Reset password", "lock": "Lock", "unlock": "Unlock", "vietnamese": "Vietnamese", "english": "English", "password": "Password", "searchAllParam": "Code, name, phone number and email", "selectStatus": "Select status", "blocked": "Locked", "customerList": "Customer List", "customerName": "Customer name", "balance": "Wallet balance", "BLOCKED": "Blocked", "exporting": "Exporting excel file (please do not close browser)", "fullName": "Full name", "enterFullName": "Enter your first and last name", "enterPhoneNumber": "Enter phone number", "avatar": "Avatar", "currentSize": "Current size", "recommendSize": "Recommended size", "basicInformation": "Basic information", "faqCategoryName": "FAQ category", "vietnameseName": "Vietnamese name", "englishName": "English name", "enterName": "Enter name", "faqName": "FAQ Title", "faqContent": "FAQ Content", "selectFaqCateogry": "Select FAQ type", "vietnameseTitle": "Vietnamese title", "vietnameseContent": "Vietnamese content", "englishTitle": "English title", "englishContent": "English content", "information": "Information", "info": "Information", "notUpdate": "Not updated yet", "changePass": "Change password", "oldPass": "Old password", "newPass": "New Password", "reEnterNewPass": "Re-enter new password", "passNotMatch": "New password does not match!", "productList": "List of service packages", "nameAndPhone": "Name, phone number", "upload": "Upload", "wrongFormatUsername": "Incorrect format (Cannot contain spaces, special characters, accented letters)", "supportTicket": "Support Ticket", "supportTicketName": "Support Ticket Title", "ALL": "All", "title": "Title", "content": "Content", "contentVi": "Vietnamese content", "contentEn": "English content", "reply": "Reply", "changeStatus": "Change status", "detail": "Detail", "noReplyYet": "No response yet", "INSPEC_REVIEW": "Approve reviews", "REVIEW_IMAGE": "Review feature by image", "photoReview": "Enable/disable review feature by image when creating a project", "MAX_GENERATE_CONTENT_WITH_AI": "Maximum number of AI reviews generated per time", "intevalRadius": "If the number of reviews yesterday was not enough, increase this radius according to the configuration.", "TAX_PERCENT": "Configure %VAT", "SCAN_RADIUS": "Initial Radius (Km)", "INTERVAL_SCAN_RADIUS": "<PERSON><PERSON><PERSON> (Km)", "MAX_MINUTE_TO_COMPLETE_REVIEW": "Maximum time to complete the task (In minutes)", "minDropDes": "When creating a project the number of drop must not be less than the minimum.", "maxDropDes": "When creating a project the number of drop must not be greater than the maximum.", "MIN_DROP_REVIEW": "Minimum number of drop per day", "MAX_DROP_REVIEW": "Maximum number of drop per day", "percentImageDes": "Calculated as a % of project reviews", "PERCENT_IMAGE_IN_PROJECT": "Maximum number of images per project", "MINUTE_TO_PENALIZE": "Penalty time (In minutes)", "MONEY_DROP_IN_PROJECT": "Cost of slow drop per day (VND)", "MONEY_IMAGE_IN_PROJECT": "Amount to pay per image (VND)", "MAX_DROP_PERCENT": "Maximum slow drop quantity compared to service package (%)", "value": "Value", "configuration": "Configuration", "notification": "Notification", "notificationName": "Notification Title", "notificationContent": "Notification content", "notificationPublicAt": "Time of notification", "notificationScope": "Notification object", "wrongFormatPhone": "Incorrect phone number format", "send": "Send", "uploadAttachments": "Upload attachments (can upload multiple files at once)", "clickOrDrag": "Click or drag and drop files to upload", "SYSTEM_REVIEW": "The system approves, then the person approves.", "STAFF_REVIEW": "Staff approved", "AUTO_APPROVE_ALL": "System approves completely", "username": "Username", "welcomeArivi": "Welcome to Riv<PERSON>", "selectLanguage": "Select language", "language": "Language", "waitingTime": "Time to wait for mission (minutes)", "needMission": "Minimum number of mission to complete", "moneyMission": "Reward for a mission", "rank": "Rank", "rankName": "Rank name", "rankNameEn": "Rank name in English", "position": "Position", "ADMIN": "Staff", "CUSTOMER": "Client", "PARTNER": "Partner", "paymentMethod": "Payment method", "generalConfiguration": "General configuration", "paymentConfigName": "Payment method name", "paymentConfigNameEn": "Payment method name in English", "totalProduct": "Total packages purchased", "searchTitle": "Search title", "department": "Department", "downloadFile": "Download file", "confirmDeleteFile": "Confirm deletion of this file?", "requireEmail": "Email is required", "requirePass": "Password is required", "requirePartnerName": "Partner name is required", "samplePartnerList": "Partner List Entry Guide", "sample": "<PERSON><PERSON>", "instruction": "Instruction", "required": "Required", "optional": "Optional", "requirePhoneNum": "Phone number is required", "importExcel": "Import Excel file", "pleaseDownload": "Please download and use the sample file to import data correctly.", "notChangeSample": "Do not change the title in the sample csv/excel file to avoid missing data import.", "forDetailSheet": "For details, see the Instructions sheet of the sample import file.", "dragOrDrop": "Drag and drop or click here to upload file", "note": "Note", "downloadFileSample": "Download sample import file", "uploadOnlyExcel": "You can only upload excel files!", "totalImportLine": "Total import line", "totalImportLineSuccess": "Total successful line", "totalImportLineFail": "Total failure current", "listFail": "List of failed lines", "line": "Line", "error": "Error", "select": "Select", "positionChooseLabel": "Select location", "plsFillDes": "Please fill in description", "keywordPlaceholder": "Enter keyword, press Enter to add", "keyword": "Keywords", "genKeywords": "Generate keywords", "plsFillKeyword": "Please enter keyword", "plsSelectProduct": "Please select package", "plsFillQuantity": "Please fill in the number of reviews", "genReviews": "Automatically generate reviews", "confirmChange": "Confirm changes", "reviewLost": "Changing this will delete the review list you created. Are you sure?", "promotion": "Voucher", "promotionName": "Voucher title", "promotionQuantity": "Number of vouchers", "voucherPeriod": "Time of application", "discount": "Discount", "minAmountOrder": "Minimum order value", "voucherList": "Voucher list", "useImage": "Add image?", "selectPromotion": "Select voucher", "totalUsed": "Quantity used", "voucherInfo": "Voucher information", "orderApplied": "Applicable orders", "totalProject": "Total project", "ongoing": "In distribution", "paused": "Paused", "spent": "Spent", "PENDING": "Pending", "APPROVE": "Approved", "REJECT": "Refused", "createDeposit": "Create deposit order", "createOrderFor": "Create order for customer", "amountRequest": "Amount requested", "depositMoney": "<PERSON><PERSON><PERSON><PERSON>", "bankInfo": "Bank information", "bankAccountName": "Bank account name", "stk": "Bank account number", "inspector": "Approver", "inspectAt": "approved at", "inspectNote": "approval note", "approve": "Approve", "reject": "Reject", "enterNote": "Enter note", "doneRequest": "Confirm request processed?", "approveRequest": "Confirm request approval?", "rejectRequest": "Confirm request rejection?", "withdrawMoney": "Withdraw money", "customerDepositOrderList": "Customer deposit order list", "withdrawOrderList": "Partner withdraw order list", "projectCreate": "Create project", "projectPlaceChoose": "Select location on map", "projectDescription": "Project Description", "projectProductChoose": "Select service package", "slowDrop": "drop slowly", "slowDropTooltip": "Slow drop is a form of daily review evaluation. For example: If you enter the slow drop quantity as 2, your project will receive 2 reviews per day.", "slowDropInfo": "Slow dropping helps reviews to be more real. The cost of slow dropping per day is {{number}} ₫", "slowDropEstimate": "Expected completion in {{number}} days.", "slowDropPerDay": "Number of reviews per day", "image": "Image", "projectManagement": "Project Management", "tutorialGetUrlLink": "Instructions for getting URL", "keywordInfo": "RIVI AI will use artificial intelligence to create review content that closely follows your product/service. \nFor example: RIVI AI will use artificial intelligence to create review content that closely follows your product/service. \n\n* Delicious coffee shop, nutritious drinks, comfortable space, quick service \n* Delicious and cozy coffee shop space, great drinks, friendly staff \n* Delicious and diverse drinks at the coffee shop, luxurious and clean space \n* Delicious coffee shop, quality drinks, quiet and relaxing space", "estimatePrice": "Estimate price", "estimatePackage": "Package price", "estimateSlowDrop": "Slow drop price", "estimateImage": "Price of review by image", "estimateTax": "VAT", "fee": "Cost", "totalPrice": "Total price", "projectImageInfo": "Images must be taken with real devices, we will distribute each review with 1 image. Reviews with images will be randomly distributed alternating with reviews with only text.", "projectImagePrice": "The number of images must not exceed {{percent}}% of the package's rating. The image format is (*.jpeg, *.png). The price of 1 image is {{number}} VND/photo.", "totalReview": "Total review", "projectList": "Project List", "enterRateWish": "Enter the number of stars you want to achieve", "rateWish": "Total desired review", "packageNeededInfo": "The number of reviews needed to get a {{rate}} star rating is {{reviews}} reviews.", "packageRecommend": "Recommended review package", "confirmProjectCreate": "Confirm project creation", "confirmProjectCreateInfo": "Please check the project information carefully, you cannot edit the project information (name, description,...) after it has been successfully created.\n\nYou can still edit the review content before payment.", "selectPartner": "Choose a partner", "orderHistory": "Order history", "onlinePayment": "Online payment", "onlinePaymentName": "Bank name", "paymentSource": "Payment source", "selectBank": "Select bank", "paymentUnit": "Payment Unit", "ownerName": "Account holder name", "enterStk": "Enter bank account number", "createdDate": "Date created", "paymentStatus": "Payment Status", "address": "Address", "package": "Package", "stt": "No", "regenerate": "Regenerate", "reviewList": "List of reviews", "projectDetail": "Project details", "payment": "Payment", "paymentPENDING": "Unpaid", "paymentCOMPLETE": "Paid", "paymentFAIL": "Payment failed", "operationSuccess": "Operation successful", "projectPayment": "Project Payment", "checkout": "Checkout", "payAgain": "Repayment", "paymentInfo": "Payment information", "inspection": "Inspection", "aiSetting": "AI Configuration", "scan": "Radius configuration", "penalty": "Penalty configuration", "searchAllParam2": "Customer code and name", "searchAllParam3": "Partner code and name", "searchWithdraw": "Partner name and phone number", "approveProject": "Task List", "type": "Type", "E-WALLET": "E-Wallet", "BANK": "Bank", "selectProject": "Select project", "pleaseSelectProject": "Please select project", "review": "Review", "missionDetail": "Mission details", "mission": "Mission", "notYet": "Not used yet", "seeReview": "See review", "wrongPhoto": "No image, wrong image", "noReview": "Review does not exist", "pleaseSelectMission": "Select a task to view details", "SYSTEM_PENDING": "Waiting for system approval", "ADMIN_PENDING": "Waiting for staff approval", "ASSIGNING": "Already received", "ACCEPT": "Accept the mission", "generatingReview": "Generating review", "PROMPT_CONTENT": "Prompt to create content for review", "PROMPT_KEYWORD": "Prompt to create keyword", "actionConfirm": "Confirm continue", "confirm": "Confirm", "moneyFinal": "Total money", "totalStar": "Total stars", "progress": "Progress", "FAIL": "Failure", "customerTransaction": "Customer wallet history", "DEPOSIT": "<PERSON><PERSON><PERSON><PERSON>", "WITHDRAW": "Withdraw", "CANCEL_WITHDRAW": "Cancel withdrawal", "PAYMENT_ORDER": "Payment order", "beforeChange": "Previous balance", "afterChange": "Final balance", "moneyChange": "Amount change", "keywordProject": "Project keywords", "descriptionProject": "Project Description", "contentProject": "Review content must not be the same as previous review content.", "maxCharacterProject": "Maximum number of characters when creating a review", "maxReviewGemini": "Maximum number of reviews per Gemini call", "reviewLocation": "Name of review location", "profit": "Profit", "missionNotCompleted": "Mission not completed", "filter": "Filter", "clearFilter": "Clear filter", "missionHistory": "Mission History", "partnerTransaction": "Partner Wallet History", "projectCode": "Project code", "reviewUrl": "Link has been rated", "reviewContent": "Review content", "rewardPoint": "Bonus", "minimumAmountIs": "The minimum amount is", "REVIEW": "Complete the mission", "dashboardManagement": "Overview", "maxPerOne": "Maximum usage / customer", "WARRANTY": "Redistributing for the second time", "warrantyPeriod": "Warranty period", "warranty": "Warranty", "validateMinAmount": "The minimum amount cannot be less than the discount amount.", "reviewAgain": "Review again", "totalMoney": "Total amount", "completed": "Completed", "onProcess": "In progress", "onPause": "Suspended", "onNeedWarranty": "<PERSON><PERSON><PERSON>", "notDistributed": "Not distributed yet", "pendingApproval": "Pending approval", "totalCustomer": "Total number of customers", "totalPartner": "Total number of partners", "success": "Success", "onHandling": "Processing", "total": "Total", "cashFlow": "Cash flow", "projectPaymentChart": "Project Payment Chart", "partnerWithdrawalChart": "Partner withdrawal chart", "numberOfPartnerByLevel": "Chart of number of partners by level", "topTenHighestPayingProject": "Top 10 most paid service packages", "topTenBonusPartner": "Top 10 Most Bonus Partners", "topTenHighestPayingCustomer": "Top 10 highest paying customers", "waitingPayment": "Waiting for payment", "congratulationMsg": "Congratulations on your business achieving the maximum review score. You should buy some more reviews.", "notificationList": "Notification list", "notificationNameVI": "Notification name", "notificationNameEN": "Notification Name (EN)", "notificationContentEN": "Notification content (EN)", "silver": "Silver", "bronze": "Copper", "newMember": "New Member", "noData": "No data yet", "nameAndCodeProject": "Project name and code", "moneyWithdrawRequest": "Partner withdrawal request", "moneyProjectWaitingForPayment": "Project money awaiting payment", "promotionPrice": "Discount", "reviewNumber": "Number of reviews", "maxSlowDropPerDay": "The maximum number of slow drops per day is {{number}}.", "selectReviewType": "Select review configuration type", "configName": "Configuration name", "MIN_WITHDRAW_AMOUNT": "Minimum amount that a partner can withdraw (VND)", "REVIEW_CHARACTER_COUNT": "Number of characters of the review", "WARRANTY_PERIOD_DAYS": "Warranty claim period (days)", "PROJECT_CREATION_DELAY": "Project creation wait time", "createDelay": "Waiting time between two customer project creations (In seconds)", "basicInfo": "General information", "RESPONDED": "Answered", "paymentSuccess": "Project payment successful!", "paymentSuccess2": "Thank you for your payment. The project will be distributed to the staff immediately.", "keywordsCannotExceedLimit": "You have exceeded the keyword limit of {{number}} words. You need to remove keywords.", "CHECK_IDENTITY_CARD": "Check account authentication", "warrantyDes": "Distribute to other collaborators", "processingDes": "Waiting for distribution", "requestWarrantyDes": "Waiting for system check", "approveWarranty": "Approve warranty", "projectREQUEST_WARRANTY": "<PERSON><PERSON><PERSON>", "projectREJECT_WARRANTY": "Cancel warranty claim", "projectPROCESSING": "In progress", "projectNEW": "New", "projectCOMPLETE": "Completed", "projectPAUSE": "Paused", "projectCANCEL": "Canceled", "projectWARRANTY": "2nd distribution", "selectReview": "Select mission", "IOS": "iOS", "ANDROID": "Android", "REVIEW_VIDEO_IMAGE_PC": "Mission tutorial video (Desktop - with images)", "REVIEW_VIDEO_PC": "Mission tutorial video (Desktop - no image)", "REVIEW_VIDEO_IMAGE_MOBILE": "Mission tutorial video (Mobile - with images)", "REVIEW_VIDEO_MOBILE": "Mission tutorial video (Mobile - no image)", "videoRequired": "Please select video file", "fileSizeInvalid": "File size must be less than {{number}}MB", "uploadImg": "Upload image", "CONTRACT_SAMPLE_FILE": "Sample contract file", "selectFile": "Select file", "VERIFIED": "Verified", "UNVERIFIED": "Not verified", "verifyStatus": "Authentication status", "viewProfile": "View profile", "activeStatus": "Operating status", "customerDashboard": "Customer density map", "partnerDashboard": "Partner density map", "onlyShowActivePartnerDashboard": "Show only partners who have performed the mission", "frontImg": "Front photo", "backImg": "Back photo", "contract": "Contract", "paymentTypeTable": "Payment source", "MANUAL": "Admin manually deposit money", "MOMO": "<PERSON><PERSON>", "ONE_PAY": "OnePay", "PAY_OS": "PayOS", "PAYPAL": "<PERSON><PERSON>", "depositSYSTEM_REVIEW": "System approved", "rejectWarranty": "Warranty denied", "withdrawPENDING": "Pending processing", "withdrawALL": "All", "withdrawPROCESSING": "Waiting for payment", "withdrawAPPROVE": "Success", "withdrawREJECT": "Unsuccessful", "withdrawPENDINGNote": "Request received, please wait for approval", "withdrawAPPROVENote": "Complete payment", "withdrawREJECTNote": "Incorrect transfer information", "withdrawPROCESSINGNote": "--", "batchAction": "Batch operations", "approveWithdraw": "Withdrawal approval", "completeRequest": "Complete the request", "supportTicketCustomer": "Request of customer", "supportTicketPartner": "Request of partner", "KD": "Sales Department", "KT": "Accounting Department", "CSKH": "Customer Service Department", "CSDT": "Partner Care Department", "slowDropExceedPercentOfReview": "The amount of slow drop does not exceed {{percent}}% of the number of review packages", "slowDropRecommend": "You should drop it slowly so that the reviews look as real as possible. Do not review too many in 1 day, it will reduce the number of reviews displayed. The number of slow drop is more than 2 reviews and less than {{percent}}% of the number of purchased\"&\" packages", "mustChooseProjectPlace": "Please Select location", "secondDistribute": "2nd distribution", "requestRejectWarranty": "Cancel warranty claim", "CREATED": "Newly created", "notificationCustomer": "Customer notification", "notificationPartner": "Partner notification", "MAX_DESCRIPTION_CREATE_PROJECT": "Maximum characters of project description", "MIN_DESCRIPTION_CREATE_PROJECT": "Minimum characters of project description", "projectDescriptionCannotExceedLimit": "Description must atleast {{min}} and not exceed {{max}} characters", "clickOrDragToUpload": "Click or drag and drop files to upload", "projectImageExceedLimit": "The number of images has exceeded the limit, please delete some images", "vietnameseShortContent": "Short description in Vietnamese", "englishShortContent": "Short description in English", "shortContent": "Short description", "faqCustomer": "Customer FAQ", "faqPartner": "Partner FAQ", "faqTarget": "FAQ for", "langConfig": "Output language", "existingKeywords": "Previous Keyword", "TIME_RECHECK_REVIEW": "System check time", "inHours": "Hourly", "deleteAll": "Delete all", "languagePlaceholder": "Select language", "updateAt": "Updated at", "link": "Link", "reviewPENDING": "Not distributed yet", "reviewWARRANTY": "Redistribution", "reviewREJECT_WARRANTY": "<PERSON><PERSON><PERSON> Disclaimer", "reviewASSIGNING": "In distribution", "reviewACCEPT": "In progress", "reviewSYSTEM_PENDING": "Waiting for system approval", "reviewADMIN_PENDING": "Waiting for staff approval", "reviewCOMPLETE": "Completed", "reviewREJECT": "Refused", "reviewREQUEST_WARRANTY": "Waiting for warranty", "operation": "Operation", "feature": "Features", "completedAt": "Completed at", "supportTicketNEW": "Not processed yet", "supportTicketPROCESSING": "Not processed yet", "supportTicketRESPONDED": "Answered", "supportTicketCOMPLETE": "Closed", "SECURITY_POLICY": "Privacy Policy (Customer)", "PAYMENT_POLICY": "Refund Payment (Customer)", "WARRANTY_POLICY": "Warranty Policy (Customer)", "policy": "Policy - Terms", "supportTicketALL": "All", "reviewUnderWarranty": "Review under warranty", "descriptionMaxCharacters": "Description maximum 255 characters", "reviewALL": "All", "selectedAmount": "Select amount ({{currencyCode}})", "slowMinQuantity": "Minimum daily slow drop quantity", "pleaseEnterNote": "Please enter notes", "codeName": "id, project name", "projectStatus": "Project status", "describe": "Please enter information about your business, including the products/services you offer, your competitive strengths, and what you want customers to know.", "numberOfReviewPerDay": "Number of reviews per day", "keywordInfo1": "RIVI AI will use artificial intelligence to create review content that closely follows your product/service.", "keywordInfo2": "When you have keywords", "keywordInfo3": "then RIVI AI will generate the following content:", "keywordInfo4": "Delicious coffee, nutritious drinks, comfortable space, quick service", "keywordInfo5": "Delicious and cozy cafe space, great drinks, friendly staff", "keywordInfo6": "Drinks at the cafe are delicious and varied, the space is luxurious and clean.", "keywordInfo7": "Delicious coffee, quality drinks, quiet and relaxing space", "keywordInfo8": "Good coffee", "keywordInfo9": "For example:", "logout": "Log out", "camelCase": "Enter to break keywords", "languageProjectDescription": "Language used to generate review content and keywords", "autoDistribution": "Automated distribution", "reasonName": "Reason in Vietnamese", "reasonNameEn": "Reason in English", "rejectReason": "Reason for rejection", "reason": "Reason", "withdrawMethod": "Withdrawal method", "withdrawConfigName": "Withdrawal method name", "withdrawConfigNameEn": "English withdrawal method name", "slowMaxQuantity": "Maximum slow dropping quantity per day", "slowMinQuantityMust": "Invalid minimum quantity", "estimatedDayMust": "Maximum quantity is invalid.", "maxLength64Characters": "Maximum number of characters is 64", "moneyImage": "Extra bonus with image", "AUTO_CHECK_WARRANTY": "Automatic warranty check", "pleaseEnterProjectName": "Please enter project name", "pleaseChooseProjectPlace": "Please select the location of your business/entrepreneur", "pleaseEnterProjectDescription": "Please describe the location you would like to review.", "pleaseChoosePackage": "Please select a service package", "pleaseChooseCustomer": "Choose your customers", "pleaseChooseKeyword": "Enter your keywords or generate keywords automatically with RiviAI", "pleaseChooseProjectImage": "Please select images for the project", "pleaseEnterYourPhoneOrEmail": "Please enter your phone number or email", "pleaseEnterYourPassword": "Please enter your password", "pleaseEnterFullName": "Please enter your full name", "pleaseEnterPhone": "Please enter phone number", "pleaseEnterEmail": "Please enter email address", "pleaseEnterPassword": "Please enter password", "pleaseEnterOldPassword": "Please enter old password", "pleaseEnterNewPassword": "Please enter new password", "pleaseEnterReNewPassword": "Please re-enter new password", "pleaseLogin": "Please login to use our services", "duplicateImage": "This image has been duplicated with the previous image.", "currentAverageRating": "Current average rating", "linkGGMap": "Google map link", "progressComplete": "Completion progress", "reviewLog": "Review History", "projectLogREJECT_REVIEW": "Refuse to review", "projectLogCOMPLETE_REVIEW": "Complete the review", "projectLogPROJECT": "Project", "projectLogADMIN_PENDING_REVIEW": "Waiting for admin approval", "projectLogSYSTEM_PENDING_REVIEW": "Waiting for system approval", "projectLogASSIGN_REVIEW": "Waiting for partner to receive", "projectLogACCEPT_REVIEW": "In progress", "projectLogWARRANTY_REVIEW": "Waiting for warranty", "projectLogREVIEW": "Evaluate", "projectLogType": "Type", "partnerCode": "Partner ID", "remainingDays": "Still under warranty", "days": "day", "pendingStatusNote": "Waiting for distribution", "userManagement": "User Management", "other": "Other", "noProjectYet": "You have no projects yet!", "noProjectYet2": "Start creating your first project to explore all the great features we have to offer.", "createdContent": "Content created", "overviewOrder": "Order Overview", "payNow": "Pay Now", "MAX_CHECK_FAIL_REVIEW": "Maximum number of checks per review", "email": "E-mail", "bankNumber": "Enter bank number", "dayNumber": "{{number}} days", "monthNumber": "{{number}} months", "yearNumber": "{{number}} years", "totalCustomers": "Total customers", "totalProjects": "Total project", "inProgress": "In progress", "pending": "Unpaid", "stopped": "Paused", "levelMember": "Membership Level", "statistical": "Statistical", "projectCreated": "Project created", "paidProject": "Paid Project", "viewDetail": "See details", "searchCustomer": "Search for customer name, phone number or email", "searchPartner": "Search for partner name, phone number or email", "levelUser": "Member Rank", "enterContentEn": "Please enter message for English", "enterContentVi": "Please enter message for Vietnamese", "requiredField": "Please enter information for field {{field}}", "roleShort": "Rights", "continue": "Continue", "totalVerifiedPartners": "Total verified partners", "totalAssignedTasks": "Total number of tasks assigned", "tasksPendingSystemApproval": "Task waiting for system approval", "tasksPendingStaffApproval": "Tasks awaiting staff approval", "completedTasks": "Number of completed tasks", "rejectedTasks": "Total number of rejected tasks", "totalWarrantyReviews": "Total warranty reviews", "MIN_DEPOSIT_AMOUNT": "Minimum customer deposit amount", "distributed": "Distributed", "searchIdMission": "Search for task ID", "idNv": "Mission ID", "location": "Location", "linkReview": "Review link", "name": "Name", "see": "See", "missionPending": "Waiting for mission", "allProject": "All projects", "allPartner": "All partners", "allTypeReview": "All types of reviews", "contentOnly": "Content only", "withPictures": "With image", "approveMission": "Mission approval", "warrantyMission": "Warranty approval", "time": "Time", "timeCreate": "Creation time", "timeDelivery": "Delivery time", "missionTrue": "Mission successful", "missionFalse": "Mission failed", "requestWithdrawalNotApproved": "<PERSON><PERSON><PERSON> request not approved", "projectWaitting": "Project awaiting payment", "totalRevenue": "Total revenue", "totalCost": "Total cost", "totalProfit": "Total profit", "revenue": "Revenue", "cost": "Expense", "warrantyAgree": "Warranty Agree", "reviewStill": "Reviews still live", "inappropriateContent": "Inappropriate content", "vaildConfirm": "Valid confirmation", "noStarReview": "No 5 star rating", "all": "All", "mustNumber": "Must be a number", "maxNumberReview": "Maximum number of {{number}} reviews / day", "SECURITY_POLICY_PARTNER": "Privacy Policy (Partners)", "WARRANTY_POLICY_PARTNER": "Tax Policy (Partner)", "PAYMENT_POLICY_PARTNER": "Payment Terms (Partner)", "bankInformation": "Bank information", "bankName": "Bank name", "bankAccountNumber": "Bank account number", "collapse": "Collapse", "expand": "Extend", "withdrawPROCESSED": "Processed", "numberOfUses": "Number of uses", "amount": "Amount", "accoutStatus": "Account Status", "changeLanguage": "Change language", "searchPartnerPlaceholder": "Search for phone number, email or partner name", "OVER_TIME": "Expired", "reviewOVER_TIME": "Expired", "maxAllowedImages": "Number of images should be less than {{number}}% of total reviews. Please remove some images", "maxFile": "Maximum number of files is", "updateTime": "Update time", "fileAttachShort": "Upload attachments", "acceptTypeUpload": "Accept PDF, JPG, JPEG, PNG formats", "submitReq": "Submit request", "money": "Money", "unpaid": "Unpaid", "bankFullName": "Full name of the bank", "bankAbbName": "Bank abbreviation", "systemStatusNote": "System is approving", "month": "Month", "year": "Year", "customerPaidProject": "Amount the customer paid for the project", "valDropNumber": "Please enter quantity", "customerWallet": "Amount remaining in customer's wallet", "used": "Used", "unpaidResquest": "Unpaid withdrawal request", "fileAttachLabel": "Attachments", "REJECT_WARRANTY": "<PERSON><PERSON><PERSON> Disclaimer", "notificationCustomerList": "Customer Notification List", "notificationPartnerList": "Partner Notification List", "login": "Log in", "companyName": "Company name", "taxCode": "Tax code", "companyAddress": "Company address", "companyEmail": "Company email", "invoiceInfo": "Invoice information", "rewardInfo": "Reward information", "reviewTurn": "reviews", "warrantyStatusNote": "Distribute to other collaborators", "invoiceRequest": "Request invoice", "invoice": "Invoice", "bill": "Bill", "print": "Print", "exportPdf": "Export PDF", "changePasswordText": "Change Password", "changeAvatar": "Change avatar", "changeAvatarContent": "Change profile picture", "changeAvatarContent2": "Change your avatar below", "maxFileSize": "Maximum file size {{size}}MB", "enterPosition": "Enter location", "assignAt": "Received at", "minKeyWordQuantity": "Minimum number of keywords", "requiredMinKeyword": "The minimum number of keywords for this service package is", "adminStatusNote": "Waiting for staff approval", "selectExportDateRange": "Select time period", "startDate": "From date", "endDate": "By date", "pleaseSelectDateRange": "Select the time period to export data", "exportSuccess": "File export successful"}