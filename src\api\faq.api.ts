import { request } from "utils/request";
import { AxiosPromise } from "axios";

export const faqApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/faq",
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/faq",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/faq/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/faq/${id}`,
      method: "delete",
    }),
};
