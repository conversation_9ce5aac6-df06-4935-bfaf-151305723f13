.profile-page {


  .profile-wrapper {
    padding: 24px;
    border-radius: 8px;
  }

  .info-text {
    display: flex;
    align-items: center;
    gap: 4px;
    line-height: 32px;
  }

  .btn-form {
    font-size: 16px;
    color: #EFF5FF;
    font-weight: 600;
    height: 42px;
  }

  .avt-profile {
    height: 129px;
    width: 132px;
  }

  .info-section--title {
    font-size: 16px !important;
    color: #2A3547;
    font-weight: 600;
    margin-bottom: 0;
  }

  .info-section {
    .info-section--detail {
      margin-top: 12px;

      .info-section--detail-item {
        height: 32px;
        display: flex;
        align-items: center;
        gap: 2px;

        span {
          color: #2A3547;
        }
      }
    }
  }

  .form-profile {
    color: #2A3547;
    padding: 24px ;

    border-radius: 8px;

    .form-profile--title {
      font-size: 18px;
      font-weight: 600;
    }

    .form-profile--items {
      gap: 24px;
      margin-top: 24px;

      label {
        color: #2A3547
      }

      .ant-input-outlined {
        height: 42px;
      }
    }
  }

  .form-change-company {
    color: #2A3547;
    padding: 24px;
    box-shadow: 0px 4px 12px -2px rgba(145, 158, 171, 0.1);
    border-radius: 8px;

    .form-change-company--title {
      font-size: 18px;
      font-weight: 600;
    }

    .form-change-company--items {
      gap: 24px;
      margin-top: 24px;

      label {
        color: #2A3547
      }

      .ant-input-outlined {
        height: 42px;
      }
    }
  }

  .form-change-company--tax {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    gap: 24px;

    .ant-form-item {
      flex: 1 1 calc(50% - 12px);

    }
  }
}

@media (max-width: 429px) {
  .profile-page {

    .form-change-company--tax {
      width: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      gap: 0;
    }

    .profile-wrapper {
      padding: 16px 12px 0 12px;
    }

    .info-section {
      gap: 4px;

      .info-section--detail {
        .info-section--detail-item {
          height: 20px;
          margin-bottom: 4px;
        }
      }
    }

    .info-text {
      line-height: 24px;
    }

    .avt-profile {
      height: 84px;
      width: 84px;
      margin-top: 30px;
      margin-left: -10px;
    }

    .avt-icon {
      top: 90px;
      left: 50px;
    }

    .title-name {
      padding-top: 5px;
      margin-left: -10px;
    }

    .form-profile {

      .ant-form-item {
        margin-bottom: 16px;
      }

      padding: 16px 12px 0px 12px;

      .form-profile--title {
        font-size: 18px;
        font-weight: 600;
      }

      .form-profile--items {
        gap: 16px;

        margin-top: 16px;

        label {
          color: #2A3547;
          font-size: 14px;
        }

        .ant-input-outlined {
          height: 42px;
        }
      }
    }
  }



  .info-section--title {
    font-size: 14px;
  }
}