import { Banner } from "./banner";
import { NewsCategory } from "./newsCategory";
import { StockCode } from "./stockCode";
import { StockOrder } from "./stockOrder";

export interface News {
  id: number;
  createdAt: number;
  updatedAt: number;
  isDeleted: boolean;
  deletedAt: number;
  title: string;
  position: number;
  shortContent: string;
  content: string;
  isHighlight: boolean;
  thumbnail: string;
  totalViews: number; //số lượt view
  isVisible: boolean;
  likes: number;
  banners: Banner[];
  newsCategory: NewsCategory;
  stockOrder: StockOrder;
  stockCode: StockCode;
  authorName: string;
  isPublic: boolean;
}

export interface NewsTag {
  id: number;
  createdAt: number;
  updatedAt: number;
  isDeleted: boolean;
  name: string;
  newses: News[];
}
