import { Col, Form, Input, message, Modal, Row, Select } from "antd";
import { Rule } from "antd/lib/form";
import { withdrawApi } from "api/withdraw.api";

import React, { useEffect, useImperativeHandle, useState } from "react";
import { ModalStatus } from "types/modal";
import { Withdraw } from "types/withdraw";
import { Customer } from "types/customer";
import { InputNumber } from "components/Input/InputNumber";
import { useHandleCustomer } from "hooks/useHandleCustomer";
import { CustomerSelector } from "components/Selector/CustomerSelector";
import { useTranslation } from "react-i18next";
import TextArea from "antd/es/input/TextArea";
import { PartnerSelector } from "components/Selector/PartnerSelector";
import { appStore } from "store/appStore";
import { ConfigurationParam } from "types/configuration";
import { formatVND } from "utils";

const rules: Rule[] = [{ required: true }];

export interface WithdrawModal {
  handleCreate: () => void;
}

interface WithdrawModalProps {
  onClose: () => void;
  onSubmitOk: () => void;
  isAgent?: boolean;
}

export const WithdrawModal = React.forwardRef(
  ({ onClose, onSubmitOk, isAgent }: WithdrawModalProps, ref) => {
    const [form] = Form.useForm();
    const [loading, setLoading] = useState(false);
    const [selectedCustomerId, setSelectedCustomerId] = useState<number>();
    const [visible, setVisible] = useState(false);
    const [idCustomer, setIdCustomer] = useState<number>();
    const [status, setStatus] = useState<ModalStatus>("create");
    const { t } = useTranslation();

    useImperativeHandle<any, WithdrawModal>(
      ref,
      () => ({
        handleCreate() {
          form.resetFields();
          setVisible(true);
          setStatus("create");
        },
      }),
      []
    );
    const createData = async () => {
      try {
        await form.validateFields();
        const { partnerId, ...formData } = form.getFieldsValue();
        setLoading(true);

        const res = await withdrawApi.create({
          ...formData,
          partnerId,
        });
        message.success(t("actionSuccessfully"));
        onClose();
        setVisible(false);
        onSubmitOk();
      } finally {
        setLoading(false);
      }
    };

    return (
      <Modal
        onCancel={() => {
          onClose?.();
          setVisible(false);
        }}
        visible={visible}
        title={t("withdrawMoney")}
        style={{ top: 20 }}
        width={500}
        confirmLoading={loading}
        onOk={() => createData()}
      >
        <Form
          layout="vertical"
          form={form}
          validateTrigger={["onBlur", "onChange"]}
        >
          <Row>
            <Col span={24}>
              <Form.Item
                label={t("selectPartner")}
                name="partnerId"
                rules={rules}
              >
                {/* <Select
                  placeholder="Vui lòng chọn Khách hàng"
                  size="middle"
                  style={{ width: "100%", minWidth: 150, marginTop: "5px" }}
                  allowClear
                  showSearch
                  loading={loadingCustomer}
                >
                  {customers?.map((item) => (
                    <Select.Option value={item.id}>
                      {item.name} - {item.phone}
                    </Select.Option>
                  ))}
                </Select> */}
                <PartnerSelector
                  initQuery={{ isAgent }}
                  value={form.getFieldValue("partnerId")}
                  onChange={(partnerId) => {
                    form.setFieldValue("partnerId", partnerId);
                  }}
                />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item
                label={t("amountRequest")}
                name="amount"
                rules={[
                  { required: true },
                  {
                    validator: (_, value) => {
                      if (
                        !value ||
                        value <
                          appStore.getOneConfiguration(
                            ConfigurationParam.MinWithdrawAmount
                          )
                      ) {
                        return Promise.reject(
                          new Error(
                            t("minimumAmountIs") +
                              " " +
                              formatVND(
                                appStore
                                  .getOneConfiguration(
                                    ConfigurationParam.MinWithdrawAmount
                                  )
                                  .toString()
                              )
                          )
                        );
                      }

                      return Promise.resolve();
                    },
                  },
                ]}
              >
                <InputNumber
                  className="w-full"
                  // placeholder="Nhập số điểm muốn nạp"
                />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label={t("note")} name="note">
                <TextArea />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    );
  }
);
