import { request } from "utils/request";
import { AxiosPromise } from "axios";

export const reasonApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/reason",
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/reason",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/reason/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/reason/${id}`,
      method: "delete",
    }),
};
