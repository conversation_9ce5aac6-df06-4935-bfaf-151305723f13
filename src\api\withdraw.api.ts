import { request } from "utils/request";
import { AxiosPromise } from "axios";

export const withdrawApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/withdraw",
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/withdraw",
      data,
      method: "post",
    }),
  getSummary: (): AxiosPromise<any> =>
    request({
      url: "/v1/admin/withdraw/summary/status",

      method: "get",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/withdraw/${id}`,
      method: "patch",
      data,
    }),
  approve: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/withdraw/${id}/approve`,
      method: "patch",
      data,
    }),
  processing: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/withdraw/${id}/processing`,
      method: "patch",
      data,
    }),
  reject: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/withdraw/${id}/reject`,
      method: "delete",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/withdraw/${id}`,
      method: "delete",
    }),
  approveAll: (data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/withdraw/approve/batch`,
      method: "patch",
      data,
    }),
  processingAll: (data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/withdraw/processing/batch`,
      method: "patch",
      data,
    }),
  rejectAll: (data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/withdraw/reject/batch`,
      method: "delete",
      data,
    }),
};
