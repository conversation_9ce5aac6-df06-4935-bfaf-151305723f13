import { isVisible } from "@testing-library/user-event/dist/utils";
import { TelegramGroup } from "./telegramGroup";

export enum DerivativeStockProfitType {
  Lai = "LAI",
  Lo = "LO",
}
export const DerivativeStockProfitTypeTrans = {
  [DerivativeStockProfitType.Lai]: "LAI",
  [DerivativeStockProfitType.Lo]: "LO",
};

export interface DerivativeStock {
  id: number;
  createdAt: number;
  updatedAt: number;
  isDeleted: boolean;
  deletedAt: number;
  content: string;
  isVisible: boolean;
  isPublic: boolean;
  profit: number;
  profitType: DerivativeStockProfitType;
  telegramGroup: TelegramGroup;
  child: DerivativeStock;
  parent: DerivativeStock;
  value: number;
  //check là lệnh đóng thì tính lãi/lỗ
}
