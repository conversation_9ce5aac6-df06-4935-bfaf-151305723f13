import { request } from "utils/request";
import { AxiosPromise } from "axios";

export const telegramLogApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/telegramLog",
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/telegramLog",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/telegramLog/${id}`,
      method: "patch",
      data,
    }),
  active: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/telegramLog/${id}/active`,
      method: "patch",
      data,
    }),

  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/telegramLog/${id}`,
      method: "delete",
    }),
};
