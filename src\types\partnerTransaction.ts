import { Partner } from "./partner";
import { Review } from "./review";
import { Withdraw } from "./withdraw";

export enum PartnerTransactionType {
  Withdraw = "WITHDRAW",
  CancelWithdraw = "CANCEL_WITHDRAW",
  Review = "REVIEW",
}

export interface PartnerTransaction {
  id: number;
  createdAt: number;
  updatedAt: number;
  isDeleted: boolean;
  code: string;
  type: PartnerTransactionType;
  note: string;
  beforeChange: number;
  afterChange: number;
  change: number;
  date: string;
  refId: string;
  partner: Partner;
  withdraw: Withdraw;
  review: Review;
}
