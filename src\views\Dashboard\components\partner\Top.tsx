import React from "react";
import { Row, Col, Spin } from "antd";
import clsx from "clsx";
import { formatVND } from "utils";
import { settings } from "settings";
import { useMediaQuery } from "@mui/material";
import { useTranslation } from "react-i18next";

import topPartner1 from "assets/svgs/top-partner-1.svg";
import topPartner2 from "assets/svgs/top-partner-2.svg";
import topPartner3 from "assets/svgs/top-partner-3.svg";
import trophy from "assets/svgs/trophy.svg";
import { ReactComponent as MoneyIcon } from "assets/svgs/money.svg";

interface PartnerItem {
  avatar: string;
  fullname?: string;
  name?: string;
  id: number;
  rankName?: string;
  totalRewardPoint?: string;
  totalPay?: string;

}

interface Props {
  partners: PartnerItem[];
  loading: boolean
  title?: string
  hideIconTop?: boolean
  moneyIconColor?: string;
  typePartner?: boolean
}

const TopPartnerList: React.FC<Props> = ({ partners, loading, title, hideIconTop = false, typePartner = false }) => {
  const mdUp = useMediaQuery((theme: any) => theme.breakpoints.up("md"));
  const { t } = useTranslation();

  return (

    <div
      className="relative rounded-md px-[12px] py-[24px] md:p-[28px_22px_28px_22px] order-2 md:order-1 bg-[#FEEA78] flex flex-col gap-[14px]"
      style={{
        boxShadow: "0px 4px 12px -2px #919EAB1A, 0 0 2px 0 #919EAB1A",
        minHeight: "500px"
      }}
    >
      <Spin spinning={loading} >
        <h5 className="relative semibold"
          style={{ marginBottom: !hideIconTop ? 52 : 21 }}>
          {title}
        </h5>

        <div className="relative z-10 top-partner-scrollbar overflow-y-auto flex flex-col gap-1 px-[8px] mt-[42px]">
          {partners.map((partner, i) => {
            const bgColor =
              i === 0
                ? "#FFFCE8"
                : i === 1
                  ? "#F6F9FC"
                  : i === 2
                    ? "#FFF9EF"
                    : "white";

            const rankIcon =
              i === 0
                ? topPartner1
                : i === 1
                  ? topPartner2
                  : i === 2
                    ? topPartner3
                    : null;

            return (
              <div
                key={i}
                className={clsx(
                  "flex relative px-[20px] py-[8px] rounded-[8px] justify-between",
                  mdUp ? "" : "gap-[21px]"
                )}
                style={{ backgroundColor: bgColor }}
              >
                <div className="flex gap-[20px] items-center">
                  {mdUp ? (
                    <>
                      {rankIcon ? (
                        <img src={rankIcon} />
                      ) : (
                        <div className="w-[30px] h-[30px] flex justify-center items-center bg-[#EAEFF4] rounded-full text-[12px]">
                          {i + 1}
                        </div>
                      )}
                      <img
                        src={partner.avatar || settings.defaultAvatar}
                        className="w-[36px] h-[36px] rounded-full"
                      />
                    </>
                  ) : (
                    <div className="relative flex-shrink-0">
                      <div className="absolute -left-[8px] bottom-[2px]">
                        {rankIcon ? (
                          <img src={rankIcon} className="w-[26px] h-[32px]" />
                        ) : (
                          <div className="w-[30px] h-[30px] flex justify-center items-center bg-[#EAEFF4] rounded-full text-[12px]">
                            {i + 1}
                          </div>
                        )}
                      </div>
                      <img
                        src={partner.avatar || settings.defaultAvatar}
                        className="w-[55px] h-[55px] rounded-full"
                      />
                    </div>
                  )}
                  <div className="flex flex-col gap-[2px]">
                    <div className="text-medium">
                      {partner.fullname ?? partner.name}
                    </div>
                    <div
                      className={`text-caption-no-caps-regular`}
                    >
                      {partner.rankName}
                    </div>
                    {!mdUp && (
                      <div className="flex items-center gap-1 mt-2">
                        {!typePartner ? <MoneyIcon className={`size-[20px] [&_path]:stroke-[#FFCD00]`} /> : <MoneyIcon className={`size-[20px] [&_path]:stroke-[#41D664]`} />}
                        <div>{formatVND(partner.totalRewardPoint || 0)}₫</div>
                      </div>
                    )}
                  </div>
                </div>
                {mdUp && (
                  <div className="flex items-center gap-1">
                    {!typePartner ? <MoneyIcon className={`size-[20px] [&_path]:stroke-[#FFCD00]`} /> : <MoneyIcon className={`size-[20px] [&_path]:stroke-[#41D664]`} />}
                    <div>{formatVND(partner.totalRewardPoint || partner.totalPay || 0)}₫</div>
                  </div>
                )}
              </div>
            );
          })}
        </div>

        {!hideIconTop && <img
          src={trophy}
          className="hidden md:block absolute right-[30.45px] top-[11px] z-0"
        />
        }
      </Spin>
    </div>
  );
};

export default TopPartnerList;
