import { faqCategoryApi } from "api/faqCategory.api";
import { debounce } from "lodash";
import { useState } from "react";
import { FaqCategory } from "types/faq";
import { QueryParam } from "types/query";

export interface FaqCategoryQuery extends QueryParam {}

interface UsefaqCategoryProps {
  initQuery: FaqCategoryQuery;
}

export const useFaqCategory = ({ initQuery }: UsefaqCategoryProps) => {
  const [data, setData] = useState<FaqCategory[]>([]);
  const [total, setTotal] = useState(0);
  const [query, setQuery] = useState<FaqCategoryQuery>(initQuery);
  const [loading, setLoading] = useState(false);

  const fetchData = async () => {
    setLoading(true);
    try {
      const { data } = await faqCategoryApi.findAll(query);

      setData(data.faqCategories);
      setTotal(data.total);
    } finally {
      setLoading(false);
    }
  };

  const debounceSearchFAQ = debounce((search: string) => {
    query.current.page = 1;
    query.current.search = search;
    fetchData();
  }, 400);

  return {
    faqCategories: data,
    totalFaqCategory: total,
    fetchFaqCategory: fetchData,
    loadingFaqCategory: loading,
    setQueryFaqCategory: setQuery,
    queryFaqCategory: query,
    debounceSearchFAQ,
  };
};
