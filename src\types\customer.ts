import { Project } from "./project";

export enum Language {
  Vi = "VI",
  En = "EN",
}

export interface Bank {
  id: number;
  createdAt: number;
  updatedAt: number;
  isDeleted: boolean;
  name: string;
  code: string;
  fullName: string;
  logo: string;
  syncId: string;
}

// export enum CustomerType {
//   Customer = 'CUSTOMER',
//   CompanyAgent = 'COMPANY'
// }

export enum CustomerStatus {
  Active = "ACTIVE",
  Blocked = "BLOCKED",
}

// export const CustomerStatusTrans = {
//   [CustomerStatus.Active]: {
//     label: "Đang mở",
//     value: CustomerStatus.Active,
//   },
//   [CustomerStatus.Blocked]: {
//     label: "Đã chặn",
//     value: CustomerStatus.Blocked,
//   },
// };

export const CustomerStatusTrans = {
  [CustomerStatus.Active]: {
    label: "Đang mở",
    value: CustomerStatus.Active,
    color: "green",
  },
  [CustomerStatus.Blocked]: {
    label: "Đã chặn",
    value: CustomerStatus.Blocked,
    color: "red",
  },
};

export interface Customer {
  id: number;
  createdAt: number;
  updatedAt: number;
  isDeleted: boolean;
  code: string;
  status: CustomerStatus;
  // type: CustomerType;
  balance: number;
  balanceVersion: number;
  name: string;
  avatar: string;
  phone: string;
  email: string;
  password: string;
  address: string;
  taxCode: string;
  companyName: string;
  companyEmail: string;
  companyAddress: string;
  bankAccountNumber: string; //stk ngân hàng
  idNumber: string; //số  cccd
  idFrontImage: string;
  idBackImage: string;
  language: Language;
  bank: Bank;
  projects: Project[];
  totalProduct: number;
  totalProjectPaymentComplete?: number;
}
