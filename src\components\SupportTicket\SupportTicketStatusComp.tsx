import { Tag } from "antd";
import { useTranslation } from "react-i18next";
import "./styles/SupportTicketComp.scss";
import clsx from "clsx";
import {
  SupportTicketStatus,
  SupportTicketStatusTrans,
} from "types/supportTicket";

interface Props {
  status: SupportTicketStatus;
}

const getStatusColorClass = (status: SupportTicketStatus): string => {
  switch (status) {
    case SupportTicketStatus.New:
      return "orange-status-color";
    case SupportTicketStatus.Processing:
      return "orange-status-color";
    case SupportTicketStatus.Responded:
      return "green-status-color";
    case SupportTicketStatus.Complete:
      return "green-status-color";
    default:
      return "gray-status-color";
  }
};

export const SupportTicketStatusComp = ({ status }: Props) => {
  const { t } = useTranslation();

  return (
    <Tag
      className={clsx("support-ticket", status, getStatusColorClass(status))}
    >
      {t("supportTicket" + status)}
    </Tag>
  );
};
