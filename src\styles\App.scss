:root {
  --color-primary: #1a73e8;
}

.tox-notifications-container {
  display: none !important;
}

.font-bold {
  font-weight: bold;
}

.text-18 {
  font-size: 18px !important;
}

img {
  vertical-align: middle;
}

.upload-list-custom {
  .ant-upload-list {
    float: left !important;
  }
}

img.full {
  width: 100%;
  height: 100%;
}

.w-100 {
  width: 100%;
}

.text-blue {
  color: #00a751;
}

.text-error {
  color: red;
}

.text-success {
  color: green;
}

label {
  font-weight: 500;
}

.text-center {
  text-align: center;
}

.filter-container {
  margin-bottom: 24px;
  display: flex;
  flex-wrap: wrap;
  gap: 12px;

  .filter-item {
    label {
      display: inline-block;
      font-weight: 500;
      font-size: 15px;
      padding-bottom: 8px;
    }

    &.btn {
      button {
        position: relative;
        top: 16px;
      }
    }
  }
}

.btn-payment {
  color: white !important;
  border: 1px solid #fd853a !important;
  background-color: #fd853a !important;
  border-radius: 8px !important;
  font-weight: 600 !important;

  &:hover,
  &:focus {
    background-color: #fd853a !important;
    border-color: #fd853a !important;
    color: white !important;
  }

  &:active {
    background-color: #e8772e !important;
    border-color: #e8772e !important;
  }

  &.ant-btn-loading {
    background-color: #fd853a !important;
    border-color: #fd853a !important;
  }

  &:disabled {
    color: #00000040 !important;
    background-color: #0000000a !important;
    opacity: 1 !important;
    border-color: #d9d9d9 !important;
  }
}

.upload-video {
  .video-item {
    width: 104px;
    height: 104px;

    padding: 8px;
    border: 1px solid #d9d9d9;
    border-radius: 10px;
    cursor: pointer;
    position: relative;

    video {
      object-fit: cover;
      width: 100%;
      height: 100%;
      border-radius: 4px;
    }

    &:hover {
      .pseudo {
        opacity: 1;
      }
    }

    .pseudo {
      transition: opacity 0.3s;
      position: absolute;
      inset: 0;
      padding: 8px;
      opacity: 0;

      .actions {
        height: 100%;
        background-color: rgba($color: #000000, $alpha: 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;

        span {
          color: white;
          font-size: 16px;
        }
      }
    }
  }
}

.text-one-line {
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  max-width: 200px;
  white-space: pre-wrap;
  overflow: hidden;
  display: -webkit-box;
}

.wheel-piece-img {
  width: 100px !important;
  height: 100px !important;
  object-fit: cover;
  object-position: center -150px;
}

.input-no-style {
  border: unset !important;
  pointer-events: none;
}

.cursor-pointer {
  cursor: pointer;
}

.text-underline-hover:hover {
  text-decoration: underline;
}

//Winwheel game

#woay-wheel {
  border-radius: 50%;
}

.winwheel {
  &-bg {
    position: fixed;
    inset: 0;
    width: 100vw;
    height: 100vh;
    object-fit: cover;
    z-index: -1;

    img {
      height: 100%;
      width: 100%;
    }
  }

  .page-title {
    img {
      width: 60%;
      max-width: 400px;
    }
  }
}

.game-actions {
  text-align: center;
  margin-top: 50px;
}

.woay-box-wheel {
  margin: auto;
  margin-top: 30px;
  margin-bottom: 30px;
  max-width: 720px;
}

.woay-box-wheel .woay-bg {
  position: relative;
  border-radius: 50%;
  padding-bottom: 100%;
}

.woay-box-wheel #woay-wheel {
  position: absolute;
  width: 92%;
  left: 4%;
  top: 4%;
}

.woay-box-wheel .woay-border {
  position: absolute;
  width: 100%;
  left: 0;
  top: 0;
}

.woay-box-wheel .btn-woay {
  position: absolute;
  width: 20%;
  padding-bottom: 20%;
  left: 40%;
  top: 40%;
  cursor: pointer;
}

.woay-box-wheel .btn-woay .woay-button-bg {
  position: absolute;
  width: 100%;
  left: 0;
  top: 0;
}

.woay-box-wheel .btn-woay .woay-button-text {
  width: 70%;
  position: absolute;
  left: 15%;
  top: 15%;
  height: 70%;
  background-image: url("");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: contain;
}

.woay-box-wheel .woay-arrow {
  position: absolute;
  width: 12%;
  left: 44%;
  top: 0;
}

.product-category-table {
  .ant-table-column-sorter {
    display: none;
  }
}

// h2 {
//   font-weight: 600;
//   font-size: 30px;
//   line-height: 120%;
//   letter-spacing: -1%;
//   color: #2a3547;
// }

// h6 {
//   font-weight: 400;
//   font-size: 16px;
//   line-height: 120%;
//   letter-spacing: 0%;
//   color: #5a6a85;
//   height: 19px;
//   margin: 0;
// }

.rivi-small {
  .ql-toolbar.ql-snow {
    border-radius: 6px 6px 0 0;
    border-color: #eaeff4;
    color: white;
  }

  .ql-toolbar.ql-snow + .ql-container.ql-snow {
    height: 200px;
    border-radius: 0 0 6px 6px;
    border-color: #eaeff4;
  }
}

.ant-input-outlined {
  border-color: #eaeff4 !important;
}

.ant-picker {
  border-color: #eaeff4 !important;
}

.ant-select-selector {
  border-color: #eaeff4 !important;
}

.ant-btn-primary span {
  color: #eff5ff !important;
}

.ant-dropdown-trigger .text-primary {
  color: #1a73e8 !important;
  font-weight: 400 !important;
  font-size: 14 !important;

  .font-semibol {
    font-weight: 400 !important;
  }
}

// .ant-select-selector {
//   border: 1px solid #eaeff4 !important;
// }

.ant-table-cell {
  color: #2a3547 !important;
}

label {
  color: #2a3547;
}

.list-mission {
  thead tr {
    font-size: 16px;
    font-weight: 600;
    color: #2a3547;
  }
}

.action-mission-text {
  color: #eff5ff;
  font-size: 12px !important;
  font-weight: 400 !important;
}

.limit-lines {
  display: -webkit-box;
  -webkit-line-clamp: 5;
  /* Giới hạn 5 dòng */
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}
