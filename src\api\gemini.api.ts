import { request } from "utils/request";
import { AxiosPromise } from "axios";

export const geminiApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/gemini",
      params,
    }),
  getReviews: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/gemini",
      data,
      method: "post",
      timeout: 1000 * 60 * 15,
    }),
  getKeywords: (data: any, lang?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/gemini/keywords",
      data,
      method: "post",
      timeout: 1000 * 60 * 15,
      headers: lang ? { lang } : {},
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/gemini/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/gemini/${id}`,
      method: "delete",
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/gemini",
      data,
      method: "post",
    }),
};
