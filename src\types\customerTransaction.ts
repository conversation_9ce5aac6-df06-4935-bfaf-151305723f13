import { Customer } from "./customer";
import { Deposit } from "./deposit";

export enum CustomerTransactionType {
  Deposit = "DEPOSIT",
  Withdraw = "WITHDRAW",
  CancelWithdraw = "CANCEL_WITHDRAW",
  PaymentOrder = "PAYMENT_ORDER",
  //   Project = "PROJECT",
}

export const CustomerTransactionTypeTrans = {
  [CustomerTransactionType.Deposit]: {
    value: CustomerTransactionType.Deposit,
    color: "blue",
  },
  [CustomerTransactionType.Withdraw]: {
    value: CustomerTransactionType.Withdraw,
    color: "orange",
  },
  [CustomerTransactionType.CancelWithdraw]: {
    value: CustomerTransactionType.CancelWithdraw,
    color: "red",
  },
  [CustomerTransactionType.PaymentOrder]: {
    value: CustomerTransactionType.PaymentOrder,
    color: "green",
  },
  //   [CustomerTransactionType.Project]: {
  //     value: CustomerTransactionType.Project,
  //     color: "purple",
  //   },
};

export interface CustomerTransaction {
  id: number;
  createdAt: number;
  updatedAt: number;
  isDeleted: boolean;
  code: string;
  beforeChange: number;
  change: number;
  afterChange: number;
  isCompleted: boolean;
  expireBlockedAt: number;
  type: CustomerTransactionType;
  customer: Customer;
  deposit: Deposit;
}
