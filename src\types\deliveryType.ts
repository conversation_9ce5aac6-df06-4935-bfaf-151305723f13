export interface DeliveryType {
  id: number;
  name: string;
  type: EDeliveryType;
}

export enum EDeliveryType {
  Store = "STORE", //Nhận hàng tại cửa hàng
  ToDoor = "TO_DOOR", //Giao tận nơi
}

export const deliveryTypes = [EDeliveryType.Store, EDeliveryType.ToDoor];

export const DeliveryTypeTrans = {
  [EDeliveryType.Store]: {
    title: "Nhận tại cửa hàng",
    value: EDeliveryType.Store,
    color: "blue",
  },
  [EDeliveryType.ToDoor]: "Giao tận nơi",
  [EDeliveryType.ToDoor]: {
    title: "Giao tận nơi",
    value: EDeliveryType.ToDoor,
    color: "yellow",
  },
};
