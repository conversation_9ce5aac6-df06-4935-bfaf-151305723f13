import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Card, Row, Col, Select, Spin } from 'antd';
import * as echarts from 'echarts';
import { WhiteCircleIcon } from "assets/svgs/DotSvg";
import { useDashboard } from 'hooks/useDashboard';
import { useTranslation } from 'react-i18next';
import dayjs from 'dayjs';
import { colors, DateType, KeysTotalChart } from 'types/dashboard';

const ProjectDashboard: React.FC = () => {
  const { t } = useTranslation();
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts | null>(null);

  const { fetchSummaryTotal, loadingSummaryTotal, summaryTotal } = useDashboard();

  const timeOptions = [
    {
      value: DateType.Day,
      label: t("dayNumber", { number: 30 }),
      fromAt: dayjs().subtract(30, "day").startOf("day").unix(),
      toAt: dayjs().endOf("day").unix(),
    },
    {
      value: DateType.Month,
      label: t("monthNumber", { number: 12 }),
      fromAt: dayjs().subtract(12, "month").startOf("month").unix(),
      toAt: dayjs().endOf("day").unix(),
    },
    {
      value: DateType.Year,
      label: t("yearNumber", { number: 5 }),
      fromAt: dayjs().subtract(5, "year").startOf("year").unix(),
      toAt: dayjs().endOf("day").unix(),
    },
  ];

  const [timeSelect, setTimeSelect] = useState(timeOptions[1]);

  const generateTimeRange = (
    from: number,
    to: number,
    type: DateType
  ): string[] => {
    const result: string[] = [];
    let current = dayjs.unix(from);
    const end = dayjs.unix(to);

    while (current.isBefore(end) || current.isSame(end, type.toLowerCase() as any)) {
      let formatted = "";

      if (type === DateType.Day) {
        formatted = current.format("YYYY-MM-DD");
        current = current.add(1, "day");
      } else if (type === DateType.Month) {
        formatted = current.format("YYYY-MM");
        current = current.add(1, "month");
      } else if (type === DateType.Year) {
        formatted = current.format("YYYY");
        current = current.add(1, "year");
      }

      result.push(formatted);
    }

    return result;
  };

  const normalizedSummaryTotal = useMemo(() => {
    const timePoints = generateTimeRange(timeSelect.fromAt, timeSelect.toAt, timeSelect.value);

    const summaryMap = new Map(summaryTotal.map(item => [item.date, item]));

    return timePoints.map(date => {
      return {
        date,
        totalProjects: summaryMap.get(date)?.totalProjects || 0,
        totalCustomers: summaryMap.get(date)?.totalCustomers || 0,
        completed: summaryMap.get(date)?.completed || 0,
        inProgress: summaryMap.get(date)?.inProgress || 0,
        pending: summaryMap.get(date)?.pending || 0,
        stopped: summaryMap.get(date)?.stopped || 0
      };
    });
  }, [summaryTotal, timeSelect]);

  const totalStats = useMemo(() => {
    return Object.values(KeysTotalChart).reduce((acc, key) => {
      acc[key] = summaryTotal.reduce((sum, item) => sum + item[key], 0);
      return acc;
    }, {} as Record<string, number>);
  }, [summaryTotal]);

  const [visibleSeries, setVisibleSeries] = useState<Record<string, boolean>>(
    Object.values(KeysTotalChart).reduce((acc, key) => ({ ...acc, [key]: true }), {})
  );

  const defaultSeries = useMemo(() => {
    return Object.values(KeysTotalChart).filter((key) => !!visibleSeries[key]).map((key) => ({
      id: key,
      name: key,
      type: "line",
      data: normalizedSummaryTotal.map(item => item[key]),
      smooth: true,
      lineStyle: { color: colors[key] },
      itemStyle: {
        color: "white",
        borderWidth: 2,
        borderColor: colors[key]
      },
      symbol: "circle",
      symbolSize: 10
    }));
  }, [normalizedSummaryTotal, visibleSeries]);


  const [series, setSeries] = useState(defaultSeries);

  useEffect(() => {
    fetchSummaryTotal({
      type: timeSelect.value,
      fromAt: timeSelect.fromAt,
      toAt: timeSelect.toAt
    });
  }, [timeSelect]);

  useEffect(() => {
    setSeries(defaultSeries);
  }, [defaultSeries]);

  const toggleSeries = (key: string) => {
    const isVisible = visibleSeries[key];
    setVisibleSeries(prev => ({ ...prev, [key]: !isVisible }));

    setSeries(prev => {
      if (isVisible) {
        return prev.filter(s => s.id !== key);
      } else {
        const item = defaultSeries.find(s => s.id === key);
        return item ? [...prev, item] : prev;
      }
    });
  };

  useEffect(() => {
    if (!chartRef.current) return;

    const chart = echarts.init(chartRef.current);
    chartInstance.current = chart;

    chart.setOption({
      tooltip: {
        trigger: 'axis',
        backgroundColor: '#ffffff',
        borderColor: '#e0e0e0',
        borderWidth: 1,
        textStyle: { color: '#000000' },
        axisPointer: {
          type: 'line',
          lineStyle: { color: '#2A3547', width: 1, type: [8, 6] },
          z: 1
        },
        formatter(params: any) {
          const title = params[0].axisValue;
          const rows = params
            .sort((a: any, b: any) => b.value - a.value)
            .map((p: any) => `
              <div style="display: flex; align-items: center; gap: 8px;">
                <span style="width: 10px; height: 10px; border-radius: 50%; background: ${p.borderColor};"></span>
                <span style="font-size: 12px;">${p.value}</span>
              </div>`).join('');
          return `<div style="padding: 4px 8px; min-width: 124px; border-radius: 8px;">
            <div style="font-size: 12px;">${title}</div>
            <div style="height: 1px; border: 1px solid #EAEFF4; margin: 4px 0;"></div>
            ${rows}
          </div>`;
        }
      },
      legend: { show: false },
      grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true, top: 80 },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: normalizedSummaryTotal.map(item => {
          const day = dayjs(item.date);
          if (timeSelect.value === DateType.Day) return day.format("DD/MM");
          if (timeSelect.value === DateType.Month) return day.format("M/YYYY");
          if (timeSelect.value === DateType.Year) return day.format("YYYY");
          return item.date;
        }),
        axisTick: { show: false },
        axisLabel: {
          margin: 26, fontWeight: 400, fontSize: 12, color: '#7C8FAC'
        },
        axisLine: {
          lineStyle: { type: 'dashed', color: '#DFE5EF' }
        }
      },
      yAxis: {
        type: 'value',
        // max: 1000,
        axisLabel: {
          margin: 16, fontWeight: 400, fontSize: 12, color: '#7C8FAC'
        },
        splitLine: {
          lineStyle: { type: 'dashed', color: '#DFE5EF' }
        }
      },
      series
    });

    const resizeHandler = () => chart.resize();
    window.addEventListener('resize', resizeHandler);

    return () => {
      chart.dispose();
      window.removeEventListener('resize', resizeHandler);
    };
  }, [series]);

  return (
    <Card className="!border-[#EAEFF4]">
      <Row justify="space-between" className="mb-[20px]" align="middle">
        <Col span={12} className="font-semibold text-[18px] text-black">
          <h5 className="semibold">
            {t("project")}
          </h5>

        </Col>
        <Col>
          <Select size="large"
            defaultValue={timeSelect.value}
            onChange={(value) => {
              const opt = timeOptions.find(opt => opt.value === value);
              if (opt) setTimeSelect(opt);
            }}
            style={{ width: 151, height: 42 }}
          >
            {timeOptions.map(opt => (
              <Select.Option key={opt.value} value={opt.value}>
                {opt.label}
              </Select.Option>
            ))}
          </Select>
        </Col>
      </Row>
      <Spin spinning={loadingSummaryTotal}>
        <Row gutter={[1, 1]} className="px-0">
          {Object.entries(totalStats).map(([key, value]) => (
            <Col key={key} md={4} xs={12}>
              <div
                onClick={() => toggleSeries(key)}
                style={{
                  backgroundColor: visibleSeries[key] ? colors[key as KeysTotalChart] : "white",
                  color: visibleSeries[key] ? 'white' : "#2A3547",
                  cursor: 'pointer',
                  padding: "17px 12px",
                  height: 81,
                  borderRight: visibleSeries[key] ? "" : "1px solid #EAEFF4"
                }}
                className="flex flex-col gap-2"
              >
                <div className="flex gap-2 items-center">
                  <WhiteCircleIcon fill={visibleSeries[key] ? "white" : colors[key as KeysTotalChart]} />
                  <div className="text-caption-no-caps-regular">{t(key)}</div>
                </div>
                <h5 className="semibold">{value}</h5>
              </div>
            </Col>
          ))}
        </Row>

        <div ref={chartRef} style={{ width: '100%', height: 500, marginBottom: 20 }} />
      </Spin>
    </Card>
  );
};

export default ProjectDashboard;
