import { DeleteOutlined, EyeOutlined, PlusOutlined } from "@ant-design/icons";
import { message, Modal, Popconfirm, Progress, Spin, Upload } from "antd";
import { FormInstance } from "antd/es/form/Form";
import { UploadFile } from "antd/lib/upload/interface";
import { debounce } from "lodash";
import { useEffect, useMemo, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { formatVND } from "utils";
import { getToken } from "utils/auth";
import { $url } from "utils/url";

interface UploadVideoProps {
  onUpload: (files: string[] | string) => void;
  multiple?: boolean;
  maxCount?: number;
  action?: string;
  maxFileSize?: number;
  fileList?: string[];
}

const UploadVideo = ({
  onUpload,
  multiple,
  maxCount = 1,
  maxFileSize,
  fileList,
  action = `${import.meta.env.VITE_API_URL}/v1/admin/media/upload/video`,
}: UploadVideoProps) => {
  const { t } = useTranslation();

  const [previewVisible, setPreviewVisible] = useState<boolean>(false);
  const previewLink = useRef<string>();
  const uploadVideoRef = useRef<any>();

  const uploadButton = (
    <div>
      <PlusOutlined />
      <div style={{ marginTop: 8 }}>Upload</div>
    </div>
  );

  const handleOnUploadError = debounce((file, action) => {
    message.error(file.response?.message);
    action.remove();
  }, 500);

  const beforeUpload = async (file: File) => {
    const isVideo = file.type.includes("video");
    let validFileSize = true;
    if (!isVideo) {
      message.error(t("videoRequired"));
    } else if (maxFileSize && file.size > maxFileSize) {
      validFileSize = false;
      message.error(
        t("fileSizeInvalid", {
          number: formatVND(Number((maxFileSize / 1024 / 1024).toFixed(2))),
        })
      );
    }
    return (validFileSize && isVideo) || Upload.LIST_IGNORE;
  };

  const handleChange = ({ file }: { file: UploadFile<any> }) => {
    if (file.status === "done" || file.status === "removed") {
      let files = [];
      if (multiple) {
        files = uploadVideoRef.current.fileList.map((file: any) =>
          $url(file?.response?.data?.path || file.url)
        );
      } else {
        const url =
          file.status === "removed"
            ? ""
            : file?.response?.data?.path || file.url;

        files = [$url(url)];
      }
      onUpload(files);
    }
  };

  return (
    <>
      <Upload
        accept="video/*"
        ref={uploadVideoRef}
        className="upload-video"
        multiple={multiple}
        defaultFileList={
          fileList
            ? fileList
                .filter((file) => !!file)
                .map((file) => ({ uid: file, url: file, name: file }))
            : []
        }
        // fileList={fileListRender}
        headers={{ token: getToken() || "" }}
        action={action}
        maxCount={multiple ? maxCount : 1}
        listType="picture-card"
        beforeUpload={beforeUpload}
        onChange={handleChange}
        onPreview={(file) => {
          //@ts-ignore
          const url = file?.response?.data?.path
            ? $url(file?.response?.data?.path)
            : file.url;
          previewLink.current = url;
          setPreviewVisible(true);
        }}
        itemRender={(originNode, file, files, action) => {
          if (file?.response?.data?.path || file.url) {
            const url = file?.response?.data?.path
              ? $url(file?.response?.data?.path)
              : file.url;
            return (
              <div className="video-item">
                <video>
                  <source src={url} type="video/mp4"></source>
                </video>
                <div className="pseudo">
                  <div className="actions">
                    <EyeOutlined onClick={action.preview} />
                    <Popconfirm
                      placement="topLeft"
                      title={`Xác nhận xóa video này?`}
                      onConfirm={() => action.remove()}
                      okText="Xóa"
                      cancelText="Không"
                    >
                      <DeleteOutlined />
                    </Popconfirm>
                  </div>
                </div>
              </div>
            );
          } else {
            if (file.status === "uploading") {
              return (
                <div className="video-item">
                  <div
                    className="pseudo"
                    style={{
                      opacity: 1,
                    }}
                  >
                    <p>Đang tải lên</p>
                    <div
                      className="actions"
                      style={{
                        flexDirection: "column",
                        backgroundColor: "white",
                      }}
                    >
                      <Progress
                        style={{
                          width: "100%",
                        }}
                        percent={file.percent || 0}
                        size="small"
                      />
                    </div>
                  </div>
                </div>
              );
            }
            if (file.response?.message) {
              handleOnUploadError(file, action);
            }
          }
        }}
      >
        {uploadButton}
      </Upload>

      <Modal
        visible={previewVisible}
        footer={null}
        title="Preview Video"
        destroyOnClose
        width={800}
        onCancel={() => {
          setPreviewVisible(false);
          previewLink.current = "";
        }}
      >
        <video style={{ width: "100%" }} autoPlay controls>
          <source src={previewLink.current} />
        </video>
      </Modal>
    </>
  );
};

export default UploadVideo;
