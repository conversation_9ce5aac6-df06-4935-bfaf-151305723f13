import {
  CloseOutlined,
  DownOutlined,
  EditFilled,
  ImportOutlined,
  PlusOutlined,
} from "@ant-design/icons";
//@ts-ignore
import { Button, Card, message, Popconfirm, Space, Spin, Table } from "antd";
import { roleApi } from "api/role.api";
import { $isDev } from "constant";
import React, { useEffect, useMemo, useState } from "react";
import { adminRoutes } from "router";
import { ModalStatus } from "types/modal";
import { QueryParam } from "types/query";
import { Permission, Role } from "types/role";
import { getTitle } from "utils";
import { checkRoles } from "utils/auth";
import { RoleModal } from "./components/RoleModal";
import { userStore } from "store/userStore";
import DropdownCell from "components/Table/DropdownCell";
import { AriviTable } from "components/Table/AriviTable";
import { useTranslation } from "react-i18next";
import { ReactComponent as PlusIcon } from "assets/svgs/plus-icon.svg";

const { ColumnGroup, Column } = Table;

export const RolePage = ({ title = "" }) => {
  const [query, setQuery] = useState<QueryParam>({
    page: 1,
    limit: 10,
    search: "",
  });
  const [loading, setLoading] = useState(false);
  const [roles, setRoles] = useState<Role[]>([]);
  const [total, setTotal] = useState(0);
  const [visibleModal, setVisibleModal] = useState(false);
  const [selectedRole, setSelectedRole] = useState<Partial<Role>>({});
  const [modalStatus, setModalStatus] = useState<ModalStatus>("create");
  const [loadingImport, setLoadingImport] = useState(false);
  const [merchantPermissions, setMerchantPermissions] = useState<Permission[]>(
    []
  );
  const [adminPermissions, setAdminPermissions] = useState<Permission[]>([]);
  const { t } = useTranslation();

  useEffect(() => {
    document.title = getTitle(t(title));
    fetchPermissions();
  }, []);

  useEffect(() => {
    fetchData();
  }, [query]);

  const rolesCheck = useMemo(
    () =>
      checkRoles<{
        block: string;
        resetPassword: string;
      }>({
        create: "role-create-role",
        detail: "role-detail-role",
        block: "role-block-role",
        resetPassword: "role-reset-pass-role",
        update: "role-update-role",
        delete: "role-delete-role",
      }),
    []
  );

  const fetchPermissions = async () => {
    const { data } = await roleApi.getPermission();
    setAdminPermissions(data);
  };

  const fetchData = async () => {
    setLoading(true);
    const res = await roleApi.findAll(query);
    setLoading(false);
    setRoles(res.data.roles);
    setTotal(res.data.total);
  };

  const handleDeleteRole = async (roleId: number) => {
    try {
      const res = await roleApi.delete(roleId);
      fetchData();
      message.success(t("actionSuccessfully"));
    } catch (error) {}
  };

  const handleImportAdminRoutes = async () => {
    const permissions: Permission[] = [];

    for (const route of adminRoutes) {
      //bo qua public
      if (route.isPublic){
        continue
      }

      if (route.children) {
        for (const childRoute of route.children) {
          //   debugger;

          const find = adminPermissions.find((e) => e.name == childRoute.name);
          const path = route.path + "/" + childRoute.path;
          const id = find?.id;
          permissions.push({
            path,
            //@ts-ignore
            id,
            name: find?.name || (childRoute?.name as string),
          });
        }
      } else {
        const find = adminPermissions.find((e) => e.name == route.name);
        const path = route.path;
        const id = find?.id;

        permissions.push({
          path: path as string,
          //@ts-ignore
          id,
          name: find?.name || (route.name as string),
        });
      }
    }

    setLoadingImport(true);

    await roleApi.importPermission({
      permissions,
    });
    message.success(t("actionSuccessfully"));
    fetchData();
    // fetchPermissions();
    setLoadingImport(false);
  };

  return (
    // <Card bodyStyle={{ padding: "8px 20px" }} style={{ borderRadius: "8px" }}>
    <div>
      <div className="filter-container">
        {/* <Space>
          <React.Fragment key={1}>
            {$isDev && (
              <Button
                onClick={handleImportAdminRoutes}
                type="primary"
                icon={<ImportOutlined />}
                loading={loadingImport}
                size="middle"
              >
                Import routes
              </Button>
            )}
          </React.Fragment>
          <Button
            key={2}
            onClick={() => {
              setVisibleModal(true);
              setModalStatus("create");
              setSelectedRole({});
            }}
            type="primary"
            size="middle"
            icon={<PlusOutlined />}
          >
            {t("create")}
          </Button>
        </Space> */}
      </div>
      <div className="b-container">
        <Spin spinning={loading}>
          <AriviTable
            size="small"
            className="table-striped-rows"
            pagination={false}
            rowKey="id"
            dataSource={roles}
          >
            <Column
              title={
                <label className="text-[16px] !text-[#2A3547]">
                  {t("roleShort")}
                </label>
              }
              // dataIndex="name"
              render={(text, record: Role) => (
                <label className="text-[#2A3547] text-[14px] font-medium">
                  {record.name}
                </label>
              )}
            />
            <Column
              title={
                <label className="text-[16px] !text-[#2A3547]">
                  {t("description")}
                </label>
              }
              dataIndex="description"
              render={(text, record: any) => (
                <label className="text-[#2A3547] text-[14px] !font-[400]">
                  {record.description}
                </label>
              )}
            />

            {/* <Column
              align="center"
              width={150}
              title="Thao tác"
              key="action"
              render={(text, record: Role) => (
                <span>
                  {rolesCheck.update && (
                    <Button
                      disabled={!userStore.info.role?.isAdmin}
                      type="primary"
                      onClick={() => {
                        setSelectedRole(record);
                        setVisibleModal(true);
                        setModalStatus("update");
                      }}
                    >
                      Cập nhật
                    </Button>
                  )}
                </span>
              )}
            /> */}
            <Column
              width={120}
              fixed="right"
              align="center"
              title={
                <Space>
                  <Button
                    key={2}
                    onClick={() => {
                      setVisibleModal(true);
                      setModalStatus("create");
                      setSelectedRole({});
                    }}
                    type="primary"
                    size="large"
                    icon={<PlusIcon />}
                    className="h-[32px]"
                  >
                    <span className="text-[12px] !font-[400]">
                      {t("create")}
                    </span>
                  </Button>
                  {$isDev && (
                    <Button
                      onClick={handleImportAdminRoutes}
                      type="primary"
                      icon={<ImportOutlined />}
                      loading={loadingImport}
                      size="middle"
                    >
                      Import routes
                    </Button>
                  )}
                </Space>
              }
              key="moneyCommission"
              dataIndex={"moneyCommission"}
              render={(text, record: Role) => (
                //@ts-ignore
                <DropdownCell
                  text={t("action")}
                  items={[
                    {
                      onClick: () => "",
                      // createOrderModalRef.current?.handleUpdate(record),
                      label: (
                        <Button
                          block
                          icon={<EditFilled />}
                          disabled={!userStore.info.role?.isAdmin}
                          type="primary"
                          onClick={() => {
                            setSelectedRole(record);
                            setVisibleModal(true);
                            setModalStatus("update");
                          }}
                        >
                          {t("update")}
                        </Button>
                      ),
                      key: "update",
                    },

                    {
                      label: (
                        <Popconfirm
                          placement="topLeft"
                          title={
                            <div>
                              <h1 className="text-sm">{t("confirm?")}</h1>
                            </div>
                          }
                          onConfirm={async () => {
                            await handleDeleteRole?.(record.id);
                            fetchData();
                          }}
                          okText={t("yes")}
                          cancelText={t("no")}
                        >
                          <Button
                            icon={<CloseOutlined />}
                            className="w-full !text-white !bg-red-500 !font-medium"
                          >
                            {t("delete")}
                          </Button>
                        </Popconfirm>
                      ),
                      key: "delete",
                    },
                  ]}
                  trigger={["click"]}
                >
                  <a onClick={(e) => e.preventDefault()}>
                    <Space>
                      {t("action")}
                      <DownOutlined />
                    </Space>
                  </a>
                </DropdownCell>
                // <div className="flex flex-col gap-2">

                // </div>
              )}
            />
          </AriviTable>
        </Spin>
      </div>

      <RoleModal
        onSubmitOk={fetchData}
        onClose={() => {
          setVisibleModal(false);
          setSelectedRole({});
        }}
        open={visibleModal}
        role={selectedRole}
        status={modalStatus}
      />
    </div>
  );
};

export { RolePage as default };
