import { InfoCircleOutlined } from "@ant-design/icons";

const AntFormErrorLabel = ({
  label,
  hideIcon = false,
  className,
}: {
  label: string;
  hideIcon?: boolean;
  className?: string;
}) => {
  return (
    <div className={`flex items-center gap-[4px] mt-2 ${className}`}>
      <InfoCircleOutlined className="translate-y-[-1px]" />
      <span className="text-[12px] text-[#E94134] leading-[140%]">{label}</span>
    </div>
  );
};

export default AntFormErrorLabel;
