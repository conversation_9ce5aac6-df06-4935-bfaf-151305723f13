import { TelegramGroup } from "./telegramGroup";

export enum BotType {
  Telegram = "TELEGRAM",
}

export const BotTypeTrans = {
  [BotType.Telegram]: {
    label: "Telegram",
    value: BotType.Telegram,
  },
};

export interface Bot {
  id: number;
  createdAt: number;
  updatedAt: number;
  isDeleted: boolean;
  isActive: boolean;
  deletedAt: number;
  name: string;
  type: BotType;
  token: string;
  telegramGroups: TelegramGroup[];
}
