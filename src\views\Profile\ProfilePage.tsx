import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  Col,
  Divider,
  Form,
  Image,
  Input,
  message,
  Row,
  Typography,
} from "antd";
import { useForm } from "antd/lib/form/Form";
import { authApi } from "api/auth.api";
import { useEffect, useRef, useState } from "react";
import { userStore } from "store/userStore";
import { getTitle } from "utils";
import { UpdateProfileModal } from "./UpdateProfileModal";
import { observer } from "mobx-react";
import { useTranslation } from "react-i18next";
import emailIcon from "assets/svgs/mail.svg";
import phoneIcon from "assets/svgs/phone.svg";
import user from "assets/svgs/user.svg";
import AntFormErrorLabel from "components/Common/AntFormErrorLabel";
import ProfileBanner from "./ProfileBanner";
import { ReactComponent as EyesPassword } from "assets/svgs/eyes-password.svg";
import { ReactComponent as NotEyesPassword } from "assets/svgs/eyes-password-2.svg";

export const ProfilePage = observer(({ title }: { title: string }) => {
  const [form] = useForm();
  const [loading, setLoading] = useState(false);
  const updateProfileModalRef = useRef<UpdateProfileModal>();
  const { t } = useTranslation();

  useEffect(() => {
    document.title = getTitle(t(title));
  }, []);

  //handle submit form
  const onFinish = async (values: any) => {
    const oldPassword = form.getFieldValue("oldPassword");
    const newPassword = form.getFieldValue("newPassword");
    setLoading(true);

    try {
      const res = await authApi.passwordUpdate({
        oldPassword,
        newPassword,
      });
      form.resetFields();
      message.success(t("actionSuccessfully"));
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      {" "}
      <div className="profile-page">
        <Row className="flex justify-between" style={{ rowGap: "24px" }}>
          <Col sm={24} md={12} className="md:pr-3">
            <div className="">
              <ProfileBanner />
            </div>
          </Col>
          <Col sm={24} md={12} className="!w-full md:pl-3">
            <div className="profile-wrapper border-shadow h-full">
              <div className="mb-[24px]">
                <h5 className="text-gray-1 semibold !text-[18px]">
                  {t("changePasswordText")}
                </h5>
              </div>
              <div title={t("changePassword")} className="flex flex-col">
                <Form
                  form={form}
                  className="form-profile--items"
                  onFinish={onFinish}
                  layout="vertical"
                  validateTrigger={["onBlur", "onChange"]}
                >
                  <Form.Item
                    label={t("oldPass")}
                    name="oldPassword"
                    // required
                    rules={[
                      {
                        required: true,
                        message: (
                          <AntFormErrorLabel
                            label={t("pleaseEnterOldPassword")}
                          />
                        ),
                      },
                    ]}
                  >
                    <Input.Password
                      iconRender={(visible) =>
                        visible ? <NotEyesPassword /> : <EyesPassword />
                      }
                    />
                  </Form.Item>

                  <Form.Item
                    name="newPassword"
                    label={t("newPass")}
                    rules={[
                      {
                        required: true,
                        message: (
                          <AntFormErrorLabel
                            label={t("pleaseEnterNewPassword")}
                          />
                        ),
                      },
                    ]}
                  >
                    <Input.Password
                      iconRender={(visible) =>
                        visible ? <NotEyesPassword /> : <EyesPassword />
                      }
                    />
                  </Form.Item>

                  <Form.Item
                    name="renewPassword"
                    label={t("reEnterNewPass")}
                    dependencies={["newPassword"]}
                    rules={[
                      {
                        required: true,
                        message: (
                          <AntFormErrorLabel
                            label={t("pleaseEnterReNewPassword")}
                          />
                        ),
                      },
                      ({ getFieldValue }) => ({
                        validator(_, value) {
                          if (
                            !value ||
                            getFieldValue("newPassword") === value
                          ) {
                            return Promise.resolve();
                          }
                          return Promise.reject(
                            <AntFormErrorLabel label={t("passwordNotMatch")} />
                          );
                        },
                      }),
                    ]}
                  >
                    <Input.Password
                      iconRender={(visible) =>
                        visible ? <NotEyesPassword /> : <EyesPassword />
                      }
                    />
                  </Form.Item>

                  <Form.Item
                    style={{ marginBottom: 0 }}
                    className="w-full flex justify-end "
                  >
                    <Button
                      loading={loading}
                      type="primary"
                      htmlType="submit"
                      className="btn-form mb-4 md:mb-6"
                    >
                      {t("changePass")}
                    </Button>
                  </Form.Item>
                </Form>
              </div>
            </div>
          </Col>
        </Row>
      </div>

      <UpdateProfileModal
        ref={updateProfileModalRef}
        onClose={function (): void {
          throw new Error("Function not implemented.");
        }}
        onSubmitOk={() => userStore.getProfile()}
      />
    </>
  );
});
