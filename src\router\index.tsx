import {
  AreaChartOutlined,
  NotificationOutlined,
  ProductOutlined,
  ProjectOutlined,
  SettingOutlined,
  SolutionOutlined,
  TeamOutlined,
  UsergroupAddOutlined,
} from "@ant-design/icons";
import React from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>o<PERSON>ersonCircleOutline } from "react-icons/io5";
import { RouteObject } from "react-router-dom";
import { userStore } from "store/userStore";
import { NotFoundPage } from "views/404/NotFoundPage";
import { CustomerPage } from "views/Customer/CustomerPage";
import { DashboardPage } from "views/Dashboard/DashboardPage";
import ForgotPasswordPage from "views/ForgotPassword/ForgotPasswordPage";
import LoginPage from "views/Login/LoginPage";
import { ProfilePage } from "views/Profile/ProfilePage";
import RolePage from "views/Role/RolePage";
import { StaffPage } from "views/StaffPage/StaffPage";
import { AdminLayout } from "../layouts/AdminLayout";
import RegisterPage from "views/Register/RegisterPage";
import { ProductPage } from "views/Product/ProductPage";
import { PartnerPage } from "views/Partner/PartnerPage";
import { ProjectPage } from "views/ProjectPage/ProjectPage";
import { FaqCategoryPage } from "views/FaqCategory/FaqCategoryPage";
import { FaqPage } from "views/Faq/FaqPage";
import { SupportTicketPage } from "views/SupportTicket/SupportTicketPage";
import { ConfigurationPage } from "views/Configuration/ConfigurationPage";
import { GrHelp } from "react-icons/gr";
import { NotificationPage } from "views/Notification/NotificationPage";
import { RankPage } from "views/Rank/RankPage";
import { FaRankingStar } from "react-icons/fa6";
import { PaymentConfigPage } from "views/PaymentConfig/PaymentConfigPage";
import { MdOutlinePayments, MdOutlineSupportAgent } from "react-icons/md";
import { PromotionPage } from "views/Promotion/PromotionPage";
import { BiSolidDiscount } from "react-icons/bi";
import { CreateCustomerDepositPage } from "views/CreateCustomerDeposit/CreateCustomerDepositPage";
import { CustomerDepositPage } from "views/CustomerDeposit/CustomerDepositPage";
import { CustomerWithdrawPage } from "views/CustomerWithdraw/CustomerWithdrawPage";
import { WithdrawPage } from "views/Withdraw/WithdrawPage";
import { PiProjectorScreenChart } from "react-icons/pi";
import { IconHelp, IconScriptPlus } from "@tabler/icons-react";
import ProjectCreatePage from "views/ProjectCreatePage/ProjectCreatePage";
import { RiFileList3Line, RiFileListLine } from "react-icons/ri";
import { OnlinePaymentPage } from "views/OnlinePayment/OnlinePaymentPage";
import ProjectListPage from "views/ProjectListPage/ProjectListPage";
import { ApproveProjectPage } from "views/ApproveProject/ApproveProjectPage";
import { GoHome } from "react-icons/go";
import { ContentDefinePage } from "views/ContentDefine/ContentDefinePage";
import { SupportTicketCreatedBy } from "types/supportTicket";
import { NotificationScope } from "types/notification";
import { FaqCategoryType } from "types/faq";
import { TaskListPage } from "views/TaskListPage/TaskListPage";
import { ProjectLogsPage } from "views/ProjectLogs/ProjectLogsPage";
import { ReasonPage } from "views/Reason/ReasonPage";
import { WithdrawConfigPage } from "views/WithdrawConfig/WithdrawConfigPage";
import { ReactComponent as HomeSvg } from "assets/svgs/icon-home.svg";
import { ReactComponent as ProjectSvg } from "assets/svgs/icon-project.svg";
import { ReactComponent as MissionSvg } from "assets/svgs/icon-mission.svg";
import { ReactComponent as ParnertSvg } from "assets/svgs/icon-parnert.svg";
import { ReactComponent as SettingSvg } from "assets/svgs/icon-setting.svg";
import { ReactComponent as StickerSvg } from "assets/svgs/icon-sticker.svg";
import { ReactComponent as ServiceSvg } from "assets/svgs/icon-service.svg";
import { ReactComponent as LevelSvg } from "assets/svgs/icon-level.svg";
import { ReactComponent as UserSvg } from "assets/svgs/icon-list-user.svg";
import { ReactComponent as PercentSvg } from "assets/svgs/icon-percent.svg";
import { ReactComponent as NotiSvg } from "assets/svgs/icon-noti.svg";
import { ReactComponent as HelpSvg } from "assets/svgs/icon-help.svg";
import { ReactComponent as ProfileSvg } from "assets/svgs/icon-profile.svg";
import { DashboardCustomer } from "views/Dashboard/DashboardCustomer";
import { DashboardPartner } from "views/Dashboard/DashboardPartner";
import { DashboardStatistical } from "views/Dashboard/DashboardStatistical";
import { TaskApprove } from "views/TaskListPage/TaskApprove";
import { TaskWarranty } from "views/TaskListPage/TaskWarranty";

export interface Route extends RouteObject {
  title?: any;
  children?: Route[];
  icon?: React.ReactNode;
  breadcrumb?: string;
  isAccess?: boolean;
  hidden?: boolean;
  name?: string;
  isFeature?: boolean;
  noRole?: boolean;
  aliasPath?: string;
  checkIsDev?: boolean;
  isGameRouter?: boolean;
  isPublic?: boolean; //Ẩn ở menu chọn quyền và luôn hiển thị với tất cả user
  subheader?: string;
  isHeader?: boolean;
}

export enum PermissionNames {
  //* Order
  orderEdit = "order-management/order-edit",
  orderView = "order-management/order-view",

  //* Order
  courseOrderEdit = "course-order-management/course-order-edit",
  courseOrderView = "course-order-management/course-order-view",

  //* FreeStock
  freeStockView = "free-stock-management/free-stock-view",

  //* CustomerRank
  customerRankEdit = "customerRank-management/customerRank-edit",
  customerRankView = "customerRank-management/customerRank-view",

  //* Banner
  bannerEdit = "banner-management/banner-edit",
  bannerAdd = "banner-management/banner-add",
  bannerDelete = "banner-management/banner-delete",
  bannerView = "banner-management/banner-view",

  //* Customer
  customerEdit = "customer-management/customer-edit",
  customerResetPw = "customer-management/customer-reset-pw",
  customerBlock = "customer-management/customer-lock",
  customerApprove = "customer-management/customer-approve",
  changeRefEmployee = "customer-management/change-refEmployee",
  changeCustomerRank = "customer-management/change-customerRank",

  customerView = "customer-management/customer-view",
  customerDelete = "customer-management/customer-delete",
  customerResetRank = "customer-management/customer-reset-rank",
  customerSendNoti = "customer-management/customer-send-noti",

  //* News
  newsAdd = "news-management/news-add",
  newsEdit = "news-management/news-edit",
  newsDelete = "news-management/news-delete",

  //* Product
  productAdd = "product-management/product-add",
  productEdit = "product-management/product-edit",
  productDelete = "product-management/product-delete",
  productView = "product-management/product-view",

  //* Broker
  employeeAdd = "employee-management/employee-add",
  employeeEdit = "employee-management/employee-edit",
  employeeBlock = "employee-management/employee-block",
  employeeResetPw = "employee-management/employee-reset-pw",
  employeeView = "employee-management/employee-view",
  employeeDelete = "employee-management/employee-delete",
  employeeViewAllowBalance = "employee-management/employee-view-allow-balance",
  employeeEditAllowBalance = "employee-management/employee-edit-allow-balance",
  employeeViewAllowBalanceHistory = "employee-management/employee-view-allow-balance-history",

  //* Trader
  traderAdd = "trader-management/trader-add",
  traderEdit = "trader-management/trader-edit",
  traderView = "trader-management/trader-view",
  traderDelete = "trader-management/trader-delete",

  //* StockCode
  stockCodeAdd = "stockCode-management/stockCode-add",
  stockCodeEdit = "stockCode-management/stockCode-edit",
  stockCodeBlock = "stockCode-management/stockCode-block",
  stockCodeDelete = "stockCode-management/stockCode-delete",
  stockCodeView = "stockCode-management/stockCode-view",

  //* StockOrder
  stockOrderAdd = "stockOrder-management/stockOrder-add",
  stockOrderEdit = "stockOrder-management/stockOrder-edit",
  stockOrderDelete = "stockOrder-management/stockOrder-delete",

  //* NewsCategory
  newsCategoryAdd = "news-management/news-category-add",
  newsCategoryEdit = "news-management/news-category-edit",
  newsCategoryDelete = "news-management/news-category-delete",

  //* CourseCategory
  courseCategoryAdd = "course-management/course-category-add",
  courseCategoryEdit = "course-management/course-category-edit",
  courseCategoryDelete = "course-management/course-category-delete",
  courseCategoryView = "course-management/course-category-view",

  //* Course
  courseAdd = "course-management/course-add",
  courseEdit = "course-management/course-edit",
  courseDelete = "course-management/course-delete",
  courseView = "course-management/course-view",

  //* Lecture
  lectureAdd = "lecture-management/lecture-add",
  lectureEdit = "lecture-management/lecture-edit",
  lectureDelete = "lecture-management/lecture-delete",
  lectureView = "lecture-management/lecture-view",
  //* Video
  videoAdd = "video-management/video-add",
  videoEdit = "video-management/video-edit",
  videoDelete = "video-management/video-delete",
  videoView = "video-management/video-view",
  //* Review

  reviewsView = "reviews-management/reviews-view",

  //* ReviewLecture
  reviewsLectureView = "reviews-lecture-management/reviews-lecture-view",
  //* Company
  companyAdd = "company-management/company-add",
  companyEdit = "company-management/company-edit",
  companyDelete = "company-management/company-delete",
  companyView = "company-management/company-view",

  //* Telegram group
  telegramGroupBlock = "telegramGroup/telegram-group-management/telegram-group-block",
  telegramGroupAdd = "telegramGroup/telegram-group-management/telegram-group-add",
  telegramGroupEdit = "telegramGroup/telegram-group-management/telegram-group-edit",
  telegramGroupDelete = "telegramGroup/telegram-group-management/telegram-group-delete",
  telegramGroupView = "telegramGroup/telegram-group-management/telegram-group-view",
}

export const adminRoutes: Route[] = [
  {
    subheader: "MENU",
    path: "/menu",
    isPublic: true,
    isFeature: true,
    isHeader: true,
  },
  {
    title: "dashboardManagement",
    icon: <HomeSvg className="w-[24px] h-[24px]" />,
    path: "/dashboardManagement",
    name: "/dashboardManagement",
    breadcrumb: "dashboardManagement",
    children: [
      {
        title: "customer",
        icon: <IconScriptPlus className="w-[24px] h-[24px]" />,
        path: "customer",
        name: "customer",
        breadcrumb: "customer",
        element: <DashboardCustomer title="customer" />,
      },
      {
        title: "partner",
        icon: <IconScriptPlus className="w-[24px] h-[24px]" />,
        path: "partner",
        name: "partner",
        breadcrumb: "partner",
        element: <DashboardPartner title="partner" />,
      },
      {
        title: "statistical",
        icon: <IconScriptPlus className="w-[24px] h-[24px]" />,
        path: "statistical",
        name: "statistical",
        breadcrumb: "statistical",
        element: <DashboardStatistical title="statistical" />,
      },
    ],
  },
  {
    title: "projectManagement",
    icon: <ProjectSvg className="w-[24px] h-[24px]" />,
    path: "/project",
    name: "/project",
    breadcrumb: "projectManagement",
    children: [
      {
        title: "projectCreate",
        icon: <IconScriptPlus className="w-[24px] h-[24px]" />,
        path: "project-create",
        name: "project-create",
        breadcrumb: "projectCreate",
        element: <ProjectCreatePage title="projectCreate" />,
      },
      {
        title: "projectList",
        icon: <RiFileList3Line className="w-[24px] h-[24px]" />,
        path: "project-list",
        name: "project-list",
        breadcrumb: "projectList",
        element: <ProjectListPage title="projectList" />,
      },
      // {
      //   title: "reviewUnderWarranty",
      //   icon: <RiFileListLine className="w-[24px] h-[24px]" />,
      //   path: "review-under-warranty",
      //   name: "review-under-warranty",
      //   breadcrumb: "reviewUnderWarranty",
      //   element: <ProjectLogsPage title="reviewUnderWarranty" />,
      // },
      // {
      //   title: "approveProject",
      //   icon: <RiFileList3Line className="w-[24px] h-[24px]" />,
      //   path: "approve-project",
      //   breadcrumb: "approveProject",
      //   name: "approve-project",
      //   element: <ApproveProjectPage title="approveProject" />,
      // },
    ],
  },
  {
    title: "mission",
    icon: <MissionSvg className="w-[24px] h-[24px]" />,
    path: "/mission",
    name: "/mission",
    breadcrumb: "mission",
    // element: <TaskListPage title="approveProject" />,
    children: [
      {
        title: "approveProject",
        icon: <IconScriptPlus className="w-[24px] h-[24px]" />,
        path: "list",
        name: "list",
        breadcrumb: "approveProject",
        element: <TaskListPage title="approveProject" />,
      },
      {
        title: "approveMission",
        icon: <IconScriptPlus className="w-[24px] h-[24px]" />,
        path: "approve",
        name: "approve",
        breadcrumb: "approveMission",
        element: <TaskApprove title="approveMission" />,
      },
      {
        title: "warrantyMission",
        icon: <IconScriptPlus className="w-[24px] h-[24px]" />,
        path: "warranty-processing",
        name: "warranty-processing",
        breadcrumb: "warrantyMission",
        element: <TaskWarranty title="warrantyMission" />,
      },
    ],
  },
  {
    title: "customer",
    icon: <ParnertSvg className="w-[24px] h-[24px] justify-center" />,
    path: "/customer",
    name: "/customer",
    breadcrumb: "customer",
    // element: <CustomerPage title="customer" />,
    children: [
      {
        title: "customerList",
        icon: <IoPerson className="w-[24px] h-[24px] justify-center" />,
        path: "customer-list",
        name: "customer-list",
        breadcrumb: "customerList",
        element: <CustomerPage title="customerList" />,
      },
      {
        title: "depositMoney",
        icon: <IoPerson className="w-[24px] h-[24px] justify-center" />,
        path: "deposit-money",
        name: "deposit-money",
        breadcrumb: "depositMoney",
        element: <CustomerDepositPage title="depositMoney" />,
      },
    ],
  },
  {
    title: "partner",
    icon: <ParnertSvg className="w-[24px] h-[24px] justify-center" />,
    path: "/partner",
    name: "/partner",
    breadcrumb: "partner",

    children: [
      {
        title: "partnerList",
        icon: <IoPerson className="w-[24px] h-[24px] justify-center" />,
        path: "partner-list",
        name: "partner-list",
        breadcrumb: "partnerList",
        element: <PartnerPage title="partnerList" />,
      },
      {
        title: "withdrawMoney",
        icon: <IoPerson className="w-[24px] h-[24px] justify-center" />,
        path: "withdraw-money-partner",
        name: "withdraw-money-partner",
        breadcrumb: "withdrawMoney",
        element: <WithdrawPage title="withdrawMoney" />,
      },
    ],
  },
  {
    title: "userManagement",
    icon: <UserSvg className="w-[24px] h-[24px] justify-center" />,
    path: "/userManagement",
    name: "/userManagement",
    breadcrumb: "userManagement",
    children: [
      {
        title: "role",
        icon: <SettingOutlined className="w-[24px] h-[24px] justify-center" />,
        path: "role",
        name: "role",
        breadcrumb: "role",
        element: <RolePage title="role" />,
      },
      {
        title: "staff",
        icon: (
          <UsergroupAddOutlined className="w-[24px] h-[24px] justify-center" />
        ),
        path: "staff",
        name: "staff",
        breadcrumb: "staff",
        element: <StaffPage title="staffList" />,
      },
    ],
  },
  {
    title: "promotion",
    icon: <PercentSvg className="w-[24px] h-[24px] justify-center" />,
    path: "/promotion",
    name: "/promotion",
    breadcrumb: "promotion",
    element: <PromotionPage title="promotion" />,
  },
  {
    title: "supportTicket",
    icon: <StickerSvg className="w-[24px] h-[24px] justify-center" />,
    path: "/support-ticket",
    name: "/support-ticket",
    breadcrumb: "supportTicket",
    children: [
      {
        title: "supportTicketCustomer",
        icon: <SettingOutlined className="w-[24px] h-[24px] justify-center" />,
        path: "support-ticket-customer",
        name: "support-ticket-customer",
        breadcrumb: "supportTicketCustomer",
        element: (
          <SupportTicketPage
            key={"support-ticket-customer"}
            title="supportTicketCustomer"
          />
        ),
      },
      {
        title: "supportTicketPartner",
        icon: <SettingOutlined className="w-[24px] h-[24px] justify-center" />,
        path: "support-ticket-partner",
        name: "support-ticket-partner",
        breadcrumb: "supportTicketPartner",
        element: (
          <SupportTicketPage
            key={"support-ticket-partner"}
            title="supportTicketPartner"
            type={SupportTicketCreatedBy.Partner}
          />
        ),
      },
    ],
  },
  {
    title: "notification",
    icon: <NotiSvg className="w-[24px] h-[24px] justify-center" />,
    path: "/notification",
    name: "/notification",
    breadcrumb: "notification",
    children: [
      {
        title: "notificationCustomer",
        icon: (
          <NotificationOutlined className="w-[24px] h-[24px] justify-center" />
        ),
        path: "notification-customer",
        name: "notification-customer",
        breadcrumb: "notificationCustomer",
        element: (
          <NotificationPage
            key={"notification-customer"}
            title="notificationCustomer"
          />
        ),
      },
      {
        title: "notificationPartner",
        icon: (
          <NotificationOutlined className="w-[24px] h-[24px] justify-center" />
        ),
        path: "notification-partner",
        name: "notification-partner",
        breadcrumb: "notificationPartner",
        element: (
          <NotificationPage
            key={"notification-partner"}
            title="notificationPartner"
            scope={NotificationScope.Partner}
          />
        ),
      },
    ],
  },
  {
    title: "levelUser",
    icon: <LevelSvg className="w-[24px] h-[24px] justify-center" />,
    path: "/rank",
    name: "/rank",
    breadcrumb: "levelUser",
    element: <RankPage title="levelUser" />,
  },
  {
    title: "product",
    icon: <ServiceSvg className="w-[24px] h-[24px] justify-center" />,
    path: "/product",
    name: "/product",
    breadcrumb: "product",
    element: <ProductPage title="product" />,
  },
  {
    title: "configuration",
    icon: <SettingSvg className="w-[24px] h-[24px] justify-center" />,
    path: "/configuration",
    name: "/configuration",
    breadcrumb: "configuration",
    children: [
      {
        title: "generalConfiguration",
        icon: <SettingOutlined className="w-[24px] h-[24px] justify-center" />,
        path: "generalConfiguration",
        name: "generalConfiguration",
        breadcrumb: "generalConfiguration",
        element: <ConfigurationPage title="generalConfiguration" />,
      },
      // {
      //   title: "paymentMethod",
      //   icon: (
      //     <MdOutlinePayments className="w-[24px] h-[24px] justify-center" />
      //   ),
      //   path: "paymentMethod",
      //   name: "paymentMethod",
      //   breadcrumb: "paymentMethod",
      //   element: <PaymentConfigPage title="paymentMethod" />,
      // },
      {
        title: "withdrawMethod",
        icon: (
          <MdOutlinePayments className="w-[24px] h-[24px] justify-center" />
        ),
        path: "withdrawMethod",
        name: "withdrawMethod",
        breadcrumb: "withdrawMethod",
        element: <WithdrawConfigPage title="withdrawMethod" />,
      },
      {
        title: "onlinePayment",
        icon: (
          <MdOutlinePayments className="w-[24px] h-[24px] justify-center" />
        ),
        path: "online-payment",
        name: "online-payment",
        breadcrumb: "onlinePayment",
        element: <OnlinePaymentPage title="onlinePayment" />,
      },
      {
        title: "content",
        icon: <SettingOutlined className="w-[24px] h-[24px] justify-center" />,
        path: "content-define",
        name: "content-define",
        breadcrumb: "content",
        element: <ContentDefinePage title="content" />,
      },
      {
        title: "rejectReason",
        icon: <SettingOutlined className="w-[24px] h-[24px] justify-center" />,
        path: "reject-reason",
        name: "reject-reason",
        breadcrumb: "rejectReason",
        element: <ReasonPage title="rejectReason" />,
      },
    ],
  },
  {
    subheader: "other",
    path: "other",
    isPublic: true,
    isFeature: true,
    isHeader: true,
  },

  {
    title: "faqCustomer",
    icon: <HelpSvg className="w-[24px] h-[24px] justify-center" />,
    path: "/faq-customer",
    name: "/faq-customer",
    breadcrumb: "faqCustomer",
    children: [
      {
        title: "faqCategory",
        icon: <IoHelp className="w-[24px] h-[24px] justify-center" />,
        path: "faq-category-customer",
        name: "faq-category-customer",
        breadcrumb: "faqCategory",
        element: <FaqCategoryPage title="faqCategory" />,
      },
      {
        title: "faqList",
        icon: <IoHelp className="w-[24px] h-[24px] justify-center" />,
        path: "faq-list-customer",
        name: "faq-list-customer",
        breadcrumb: "faqList",
        element: <FaqPage title="faqList" />,
      },
    ],
  },
  {
    title: "faqPartner",
    icon: <HelpSvg className="w-[24px] h-[24px] justify-center" />,
    path: "/faq-partner",
    name: "/faq-partner",
    breadcrumb: "faqPartner",
    children: [
      {
        title: "faqCategory",
        icon: <IoHelp className="w-[24px] h-[24px] justify-center" />,
        path: "faq-category-partner",
        name: "faq-category-partner",
        breadcrumb: "faqCategory",
        element: (
          <FaqCategoryPage
            title="faqCategory"
            type={FaqCategoryType.Partner}
            key={FaqCategoryType.Partner}
          />
        ),
      },
      {
        title: "faqList",
        icon: <IoHelp className="w-[24px] h-[24px] justify-center" />,
        path: "faq-list-partner",
        name: "faq-list-partner",
        breadcrumb: "faqList",
        element: (
          <FaqPage
            title="faqList"
            faqCategoryType={FaqCategoryType.Partner}
            key={FaqCategoryType.Partner}
          />
        ),
      },
    ],
  },
  {
    title: "profile",
    icon: <ProfileSvg className="w-[24px] h-[24px] justify-center" />,
    path: "/profile",
    name: "/profile",
    breadcrumb: "profile",
    element: <ProfilePage title="profile" />,
  },
];

const routes: Route[] = [
  {
    path: "/",
    name: "/",
    children: adminRoutes,
    element: <AdminLayout />,
  },
  {
    name: "/login",
    element: <LoginPage title="Login" />,
    path: "/login",
  },
  // {
  //   name: "/register",
  //   element: <RegisterPage title="Register" />,
  //   path: "/register",
  // },
  {
    element: <ForgotPasswordPage />,
    path: "/forgot-password",
    name: "/forgot-password",
  },

  {
    element: <NotFoundPage />,
    name: "*",
    path: "*",
  },
];

export { routes };
