import { Modal } from "antd";
import { SingleImageUpload } from "components/Upload/SingleImageUpload";
import { useCallback, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

export const UploadImageModal = ({
  visible,
  onClose,
  onSubmitOk,
  uploadUrl,
}: {
  visible: boolean;
  onClose: () => void;
  onSubmitOk: (imagePath: string) => void;
  uploadUrl?: string;
}) => {
  const [image, setImage] = useState("");

  useEffect(() => {
    if (visible) {
      setImage("");
    }
  }, [visible]);
  const { t } = useTranslation();

  const handleUploadImageOk = useCallback((path: string) => {
    setImage((prev) => path);
  }, []);

  return (
    <Modal
      maskClosable={false}
      onCancel={onClose}
      visible={visible}
      title={t("uploadImg")}
      style={{ top: 20 }}
      width={700}
      onOk={() => {
        onSubmitOk(image);
      }}
    >
      <div className="text-center">
        <SingleImageUpload
          uploadUrl={uploadUrl}
          onUploadOk={handleUploadImageOk}
          imageUrl={image}
        />
      </div>

      {/* <Input.TextArea
        onChange={(e) => setDesc(e.target.value)}
        style={{ marginTop: "5px" }}
        placeholder="Mô tả ảnh"
      /> */}
    </Modal>
  );
};
