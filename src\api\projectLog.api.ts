import { request } from "utils/request";
import { AxiosPromise } from "axios";

export const projectLogApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/projectLog",
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/projectLog",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/projectLog/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/projectLog/${id}`,
      method: "delete",
    }),
};
