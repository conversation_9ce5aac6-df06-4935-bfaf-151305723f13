// src/metadata/configurationParamMetadata.ts

import { ConfigurationParam, DataType } from "types/configuration";

export const configurationParamMetadata: Record<
  ConfigurationParam,
  {
    title: string;
    dataType: DataType;
    description: string;
    group: string;
  }
> = {
  [ConfigurationParam.InspecReview]: {
    title: "INSPEC_REVIEW",
    dataType: DataType.Enum,
    description: "selectReviewType",
    group: "inspection",
  },
  [ConfigurationParam.CheckIdentityCard]: {
    title: "CHECK_IDENTITY_CARD",
    dataType: DataType.Boolean,
    description: "",
    group: "inspection",
  },
  [ConfigurationParam.ReviewImage]: {
    title: "REVIEW_IMAGE",
    dataType: DataType.Boolean,
    description: "photoReview",
    group: "inspection",
  },
  [ConfigurationParam.MaxGenerateContentWithAI]: {
    title: "MAX_GENERATE_CONTENT_WITH_AI",
    dataType: DataType.Number,
    group: "aiSetting",
    description: "",
  },
  [ConfigurationParam.PromptContent]: {
    title: "PROMPT_CONTENT",
    dataType: DataType.String,
    group: "aiSetting",
    description: "",
  },
  [ConfigurationParam.PromptKeyword]: {
    title: "PROMPT_KEYWORD",
    dataType: DataType.String,
    group: "aiSetting",
    description: "",
  },
  [ConfigurationParam.ReviewCharacterCount]: {
    title: "REVIEW_CHARACTER_COUNT",
    dataType: DataType.Number,
    group: "aiSetting",
    description: "",
  },

  [ConfigurationParam.TaxPercent]: {
    title: "TAX_PERCENT",
    dataType: DataType.Number,
    group: "payment",
    description: "",
  },
  [ConfigurationParam.MinWithdrawAmount]: {
    title: "MIN_WITHDRAW_AMOUNT",
    dataType: DataType.Number,
    group: "payment",
    description: "",
  },
  [ConfigurationParam.MinDepositAmount]: {
    title: ConfigurationParam.MinDepositAmount,
    dataType: DataType.Number,
    group: "payment",
    description: "",
  },
  [ConfigurationParam.ScanRadius]: {
    title: "SCAN_RADIUS",
    dataType: DataType.Number,
    group: "scan",
    description: "",
  },
  [ConfigurationParam.IntervalScanRadius]: {
    title: "INTERVAL_SCAN_RADIUS",
    dataType: DataType.Number,
    group: "scan",
    description: "intevalRadius",
  },
  [ConfigurationParam.MaxMinuteToCompleteReview]: {
    title: "MAX_MINUTE_TO_COMPLETE_REVIEW",
    dataType: DataType.Number,
    group: "project",
    description: "",
  },
  [ConfigurationParam.ContractSampleFile]: {
    title: "CONTRACT_SAMPLE_FILE",
    dataType: DataType.File,
    group: "project",
    description: "",
  },
  [ConfigurationParam.WarrantyPeriodDays]: {
    title: "WARRANTY_PERIOD_DAYS",
    dataType: DataType.Number,
    group: "project",
    description: "",
  },
  [ConfigurationParam.ProjectCreationDelay]: {
    title: "PROJECT_CREATION_DELAY",
    dataType: DataType.Number,
    group: "project",
    description: "createDelay",
  },
  [ConfigurationParam.MinDropReview]: {
    title: "MIN_DROP_REVIEW",
    dataType: DataType.Number,
    group: "project",
    description: "minDropDes",
  },
  [ConfigurationParam.MaxDropReview]: {
    title: "MAX_DROP_REVIEW",
    dataType: DataType.Number,
    group: "project",
    description: "maxDropDes",
  },
  [ConfigurationParam.PercentImageInProject]: {
    title: "PERCENT_IMAGE_IN_PROJECT",
    dataType: DataType.Number,
    group: "project",

    description: "percentImageDes",
  },
  [ConfigurationParam.TimeRecheckReview]: {
    title: "TIME_RECHECK_REVIEW",
    dataType: DataType.Number,
    group: "project",

    description: "inHours",
  },
  [ConfigurationParam.MaxDescriptionCreateProject]: {
    title: "MAX_DESCRIPTION_CREATE_PROJECT",
    dataType: DataType.Number,
    group: "project",
    description: "",
  },
  [ConfigurationParam.MinDescriptionCreateProject]: {
    title: "MIN_DESCRIPTION_CREATE_PROJECT",
    dataType: DataType.Number,
    group: "project",
    description: "",
  },
  [ConfigurationParam.MinuteToPenalize]: {
    title: "MINUTE_TO_PENALIZE",
    dataType: DataType.Number,
    group: "penalty",
    description: "",
  },
  [ConfigurationParam.MoneyDropInProject]: {
    title: "MONEY_DROP_IN_PROJECT",
    dataType: DataType.Number,
    group: "project",
    description: "",
  },
  [ConfigurationParam.MoneyImageInProject]: {
    title: "MONEY_IMAGE_IN_PROJECT",
    dataType: DataType.Number,
    group: "project",
    description: "",
  },
  [ConfigurationParam.MaxCheckFailReview]: {
    title: ConfigurationParam.MaxCheckFailReview,
    dataType: DataType.Number,
    group: "project",
    description: "",
  },
  [ConfigurationParam.MaxDropPercent]: {
    title: ConfigurationParam.MaxDropPercent,
    dataType: DataType.Number,
    group: "project",
    description: "",
  },
  [ConfigurationParam.autoCheckWarranty]: {
    title: ConfigurationParam.autoCheckWarranty,
    dataType: DataType.Boolean,
    group: "project",
    description: "",
  },
  [ConfigurationParam.SecurityPolicy]: {
    title: ConfigurationParam.SecurityPolicy,
    dataType: DataType.Content,
    group: "policy",
    description: "",
  },
  [ConfigurationParam.WarrantyPolicy]: {
    title: ConfigurationParam.WarrantyPolicy,
    dataType: DataType.Content,
    group: "policy",
    description: "",
  },
  [ConfigurationParam.PaymentPolicy]: {
    title: ConfigurationParam.PaymentPolicy,
    dataType: DataType.Content,
    group: "policy",
    description: "",
  },
  [ConfigurationParam.SecurityPolicyPartner]: {
    title: ConfigurationParam.SecurityPolicyPartner,
    dataType: DataType.Content,
    group: "policy",
    description: "",
  },
  [ConfigurationParam.WarrantyPolicyPartner]: {
    title: ConfigurationParam.WarrantyPolicyPartner,
    dataType: DataType.Content,
    group: "policy",
    description: "",
  },
  [ConfigurationParam.PaymentPolicyPartner]: {
    title: ConfigurationParam.PaymentPolicyPartner,
    dataType: DataType.Content,
    group: "policy",
    description: "",
  },
};
