import { News } from "./news";
import { Product } from "./product";

export enum BannerContentType {
  Content = "CONTENT",
  Link = "LINK",
  News = "NEWS",
  Product = "PRODUCT",
}

export const BannerContentTypeTrans = {
  [BannerContentType.Content]: "",
  [BannerContentType.Link]: "",
  [BannerContentType.News]: "",
  [BannerContentType.Product]: "",
};

export interface Banner {
  id: number;
  createdAt: number;
  updatedAt: number;
  isDeleted: boolean;
  deletedAt: number;
  title: string;
  link: string;
  content: string;
  contentType: BannerContentType;
  image: string;
  desktopImage: string;
  pos: number; //position: 1 đứng đầu ASC
  isVisible: boolean;
  isVisibleDetail: boolean; //true hiển thị ở trang chi tiết các business type
  news: News;
  product: Product;
}
