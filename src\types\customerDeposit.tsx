import { interfaceToEnum } from "utils/type";
import { Bank, Customer } from "./customer";
import { CustomerTransaction } from "./customerTransaction";
import { Staff } from "./staff";
import { PaymentType } from "./payment";

export enum CustomerDepositStatus {
  All = "ALL",
  Pending = "PENDING",
  Approve = "APPROVE",
  Reject = "REJECT",
}

export const CustomerDepositStatusTrans = {
  [CustomerDepositStatus.All]: {
    value: CustomerDepositStatus.All,
    color: "blue",
  },
  [CustomerDepositStatus.Pending]: {
    value: CustomerDepositStatus.Pending,
    color: "yellow",
  },
  [CustomerDepositStatus.Approve]: {
    value: CustomerDepositStatus.Approve,
    color: "green",
  },
  [CustomerDepositStatus.Reject]: {
    value: CustomerDepositStatus.Reject,
    color: "red",
  },
};

export interface CustomerDeposit {
  id: number;
  createdAt: number;
  updatedAt: number;
  isDeleted: boolean;
  code: string;
  note: string;
  inspecNote: string;
  amount: number;
  status: CustomerDepositStatus;
  inspecAt: number;
  bankNumber: string;
  bankName: string;
  customer: Customer;
  createdStaff: Staff;
  inspecStaff: Staff;
  customerTransactions: CustomerTransaction[];
  bank: Bank;
  paymentType: PaymentType;
}

export const CustomerDepositKeys = interfaceToEnum<CustomerDeposit>();
