import {
  ExportOutlined,
  ImportOutlined,
  PlusOutlined,
  SearchOutlined,
} from "@ant-design/icons";
import { Button, Card, Input, message, Space } from "antd";
import { useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { getTitle } from "utils";
import { unixToFullDate } from "utils/dateFormat";
import { handleExport, MyExcelColumn } from "utils/MyExcel";
import { ToneModal, ToneModalRef } from "./components/Modal/ReasonModal";
import { ToneList } from "./components/Table/ReasonList";
import { useTone } from "hooks/useTone";
import { toneApi } from "api/tone.api";
import { Tone } from "types/tone";

export const TonePage = ({ title = "" }) => {
  const toneModalRef = useRef<ToneModalRef>();
  const [openImport, setOpenImport] = useState(false);
  const { t } = useTranslation();

  const [loadingDelete, setLoadingDelete] = useState(false);
  const { tones, fetchTone, loadingTone, queryTone, totalTone } = useTone({
    initQuery: {
      page: 1,
      limit: 20,
    },
  });

  useEffect(() => {
    document.title = getTitle(t(title));
    fetchTone();
  }, []);

  // const exportColumns: MyExcelColumn<Tone>[] = [
  //   {
  //     width: 30,
  //     header: t("toneName"),
  //     headingStyle: {
  //       font: { bold: true },
  //     },
  //     key: "name",
  //     render: (record: Tone) => record.name,
  //   },
  //   {
  //     width: 20,
  //     header: t("createdAt"),
  //     headingStyle: {
  //       font: { bold: true },
  //     },
  //     key: "createdAt",
  //     render: (record: Tone) => unixToFullDate(record.createdAt),
  //   },
  // ];

  const handleDeleteTone = async (toneId: number) => {
    try {
      setLoadingDelete(true);
      await toneApi.delete(toneId);
      fetchTone();
      message.success(t("actionSuccessfully"));
    } catch (error) {
    } finally {
      setLoadingDelete(false);
    }
  };

  return (
    <Card bodyStyle={{ padding: "8px 20px" }} style={{ borderRadius: "8px" }}>
      <section className="box">
        <div className="filter-container">
          <Space wrap>
            <div className="filter-item">
              <label htmlFor="">{t("search")}</label>
              <Input
                allowClear
                onChange={(ev) => {
                  queryTone.search = ev.currentTarget.value || undefined;
                  queryTone.page = 1;
                  fetchTone();
                }}
                onKeyDown={(ev) => {
                  if (ev.code === "Enter") {
                    fetchTone();
                  }
                }}
                size="middle"
                placeholder={t("tone")}
              />
            </div>

            <div className="filter-item btn">
              <Button
                onClick={() => fetchTone()}
                type="primary"
                icon={<SearchOutlined />}
              >
                {t("search")}
              </Button>
            </div>

            <div className="filter-item btn">
              <Button
                onClick={() => {
                  toneModalRef.current?.handleCreate();
                }}
                icon={<PlusOutlined />}
                type="primary"
              >
                {t("create")}
              </Button>
            </div>
          </Space>
        </div>

        <ToneList
          onEdit={(record) => toneModalRef.current?.handleUpdate(record)}
          dataSource={tones}
          loading={loadingTone}
          loadingDelete={loadingDelete}
          pagination={{
            total: totalTone,
            defaultPageSize: queryTone.limit,
            currentPage: queryTone.page,
            onChange: ({ page, limit }) => {
              Object.assign(queryTone, { page, limit });
              fetchTone();
            },
          }}
          onDelete={handleDeleteTone}
        />
      </section>

      <ToneModal
        ref={toneModalRef}
        onClose={() => {}}
        onSubmitOk={fetchTone}
      />
    </Card>
  );
};
