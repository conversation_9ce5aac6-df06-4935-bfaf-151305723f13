import { Course } from "./course";
import { StockCode } from "./stockCode";

export enum LectureVideoType {
  File = "FILE",
  Youtube = "YOUTUBE",
}
export const LectureVideoTypeTrans = {
  [LectureVideoType.File]: {
    label: "Upload file",
    value: LectureVideoType.File,
  },
  [LectureVideoType.Youtube]: {
    label: "Link youtube",
    value: LectureVideoType.Youtube,
  },
};
export enum LectureType {
  Lecture = "LECTURE",
  TTCK = "TTCK", //phân tích thị trường CK
  CP = "CP", //Phân tích cổ phiếu
}
export const LectureTypeTrans = {
  [LectureType.CP]: {
    label: "Phân tích cổ phiếu",
    value: LectureType.CP,
  },
  [LectureType.TTCK]: {
    label: "Phân tích thị trường CK",
    value: LectureType.TTCK,
  },
};
export interface Lecture {
  id: number;
  createdAt: number;
  updatedAt: number;
  deletedBy: string;
  isDeleted: boolean;
  deletedAt: number;
  code: string;
  videoType: LectureVideoType;
  type: LectureType;
  duration: number;
  no: number;
  selfNo: number;
  name: string;
  desc: string;
  image: string;
  video: string;
  doc: string;
  testContent: string;
  testUrl: string;
  totalView: number;
  isVisible: boolean;
  like: number;
  dislike: number;
  price: number;
  finalPrice: number;
  isFree: boolean; //true: miễn phí
  totalStar: number;
  totalRate: number;
  confirmDate: string;
  stockCodes: StockCode[];
  isForUser: boolean;
  //custom
  course: Course;
  // favorites: Favorite[]
  // courseOrderDetails: CourseOrderDetail[]
}
