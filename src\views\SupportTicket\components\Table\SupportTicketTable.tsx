import {
  DownOutlined,
  EditOutlined,
  LockOutlined,
  RetweetOutlined,
  UnlockOutlined,
} from "@ant-design/icons";
import { Button, message, Popconfirm, Space, Table, Tag } from "antd";
import Column from "antd/lib/table/Column";
import { supportTicketApi } from "api/supportTicket.api";
import { IPagination, Pagination } from "components/Pagination";
import { SupportTicketStatusComp } from "components/SupportTicket/SupportTicketStatusComp";
import { AriviTable } from "components/Table/AriviTable";
import DropdownCell from "components/Table/DropdownCell";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { GrEdit } from "react-icons/gr";
import { HiOutlineTrash } from "react-icons/hi2";
import {
  SupportTicket,
  SupportTicketCreatedBy,
  SupportTicketStatus,
  SupportTicketStatusTrans,
} from "types/supportTicket";
import { formatVND } from "utils";
import { unixToFullDate } from "utils/dateFormat";

interface PropsType {
  dataSource: SupportTicket[];
  loading: boolean;
  loadingDelete?: boolean;
  pagination?: IPagination;
  onEdit?: (supportTicket: SupportTicket) => void;
  onDelete?: (supportTicketId: number) => void;
  onActive?: (supportTicketId: number) => void;
  onInactive?: (supportTicketId: number) => void;
  onRefreshData: () => void;
  type?: SupportTicketCreatedBy; // hasDeleteSupportTicketPermission?: boolean;
  // hasUpdateSupportTicketPermission?: boolean;
}

export const SupportTicketList = ({
  dataSource,
  loading,
  loadingDelete,
  pagination,
  onDelete,
  onEdit,
  onActive,
  onInactive,
  onRefreshData,
  type,
}: // hasDeleteSupportTicketPermission,
  // hasUpdateSupportTicketPermission,

  PropsType) => {
  const [loadingStatus, setLoadingStatus] = useState(false);
  const { t } = useTranslation();
  const handleComplete = async (id: number) => {
    try {
      setLoadingStatus(true);
      const { data } = await supportTicketApi.complete(id);
      message.success(t("actionSuccessfully"));
      onRefreshData();
    } catch (error) {
      console.log(error);
    } finally {
      setLoadingStatus(false);
    }
  };

  const handleReopen = async (id: number) => {
    try {
      setLoadingStatus(true);
      const { data } = await supportTicketApi.reopen(id);
      message.success(t("actionSuccessfully"));
      onRefreshData();
    } catch (error) {
      console.log(error);
    } finally {
      setLoadingStatus(false);
    }
  };

  

  return (
    <div>
      <AriviTable
        // bordered

        loading={loading}
        pagination={false}
        rowKey="id"
        dataSource={dataSource}
        className="custom-scrollbar"
        scroll={{ x: "max-content", y: "calc(100vh - 340px)" }}

      // onChange={}
      >
        <Column
          width={165}
          title={t("updateTime")}
          dataIndex="updatedAt"
          key="updatedAt"
          render={(updatedAt) => {
            return (
              <span className="text-gray-1 text-regular">
                {unixToFullDate(updatedAt)}
              </span>
            );
          }}
        />

        <Column
          width={180}
          title={t("title")}
          dataIndex="title"
          key="title"
          render={(createdAt, record: SupportTicket) => {
            return (
              <div
                className="text-title w-[180px] break-words"
                onClick={() => {
                  onEdit?.(record);
                }}
              >
                <span className="text-primary text-regular line-clamp-3">
                  {record.title}
                </span>
              </div>
            );
          }}
        />
        <Column
          title={t("department")}
          width={210}
          dataIndex="department"
          key="department"
          render={(department) => {
            return <div className="whitespace-pre-wrap">{t(department)}</div>;
          }}
        />
        <Column
          width={100}
          title={t("status")}
          dataIndex="name"
          align="center"
          key={"name"}
          render={(text, record: SupportTicket) => (
            <SupportTicketStatusComp status={record.status} />
          )}
        />
        <Column
          width={240}
          title={
            type === SupportTicketCreatedBy.Customer
              ? t("customer")
              : t("partner")
          }
          dataIndex="customer"
          key="customer"
          render={(text, record: SupportTicket) => (
            <>
              <div>
                {t("fullName")}:{" "}
                {record.customer?.name || record.partner?.fullName}
              </div>
              <div>
                Email: {record.customer?.email || record.partner?.email}
              </div>
              <div>
                {t("phoneNumber")}:{" "}
                {record.customer?.phone || record.partner?.phone}
              </div>
            </>
          )}
        />
        <Column
          title={t("action")}
          fixed="right"
          width={120}
          align="center"
          key="action"
          dataIndex={""}
          render={(text, record: SupportTicket) => (
            <Button
              type="primary"
              className="btn-detail"
              onClick={() => {
                onEdit?.(record);
              }}
            >
              <span className="text-regular text-action-light-4">
                {t("detail")}
              </span>
            </Button>

          )}
        />
      </AriviTable>
      {pagination && <Pagination {...pagination} />}
    </div>
  );
};
