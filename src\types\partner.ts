import { Bank } from "./customer";
import { PaymentType } from "./payment";
import { Rank } from "./rank";

export enum PartnerStatus {
  Created = "CREATED",
  Active = "ACTIVE",
  Blocked = "BLOCKED",
}

export enum PartnerVerifyStatus {
  Pending = "PENDING",
  Verified = "VERIFIED",
  Unverified = "UNVERIFIED",
  All = "ALL"
}

export const PartnerVerifyStatusTrans = {
  [PartnerVerifyStatus.All]: {
    color: "",
    value: PartnerVerifyStatus.All,
  },
  [PartnerVerifyStatus.Pending]: {
    color: "orange",
    value: PartnerVerifyStatus.Pending,
  },
  [PartnerVerifyStatus.Unverified]: {
    color: "red",
    value: PartnerVerifyStatus.Unverified,
  },
  [PartnerVerifyStatus.Verified]: {
    color: "green",
    value: PartnerVerifyStatus.Verified,
  },
};

export const PartnerStatusTrans = {
  [PartnerStatus.Active]: {
    label: "Hoạt động",
    value: PartnerStatus.Active,
    color: "green",
  },
  [PartnerStatus.Blocked]: {
    label: "Bị chặn",
    value: PartnerStatus.Blocked,
    color: "red",
  },
  [PartnerStatus.Created]: {
    label: "Mới tạo",
    value: PartnerStatus.Created,
    color: "orange",
  },
};

export interface Partner {
  id: number;
  createdAt: number;
  updatedAt: number;
  isDeleted: boolean;
  code: string;
  password: string;
  balance: number;
  balanceVersion: number;
  fullName: string;
  isBlocked: boolean;
  loginFailCount: number;
  lat: number;
  long: number;
  email: string;
  ggId: string; //googleID (login social)
  phone: string;
  ipAddress: string;
  status: PartnerStatus;
  deviceId: string;
  taxCode: string;
  address: string;
  avatar: string; //
  rank: Rank;
  bank: Bank;
  paymentType: PaymentType;
  bankAccountName: string;
  bankAccountNumber: string;
  verifyStatus: PartnerVerifyStatus;
  contract: string; // hợp đồng
  identityCardFront: string; // ảnh mặt trước
  identityCardBack: string; // ảnh mặt sao
}
