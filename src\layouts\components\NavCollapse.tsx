// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
import React, { useEffect, useRef } from "react";

import { useState } from "react";
import { useLocation } from "react-router";

// mui imports
import {
  ListItemIcon,
  ListItemButton,
  Collapse,
  styled,
  ListItemText,
  useTheme,
} from "@mui/material";

// custom imports
import NavItem from "./NavItem";

// plugins
import { IconChevronDown, IconChevronUp } from "@tabler/icons-react";
import { useTranslation } from "react-i18next";
import { appStore } from "store/appStore";
import { observer } from "mobx-react";
import clsx from "clsx";

type NavGroupProps = {
  [x: string]: any;
  navlabel?: boolean;
  subheader?: string;
  title?: string;
  icon?: any;
  href?: any;
};

interface NavCollapseProps {
  menu: NavGroupProps;
  level: number;
  pathWithoutLastPart: any;
  pathDirect: any;
  hideMenu: any;
  onClick: (event: React.MouseEvent<HTMLElement>) => void;
}

// FC Component For Dropdown Menu
const NavCollapse = ({
  menu,
  level,
  pathWithoutLastPart,
  pathDirect,
  hideMenu,
  onClick,
}: NavCollapseProps) => {
  const { t } = useTranslation();
  const customizer = appStore.customizer;
  const Icon = menu?.icon;
  const theme = useTheme();
  const { pathname } = useLocation();
  const [open, setOpen] = useState(true);
  const openRef = useRef(true);
  const [isHover, setIsHover] = useState(false);

  const isSelected = pathWithoutLastPart === menu.href;
  // const menuIcon =
  //   level > 1 ? (
  //     <Icon stroke={1.5} size="1rem" />
  //   ) : (
  //     <Icon stroke={1.5} size="1.3rem" />
  //   );

  // useEffect(() => {
  //   if (hideMenu) {
  //     setOpen(false);
  //   } else if (openRef.current) {
  //     setOpen(true);
  //   }
  // }, [hideMenu]);

  const handleClick = () => {
    setOpen(!open);
    openRef.current = !open;
  };

  // menu collapse for sub-levels
  React.useEffect(() => {
    setOpen(false);
    openRef.current = false;
    if (hideMenu) {
      return;
    }
    menu?.children?.forEach((item: any) => {
      if (item?.href === pathname) {
        setOpen(true);
        openRef.current = true;
      }
    });
  }, [pathname, menu.children, hideMenu]);

  const ListItemStyled = styled(ListItemButton)(() => ({
    height: "48px",
    width: hideMenu && "48px",
    // marginBottom: "2px",
    // marginLeft: hideMenu && "2px",
    padding: hideMenu ? "8px 0px" : "8px 10px",
    paddingLeft: hideMenu ? "0px" : level > 2 ? `${level * 15}px` : "10px",
    backgroundColor: open && level < 2 ? theme.palette.primary.main : "",
    justifyContent: hideMenu ? "center" : "flex-start",
    whiteSpace: "nowrap",
    "&:hover": {
      backgroundColor:
        pathname.includes(menu.href) || open
          ? theme.palette.primary.main
          : theme.palette.primary.light,
      color:
        pathname.includes(menu.href) || open
          ? "white"
          : theme.palette.primary.main,
    },
    color:
      open && level < 2
        ? "white"
        : level > 1 && open
        ? theme.palette.primary.light
        : theme.palette.text.secondary,
    borderRadius: `${customizer.borderRadius}px`,
  }));

  // If Menu has Children
  const submenus = menu.children?.map((item: any) => {
    if (item.children) {
      return (
        <NavCollapse
          key={item?.id}
          menu={item}
          level={level + 1}
          pathWithoutLastPart={pathWithoutLastPart}
          pathDirect={pathDirect}
          hideMenu={hideMenu}
          onClick={onClick}
        />
      );
    } else {
      return (
        <NavItem
          key={item.id}
          item={item}
          level={level + 1}
          pathDirect={pathDirect}
          hideMenu={hideMenu}
          onClick={onClick}
        />
      );
    }
  });

  return (
    <>
      <ListItemStyled
        onClick={handleClick}
        selected={isSelected}
        key={menu?.id}
        onMouseEnter={() => {
          setIsHover(true);
        }}
        onMouseLeave={() => {
          setIsHover(false);
        }}
        style={{
          backgroundColor: open || isSelected ? theme.palette.primary.main : "",
        }}
      >
        <ListItemIcon
          sx={{
            minWidth: "36px",
            p: "3px 0",
            color: "inherit",
            justifyContent: hideMenu ? "center" : "",
            marginLeft: hideMenu && "6px",
            alignItems: "center",
          }}
        >
          <div
            className={clsx(
              "flex items-center",
              (isSelected || open) && "invert brightness-0"
            )}
          >
            {menu?.icon}
          </div>
        </ListItemIcon>
        <ListItemText
          color="inherit"
          className={clsx((isSelected || open) && "text-white")}
        >
          {!hideMenu && (
            <div className={"text-ellipsis overflow-hidden whitespace-nowrap"}>
              {t(`${menu.title}`)}
            </div>
          )}
        </ListItemText>
        {!hideMenu &&
          (!open ? (
            <IconChevronDown size="1rem" />
          ) : (
            <IconChevronUp size="1rem" className="text-white" />
          ))}
      </ListItemStyled>
      <Collapse in={open} timeout="auto" unmountOnExit>
        {submenus}
      </Collapse>
    </>
  );
};

export default observer(NavCollapse);
