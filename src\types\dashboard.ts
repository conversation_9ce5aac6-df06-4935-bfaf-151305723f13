export interface ProjectData {
  date: string;
  totalProjects: number;
  totalCustomers: number;
  completed: number;
  inProgress: number;
  pending: number;
  stopped: number;
}

export interface RankSummary {
  id: number;
  fullname: string;
  nameEn: string;
  total: number;
  percent: number;
}

export interface PartnerSummary {
  total: number;
  totalVerified: number;
}

export interface TopCustomer {
  id: number;
  name: string;
  totalPay: number;
}

export interface ReviewTotalSummary {
  totalNotAssign: number;
  totalPending: number;
  totalComplete: number;
  totalReject: number;
}
export interface SummaryProfitItem {
  total: number;
  date: string;
  profit: number;
  rewardPoint: number;
  price: number;
}
export interface TopProduct {
  id: number;
  name: string;
  code: string;
  totalMoney: number;
  total: number;
}

export interface SummaryStatistical {
  project: {
    moneyPaymentComplete: number;
    moneyPaymentPending: number;
  };
  review: {
    totalProfit: number;
    totalPrice: number;
  };
}

export enum DateType {
  Day = "DAY",
  Month = "MONTH",
  Year = "YEAR"
}

export enum KeysTotalChart {
  TotalProjects = "totalProjects",
  TotalCustomers = "totalCustomers",
  Completed = "completed",
  InProgress = "inProgress",
  Pending = "pending",
  Stopped = "stopped"
}

export const colors: Record<KeysTotalChart, string> = {
  [KeysTotalChart.TotalProjects]: '#1A73E8',
  [KeysTotalChart.TotalCustomers]: '#8B2DFF',
  [KeysTotalChart.Completed]: '#41D664',
  [KeysTotalChart.InProgress]: '#FFCD00',
  [KeysTotalChart.Pending]: '#5A6A85',
  [KeysTotalChart.Stopped]: '#E94134'
};

export enum MemberLevel {
  NewMember = "NEW_MEMBER",
  BronzeMember = "BRONZE_MEMBER",
  SilverMember = "SILVER_MEMBER",
  GoldMember = "GOLD_MEMBER",
  PlatinumMember = "PLATINUM_MEMBER"
}

export const colorsLevel = {
  [MemberLevel.NewMember]: "#4D8CFF",
  [MemberLevel.BronzeMember]: "#EB9113",
  [MemberLevel.SilverMember]: "#BDC7D5",
  [MemberLevel.GoldMember]: "#FFCD00",
  [MemberLevel.PlatinumMember]: "#1A73E8",
}