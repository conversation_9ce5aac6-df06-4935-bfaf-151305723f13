import { request } from "utils/request";
import { AxiosPromise } from "axios";

export const configurationApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/configuration",
      params,
    }),
  findOne: (params?: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/configuration/param`,
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/configuration",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/configuration/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/configuration/${id}`,
      method: "delete",
    }),
};
