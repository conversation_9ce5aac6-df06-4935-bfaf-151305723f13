import {
  ExportOutlined,
  ImportOutlined,
  PlusOutlined,
  SearchOutlined,
} from "@ant-design/icons";
import { Button, Card, Input, message, Popconfirm, Space } from "antd";
import { withdrawMethodApi } from "api/withdrawMethod.api";
import { useRank } from "hooks/useRank";
import { useEffect, useMemo, useRef, useState } from "react";
import { PermissionNames } from "router";
import { permissionStore } from "store/permissionStore";
import { WithdrawMethod } from "types/withdrawMethod";
import { formatVND, getTitle } from "utils";
import { checkRole } from "utils/auth";
import { unixToFullDate } from "utils/dateFormat";
import { handleExport, MyExcelColumn } from "../../utils/MyExcel";
import {
  WithdrawConfigModal,
  WithdrawConfigModalRef,
} from "./components/Modal/WithdrawConfigModal";
import { WithdrawConfigList } from "./components/Table/WithdrawConfigList";
import { TextField } from "@mui/material";
import { useTranslation } from "react-i18next";
import { useWithdrawMethod } from "hooks/useWithdrawMethod";
import { PaymentConfigModal } from "views/PaymentConfig/components/Modal/PaymentConfigModal";
import { PaymentConfigList } from "views/PaymentConfig/components/Table/PaymentConfigList";

export const WithdrawConfigPage = ({ title = "" }) => {
  const withdrawConfigModalRef = useRef<WithdrawConfigModalRef>();
  const [openImport, setOpenImport] = useState(false);
  const { t } = useTranslation();

  const [loadingDelete, setLoadingDelete] = useState(false);
  const { withdrawMethods, fetchData, loading, query, total } =
    useWithdrawMethod({
      initQuery: {
        page: 1,
        limit: 20,
      },
    });
  // const hasPaymentConfigAddPermission = checkRole(
  //   PermissionNames.consumerPaymentConfigAdd,
  //   permissionStore.permissions
  // );
  // const hasPaymentConfigUpdatePermission = checkRole(
  //   PermissionNames.consumerIndustryEdit,
  //   permissionStore.permissions
  // );
  // const hasIndustryDeletePermission = checkRole(
  //   PermissionNames.consumerIndustryDelete,
  //   permissionStore.permissions
  // );
  useEffect(() => {
    document.title = getTitle(t(title));
    fetchData();
  }, []);
  const exportColumns: MyExcelColumn<WithdrawMethod>[] = [
    {
      width: 30,
      header: t("withdrawConfigName"),
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "name",
      // style: { font: { color: { argb: "004e47cc" } } },
      render: (record: WithdrawMethod) => {
        return record.name;
      },
    },
    {
      width: 30,
      header: t("withdrawConfigNameEn"),
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "nameEn",
      // style: { font: { color: { argb: "004e47cc" } } },
      render: (record: WithdrawMethod) => {
        return record.nameEn;
      },
    },
    {
      width: 20,
      header: t("createdAt"),
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "createdAt",
      render: (record: WithdrawMethod) => {
        return unixToFullDate(record.createdAt);
      },
    },
  ];
  const handleDeleteWithdrawConfig = async (withdrawConfigId: number) => {
    try {
      setLoadingDelete(true);
      const res = await withdrawMethodApi.delete(withdrawConfigId);
      fetchData();
      message.success(t("actionSuccessfully"));
    } catch (error) {
    } finally {
      setLoadingDelete(false);
    }
  };

  return (
    <Card bodyStyle={{ padding: "8px 20px" }} style={{ borderRadius: "8px" }}>
      <section className="box">
        <div className="filter-container">
          <Space wrap>
            <div className="filter-item ">
              <label htmlFor="">{t("search")}</label>
              <Input
                allowClear
                onChange={(ev) => {
                  if (ev.currentTarget.value) {
                    query.search = ev.currentTarget.value;
                  } else {
                    query.search = undefined;
                  }
                  query.page = 1;
                  fetchData();
                }}
                onKeyDown={(ev) => {
                  if (ev.code == "Enter") {
                    fetchData();
                  }
                }}
                size="middle"
                placeholder={t("withdrawMethod")}
              />
              {/* <TextField
                  size="small"
                  onChange={(ev) => {
                    const value = ev.currentTarget.value;
                    if (value) {
                      query.page = 1;
                      query.search = value;
                    } else {
                      query.search = undefined;
                      fetchData();
                    }
                  }}
                  onKeyDown={(ev) => {
                    if (ev.code == "Enter") {
                      fetchData();
                    }
                  }}
                  label={t("paymentMethod")}
                /> */}
            </div>

            <div className="filter-item btn">
              <Button
                onClick={() => fetchData()}
                type="primary"
                icon={<SearchOutlined />}
              >
                {t("search")}
              </Button>
            </div>
            {/* {hasPaymentConfigAddPermission && ( */}
            {/* <div className="filter-item ">
                <Button
                  onClick={() => {
                    paymentConfigModalRef.current?.handleCreate();
                  }}
                  icon={<PlusOutlined />}
                  type="primary"
                >
                  {t("create")}
                </Button>
              </div> */}
            {/* )} */}
            {/* <div className="filter-item btn">
                      <Button
                        onClick={() => {
                          setOpenImport(true);
                        }}
                        type="primary"
                        icon={<PlusOutlined />}
                      >
                        Nhập excel
                      </Button>
                    </div> */}
            {/* <div className="filter-item btn">
                        <Button
                          onClick={() => {
                            importModal.current?.open();
                          }}
                          type="primary"
                          icon={<ImportOutlined />}
                        >
                          Nhập excel
                        </Button>
                      </div> */}

            <div className="filter-item btn">
              <Popconfirm
                title={t("exportAsk")}
                onConfirm={() =>
                  handleExport({
                    onProgress(percent) {},
                    exportColumns,
                    fileType: "xlsx",
                    dataField: "withdrawMethods",
                    query: query,
                    api: withdrawMethodApi.findAll,
                    fileName: t("withdrawMethod"),
                    sheetName: t("withdrawMethod"),
                  })
                }
                okText={t("exportExcel")}
                cancelText={t("cancel")}
              >
                <Button
                  type="primary"
                  loading={false}
                  icon={<ExportOutlined />}
                >
                  {t("exportExcel")}
                </Button>
              </Popconfirm>
            </div>
          </Space>
        </div>

        <WithdrawConfigList
          onEdit={(record) =>
            withdrawConfigModalRef.current?.handleUpdate(record)
          }
          dataSource={withdrawMethods}
          loading={loading}
          loadingDelete={loadingDelete}
          pagination={{
            total: total,
            defaultPageSize: query.limit,
            currentPage: query.page,
            onChange: ({ page, limit }) => {
              Object.assign(query, {
                page,
                limit,
              });
              fetchData();
            },
          }}
          onDelete={handleDeleteWithdrawConfig}
          onSubmitOk={fetchData}

          // onActive={handleActivePaymentConfig}
          // onInactive={handleInactivePaymentConfig}

          // hasDeletePaymentConfigPermission={hasPaymentConfigDeletePermission}
          // hasUpdatePaymentConfigPermission={hasPaymentConfigUpdatePermission}
        />
      </section>

      <PaymentConfigModal
        ref={withdrawConfigModalRef}
        onClose={function (): void {
          throw new Error("Function not implemented.");
        }}
        onSubmitOk={fetchData}
        // hasAddIndustryPermission={hasIndustryAddPermission}
        // hasUpdateIndustryPermission={hasIndustryUpdatePermission}
      />
    </Card>
  );
};
