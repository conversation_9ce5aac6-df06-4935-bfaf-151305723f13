import {
  Alert,
  Checkbox,
  Col,
  Form,
  Input,
  InputNumber,
  message,
  Modal,
  Radio,
  Row,
  Select,
} from "antd";
import TextArea from "antd/es/input/TextArea";
import { UploadFile } from "antd/lib";
import { Rule } from "antd/lib/form";
import { useWatch } from "antd/lib/form/Form";
import { configurationApi } from "api/configuration";
import { RichTextEditorV2 } from "components/Editor/RichTextEditorV2";
import { FileUpload } from "components/Upload/FileUpload";
import { useEffect, useMemo, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import {
  ConfigSlugsNote,
  Configuration,
  ConfigurationParam,
  DataType,
  InspecReviewOptionTrans,
} from "types/configuration";
import { ModalStatus } from "types/modal";

const rules: Rule[] = [{ required: true }];

const ZALO_PREFIX = "https://zalo.me/";
const MESSAGER_PREFIX = "https://www.messenger.com/t/";

const slugs = {};

const emailRule = {
  pattern:
    /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
  // pattern: /\d+/g,
  message: "Email không đúng định dạng.",
  required: true,
};

export const ConfigurationModal = ({
  visible,
  status,
  configuration,
  onClose,
  onSubmitOk,
}: {
  visible: boolean;
  status: ModalStatus;
  configuration: Partial<
    Configuration & {
      section: {
        title: string;
        params: ConfigurationParam[];
        list: Configuration[];
        type: DataType;
      };
    }
  >;
  onClose: () => void;
  onSubmitOk: () => void;
}) => {
  const [form] = Form.useForm<
    Configuration & { configuration: Configuration }
  >();
  const value = useWatch("value", form);
  const [docFile, setDocFile] = useState<UploadFile[]>([]);

  const [loading, setLoading] = useState(false);
  const [content, setContent] = useState("");
  const [contentEn, setContentEn] = useState("");
  const { t } = useTranslation();

  useEffect(() => {
    if (status == "create" && visible) {
      form.resetFields();
    }
  }, [visible, status]);

  useEffect(() => {
    console.log({ configuration });
    form.setFieldsValue({ ...configuration });
    setContent(configuration.value || "");
    setContentEn(configuration.valueEn || "");
    setDocFile([
      {
        uid: configuration.value || "",
        name: configuration.value || "",
      },
    ]);
    return () => {};
  }, [configuration]);

  const isPoliciesContent = useMemo(() => {
    return (
      configuration?.param &&
      [
        ConfigurationParam.SecurityPolicy,
        ConfigurationParam.WarrantyPolicy,
        ConfigurationParam.PaymentPolicy,
        ConfigurationParam.SecurityPolicyPartner,
        ConfigurationParam.WarrantyPolicyPartner,
        ConfigurationParam.PaymentPolicyPartner,
      ].includes(configuration?.param)
    );
  }, [configuration]);

  const createData = async () => {
    const valid = await form.validateFields();
    const data = { staff: form.getFieldsValue() };

    setLoading(true);
    try {
      const res = await configurationApi.create(data);
      message.success(t("actionSuccessfully"));
      onClose();
      onSubmitOk();
    } finally {
      setLoading(false);
    }
  };

  const updateData = async () => {
    const valid = await form.validateFields();
    const { value, valueEn } = form.getFieldsValue();
    setLoading(true);
    try {
      const res = await configurationApi.update(configuration?.id || 0, {
        configuration: { ...configuration, value, valueEn },
      });
      message.success(t("actionSuccessfully"));
      onClose();
      onSubmitOk();
    } finally {
      setLoading(false);
    }
  };

  const rules = useMemo(() => {
    switch (configuration.param) {
      default:
        return [];
    }
  }, [configuration]);

  return (
    <Modal
      onCancel={onClose}
      visible={visible}
      title={status == "create" ? t("create") : t("update")}
      style={{ top: 20 }}
      width={isPoliciesContent ? 1300 : 700}
      confirmLoading={loading}
      onOk={() => {
        status == "create" ? createData() : updateData();
      }}
      okText={t("save")}
      cancelText={t("close")}
    >
      <Form
        layout="vertical"
        form={form}
        validateTrigger={["onBlur", "onChange"]}
      >
        <Row gutter={16}>
          <Col span={24}>
            <Form.Item
              hidden
              name="configuration"
              initialValue={configuration}
              label={configuration.title}
            >
              <Input placeholder="" value={configuration.value} />
            </Form.Item>

            {configuration?.dataType == DataType.Boolean ? (
              <Form.Item
                valuePropName="checked"
                name="value"
                label={t(configuration?.param)}
                rules={rules}
              >
                <Radio.Group
                  value={value}
                  onChange={(e) => {
                    form.setFieldsValue({
                      value: e.target.value,
                    });
                  }}
                >
                  <Radio value={"true"}>{t("on")}</Radio>
                  <Radio value={"false"}>{t("off")}</Radio>
                </Radio.Group>
              </Form.Item>
            ) : configuration?.dataType == DataType.Enum ? (
              <Form.Item
                name="value"
                label={t(configuration?.param)}
                rules={rules}
              >
                <Select>
                  {Object.values(InspecReviewOptionTrans).map(({ value }) => (
                    <Select.Option key={value} value={value}>
                      {t(value)}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            ) : configuration?.dataType == DataType.Number ? (
              <Form.Item
                name="value"
                label={t(configuration?.param)}
                rules={rules}
              >
                <InputNumber
                  className="w-full"
                  placeholder=""
                  value={configuration.value}
                  min="0"
                />
              </Form.Item>
            ) : configuration?.dataType == DataType.Content ? (
              <>
                <Form.Item name="value" hidden>
                  <Input />
                </Form.Item>
                <Form.Item name="valueEn" hidden>
                  <Input />
                </Form.Item>
                {isPoliciesContent ? (
                  <div className="grid grid-cols-2 gap-4">
                    <RichTextEditorV2
                      key={"vi"}
                      label={
                        <span className="font-medium">{t("contentVi")}</span>
                      }
                      onChange={(content) => {
                        setContent(content);
                        form.setFieldsValue({ value: content });
                      }}
                      content={content}
                    />
                    <RichTextEditorV2
                      key={"en"}
                      label={
                        <span className="font-medium">{t("contentEn")}</span>
                      }
                      onChange={(content) => {
                        setContentEn(content);
                        form.setFieldsValue({ valueEn: content });
                      }}
                      content={contentEn}
                    />
                  </div>
                ) : (
                  <RichTextEditorV2
                    label={<span className="font-medium">{t("content")}</span>}
                    onChange={(content) => {
                      setContent(content);
                      console.log({ content });
                      form.setFieldsValue({ value: content });
                    }}
                    content={content}
                  />
                )}
              </>
            ) : configuration?.dataType == DataType.File ? (
              <Form.Item shouldUpdate={true}>
                {() => {
                  return (
                    <Form.Item
                      name="value"
                      label={t(configuration?.param)}
                      rules={rules}
                      // rules={[...rules]}
                    >
                      <FileUpload
                        fileList={docFile}
                        onDelete={() =>
                          form.setFieldsValue({ value: undefined })
                        }
                        onUploadOk={(filePath: string) => {
                          console.log(filePath);
                          form.setFieldsValue({
                            value: filePath,
                          });
                          setDocFile([
                            {
                              uid: filePath || "",
                              name: filePath || "",
                            },
                          ]);
                        }}
                      />
                    </Form.Item>
                  );
                }}
              </Form.Item>
            ) : (
              <Form.Item
                name="value"
                label={t(configuration?.param)}
                rules={rules}
              >
                <TextArea
                  placeholder=""
                  value={configuration.value}
                  rows={10}
                />
              </Form.Item>
            )}
          </Col>
        </Row>
        {(configuration.param == ConfigurationParam.PromptContent ||
          configuration.param == ConfigurationParam.PromptKeyword) && (
          <div className="bg-[#fbf4c2] p-2 rounded mb-2">
            <div className="flex gap-2">
              <div className="font-bold">{`{keywords}: `}</div>
              <div>{t("keywordProject")}</div>
            </div>

            <div className="flex gap-2">
              <div className="font-bold">{`{description}: `}</div>
              <div>{t("descriptionProject")}</div>
            </div>

            <div className="flex gap-2">
              <div className="font-bold">{`{existingContents}: `}</div>
              <div>{t("contentProject")}</div>
            </div>
            <div className="flex gap-2">
              <div className="font-bold">{`{characters}: `}</div>
              <div>{t("maxCharacterProject")}</div>
            </div>
            <div className="flex gap-2">
              <div className="font-bold">{`{maxContent}: `}</div>
              <div>{t("maxReviewGemini")}</div>
            </div>
            <div className="flex gap-2">
              <div className="font-bold">{`{merchant}: `}</div>
              <div>{t("reviewLocation")}</div>
            </div>
            <div className="flex gap-2">
              <div className="font-bold">{`{lang}: `}</div>
              <div>{t("langConfig")}</div>
            </div>
            <div className="flex gap-2">
              <div className="font-bold">{`{existingKeywords}: `}</div>
              <div>{t("existingKeywords")}</div>
            </div>
          </div>
        )}
      </Form>
      {/* @ts-ignore */}
      {ConfigSlugsNote[configuration?.param]?.length && (
        <Alert
          type="warning"
          description={
            <ul style={{ marginBottom: 0 }}>
              {/* @ts-ignore */}
              {ConfigSlugsNote[configuration?.param]?.map((item) => (
                <li>
                  <b>{item.slug}</b> : {item.content}
                </li>
              ))}
            </ul>
          }
        />
      )}
    </Modal>
  );
};
