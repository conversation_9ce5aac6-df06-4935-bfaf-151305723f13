import { Col, Image, Popconfirm, Spin, Tooltip } from "antd";
import React, { useState } from "react";
import { FiDownload } from "react-icons/fi";
import { IoTrashOutline } from "react-icons/io5";
import { downloadFile } from "utils/downloadFile";
import { FaFileAlt } from "react-icons/fa";
import FileIcon from "./RenderIconFile";
import { useTranslation } from "react-i18next";
import { FileAttachType } from "types/fileAttach";
import clsx from "clsx";

export interface FileCustomProps {
  id?: string;
  fileName: string;
  fileSize: number;
  fileUrl: string;
  fileType?: string;
}

export interface FileAttachPayload {
  name: string;
  size: number;
  type: FileAttachType;
  uid: string;
  url: string;
  path: string;
  destination: string;
  id?: number;
}

interface FileUploadItemProps {
  file: FileCustomProps;
  loadingDelete?: boolean;
  showName?: boolean;
  onDelete?: (id: string) => void;
}

const FileUploadItem: React.FC<FileUploadItemProps> = ({
  file,
  loadingDelete,
  showName,
  onDelete,
}) => {
  const { t } = useTranslation();
  const [loadingDownload, setLoadingDownload] = useState(false);

  const content = (
    <div className="relative shadow-md w-fit rounded-xl">
      <div
        className={clsx(
          "flex items-center justify-center gap-5",
          showName && "bg-[#FAFAFA] p-2 rounded-xl flex-col w-[136px] h-[136px]"
        )}
      >
        <FileIcon
          mimeType={file.fileType}
          url={file.fileUrl}
          className="rounded-xl overflow-hidden flex items-center justify-center w-20 h-20"
        />
        {showName && (
          <div className="flex flex-col gap-1 flex-1">
            <Tooltip title={file.fileName}>
              <span className="text-sm font-bold break-all line-clamp-1">
                {file.fileName}
              </span>
            </Tooltip>
          </div>
        )}
        {onDelete && (
          <Popconfirm
            title={t("confirmDeleteFile")}
            onConfirm={() => {
              onDelete(file.id || "");
            }}
            okButtonProps={{ loading: loadingDelete }}
          >
            <div className="absolute -top-2 -right-2 flex justify-center items-center bg-red-500 p-[6px] rounded-full shadow-md">
              <IoTrashOutline className="cursor-pointer text-white text-base"></IoTrashOutline>
            </div>
          </Popconfirm>
        )}
      </div>
    </div>
  );

  return file.fileType === "application/pdf" ? (
    <a href={file.fileUrl} target="_blank" rel="noopener noreferrer">
      {content}
    </a>
  ) : (
    content
  );
  // return (
  //   <div className="p-2 rounded-xl bg-[#FAFAFA] mt-3">
  //     <div className="flex items-center gap-5">
  //       <div className="">
  //         <FileIcon mimeType={file.fileType} url={file.fileUrl} />
  //       </div>
  //       <div className="flex flex-col gap-1 flex-1">
  //         <Tooltip title={file.fileName}>
  //           <span className="text-sm font-bold break-all line-clamp-1">
  //             {file.fileName}
  //           </span>
  //         </Tooltip>
  //         <span className="text-sm text-gray">
  //           {file.fileSize >= 1024 ? (
  //             <span>{(file.fileSize / 1024 / 1024).toFixed(2)}Mb</span>
  //           ) : (
  //             <span>{(file.fileSize / 1024).toFixed(2)}Kb</span>
  //           )}
  //         </span>
  //         <div>
  //           <div className="flex items-center justify-between">
  //             <Spin spinning={loadingDownload}>
  //               <span
  //                 onClick={() =>
  //                   downloadFile(
  //                     file.fileUrl,
  //                     file.fileName,
  //                     setLoadingDownload
  //                   )
  //                 }
  //                 className="cursor-pointer text-[18px] flex items-center gap-1 justify-between"
  //               >
  //                 <div className="">
  //                   <FiDownload className="text-primary text-sm" />
  //                 </div>
  //                 <span className="text-primary text-sm font-medium">
  //                   {t("downloadFile")}
  //                 </span>
  //               </span>
  //             </Spin>
  //             {onDelete && (
  //               <Popconfirm
  //                 title={t("confirmDeleteFile")}
  //                 onConfirm={() => {
  //                   onDelete(file.id || "");
  //                 }}
  //                 okButtonProps={{ loading: loadingDelete }}
  //               >
  //                 <IoTrashOutline className="cursor-pointer text-red-500 text-base"></IoTrashOutline>
  //               </Popconfirm>
  //             )}
  //           </div>
  //         </div>
  //       </div>
  //     </div>
  //   </div>
  // );
};

export default FileUploadItem;
