import {
  ExportOutlined,
  PlusOutlined,
  SearchOutlined,
} from "@ant-design/icons";
import { Button, Card, Input, Popconfirm, Select, Space, message } from "antd";
import { onlinePaymentApi } from "api/onlinePayment.api";
import { useOnlinePayment } from "hooks/useOnlinePayment";
import { observer } from "mobx-react-lite";
import { useEffect, useRef } from "react";
import { getTitle } from "utils";
import { OnlinePaymentModal } from "./components/Modal/OnlinePaymentModal";
import { OnlinePaymentList } from "./components/Table/OnlinePaymentList";
import ConfirmExportExcel from "components/ConfirmExportExcel/ConfirmExportExcel";
import { AxiosPromise } from "axios";
import { useTranslation } from "react-i18next";
import { handleExport, MyExcelColumn } from "utils/MyExcel";
import { OnlinePayment } from "types/online-payment";
import { unixToFullDate } from "utils/dateFormat";
// import { exportOnlinePayments } from "./util/ExportOnlinePayment";

export const OnlinePaymentPage = observer(
  ({ title = "" }: { title?: string }) => {
    const onlinePaymentModalRef = useRef<OnlinePaymentModal>();
    const { t } = useTranslation();
    const exportColumns: MyExcelColumn<OnlinePayment>[] = [
      {
        width: 80,
        header: t("onlinePaymentName"),
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "name",
        // style: { font: { color: { argb: "004e47cc" } } },
        render: (record: OnlinePayment) => {
          return record.name;
        },
      },
      {
        width: 30,
        header: t("stk"),
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "bankNumber",
        // style: { font: { color: { argb: "004e47cc" } } },
        render: (record: OnlinePayment) => {
          return record.bankNumber;
        },
      },
      {
        width: 30,
        header: t("ownerName"),
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "ownerName",
        // style: { font: { color: { argb: "004e47cc" } } },
        render: (record: OnlinePayment) => {
          return record.ownerName;
        },
      },
      {
        width: 30,
        header: t("type"),
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "type",
        // style: { font: { color: { argb: "004e47cc" } } },
        render: (record: OnlinePayment) => {
          return record.type;
        },
      },
      {
        width: 20,
        header: t("createdAt"),
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "createdAt",
        render: (record: OnlinePayment) => {
          return unixToFullDate(record.createdAt);
        },
      },
    ];
    const {
      onlinePayments,
      fetchOnlinePayment,
      loadingOnlinePayment,
      queryOnlinePayment,
      totalOnlinePayment,
    } = useOnlinePayment({
      initQuery: {
        limit: 20,
        search: "",
        page: 1,
      },
    });

    useEffect(() => {
      document.title = getTitle(t(title));
      fetchOnlinePayment();
    }, []);

    const handleDeleteOnlinePayment = async (onlinePaymentId: number) => {
      try {
        const res = await onlinePaymentApi.delete(onlinePaymentId);
        message.success(t("actionSuccessfully"));
        fetchOnlinePayment();
      } catch (error) {}
    };

    // useEffect(() => {
    //   queryOnlinePayment.useFor = useFor;
    //   fetchOnlinePayment();
    // }, [useFor]);

    return (
      <Card bodyStyle={{ padding: "8px 20px" }} style={{ borderRadius: "8px" }}>
        <div className="filter-container">
          <Space>
            <div className="filter-item">
              <label htmlFor="">{t("search")}</label>
              <Input
                allowClear
                onChange={(ev) => {
                  if (ev.currentTarget.value) {
                    queryOnlinePayment.search = ev.currentTarget.value;
                  } else {
                    queryOnlinePayment.search = undefined;
                  }
                  queryOnlinePayment.page = 1;
                  fetchOnlinePayment();
                }}
                onKeyDown={(ev) => {
                  if (ev.code == "Enter") {
                    fetchOnlinePayment();
                  }
                }}
                size="middle"
                placeholder={t("onlinePaymentName")}
              />
            </div>
            <div className="filter-item btn">
              <Button
                onClick={() => {
                  fetchOnlinePayment();
                }}
                type="primary"
                icon={<SearchOutlined />}
              >
                {t("search")}
              </Button>
            </div>

            <div className="filter-item btn">
              <Button
                onClick={() => {
                  onlinePaymentModalRef.current?.handleCreate();
                }}
                type="primary"
                icon={<PlusOutlined />}
              >
                {t("create")}
              </Button>
            </div>
            {/* <div className="filter-item btn">
              <ConfirmExportExcel
                list={onlinePayments}
                query={queryOnlinePayment}
                dataField={"onlinePayments"}
                api={onlinePaymentApi.findAll}
                fileName={"Danh sách thông tin thanh toán"}
                sheetName={"Danh sách thông tin thanh toán"}
                exportColumns={exportOnlinePayments}
              />
            </div> */}
{/* 
            <div className="filter-item btn">
              <Popconfirm
                title={t("exportAsk")}
                onConfirm={() =>
                  handleExport({
                    onProgress(percent) {},
                    exportColumns,
                    fileType: "xlsx",
                    dataField: "onlinePayments",
                    query: queryOnlinePayment,
                    api: onlinePaymentApi.findAll,
                    fileName: t("onlinePayment"),
                    sheetName: t("onlinePayment"),
                  })
                }
                okText={t("exportExcel")}
                cancelText={t("cancel")}
              >
                <Button
                  type="primary"
                  loading={false}
                  icon={<ExportOutlined />}
                >
                  {t("exportExcel")}
                </Button>
              </Popconfirm>
            </div> */}
          </Space>
        </div>

        <OnlinePaymentList
          dataSource={onlinePayments}
          loading={loadingOnlinePayment}
          pagination={{
            total: totalOnlinePayment,
            defaultPageSize: queryOnlinePayment.limit,
            currentPage: queryOnlinePayment.page,
            onChange: ({ page, limit }) => {
              Object.assign(queryOnlinePayment, {
                page,
                limit,
              });
              fetchOnlinePayment();
            },
          }}
          onlinePaymentModalRef={onlinePaymentModalRef}
          onRefreshData={fetchOnlinePayment}
          onDelete={handleDeleteOnlinePayment}
        />

        <OnlinePaymentModal
          ref={onlinePaymentModalRef}
          onClose={() => {
            ("");
          }}
          onSubmitOk={fetchOnlinePayment}
        />
      </Card>
    );
  }
);
