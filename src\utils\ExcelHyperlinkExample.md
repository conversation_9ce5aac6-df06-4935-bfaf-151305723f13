# Excel Hyperlink Export Example

Ví dụ về cách xuất Excel với hyperlink trong TaskListTab.

## Tính năng đã thêm

1. **Hyperlink Support**: Cột hình ảnh sẽ tạo hyperlink khi có cả hình ảnh và link đánh giá
2. **Custom Text**: Hiển thị text "Xem hình ảnh" thay vì URL dài
3. **Fallback Logic**: Xử lý các trường hợp khác nhau một cách thông minh

## Logic xử lý

```typescript
// Trong TaskListTab exportColumns
{
  width: 25,
  header: t("image"),
  key: "isExistImage",
  isHyperLink: true, // Bật tính năng hyperlink
  render: (record: Review) => {
    if (record.isExistImage && record.fileAttaches?.[0]?.url && record.reviewUrl) {
      // C<PERSON> hình ảnh VÀ có link đánh giá -> tạo hyperlink
      return {
        text: "Xem hình ảnh",           // Text hiển thị
        hyperlink: record.reviewUrl,    // URL đích (link đánh giá)
        styles: {
          font: { color: { argb: "#0066cc" } }
        }
      };
    } else if (record.isExistImage && record.fileAttaches?.[0]?.url) {
      // Có hình ảnh NHƯNG không có link đánh giá -> hiển thị URL hình ảnh
      return record.fileAttaches[0].url;
    } else {
      // Không có hình ảnh -> hiển thị "Không"
      return t("no");
    }
  }
}
```

## Các trường hợp xử lý

### Trường hợp 1: Có hình ảnh + có link đánh giá
- **Hiển thị**: "Xem hình ảnh" (màu xanh, có thể click)
- **Khi click**: Mở link đánh giá (`record.reviewUrl`)
- **Mục đích**: Người dùng click vào sẽ đi đến trang đánh giá để xem chi tiết

### Trường hợp 2: Có hình ảnh + không có link đánh giá  
- **Hiển thị**: URL của hình ảnh (có thể click)
- **Khi click**: Mở hình ảnh trực tiếp
- **Mục đích**: Xem hình ảnh gốc

### Trường hợp 3: Không có hình ảnh
- **Hiển thị**: "Không" (text thường)
- **Khi click**: Không có action
- **Mục đích**: Thông báo không có hình ảnh

## Cải tiến trong MyExcel

MyExcel đã được cập nhật để hỗ trợ:

1. **Object Hyperlink**: Nhận object với `text`, `hyperlink`, và `styles`
2. **Backward Compatibility**: Vẫn hoạt động với string đơn giản
3. **Custom Styling**: Cho phép tùy chỉnh màu sắc và style

```typescript
// MyExcel sẽ xử lý cả hai format:

// Format 1: String đơn giản (như trước)
render: () => "https://example.com"

// Format 2: Object với text và hyperlink riêng biệt (mới)
render: () => ({
  text: "Click here",
  hyperlink: "https://example.com",
  styles: { font: { color: { argb: "#0066cc" } } }
})
```

## Lợi ích

1. **User Experience**: Text "Xem hình ảnh" dễ hiểu hơn URL dài
2. **Functionality**: Click vào sẽ đi đến trang đánh giá (có context đầy đủ)
3. **Flexibility**: Xử lý được nhiều trường hợp khác nhau
4. **Consistency**: Giữ được tính nhất quán với UI hiện tại
