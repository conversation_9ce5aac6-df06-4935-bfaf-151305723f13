import {
  LoadingOutlined,
  PlusOutlined,
  DeleteOutlined,
  CloseOutlined,
  UploadOutlined,
} from "@ant-design/icons";
import { Button, Space, Spin, Tooltip, Upload, message } from "antd";
import { UploadChangeParam } from "antd/lib/upload";
import { useEffect, useRef, useState } from "react";
import { AWS } from "utils/Aws";
import { $url } from "utils/url";
import { v4 } from "uuid";
import { ReactComponent as UploadVideo } from "../../../public/uploadVideo.svg";

import { CONFIG } from "config";

interface SingleImageUploadProps {
  videoUrl: string;
  onUploadOk: (path: string, thumbnail: string) => void;
  onChangeDuration?: (duration: number) => void;
  onBefore?: () => Promise<boolean>;
  onRemoveVideo: () => void;
}

export const SingleVideoUploadS3 = ({
  videoUrl,
  onUploadOk,
  onChangeDuration,
  onBefore,
  onRemoveVideo,
}: SingleImageUploadProps) => {
  const [loading, setLoading] = useState(false);
  const [visible, setVisible] = useState(false);
  const sourceVideoRef = useRef<HTMLVideoElement>(null);

  const beforeUpload = async (file: File) => {
    let isValid = true;
    if (onBefore) {
      isValid = await onBefore?.();
    }

    const extension = file.name.substring(file.name.lastIndexOf("."));
    let isVideo = true;
    if (extension != ".mp4") {
      isVideo = false;
      message.error("Chỉ hỗ trợ file MP4");
    }

    return isValid && isVideo ? true : Upload.LIST_IGNORE;
  };
  const handleChange = (info: UploadChangeParam<any>) => {
    // if (info.file.status === "uploading") {
    //   setLoading(true);
    //   return;
    // }
    // if (info.file.status === "done") {
    //   // onUploadOk(process.env.REACT_APP_API_URL + info.file.response.data.path);
    //   setLoading(false);
    // }
  };

  const uploadButton = (
    <div>
      <Spin spinning={loading}>
        <div className="bg-white relative">
          <UploadVideo />
          <h1 className="absolute bottom-[20px] text-xs text-center px-[15px]">
            Kéo thả vào đây hoặc click vào để upload
          </h1>
        </div>
      </Spin>
    </div>
  );

  const uploadFile = async (file: File) => {
    const filename = v4() + ".mp4";
    setLoading(true);
    try {
      const res = await AWS.uploadToS3({
        contentType: "video/mp4",
        file,
        key: filename,
        onProgress: (percent) => {
          //
        },
      });

      // const { data } = await mediaApi.getThumnail({
      //   key: filename,
      // });
      // const thumbnail = data.thumbnail;

      setLoading(false);

      onUploadOk(CONFIG.AWS_S3_BUCKET_DOMAIN + "/" + filename, "thumbnail");
    } catch (error) {}
    return filename;
  };

  console.log(videoUrl);

  return (
    <div className="flex justify-center items-center">
      <Upload
        name="file"
        accept={"video/mp4"}
        className="video-uploader"
        showUploadList={false}
        action={uploadFile}
        beforeUpload={beforeUpload}
        onChange={handleChange}
      >
        {!videoUrl && <>{uploadButton}</>}
      </Upload>

      {!!videoUrl && (
        <div style={{ position: "relative" }}>
          <Tooltip title={"Xóa"}>
            <div
              onClick={onRemoveVideo}
              style={{
                position: "absolute",
                right: "-5px",
                top: 5,
                zIndex: 99,
              }}
              className="cursor-pointer !rounded-full !border-none !text-white !bg-[#24242480] !w-[35px] !h-[35px] flex justify-center"
            >
              <CloseOutlined className="text-lg " />
            </div>
          </Tooltip>
          <video
            onLoadedData={(e) => {
              //@ts-ignore
              onChangeDuration(Math.floor(e.target?.duration || 0));
            }}
            ref={sourceVideoRef}
            controls
            src={$url(videoUrl)}
            style={{ width: 400, height: 200, borderRadius: 5 }}
          />
        </div>
      )}
    </div>
  );
};
