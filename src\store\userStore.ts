import OneSignalReact from "react-onesignal";

import { authApi } from "api/auth.api";
import { setToken } from "utils/auth";
import { action, makeAutoObservable } from "mobx";
import { makePersistable } from "mobx-persist-store";
import { Staff } from "types/staff";
import { $isDev } from "constant";

class UserStore {
  constructor() {
    makeAutoObservable(this);
    makePersistable(this, {
      name: "UserStore",
      properties: ["info", "token"],
      storage: window.localStorage,
    });
  }

  info: Partial<Staff> = {};
  token = "";
  isLoggedIn: boolean = false;

  @action
  async login(username: string, password: string) {
    const res = await authApi.login({ username, password });
    setToken(res.data.token);
    this.token = res.data.token;
    return res.data.token;
  }

  @action
  async getProfile() {
    const res = await authApi.profile();
    this.info = res.data;
    this.isLoggedIn = true;
  }

  @action
  async changeLanguage(language: string) {
    const res = await authApi.updateLang({
      lang: language?.toUpperCase(),
    });
    await this.getProfile();
  }

  @action
  logout = () => {
    if (!$isDev) {
      const token = this.token;
      //   oneSignal.userLogout(token);
    }
    setToken("");
    this.token = "";
    this.info = {};
    this.isLoggedIn = false;
  };
}

const userStore = new UserStore();

export { userStore };
