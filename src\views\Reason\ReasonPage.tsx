import {
  ExportOutlined,
  ImportOutlined,
  PlusOutlined,
  SearchOutlined,
} from "@ant-design/icons";
import { Button, Card, Input, message, Popconfirm, Space } from "antd";
import { reasonApi } from "api/reason.api";
import { useReason } from "hooks/useReason";
import { useEffect, useMemo, useRef, useState } from "react";
import { PermissionNames } from "router";
import { permissionStore } from "store/permissionStore";
import { Reason } from "types/reason";
import { formatVND, getTitle } from "utils";
import { checkRole } from "utils/auth";
import { unixToFullDate } from "utils/dateFormat";
import { handleExport, MyExcelColumn } from "../../utils/MyExcel";
import { ReasonModal, ReasonModalRef } from "./components/Modal/ReasonModal";
import { ReasonList } from "./components/Table/ReasonList";
import { TextField } from "@mui/material";
import { useTranslation } from "react-i18next";

export const ReasonPage = ({ title = "" }) => {
  const reasonModalRef = useRef<ReasonModalRef>();
  const [openImport, setOpenImport] = useState(false);
  const { t } = useTranslation();

  const [loadingDelete, setLoadingDelete] = useState(false);
  const { reasons, fetchReason, loadingReason, queryReason, totalReason } =
    useReason({
      initQuery: {
        page: 1,
        limit: 20,
      },
    });
  // const hasReasonAddPermission = checkRole(
  //   PermissionNames.consumerReasonAdd,
  //   permissionStore.permissions
  // );
  // const hasReasonUpdatePermission = checkRole(
  //   PermissionNames.consumerIndustryEdit,
  //   permissionStore.permissions
  // );
  // const hasIndustryDeletePermission = checkRole(
  //   PermissionNames.consumerIndustryDelete,
  //   permissionStore.permissions
  // );
  useEffect(() => {
    document.title = getTitle(t(title));
    fetchReason();
  }, []);
  const exportColumns: MyExcelColumn<Reason>[] = [
    {
      width: 30,
      header: t("reasonName"),
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "name",
      render: (record: Reason) => {
        return record.name;
      },
    },
    {
      width: 30,
      header: t("reasonNameEn"),
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "nameEn",
      render: (record: Reason) => {
        return record.nameEn;
      },
    },

    {
      width: 20,
      header: t("createdAt"),
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "createdAt",
      render: (record: Reason) => {
        return unixToFullDate(record.createdAt);
      },
    },
  ];

  const handleDeleteReason = async (reasonId: number) => {
    try {
      setLoadingDelete(true);
      const res = await reasonApi.delete(reasonId);
      fetchReason();
      message.success(t("actionSuccessfully"));
    } catch (error) {
    } finally {
      setLoadingDelete(false);
    }
  };

  return (
    <Card bodyStyle={{ padding: "8px 20px" }} style={{ borderRadius: "8px" }}>
      <section className="box">
        <div className="filter-container">
          <Space wrap>
            <div className="filter-item ">
              <label htmlFor="">{t("search")}</label>
              <Input
                allowClear
                onChange={(ev) => {
                  if (ev.currentTarget.value) {
                    queryReason.search = ev.currentTarget.value;
                  } else {
                    queryReason.search = undefined;
                  }
                  queryReason.page = 1;
                  fetchReason();
                }}
                onKeyDown={(ev) => {
                  if (ev.code == "Enter") {
                    fetchReason();
                  }
                }}
                size="middle"
                placeholder={t("reason")}
              />
            </div>

            <div className="filter-item btn">
              <Button
                onClick={() => fetchReason()}
                type="primary"
                icon={<SearchOutlined />}
              >
                {t("search")}
              </Button>
            </div>
            {/* {hasReasonAddPermission && ( */}
            <div className="filter-item btn">
              <Button
                onClick={() => {
                  reasonModalRef.current?.handleCreate();
                }}
                icon={<PlusOutlined />}
                type="primary"
              >
                {t("create")}
              </Button>
            </div>
            {/* )} */}
            {/* <div className="filter-item btn">
                    <Button
                      onClick={() => {
                        setOpenImport(true);
                      }}
                      type="primary"
                      icon={<PlusOutlined />}
                    >
                      Nhập excel
                    </Button>
                  </div> */}
            {/* <div className="filter-item btn">
                      <Button
                        onClick={() => {
                          importModal.current?.open();
                        }}
                        type="primary"
                        icon={<ImportOutlined />}
                      >
                        Nhập excel
                      </Button>
                    </div> */}
{/* 
            <div className="filter-item btn">
              <Popconfirm
                title={t("exportAsk")}
                onConfirm={() =>
                  handleExport({
                    onProgress(percent) {},
                    exportColumns,
                    fileType: "xlsx",
                    dataField: "reasons",
                    query: queryReason,
                    api: reasonApi.findAll,
                    fileName: t("reason"),
                    sheetName: t("reason"),
                  })
                }
                okText={t("exportExcel")}
                cancelText={t("cancel")}
              >
                <Button
                  type="primary"
                  loading={false}
                  icon={<ExportOutlined />}
                >
                  {t("exportExcel")}
                </Button>
              </Popconfirm>
            </div> */}
          </Space>
        </div>

        <ReasonList
          onEdit={(record) => reasonModalRef.current?.handleUpdate(record)}
          dataSource={reasons}
          loading={loadingReason}
          loadingDelete={loadingDelete}
          pagination={{
            total: totalReason,
            defaultPageSize: queryReason.limit,
            currentPage: queryReason.page,
            onChange: ({ page, limit }) => {
              Object.assign(queryReason, {
                page,
                limit,
              });
              fetchReason();
            },
          }}
          onDelete={handleDeleteReason}
          // onActive={handleActiveReason}
          // onInactive={handleInactiveReason}

          // hasDeleteReasonPermission={hasReasonDeletePermission}
          // hasUpdateReasonPermission={hasReasonUpdatePermission}
        />
      </section>

      <ReasonModal
        ref={reasonModalRef}
        onClose={function (): void {
          throw new Error("Function not implemented.");
        }}
        onSubmitOk={fetchReason}
        // hasAddIndustryPermission={hasIndustryAddPermission}
        // hasUpdateIndustryPermission={hasIndustryUpdatePermission}
      />
    </Card>
  );
};
