import {
  DownOutlined,
  EditOutlined,
  LockOutlined,
  SendOutlined,
  UnlockOutlined,
} from "@ant-design/icons";
import { Button, Popconfirm, Space, Table } from "antd";
import Column from "antd/lib/table/Column";
import { IPagination, Pagination } from "components/Pagination";
import { AriviTable } from "components/Table/AriviTable";
import DropdownCell from "components/Table/DropdownCell";
import { useTranslation } from "react-i18next";
import { GrEdit } from "react-icons/gr";
import { HiOutlineTrash } from "react-icons/hi2";
import { Notification } from "types/notification";
import { formatVND } from "utils";
import { unixToFullDate } from "utils/dateFormat";

interface PropsType {
  dataSource: Notification[];
  loading: boolean;
  loadingDelete?: boolean;
  pagination?: IPagination;
  onEdit?: (notification: Notification) => void;
  onDelete?: (notificationId: number) => void;
  onActive?: (notificationId: number) => void;
  onInactive?: (notificationId: number) => void;
  onSend?: (notificationId: number) => void;

  // hasDeleteNotificationPermission?: boolean;
  // hasUpdateNotificationPermission?: boolean;
}

export const NotificationList = ({
  dataSource,
  loading,
  loadingDelete,
  pagination,
  onDelete,
  onEdit,
  onSend,
  onActive,
  onInactive,
}: // hasDeleteNotificationPermission,
// hasUpdateNotificationPermission,
PropsType) => {
  const { t } = useTranslation();

  return (
    <div>
      <AriviTable
        // bordered
        scroll={{ x: "max-content", y: "calc(100vh - 310px)" }}
        loading={loading}
        pagination={false}
        rowKey="id"
        dataSource={dataSource}
        className="custom-scrollbar"
        // onChange={}
      >
        <Column
          width={400}
          title={t("notificationName")}
          dataIndex="name"
          key={"name"}
          render={(text, record: Notification) => (
            <div>
              <div>
                <span className="font-bold">{t("vietnamese")}</span>:{" "}
                {record.title}
              </div>
              <div>
                <span className="font-bold">{t("english")}</span>:{" "}
                {record.titleEn}
              </div>
            </div>
          )}
        />
        {/* <Column
          title={t("shortContent")}
          width={300}
          dataIndex="shortContent"
          key={"shortContent"}
          render={(text, record: Notification) => (
            <div>
              <div>
                <span className="font-bold">{t("vietnamese")}</span>:{" "}
                {record.shortContent}
              </div>
              <div>
                <span className="font-bold">{t("english")}</span>:{" "}
                {record.shortContentEn}
              </div>
            </div>
          )}
        /> */}
        {/* <Column
          title={t("notificationContent")}
          width={400}
          dataIndex="name"
          key={"name"}
          render={(text, record: Notification) => (
            <div>
              <div>
                <span className="font-bold">{t("vietnamese")}</span>:{" "}
                {record.content}
              </div>
              <div>
                <span className="font-bold">{t("english")}</span>:{" "}
                {record.contentEn}
              </div>
            </div>
          )}
        /> */}
        {/* <Column
          title={t("notificationScope")}
          width={200}
          dataIndex="scope"
          key={"scope"}
          render={(scope, record: Notification) => <div>{t(scope)}</div>}
        /> */}
        <Column
          title={t("notificationPublicAt")}
          width={200}
          dataIndex="publicAt"
          key={"publicAt"}
          render={(publicAt, record: Notification) => (
            <div>{publicAt ? unixToFullDate(publicAt) : "--"}</div>
          )}
        />

        <Column
          fixed="right"
          width={120}
          align="center"
          title=""
          key="action"
          dataIndex={""}
          render={(text, record: Notification) => (
            //@ts-ignore
            <DropdownCell
              text={t("action")}
              items={[
                {
                  onClick: () => "",
                  // createOrderModalRef.current?.handleUpdate(record),
                  label: (
                    <Button
                      icon={<GrEdit />}
                      type="primary"
                      className="w-full justify-center !flex !items-center gap-2 !font-medium"
                      onClick={() => onEdit?.(record)}
                    >
                      {t("update")}
                    </Button>
                  ),
                  key: "update",
                  // hidden: !hasUpdateNotificationPermission,
                },

                {
                  onClick: () => "",
                  // createOrderModalRef.current?.handleUpdate(record),
                  label: (
                    <Button
                      icon={<SendOutlined />}
                      type="primary"
                      className="w-full justify-center !flex !items-center gap-2 !font-medium"
                      onClick={() => onSend?.(record.id)}
                    >
                      {t("send")}
                    </Button>
                  ),
                  key: "send",
                  hidden: record.publicAt !== 0,
                },
                {
                  label: (
                    <Popconfirm
                      placement="topLeft"
                      title={
                        <div>
                          <h1 className="text-sm">{t("confirm?")}</h1>
                        </div>
                      }
                      onConfirm={() => onDelete?.(record.id)}
                      okText={t("yes")}
                      cancelText={t("no")}
                    >
                      <Button
                        loading={loadingDelete}
                        icon={<HiOutlineTrash className="text-lg" />}
                        className={`w-full justify-center !flex !items-center gap-2 !text-red-500 !font-medium`}
                      >
                        {t("delete")}
                      </Button>
                    </Popconfirm>
                  ),
                  key: "delete",
                  // hidden: !hasDeleteNotificationPermission,
                },
              ].filter((it) => !it.hidden)}
              trigger={["click"]}
            >
              <a onClick={(e) => e.preventDefault()}>
                <Space>
                  {t("action")}
                  <DownOutlined />
                </Space>
              </a>
            </DropdownCell>
          )}
        />
      </AriviTable>
      {pagination && <Pagination {...pagination} />}
    </div>
  );
};
