import { configurationApi } from "api/configuration";
import { useState } from "react";
import { Configuration } from "types/configuration";
import { QueryParam } from "types/query";

export interface ConfigurationQuery extends QueryParam {}

interface UseConfigurationProps {
  initQuery: ConfigurationQuery;
}

export const useConfiguration = ({ initQuery }: UseConfigurationProps) => {
  const [data, setData] = useState<Configuration[]>([]);
  const [total, setTotal] = useState(0);
  const [query, setQuery] = useState<ConfigurationQuery>(initQuery);
  const [loading, setLoading] = useState(false);

  const fetchData = async () => {
    setLoading(true);
    try {
      const { data } = await configurationApi.findAll(query);

      setData(data.configurations);
      setTotal(data.total);
    } finally {
      setLoading(false);
    }
  };

  return {
    configurations: data,
    totalConfiguration: total,
    fetchConfiguration: fetchData,
    loadingConfiguration: loading,
    setQueryConfiguration: setQuery,
    queryConfiguration: query,
  };
};
