import { request } from "utils/request";
import { AxiosPromise } from "axios";

export const projectApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/project",
      params,
    }),
  findOne: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/project/${id}`,
    }),
  findSummary: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/project/summary",
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/project",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/project/${id}`,
      method: "patch",
      data,
    }),
  payAgain: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/project/${id}/confirmPayment/complete`,
      method: "patch",
      // data,
    }),
  payment: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/project/${id}/payment`,
      method: "post",
    }),
  reopen: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/project/${id}/reopen`,
      method: "patch",
    }),
  cancel: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/project/${id}/cancel`,
      method: "patch",
    }),
  rejectWarranty: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/project/${id}/reject/warranty`,
      data,
      method: "patch",
    }),
  warranty: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/project/${id}/warranty`,
      data,
      method: "patch",
    }),
  pause: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/project/${id}/pause`,
      method: "patch",
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/project/${id}`,
      method: "delete",
    }),
  estimate: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/project/estimate",
      data,
      method: "post",
    }),
  preEstimate: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/project/pre-estimate",
      data,
      method: "post",
    }),
};
