import { Customer, Language } from "./customer";
import { FileAttach } from "./fileAttach";
import { Keyword } from "./keyword";
import { PaymentStatus } from "./payment";
import { PaymentType } from "./paymentType";
import { Product } from "./product";
import { Promotion } from "./promotion";
import { Review } from "./review";
import { Staff } from "./staff";
import { StarDetail } from "./starDetail";

export enum ProjectStatus {
  New = "NEW",
  Processing = "PROCESSING", // đang phân phối
  Completed = "COMPLETE",
  Paused = "PAUSE",
  Cancel = "CANCEL",
  Warranty = "WARRANTY", // đang phân phối lại lần 2
  RequestWarranty = "REQUEST_WARRANTY", // yêu cầu bảo hành
  RejectWarranty = "REJECT_WARRANTY", // Huỷ yêu cầu bảo hành
  <PERSON> = "ALL",
}

export const ProjectStatusTrans = {
  [ProjectStatus.New]: {
    value: ProjectStatus.New,
    color: "green",
    label: `project${ProjectStatus.New}`,
  },
  [ProjectStatus.Processing]: {
    value: ProjectStatus.Processing,
    color: "orange",
    label: `project${ProjectStatus.Processing}`,
  },
  [ProjectStatus.Completed]: {
    value: ProjectStatus.Completed,
    color: "blue",
    label: `project${ProjectStatus.Completed}`,
  },
  [ProjectStatus.Paused]: {
    value: ProjectStatus.Paused,
    color: "red",
    label: `project${ProjectStatus.Paused}`,
  },
}

// export const ProjectStatusTrans = {
//   [ProjectStatus.All]: {
//     label: "Tất cả",
//     value: ProjectStatus.All,
//     color: "",
//     description: "",
//   },
//   [ProjectStatus.New]: {
//     label: "Mới",
//     value: ProjectStatus.New,
//     color: "purple",
//     description: "",
//   },
//   [ProjectStatus.Processing]: {
//     label: "Đang diễn ra",
//     value: ProjectStatus.Processing,
//     color: "yellow",
//     description: "processingDes",
//   },
//   [ProjectStatus.Completed]: {
//     label: "Hoàn thành",
//     value: ProjectStatus.Completed,
//     color: "green",
//     description: "",
//   },
//   [ProjectStatus.Paused]: {
//     label: "Đã dừng",
//     value: ProjectStatus.Paused,
//     color: "red",
//     description: "",
//   },
//   [ProjectStatus.Cancel]: {
//     label: "Đã hủy",
//     value: ProjectStatus.Cancel,
//     color: "red",
//     description: "",
//   },
//   [ProjectStatus.Warranty]: {
//     label: "Đang bảo hành",
//     value: ProjectStatus.Warranty,
//     color: "orange",
//     description: "warrantyDes",
//   },
//   [ProjectStatus.RequestWarranty]: {
//     label: "Đang bảo hành",
//     value: ProjectStatus.RequestWarranty,
//     color: "pink",
//     description: "requestWarrantyDes",
//   },
//   [ProjectStatus.RejectWarranty]: {
//     label: "Đang bảo hành",
//     value: ProjectStatus.RejectWarranty,
//     color: "",
//     description: "",
//   },
// };

export interface Project {
  id: number;
  createdAt: number;
  updatedAt: number;
  expireWarrantyAt: number;
  isDeleted: boolean;
  code: string;
  name: string;
  placeId: string;
  address: string;
  addressName: string;
  long: number;
  lat: number;
  mapUrl: string;
  totalStar: number; // tổng số sao
  totalReview: number; // tổng số review hiện có
  quantityReview: number; // số lượng review mua từ gói
  isDropSlow: boolean; // true: rải chậm
  dropSlowNumber: number; // số lượng rải chậm trong 1 ngày
  description: string;
  quantity: number;
  amount: number; // tổng tiền
  moneyDiscount: number;
  moneyImage: number; // số tiền phát sinh khi có ảnh
  moneyDrop: number; // số tiền phát sinh khi chọn rải chậm
  moneyFinal: number;
  numberReviewComplete: number; // số lượng review đã hoàn thành
  paymentStatus: PaymentStatus;
  paymentType: PaymentType;
  paidAt: number; // thời gian bắt đầu
  status: ProjectStatus;
  paymentUrl: string;
  moneyTax: number;
  moneyTotal: number;
  tax: number;
  paymentDeepLink: string;
  paymentCode: string;
  transId: string;
  lang: Language; // Ngôn ngữ dự án
  // CUSTOM
  keywords: Keyword[];
  product: Product;
  createdStaff: Staff;
  customer: Customer | null;
  fileAttaches: FileAttach[];
  reviews: Review[];
  starDetails: StarDetail[];
  promotion?: Promotion;
  staff: Staff; // nhân viện phê duyệt thanh toán
  completePercent: number;
  currentCompleteReview: number;
  isCompletedWarranty: boolean;
  totalContentEmpty: number; //Số lượng review chưa có nội dung
  // TODO tính tiền rải chậm
  // TODO tinh thue
}
