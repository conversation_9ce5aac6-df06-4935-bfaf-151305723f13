export enum PromotionStatus {
  Active = "ACTIVE",
  Inactive = "INACTIVE",
}

export enum PromotionDiscountType {
  Percent = "PERCENT",
  Fixed = "FIXED",
}

export const PromotionDiscountTypeTrans = {
  [PromotionDiscountType.Percent]: {
    label: "%",
    value: PromotionDiscountType.Percent,
    color: "green",
  },
  [PromotionDiscountType.Fixed]: {
    label: "đ",
    value: PromotionDiscountType.Fixed,
    color: "red",
  },
};

export const PromotionStatusTrans = {
  [PromotionStatus.Active]: {
    //   label: "Mới",
    value: PromotionStatus.Active,
    color: "green",
  },
  [PromotionStatus.Inactive]: {
    //   label: "<PERSON><PERSON> diễn ra",
    value: PromotionStatus.Inactive,
    color: "red",
  },
};

export interface Promotion {
  id: number;
  createdAt: number;
  updatedAt: number;
  isDeleted: boolean;
  title: string;
  status: PromotionStatus;
  startAt: number;
  endAt: number;
  quantity: number;
  minAmount: number; // số tiền tối thiểu để áp dụng
  discountType: PromotionDiscountType;
  discountValue: number;
  code: string; //
  description: string;
  totalUsed: number;
}
