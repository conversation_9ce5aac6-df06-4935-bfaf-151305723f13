import {
  ExportOutlined,
  ImportOutlined,
  PlusOutlined,
  SearchOutlined,
} from "@ant-design/icons";
import { Button, Card, Input, message, Popconfirm, Space } from "antd";
import { faqApi } from "api/faq.api";
import { useEffect, useMemo, useRef, useState } from "react";
import { PermissionNames } from "router";
import { permissionStore } from "store/permissionStore";
import { getTitle } from "utils";
import { checkRole } from "utils/auth";
import { unixToFullDate } from "utils/dateFormat";
import { handleExport, MyExcelColumn } from "../../utils/MyExcel";
import { FaqModal, FaqModalRef } from "./components/Modal/FaqModal";
import { FaqList } from "./components/Table/FaqList";
import { TextField } from "@mui/material";
import { Faq, FaqCategoryType } from "types/faq";
import { useFaq } from "hooks/useFaq";
import { useTranslation } from "react-i18next";
import { ReactComponent as PlusIcon } from "assets/svgs/plus-icon.svg";
import { ReactComponent as SearchIcon } from "assets/svgs/search-icon.svg";

const exportColumns: MyExcelColumn<Faq>[] = [
  {
    header: "Tên Faq (VI)",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "name",
    columnKey: "name",
    render: (record) => record.name,
  },
  {
    header: "Tên Faq (ENG)",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "nameEn",
    columnKey: "nameEn",
    render: (record) => record.nameEn,
  },
  {
    header: "Ngày tạo",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "createdDateTime",
    columnKey: "createdDateTime",
    render: (record) => unixToFullDate(record.createdAt),
  },
];

export const FaqPage = ({
  title = "",
  faqCategoryType = FaqCategoryType.Customer,
}) => {
  const faqModalRef = useRef<FaqModalRef>();
  const [openImport, setOpenImport] = useState(false);
  const { t } = useTranslation();

  const [loadingDelete, setLoadingDelete] = useState(false);
  const { faqs, fetchFaq, loadingFaq, queryFaq, totalFaq } = useFaq({
    initQuery: {
      page: 1,
      limit: 20,
      faqCategoryType,
    },
  });

  // const hasFaqAddPermission = checkRole(
  //   PermissionNames.consumerFaqAdd,
  //   permissionStore.permissions
  // );
  // const hasFaqUpdatePermission = checkRole(
  //   PermissionNames.consumerIndustryEdit,
  //   permissionStore.permissions
  // );
  // const hasIndustryDeletePermission = checkRole(
  //   PermissionNames.consumerIndustryDelete,
  //   permissionStore.permissions
  // );
  useEffect(() => {
    document.title = getTitle(t(title));
    fetchFaq();
  }, []);

  const handleDeleteFaq = async (faqId: number) => {
    try {
      setLoadingDelete(true);
      const res = await faqApi.delete(faqId);
      fetchFaq();
      message.success(t("actionSuccessfully"));
    } catch (error) {
    } finally {
      setLoadingDelete(false);
    }
  };

  const handleSearch = (search: string) => {
    queryFaq.search = search;
    queryFaq.page = 1;
    fetchFaq();
  };

  return (
    // <Card bodyStyle={{ padding: "8px 20px" }} style={{ borderRadius: "8px" }}>
    <div>
      <section className="box">
        <div className="filter-container">
          <Space wrap>
            {/* <div className="filter-item"> */}
              {/* <label htmlFor="">Tìm kiếm</label>
                  <br /> */}
              {/* 
              <TextField
                // allowClear
                size="small"
                onChange={(ev) => {
                  const value = ev.currentTarget.value;
                  if (value) {
                    queryFaq.page = 1;
                    queryFaq.search = value;
                  } else {
                    queryFaq.search = undefined;
                    fetchFaq();
                  }
                }}
                onKeyDown={(ev) => {
                  if (ev.code == "Enter") {
                    fetchFaq();
                  }
                }}
                label={t("faqName")}
              /> */}
              {/* <label htmlFor="">{t("search")}</label>
              <Input.Search
                allowClear
                onChange={(ev) => {
                  if (ev.currentTarget.value) {
                    queryFaq.search = ev.currentTarget.value;
                  } else {
                    queryFaq.search = undefined;
                  }
                  queryFaq.page = 1;
                  // fetchFaq();
                }}
                onKeyDown={(ev) => {
                  if (ev.code == "Enter") {
                    // fetchFaq();
                  }
                }}
                size="large"
                className="w-full search-btn mt-1"
                // placeholder={t("productName")}
                enterButton={< SearchIcon />}
                onSearch={handleSearch}
              /> */}
            {/* </div> */}
            {/* 
            <div className="filter-item btn">
              <Button
                onClick={() => fetchFaq()}
                type="primary"
                icon={<SearchOutlined />}
              >
                {t("search")}
              </Button>
            </div> */}
            {/* {hasFaqAddPermission && ( */}
            <div className="filter-item">
              <Button
                onClick={() => {
                  faqModalRef.current?.handleCreate();
                }}
                icon={<PlusIcon />}
                type="primary"
                size="large"
              >
                {t("create")}
              </Button>
            </div>
            {/* )} */}
            {/* <div className="filter-item btn">
                  <Button
                    onClick={() => {
                      setOpenImport(true);
                    }}
                    type="primary"
                    icon={<PlusOutlined />}
                  >
                    Nhập excel
                  </Button>
                </div> */}
            {/* <div className="filter-item btn">
                    <Button
                      onClick={() => {
                        importModal.current?.open();
                      }}
                      type="primary"
                      icon={<ImportOutlined />}
                    >
                      Nhập excel
                    </Button>
                  </div> */}

            {/* <div className="filter-item btn">
                  <Popconfirm
                    title={`Bạn có muốn xuất file excel`}
                    onConfirm={() =>
                      handleExport({
                        onProgress(percent) {},
                        exportColumns,
                        fileType: "xlsx",
                        dataField: "industries",
                        query: queryFaq,
                        api: FaqApi.findAll,
                        fileName: "Danh sách FAQ",
                        sheetName: "Danh sách FAQ",
                      })
                    }
                    okText={"Xuất excel"}
                    cancelText={"Huỷ"}
                  >
                    <Button
                      type="primary"
                      loading={false}
                      icon={<ExportOutlined />}
                    >
                      Xuất excel
                    </Button>
                  </Popconfirm>
                </div> */}
          </Space>
        </div>

        <FaqList
          onEdit={(record) => faqModalRef.current?.handleUpdate(record)}
          dataSource={faqs}
          loading={loadingFaq}
          loadingDelete={loadingDelete}
          pagination={totalFaq > queryFaq.limit ? {
            total: totalFaq,
            defaultPageSize: queryFaq.limit,
            currentPage: queryFaq.page,
            onChange: ({ page, limit }) => {
              Object.assign(queryFaq, {
                page,
                limit,
              });
              fetchFaq();
            },
          } : undefined}
          onDelete={handleDeleteFaq}

        // hasDeleteFaqPermission={hasFaqDeletePermission}
        // hasUpdateFaqPermission={hasFaqUpdatePermission}
        />
      </section>

      <FaqModal
        ref={faqModalRef}
        onClose={function (): void {
          throw new Error("Function not implemented.");
        }}
        onSubmitOk={fetchFaq}
        faqCategoryType={faqCategoryType}
      // hasAddIndustryPermission={hasIndustryAddPermission}
      // hasUpdateIndustryPermission={hasIndustryUpdatePermission}
      />
      {/* </Card> */}
    </div>
  );
};
