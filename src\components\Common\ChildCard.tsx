// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore

import { <PERSON>, Card<PERSON>ontent, CardHeader, Divider } from "@mui/material";

type Props = {
  title?: React.ReactNode;
  children: any | any[];
  codeModel?: any | any[];
  className?: string;
  classNameContent?: string;
};

const ChildCard = ({
  title,
  children,
  codeModel,
  className,
  classNameContent,
}: Props) => (
  <Card
    sx={{ padding: 0, borderColor: (theme: any) => theme.palette.divider }}
    variant="outlined"
    className={className}
  >
    {title ? (
      <>
        <CardHeader title={title} action={codeModel} />
        <Divider />{" "}
      </>
    ) : (
      ""
    )}

    <CardContent className={classNameContent}>{children}</CardContent>
  </Card>
);

export default ChildCard;
