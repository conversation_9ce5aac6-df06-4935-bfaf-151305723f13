import { Col, Form, Input, message, Modal, Row } from "antd";
import { useForm } from "antd/lib/form/Form";
import { feelingApi } from "api/feeling.api";
import { forwardRef, useImperativeHandle, useState } from "react";
import { useTranslation } from "react-i18next";
import { ModalStatus } from "types/modal";
import { Feeling } from "types/feeling";
import { requiredRule } from "utils/validate-rules";

export interface FeelingModalProps {
  onClose: () => void;
  onSubmitOk: () => void;
}

export interface FeelingModalRef {
  handleUpdate: (feeling: Feeling) => void;
  handleCreate: () => void;
}

export const FeelingModal = forwardRef(
  ({ onSubmitOk }: FeelingModalProps, ref) => {
    const [form] = useForm();
    const [loading, setLoading] = useState(false);
    const [visible, setVisible] = useState<boolean>();
    const { t } = useTranslation();
    const [selectedFeeling, setSelectedFeeling] = useState<Feeling>();
    const [status, setStatus] = useState<ModalStatus>("create");

    useImperativeHandle(ref, () => ({
      handleUpdate,
      handleCreate,
    }));

    const handleUpdate = (feeling: Feeling) => {
      form.setFieldsValue({ ...feeling });
      setSelectedFeeling(feeling);
      setStatus("update");
      setVisible(true);
    };

    const handleCreate = () => {
      setStatus("create");
      setVisible(true);
    };

    const handleSubmitForm = async () => {
      await form.validateFields();
      const dataForm = form.getFieldsValue();
      if (dataForm.name) {
        dataForm.name = dataForm.name.trim();
      }

      const payload = {
        feeling: {
          ...dataForm,
        },
      };

      try {
        setLoading(true);
        if (status === "update") {
          await feelingApi.update(selectedFeeling?.id || 0, payload);
        } else {
          await feelingApi.create(payload);
        }
        message.success(t("actionSuccessfully"));
        setVisible(false);
        onSubmitOk();
      } finally {
        setLoading(false);
      }
    };

    return (
      <Modal
        visible={visible}
        centered
        title={
          <h1 className="mb-0 text-lg text-primary font-bold">
            {status === "create" ? t("create") : t("update")}
          </h1>
        }
        confirmLoading={loading}
        destroyOnClose
        width={800}
        onOk={handleSubmitForm}
        onCancel={() => setVisible(false)}
        afterClose={() => {
          form.resetFields();
        }}
      >
        <Form
          form={form}
          layout="vertical"
          validateTrigger={["onBlur", "onChange"]}
        >
          <Row gutter={[12, 0]}>
            <Col span={24}>
              <Form.Item
                className="!mb-2"
                name="name"
                label={t("content")}
                rules={[requiredRule]}
              >
                <Input placeholder={t("enterName")} size="small" />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    );
  }
);
