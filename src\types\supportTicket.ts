import { Customer } from "./customer";
import { FileAttach } from "./fileAttach";
import { Partner } from "./partner";
import { Staff } from "./staff";

export enum SupportTicketCreatedBy {
  Customer = "CUSTOMER",
  Partner = "PARTNER",
  Admin = "ADMIN",
}

export enum SupportTicketStatus {
  All = "ALL",
  New = "NEW",
  Processing = "PROCESSING", //
  Complete = "COMPLETE",
  Responded = "RESPONDED",
}

export const SupportTicketStatusTrans = {
  [SupportTicketStatus.All]: {
    label: "Tất cả",
    value: SupportTicketStatus.All,
    color: "blue",
  },
  [SupportTicketStatus.New]: {
    label: "Mới",
    value: SupportTicketStatus.New,
    color: "yellow",
  },
  // [SupportTicketStatus.Processing]: {
  //   label: "Đang xử lý",
  //   value: SupportTicketStatus.Processing,
  //   color: "orange",
  // },
  [SupportTicketStatus.Responded]: {
    label: "<PERSON><PERSON><PERSON> thành",
    value: SupportTicketStatus.Responded,
    color: "green",
  },
  // [SupportTicketStatus.Complete]: {
  //   label: "<PERSON><PERSON>n thành",
  //   value: SupportTicketStatus.Complete,
  //   color: "blue",
  // },
};

export interface SupportTicket {
  id: number;
  createdAt: number;
  updatedAt: number;
  isDeleted: boolean;
  title: string;
  createdBy: SupportTicketCreatedBy; //dành cho KH, partner
  content: string;
  status: SupportTicketStatus;
  customer: Customer;
  partner: Partner;
  staff: Staff;
  fileAttaches: FileAttach[];
  children: SupportTicket[];
  parent: SupportTicket;
  department: string;
}
