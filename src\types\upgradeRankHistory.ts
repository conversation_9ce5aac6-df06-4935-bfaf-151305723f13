import { Customer } from "./customer";
import { CustomerRank } from "./customer-rank";
import { Product } from "./product";
import { Staff } from "./staff";

export interface UpgradeRankHistory {
  id: number;
  createdAt: number;
  updatedAt: number;
  isDeleted: boolean;
  deletedAt: number;
  customer: Customer;
  preRank: CustomerRank;
  prevProduct: Product;
  product: Product;
  subPreRank: CustomerRank;
  nextRank: CustomerRank;
  subNextRank: CustomerRank;
  staff: Staff;
  expiredAt: number;
  note: string;
}
