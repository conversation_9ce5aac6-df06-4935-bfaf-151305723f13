.rich-text-editor {
  position: relative;

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;

    &-label {
      font-weight: 600;
    }

    &-actions {
      display: flex;
      gap: 8px;
    }
  }

  &__expand-btn {
    position: absolute;
    top: 8px;
    right: 8px;
    z-index: 1001;
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid #d9d9d9;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    &:hover {
      background: rgba(255, 255, 255, 1);
      border-color: #40a9ff;
    }
  }

  &__container {
    position: relative;

    &--expanded {
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      background-color: white;
      z-index: 9999;
      padding: 20px;
      box-sizing: border-box;
      overflow: hidden;
    }
  }

  &__editor {
    &--expanded {
      .ql-container {
        height: calc(100vh - 100px) !important;
        max-height: calc(100vh - 100px) !important;
        min-height: calc(100vh - 100px) !important;
      }

      .ql-editor {
        height: calc(100vh - 100px) !important;
        max-height: calc(100vh - 100px) !important;
        min-height: calc(100vh - 100px) !important;
      }

      .ql-toolbar {
        position: sticky;
        top: 0;
        z-index: 1000;
        background: white;
        border-bottom: 1px solid #ccc;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
    }
  }

  &__hint {
    position: absolute;
    top: 60px;
    right: 20px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 12px;
    z-index: 1002;
    pointer-events: none;

    &::before {
      content: '';
      position: absolute;
      top: -5px;
      right: 20px;
      width: 0;
      height: 0;
      border-left: 5px solid transparent;
      border-right: 5px solid transparent;
      border-bottom: 5px solid rgba(0, 0, 0, 0.7);
    }
  }

  // Responsive
  @media (max-width: 768px) {
    &__container--expanded {
      padding: 10px;
    }

    &__editor--expanded {
      .ql-container {
        height: calc(100vh - 120px) !important;
        max-height: calc(100vh - 120px) !important;
        min-height: calc(100vh - 120px) !important;
      }

      .ql-editor {
        height: calc(100vh - 160px) !important;
        max-height: calc(100vh - 160px) !important;
        min-height: calc(100vh - 160px) !important;
      }
    }

    &__hint {
      top: 50px;
      right: 10px;
      font-size: 11px;
      padding: 4px 8px;
    }
  }
}

.ql-editor {

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    +br {
      display: none; // Ẩn br ngay sau heading
    }

    +p:empty {
      display: none; // Ẩn p rỗng sau heading
    }
  }
}