import { request } from "utils/request";
import { AxiosPromise } from "axios";

export const withdrawMethodApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/withdrawMethod",
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/withdrawMethod",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/withdrawMethod/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/withdrawMethod/${id}`,
      method: "delete",
    }),
};
