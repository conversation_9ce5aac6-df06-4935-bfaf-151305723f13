import { configurationApi } from "api/configuration";
import { action, makeAutoObservable } from "mobx";
import { makePersistable } from "mobx-persist-store";
import {
  Configuration,
  ConfigurationParam,
  DataType,
} from "types/configuration";

interface CustomizerType {
  activeDir?: string | any;
  activeMode?: string; // This can be light or dark
  activeTheme?: string; // BLUE_THEME, GREEN_THEME, BLACK_THEME, PURPLE_THEME, ORANGE_THEME
  SidebarWidth?: number;
  MiniSidebarWidth?: number;
  TopbarHeight?: number;
  isCollapse?: boolean;
  isLayout?: string;
  isSidebarHover?: boolean;
  isMobileSidebar?: boolean;
  isHorizontal?: boolean;
  isLanguage?: string;
  isCardShadow?: boolean;
  borderRadius?: number | any;
}

class AppStore {
  constructor() {
    makeAutoObservable(this);
    makePersistable(this, {
      name: "AppStore",
      properties: ["customizer", "rememberThisDevice"],
      storage: window.localStorage,
    });
  }

  customizer: CustomizerType = {
    activeDir: "ltr",
    activeMode: "light", // This can be light or dark
    activeTheme: "BLUE_THEME", // BLUE_THEME, GREEN_THEME, BLACK_THEME, PURPLE_THEME, ORANGE_THEME
    SidebarWidth: 270,
    MiniSidebarWidth: 87,
    TopbarHeight: 88,
    isLayout: "boxed", // This can be full or boxed
    isCollapse: false, // to make sidebar Mini by default
    isSidebarHover: false,
    isMobileSidebar: false,
    isHorizontal: false,
    isLanguage: "en",
    isCardShadow: true,
    borderRadius: 7,
  };
  configurations: Configuration[] = [];

  rememberThisDevice: boolean = true;
  refreshSticket: boolean = true;

  changeRememberThisDevice(value: boolean) {
    this.rememberThisDevice = value;
  }
  async fetchConfiguration() {
    const { data } = await configurationApi.findAll({ page: 1, limit: 100 });
    this.configurations = data.configurations;
  }
  getOneConfiguration(param: ConfigurationParam) {
    const config = this.configurations.find((c) => c.param == param);
    if (config?.dataType == DataType.Boolean) {
      return config.value == "true" ? true : false;
    } else if (config?.dataType == DataType.Number) {
      return Number(config.value);
    }

    return config?.value || "";
  }

  //customizer
  setTheme(payload: any) {
    this.customizer.activeTheme = payload;
  }
  setDarkMode(payload: any) {
    this.customizer.activeMode = payload;
  }

  setDir(payload: any) {
    this.customizer.activeDir = payload;
  }
  setLanguage(payload: any) {
    this.customizer.isLanguage = payload;
  }
  setCardShadow(payload: any) {
    this.customizer.isCardShadow = payload;
  }
  toggleSidebar() {
    this.customizer.isCollapse = !this.customizer.isCollapse;
  }
  hoverSidebar(payload: any) {
    this.customizer.isSidebarHover = payload;
  }
  toggleMobileSidebar() {
    this.customizer.isMobileSidebar = !this.customizer.isMobileSidebar;
  }
  toggleLayout(payload: any) {
    this.customizer.isLayout = payload;
  }
  toggleHorizontal(payload: any) {
    this.customizer.isHorizontal = payload;
  }
  setBorderRadius(payload: any) {
    this.customizer.borderRadius = payload;
  }
  @action
  setRefreshSticket(value: boolean) {
    this.refreshSticket = value;
  }
}

const appStore = new AppStore();

export { appStore };
