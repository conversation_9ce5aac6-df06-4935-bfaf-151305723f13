import { Customer } from "./customer";
import { MultipleLang } from "./faq";
import { Partner } from "./partner";
import { Staff } from "./staff";

export enum NotificationScope {
  Customer = "CUSTOMER",
  Partner = "PARTNER",
  Admin = "ADMIN",
}

export enum NotificationMode {
  Public = "PUBLIC",
  Private = "PRIVATE",
}

export enum NotificationEventType {
  PurchaseOrder = "PURCHASE_ORDER",
}

export interface ReadNotification {
  id: number;
  createdAt: number;
  updatedAt: number;
  isDeleted: boolean;
  staff: Staff;
  customer: Customer;
  partner: Partner;
  notification: Notification;
}

export interface Notification {
  id: number;
  createdAt: number;
  updatedAt: number;
  isDeleted: boolean;
  title: string;
  content: string;
  shortContent: string;
  shortContentEn: string;
  mode: NotificationMode;
  scope: NotificationScope;
  eventType: NotificationEventType;
  publicAt: number; // thời gian gửi
  //custom
  titleEn: string;
  contentEn: string;
  multipleLangs: MultipleLang[];
  readNotifications: ReadNotification[];
}
