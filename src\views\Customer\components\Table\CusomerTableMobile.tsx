import { <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>confirm, <PERSON>, Space, Tag } from "antd";
import { Customer, CustomerStatus, CustomerStatusTrans } from "types/customer";
import { formatVND } from "utils";
import { useTranslation } from "react-i18next";
import { KeyOutlined, LockOutlined, UnlockOutlined } from "@ant-design/icons";
import { GrEdit } from "react-icons/gr";
import { HiOutlineTrash } from "react-icons/hi2";
import { unixToFullDate } from "utils/dateFormat";
import { ReactComponent as UserIcon } from "assets/svgs/user-icon.svg";
import { ReactComponent as MailIcon } from "assets/svgs/mail-icon.svg";
import { ReactComponent as CallIcon } from "assets/svgs/call-icon.svg";
import { ReactComponent as ResetPasswordIcon } from "assets/svgs/reset-password.svg";
import { ReactComponent as LockIcon } from "assets/svgs/icon-lock.svg";
import { ReactComponent as UnlockIcon } from "assets/svgs/icon-unlock.svg";
import { ReactComponent as DeleteIcon } from "assets/svgs/icon-delete.svg";

interface Props {
  dataSource: Customer[];
  loading?: boolean;
  onEdit: (record: Customer) => void;
  onActive?: (id: number) => void;
  onInactive?: (id: number) => void;
  onDelete?: (id: number) => void;
  onReset?: (record: Customer) => void;
}

export const CustomerTableMobile: React.FC<Props> = ({
  dataSource,
  loading,
  onEdit,
  onActive,
  onInactive,
  onDelete,
  onReset,
}) => {
  const { t } = useTranslation();

  return (
    <Row gutter={[16, 16]}>
      {dataSource.map((customer) => (
        <Col key={customer.id} xs={24} sm={12} md={8} lg={6}>
          <Card loading={loading} className="shadow">
            <div className="flex flex-col gap-3 text-[#32343A]">
              <div className="flex items-center gap-2">
                <img
                  src={
                    customer.avatar ||
                    "https://i.pinimg.com/736x/0d/64/98/0d64989794b1a4c9d89bff571d3d5842.jpg"
                  }
                  alt="avatar"
                  className="w-[46px] min-w-[46px] h-[46px] rounded-full object-cover"
                />
                <div className="w-full flex flex-col gap-1">
                  <div className="font-semibold text-[16px]">{customer.name}</div>
                  <div className="flex justify-between w-full">
                    <div className="flex items-center gap-1">
                      <UserIcon />
                      <div className="text-[12px] font-regular">{t("code")}: {customer.code}</div>
                    </div>

                    <Tag color={CustomerStatusTrans[customer.status]?.color} className="text-[10px] me-0">
                      {t(customer.status)}
                    </Tag>
                  </div>
                </div>
              </div>

              <div className="flex justify-between">
                <div className="flex gap-1 items-center">
                  <MailIcon />
                  <div className="text-[12px] font-regular">{customer.email}</div>
                </div>
                <div className="flex gap-1 items-center">
                  <CallIcon />
                  <div className="text-[12px] font-regular">{customer.phone}</div>
                </div>
              </div>

              <div className="flex justify-between text-[12px] font-regular">
                <label>
                  {t("projectCreated")}
                </label>
                <label>
                  {customer.totalProduct ?? 0}
                </label>
              </div>

              <div className="flex justify-between text-[12px] font-regular">
                <label>
                  {t("paidProject")}
                </label>
                <label>
                  {customer.totalProduct ?? 0}
                </label>
              </div>

              <div className="flex justify-between text-[12px] font-regular">
                <label>
                  {t("balance")}
                </label>
                <label>
                  {formatVND(customer.balance || 0)}₫
                </label>
              </div>

              <div className="flex justify-between text-[12px] font-regular">
                <label>
                  {t("createdAt")}
                </label>
                <label>
                  {unixToFullDate(customer.createdAt)}
                </label>
              </div>

              <div className="flex gap-2">
                <Button
                  type="primary"
                  onClick={() => onEdit(customer)}
                  className="flex-1 h-[36px] !flex items-center justify-center gap-2"
                >
                  <label className="text-sm font-regular">{t("viewDetail")}</label>
                </Button>
                <Button
                  className="h-[36px] w-[36px] min-w-[36px] flex-shrink-0 border-[#5A6A85]"
                  icon={<ResetPasswordIcon className="mt-0.5" />}
                  onClick={() => onReset?.(customer)}
                />

                <Popconfirm
                  title={t("confirm?")}
                  onConfirm={() =>
                    customer.status === CustomerStatus.Active
                      ? onInactive?.(customer.id)
                      : onActive?.(customer.id)
                  }
                >
                  <Button
                    className="h-[36px] w-[36px] min-w-[36px] flex-shrink-0"
                    style={{
                      borderColor:
                        customer.status === CustomerStatus.Active ? "#EB9113" : "#3BBF5C",
                    }}
                    icon={
                      customer.status === CustomerStatus.Active ? (
                        <LockIcon className="mt-0.5" />
                      ) : (
                        <UnlockIcon className="mt-0.5" />
                      )
                    }
                  />
                </Popconfirm>

                <Popconfirm title={t("confirm?")} onConfirm={() => onDelete?.(customer.id)}>
                  <Button
                    className="h-[36px] w-[36px] min-w-[36px] flex-shrink-0 border-[#E94134]"
                    danger
                    icon={<DeleteIcon />}
                  />
                </Popconfirm>
              </div>


            </div>
          </Card>
        </Col>
      ))}
    </Row>
  );
};
