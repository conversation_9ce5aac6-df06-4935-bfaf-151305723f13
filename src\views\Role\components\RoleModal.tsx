import { Button, Form, Input, message, Modal, Tag, Tree } from "antd";
import { Rule } from "antd/lib/form";
import TextArea from "antd/lib/input/TextArea";
import { roleApi } from "api/role.api";
import { observer } from "mobx-react";
import { useEffect, useState } from "react";
import { adminRoutes, PermissionNames } from "router";
import { permissionStore } from "store/permissionStore";
import { userStore } from "store/userStore";
import { ModalStatus } from "types/modal";
import { Permission, Role } from "types/role";
import { Staff } from "types/staff";
import { checkRole } from "utils/auth";
import { CheckInfo } from "rc-tree/lib/Tree";
import { useTranslation } from "react-i18next";
import { requiredRule } from "utils/validate-rules";

interface ITreeData {
  title: string;
  key: string;
  children?: ITreeData[];
  disableCheckbox?: boolean;
}

export const RoleModal = observer(
  ({
    open,
    status,
    role,
    onClose,
    onSubmitOk,
  }: {
    open: boolean;
    status: ModalStatus;
    role: Partial<Role>;
    onClose: () => void;
    onSubmitOk: () => void;
  }) => {
    const [form] = Form.useForm<Staff>();
    const [loading, setLoading] = useState(false);
    const [treeData, setTreeData] = useState<ITreeData[]>([]);
    const [checkedKeys, setCheckedKeys] = useState<string[]>([]);
    const [permissions, setPermissions] = useState<Permission[]>([]);
    const { t } = useTranslation();

    useEffect(() => {
      if (status == "create" && open) {
        form.resetFields();
      }
    }, [open, status]);

    // console.log("role", role);
    useEffect(() => {
      form.setFieldsValue({ ...role });
      generateSelectedKeys();
      return () => {};
    }, [role]);

    useEffect(() => {
      if (open) {
        generateTreeData();
        fetchPermissions();
      }
    }, [open]);

    const fetchPermissions = async () => {
      const res = await roleApi.getPermission();
      setPermissions(res.data);
    };

    const generateSelectedKeys = async () => {
      if (role.id) {
        const res = await roleApi.findOne(role.id);
        const permissions: Permission[] = res.data.permissions;
        // permissions.forEach((item) => {
        //   if (item.path?.includes("customer-management/customer-edit"))
        //     debugger;
        // });
        // console.log(
        //   "quyen ne",
        //   JSON.parse(
        //     JSON.stringify(
        //       permissions.filter(
        //         (item) => !item.path.includes(PermissionNames.customerBlock)
        //       )
        //     )
        //   )
        // );
        // permissions.forEach((item) => console.log(item.path.includes("news")));
        setCheckedKeys(
          permissions
            .filter(
              (item) => !item.path.includes(PermissionNames.customerBlock)
            )
            .map((e) => e.path)
        );
      }
    };

    const generateTreeData = () => {
      if (treeData.length) {
        return;
      }

      // console.log(checkedKeys?.some((item) => item == "/role"));
      for (const route of adminRoutes) {
        if (route.isPublic) {
          //Không hiển thị các route public
          continue;
        }
        if (!route.noRole && route.checkIsDev ? userStore.info.isDev : true) {
          const data: ITreeData = {
            title: t(route.title) || "",
            key: route.path || "",
          };

          treeData.push(data);
          if (route.children) {
            data.children = [];
            for (const childRoute of route.children) {
              if (route.checkIsDev ? userStore.info.isDev : true) {
                const isFeature = childRoute.isFeature;
                // if (
                //   childRoute.name?.includes("customer-management/customer-edit")
                // )
                //   debugger;
                data.children.push({
                  //@ts-ignore
                  title: (
                    <div>
                      <span className="mr-2">{t(childRoute.title) || ""}</span>
                      {/* {childRoute.isFeature && (
                        <Tag color="blue" className="mr-2">
                          Tính năng
                        </Tag>
                      )} */}
                    </div>
                  ),
                  key: route.path + "/" + childRoute.name || "",
                });
              }
            }
          }
        }
      }

      setTreeData([...treeData]);
    };

    const createData = async (data: any) => {
      setLoading(true);
      try {
        const res = await roleApi.create(data);
        message.success(t("actionSuccessfully"));
        onClose();
        onSubmitOk();
      } finally {
        setLoading(false);
      }
    };

    const updateData = async (data: any) => {
      setLoading(true);
      try {
        const res = await roleApi.update(role?.id || 0, data);
        message.success(t("actionSuccessfully"));
        onClose();
        onSubmitOk();
      } finally {
        setLoading(false);
      }
    };

    const handleSubmit = async () => {
      const valid = await form.validateFields();
      // debugger;
      const permissionIds = checkedKeys
        ?.map((e) => {
          const find = permissions.find((p) => p.path == e);
          return find?.id;
        })
        .filter((e) => e);
      const data = {
        role: form.getFieldsValue(),

        permissionIds,
      };
      // debugger
      if (status == "create") {
        createData(data);
      } else {
        updateData(data);
      }
    };

    const onSelect = (selectedKeys: any, info: any) => {};

    const onCheck = (keysChecked: any, info: CheckInfo<ITreeData>) => {
      // debugger;
      setCheckedKeys(info.checkedNodes.map((item) => item.key));
    };

    const renderFooter = () => {
      const arr = [];

      const cancelBtn = (
        <Button onClick={onClose} type="default">
          {t("close")}
        </Button>
      );
      const okBtn = (
        <Button
          type="primary"
          onClick={() => {
            handleSubmit();
          }}
        >
          {t("save")}
        </Button>
      );

      // if (status == "create" || role?.id != 1) {
      arr.push(cancelBtn);
      arr.push(okBtn);
      // }

      return arr.length ? arr : false;
    };

    console.log("tree ne", treeData);
    console.log("check key ne", checkedKeys);

    return (
      <Modal
        onCancel={onClose}
        visible={open}
        title={status == "create" ? t("create") : t("update")}
        style={{ top: 20 }}
        footer={renderFooter()}
        width={700}
        confirmLoading={loading}
      >
        <Form
          layout="vertical"
          form={form}
          validateTrigger={["onBlur", "onChange"]}
        >
          <Form.Item label="Level" name="name" rules={[requiredRule]}>
            <Input placeholder="" />
          </Form.Item>

          <Form.Item label={t("description")} name="description">
            <TextArea rows={3} />
          </Form.Item>
        </Form>
        {status == "update" && (
          <Tree
            checkable
            onSelect={onSelect}
            onCheck={onCheck}
            treeData={treeData
              // .filter((item) => item.key != "/profile")
              .map((item) => {
                return {
                  ...item,
                  disableCheckbox:
                    permissions?.some((item) => item.path == "/role") &&
                    role.isAdmin &&
                    item.key == "/role"
                      ? true
                      : false,
                };
              })}
            // checkStrictly
            checkedKeys={checkedKeys}
          ></Tree>
        )}
      </Modal>
    );
  }
);
