import React, { useRef } from "react";
import { useReactToPrint } from "react-to-print";
import jsPDF from "jspdf";
import html2canvas from "html2canvas";
import { useTranslation } from "react-i18next";
import { Button } from "antd";
import "./invoice.scss";

interface Keyword {
  id: number;
  name: string;
}

interface InvoiceData {
  customerName: string;
  companyName: string;
  companyEmail: string;
  companyAddress: string;
  taxCode: string;

  code: string;
  name: string; // địa điểm
  address: string;
  description: string;

  product: {
    name: string;
    code: string;
    numReview: number;
    price: number;
    unitPrice: number;
  };

  amount: number;
  moneyTax: number;
  moneyFinal: number;
  tax: number;

  keywords: Keyword[];
}

interface InvoiceProps {
  data: InvoiceData;
}

const Invoice: React.FC<InvoiceProps> = ({ data }) => {
  const invoiceRef = useRef<HTMLDivElement>(null);
  const { t } = useTranslation();
  const handlePrint = useReactToPrint({
    content: () => invoiceRef.current,
  });

  const handleDownloadPDF = async () => {
    if (!invoiceRef.current) return;
    const canvas = await html2canvas(invoiceRef.current);
    const imgData = canvas.toDataURL("image/png");
    const pdf = new jsPDF();
    const margin = 10;
    const pdfWidth = pdf.internal.pageSize.getWidth() - margin * 2;
    const imgProps = pdf.getImageProperties(imgData);
    const pdfHeight = (imgProps.height * pdfWidth) / imgProps.width;

    pdf.addImage(imgData, "PNG", 0, 0, pdfWidth, pdfHeight);
    pdf.save(`${t("bill")}-${data.code}.pdf`);
  };

  return (
    <div className="p-4 max-w-2xl mx-auto">
      <div ref={invoiceRef} className="bg-white invoice-print p-6">
        <h2 className="text-2xl font-bold mb-4 text-center">{t("bill")}</h2>

        <div className="space-y-2">
          <div><strong>{t("CUSTOMER")}:</strong> {data.customerName}</div>
          <div><strong>{t("companyName")}:</strong> {data.companyName}</div>
          <div><strong>{t("companyEmail")}:</strong> {data.companyEmail}</div>
          <div><strong>{t("companyAddress")}:</strong> {data.companyAddress}</div>
          <div><strong>{t("taxCode")}:</strong> {data.taxCode}</div>
        </div>

        <hr className="my-4" />

        <div className="space-y-2">
          <div><strong>{t("code") + " " + t("project")}:</strong> {data.code}</div>
          <div><strong>{t("projectName")}:</strong> {data.name}</div>
          <div><strong>{t("package")}:</strong> {data.product.name}</div>
          <div><strong>{t("address")}:</strong> {data.address}</div>
          <div><strong>{t("projectDescription")}:</strong> {data.description}</div>
          {data.keywords.length > 0 && (
            <div>
              <strong>{t("keyword")}:</strong>{" "}
              {data.keywords.map(k => k.name).join(", ")}
            </div>
          )}
        </div>

        <hr className="my-4" />

        <div className="mt-4 space-y-1 text-right">
          <div className="text-lg font-bold">{t("totalPrice")}: {data.moneyFinal.toLocaleString()} VND</div>
        </div>
      </div>

      <div className="flex gap-2 mt-4 justify-center">
        <Button
          onClick={handlePrint}
          type="primary"
          className="no-print"
        >
          {t("print")}
        </Button>
        <Button
          onClick={handleDownloadPDF}
          type="primary"
          className="no-print"
        >
          {t("exportPdf")}
        </Button>
      </div>
    </div>
  );
};

export default Invoice;
