import { Button, Col, Form, Input, message, Modal, Row, Select } from "antd";
import { useForm } from "antd/lib/form/Form";
import { forwardRef, useEffect, useImperativeHandle, useState } from "react";
import { ModalStatus } from "types/modal";
import { SurveyCampaign } from "types/survey";
import { requiredRule } from "utils/validate-rules";
import { InputNumber } from "components/Input/InputNumber";
import { faqApi } from "api/faq.api";
import { Faq, FaqCategoryType } from "types/faq";
import TextArea from "antd/es/input/TextArea";
import { useFaqCategory } from "hooks/useFaqCategory";
import { userStore } from "store/userStore";
import { useTranslation } from "react-i18next";

export interface FaqModalProps {
  onClose: () => void;
  onSubmitOk: () => void;
  faqCategoryType?: FaqCategoryType;
  // hasAddFaqPermission?: boolean;
  // hasUpdateFaqPermission?: boolean;
}

export interface FaqModalRef {
  handleUpdate: (faq: Faq) => void;
  handleCreate: () => void;
}

export const FaqModal = forwardRef(
  (
    {
      onSubmitOk,
      faqCategoryType,
    }: // hasAddFaqPermission,
      // hasUpdateFaqPermission,
      FaqModalProps,
    ref
  ) => {
    const [form] = useForm();
    const [loading, setLoading] = useState(false);
    const [visible, setVisible] = useState<boolean>();
    const { t } = useTranslation();

    const [selectedFaq, setSelectedFaq] = useState<Faq>();
    const [status, setStatus] = useState<ModalStatus>("create");
    const {
      faqCategories,
      fetchFaqCategory,
      queryFaqCategory,
      loadingFaqCategory,
      debounceSearchFAQ,
    } = useFaqCategory({
      initQuery: {
        page: 1,
        limit: 50,
        type: faqCategoryType,
        lang:
          userStore.info.language ||
          localStorage.getItem("lng")?.toUpperCase() ||
          "EN",
      },
    });

    useEffect(() => {
      fetchFaqCategory();
    }, []);

    useImperativeHandle(
      ref,
      () => ({
        handleUpdate,
        handleCreate,
      }),
      []
    );

    const handleUpdate = (faq: Faq) => {
      form.setFieldsValue({
        ...faq,
        faqCategoryId: faq.faqCategory?.id,
      });
      setSelectedFaq(faq);
      setStatus("update");
      setVisible(true);
    };

    const handleCreate = () => {
      setStatus("create");
      setVisible(true);
    };

    const handleSubmitForm = async () => {
      await form.validateFields();
      const dataForm = form.getFieldsValue();
      if (dataForm.name) {
        dataForm.name = dataForm.name.trim();
      }
      const payload = {
        faq: {
          ...dataForm,
        },
        faqCategoryId: dataForm.faqCategoryId,
      };

      try {
        setLoading(true);
        switch (status) {
          case "update":
            await faqApi.update(selectedFaq?.id || 0, payload);
            message.success(t("actionSuccessfully"));
            break;

          //create
          default:
            await faqApi.create(payload);
            message.success(t("actionSuccessfully"));
            break;
        }
        setVisible(false);
        onSubmitOk();
      } catch (error) {
      } finally {
        setLoading(false);
        // onSubmitOk();
      }
    };

    return (
      <Modal
        onCancel={() => {
          setVisible(false);
        }}
        visible={visible}
        centered
        title={
          <h1 className="mb-0 text-lg text-primary font-bold">
            {status == "create" ? t("create") : t("update")}
          </h1>
        }
        confirmLoading={loading}
        destroyOnClose
        width={800}
        onOk={handleSubmitForm}
        afterClose={() => {
          form.resetFields();
        }}
        okText={t("save")}
      // okButtonProps={{
      //   hidden:
      //     (!hasAddFaqPermission && status == "create") ||
      //     (!hasUpdateFaqPermission && status == "update"),
      // }}
      >
        <Form
          form={form}
          layout="vertical"
          validateTrigger={["onBlur", "onChange"]}
        >
          <Row gutter={[12, 0]} >
            <Col span={18}>
              <Form.Item
                name="faqCategoryId"
                label={t("faqCategory")}
                rules={[requiredRule]}
              >
                <Select
                  showSearch
                  onSearch={(value) => debounceSearchFAQ(value)}
                  allowClear
                  options={faqCategories.map((item) => {
                    return {
                      label: (
                        <div>
                          <span className="">{item.name}</span>
                        </div>
                      ),
                      value: item.id,
                    };
                  })}
                  onClear={() => {
                    queryFaqCategory.search = "";
                    fetchFaqCategory();
                  }}
                  loading={loadingFaqCategory}
                  filterOption={false}
                  placeholder={t("selectFaqCateogry")}
                  size="large"
                ></Select>
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                name="position"
                label={t("position")}
                rules={[requiredRule]}
              >
                <InputNumber placeholder={t("enterPosition")} />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item
                className="!mb-2"
                name="title"
                label={t("vietnameseTitle")}
                rules={[requiredRule]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item
                className="!mb-2"
                name="content"
                label={t("vietnameseContent")}
                rules={[requiredRule]}
              >
                <TextArea />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item
                className="!mb-2"
                name="titleEn"
                label={t("englishTitle")}
                rules={[requiredRule]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item
                className="!mb-2"
                name="contentEn"
                label={t("englishContent")}
                rules={[requiredRule]}
              >
                <TextArea />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    );
  }
);
