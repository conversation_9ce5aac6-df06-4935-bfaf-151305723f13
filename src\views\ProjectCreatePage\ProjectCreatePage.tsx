import { InfoCircleOutlined, QuestionCircleOutlined } from "@ant-design/icons";
import {
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  Divider,
  Stack,
  Typography,
  useMediaQuery,
  useTheme,
} from "@mui/material";
import {
  Col,
  Form,
  Image,
  Input,
  InputNumber,
  message,
  Modal,
  Rate,
  Row,
  Select,
  Spin,
  Switch,
  Tag,
  UploadFile,
  Tooltip,
  Divider as AntdDivider,
  <PERSON><PERSON> as AntdButton,
} from "antd";
import { useForm, useWatch } from "antd/es/form/Form";
import { fileAttachApi } from "api/fileAttach.api";
import { geminiApi } from "api/gemini.api";
import { googleMapsApi } from "api/googleMap.api";
import { projectApi } from "api/project.api";
import clsx from "clsx";
import { GoogleLocationModal } from "components/GoogleLocationModal/GoogleLocationModal";
import { CoordAddress } from "components/Map/GoogleMapAutoComplete";
import MapWithAutocomplete from "components/Map/MapWithAutocomplete";
import { FileUploadMultiple } from "components/Upload/FileUploadMultiple";
import { langResources } from "config-translation";
import { $googleApiKey } from "constant";
import { useHandleCustomer } from "hooks/useHandleCustomer";
import { useProduct } from "hooks/useProduct";
import { cloneDeep } from "lodash";
import { observer } from "mobx-react";
import React, { useEffect, useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import { FaMapPin, FaStar } from "react-icons/fa";
import { IoLanguageOutline } from "react-icons/io5";
import { useNavigate } from "react-router-dom";
import { appStore } from "store/appStore";
import { ConfigurationParam } from "types/configuration";
import { FileAttach, FileAttachType } from "types/fileAttach";
import { ProductStatus } from "types/product";
import { Project } from "types/project";
import { formatVND, getTitle } from "utils";
import { $url } from "utils/url";
import { requiredRule } from "utils/validate-rules";
import vietnamIcon from "../../assets/svgs/vietnam.svg";
import { productApi } from "api/product.api";
import { userStore } from "store/userStore";
import mapPinIcon from "assets/svgs/icon-map-pinned.svg";
import AntFormErrorLabel from "components/Common/AntFormErrorLabel";
import { SlArrowDown, SlArrowUp } from "react-icons/sl";
import { ReactComponent as MapPinCheckInsideIcon } from "../../assets/svgs/map-pin-check-inside.svg";
import { ReactComponent as MessagePlusIcon } from "../../assets/svgs/message-circle-plus.svg";
import { ReactComponent as Package2Icon } from "../../assets/svgs/package-2.svg";
import "./ProjectCreatePage.scss";

interface ProjectForm extends Omit<Project, "keywords"> {
  productId: number;
  isImage: boolean;
  fileAttachList: FileAttachRaw[];
  dropSlowNumber: number;
  keywords: string[];
  customerId: number;
}

interface FileAttachRaw {
  id: number;
  url: string;
  name: string;
  size: number;
  uid: number;
  type: string;
  path: string;
  destination: string;
}

interface KeywordItem {
  name: string;
  position: number;
}
const TAG_COLOR_PRESETS = [
  "magenta",
  "purple",
  "volcano",
  "blue",
  "cyan",
  "lime",
  "gold",
  "green",
  "orange",
  "geekblue",
  "red",
];
interface Estimate {
  amount: number;
  dropSlowNumber: number;
  isDropSlow: boolean;
  fileAttaches: FileAttach[];
  isError: boolean;
  message: string;
  moneyDiscount: number;
  moneyDrop: number;
  moneyFinal: number;
  moneyImage: number;
  moneyTax: number;
  moneyTotal: number;
  quantity: number;
  quantityReview: number;
  tax: number;
}

const ProjectCreatePage = ({ title = "" }) => {
  const navigate = useNavigate();

  const moneySlowDrop = appStore.getOneConfiguration(
    ConfigurationParam.MoneyDropInProject
  ) as number;
  const moneyImage = appStore.getOneConfiguration(
    ConfigurationParam.MoneyImageInProject
  ) as number;

  const theme = useTheme();
  const { t } = useTranslation();
  const {
    fetchProduct,
    loadingProduct,
    products,
    setQueryProduct,
    totalProduct,
  } = useProduct({
    initQuery: {
      limit: 0,
      page: 1,
      status: ProductStatus.Active,
      queryObject: JSON.stringify([
        {
          type: "sort",
          field: "product.price",
          value: "ASC",
        },
      ]),
    },
  });
  const {
    customers,
    fetchCustomer,
    queryCustomer,
    loadingCustomer,
    debounceSearchCustomer,
  } = useHandleCustomer({
    initQuery: {
      page: 1,
      limit: 50,
    },
  });
  const [form] = useForm<ProjectForm>();
  const lang = useWatch("lang", form);
  const description = useWatch("description", form);
  const address = useWatch("address", form);
  const placeId = useWatch("placeId", form);
  const lat = useWatch("lat", form);
  const long = useWatch("long", form);
  const isDropSlow = useWatch("isDropSlow", form);
  const dropSlowNumber = useWatch("dropSlowNumber", form);
  const isImage = useWatch("isImage", form);
  const productId = useWatch("productId", form);
  const keywords = useWatch("keywords", form);
  const fileAttachList = useWatch("fileAttachList", form);

  const [chooseMapVisible, setChooseMapVisible] = useState(false);
  const [loadingKeyword, setLoadingKeyword] = useState(false);
  const [loadingProject, setLoadingProject] = useState(false);
  const [loadingMapChoose, setLoadingMapChoose] = useState(false);
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [placeInfo, setPlaceInfo] = useState<CoordAddress>();
  const [keywordPreset, setKeywordPreset] = useState([]);
  const [estimate, setEstimate] = useState<Estimate>();
  const [loadingEstimate, setLoadingEstimate] = useState(false);

  // min quantity keyword of product
  const [selectedMinKeyword, setSelectedMinKeyword] = useState<number>();

  const isMd = useMediaQuery((theme: any) => theme.breakpoints.up("md"));

  const isTouchDevice =
    typeof window !== "undefined" &&
    ("ontouchstart" in window || navigator.maxTouchPoints > 0);

  const triggerType = isTouchDevice ? "click" : "hover";

  useEffect(() => {
    document.title = getTitle(t(title));
    fetchProduct();
    fetchCustomer();
  }, []);

  useEffect(() => {
    if (products.length > 0) {
      form.setFieldValue("productId", products[0].id);
    }
  }, [products]);
  // useEffect(() => {
  //   if (appStore.configurations.length > 0) {
  //     form.setFieldValue(
  //       "dropSlowNumber",
  //       appStore.getOneConfiguration(ConfigurationParam.MinDropReview)
  //     );
  //   }
  // }, [appStore.configurations]);

  const generateKeywords = async () => {
    try {
      setLoadingKeyword(true);
      if (!description) {
        message.error(t("plsFillDes"));
        return;
      }
      console.log({ lang });
      const { data } = await geminiApi.getKeywords(
        {
          description,
          keywords: keywordPreset,
          selectedKeywords: keywords,
        },
        lang.toUpperCase()
      );
      console.log("Keyword after generate", data);
      // form.setFieldsValue({ keywords });
      setKeywordPreset(data.map((item: KeywordItem) => item.name));

      // console.log(data);
    } catch (error) {
      console.log(error);
    } finally {
      setLoadingKeyword(false);
    }
  };

  const getProjectPayload = async () => {
    const {
      fileAttachList,
      productId,
      keywords,
      customerId,
      isDropSlow,
      lang,
      ...rest
    } = form.getFieldsValue();
    // debugger;
    const payload = {
      project: {
        ...rest,
        quantity: 1,
        isDropSlow: !!isDropSlow,
        lang: lang.toUpperCase(),
      },
      productId,
      customerId,
      keywords: keywords.map((word, index) => ({
        name: word,
        position: index,
      })),
    };
    let fileAttachIds: number[] = [];
    if (fileAttachList && fileAttachList.length > 0) {
      const results = await Promise.allSettled(
        fileAttachList.map((file) =>
          fileAttachApi.create({
            fileAttach: {
              name: file.name,
              type: FileAttachType.Image,
              url: file.url,
              path: file.path,
              size: file.size,
            },
          })
        )
      );
      //@ts-ignore
      fileAttachIds = results.map((result) => result.value?.data?.id);
      if (fileAttachIds.length > 0) {
        Object.assign(payload, { fileAttachIds });
      }
    }
    return payload;
  };

  const handleSubmitForm = async () => {
    try {
      await form.validateFields();
      const payload = await getProjectPayload();
      if (!isDropSlow) payload.project.dropSlowNumber = minDropNumber;
      setLoadingProject(true);
      const { data } = await projectApi.create(payload);
      navigate(`/project/project-list?projectId=${data.id}&&isOpen=false`);
      message.success(t("operationSuccess"));
      handleClear();
    } catch (error) {
      console.log({ error });
    } finally {
      setLoadingProject(false);
      // onSubmitOk();
    }
  };

  const handleClear = () => {
    form.resetFields();
    setPlaceInfo(undefined);
    setFileList([]);
  };

  const RatingBreakdown = ({ placeInfo }: { placeInfo: CoordAddress }) => {
    const getCountByStar = (star: number) => {
      const found = placeInfo?.countReviews?.find((item) => item.star === star);
      return found ? found.count : 0;
    };

    const average =
      placeInfo.totalRate && placeInfo.countReviews?.length
        ? placeInfo.countReviews.reduce((sum, r) => sum + r.star * r.count, 0) /
        placeInfo.totalRate
        : 0;

    // Tìm count lớn nhất để làm mốc 100%
    const maxCount =
      placeInfo.countReviews?.reduce(
        (max, item) => (item.count > max ? item.count : max),
        0
      ) || 1;

    return (
      <div className="w-full max-w-md p-4">
        <div className="flex">
          {/* Rating bars */}
          <div className="flex-1 ">
            {[5, 4, 3, 2, 1].map((star) => {
              const count = getCountByStar(star);
              const percentage = (count / maxCount) * 100;

              return (
                <div key={star} className="flex items-center space-x-2">
                  <span className="w-4 text-sm text-gray-600">{star}</span>
                  <div className="w-full h-2 bg-gray-200 rounded-full">
                    <div
                      className="h-2 rounded-full bg-yellow-400"
                      style={{ width: `${percentage}%` }}
                    />
                  </div>
                </div>
              );
            })}
          </div>

          {/* Summary */}
          <div className="w-28 flex flex-col items-center justify-center pl-4">
            <span className="text-5xl font-semibold text-gray-700">
              {average.toFixed(1)}
            </span>
            <Rate
              value={placeInfo.rate}
              className="text-[13px] text-yellow-400 custom-rate"
              allowHalf
              disabled
            />{" "}
            <div className="text-sm">{placeInfo.totalRate} reviews</div>
          </div>
        </div>
      </div>
    );
  };

  const handleMapChoosen = async (addressInfo: CoordAddress) => {
    try {
      setLoadingMapChoose(true);
      form.setFieldsValue({
        address: addressInfo.address,
        lat: addressInfo.lat,
        long: addressInfo.lng,
        placeId: addressInfo.placeId,
        mapUrl: addressInfo.mapUrl,
        addressName: addressInfo.name,
        totalStar: addressInfo.rate || 0,
      });
      addressInfo.rateWant = (addressInfo.rate ?? 0) < 4.9 ? 4.9 : 5;
      setPlaceInfo(addressInfo);
    } catch (error) {
      console.log({ error });
    } finally {
      setLoadingMapChoose(false);
    }
  };

  const { currentProduct, estimateDayForSlowDrop, slowDropFee } =
    useMemo(() => {
      const currProduct = products.find((p) => p.id == productId);
      let estimateDayForSlowDrop = 0;
      let slowDropFee = 0;
      if (currProduct) {
        estimateDayForSlowDrop = currProduct.numReview / (dropSlowNumber || 1);
        slowDropFee = Math.ceil(estimateDayForSlowDrop) * moneySlowDrop;
      }

      return {
        currentProduct: currProduct,
        estimateDayForSlowDrop: Math.ceil(estimateDayForSlowDrop),
        slowDropFee,
      };
    }, [productId, products, dropSlowNumber, moneySlowDrop]);

  const { recommendProduct, numOfReviewNeed } = useMemo(() => {
    if (placeInfo) {
      // debugger;
      let A = placeInfo.rate || 0;
      let B = placeInfo.totalRate || 0;
      let C = placeInfo.rateWant || 5;

      let numOfReviewNeed = 0;
      if (A === 0 && B === 0) {
        numOfReviewNeed = 1;
      } else if (C >= 5) {
        C = 4.99;
        numOfReviewNeed = Math.ceil((B * (C - A)) / (5 - C)); // bạn cần tất cả điểm về 5.0
      } else {
        numOfReviewNeed = Math.ceil((B * (C - A)) / (5 - C));
      }
      const sortedProducts = cloneDeep(products).sort(
        (a, b) => a.numReview - b.numReview
      );
      // debugger;
      const recommendProduct = sortedProducts.find(
        (p) => p.numReview >= numOfReviewNeed
      );
      if (!recommendProduct && sortedProducts.length > 0) {
        return {
          recommendProduct: sortedProducts[sortedProducts.length - 1],
          numOfReviewNeed,
        };
      }
      return { recommendProduct, numOfReviewNeed };
    } else {
      return { recommendProduct: undefined, numOfReviewNeed: 0 };
    }
  }, [placeInfo, products]);

  const estimateTotalPrice = useMemo(() => {
    const productPrice = currentProduct?.price || 0;
    const dropSlowPrice = isDropSlow ? slowDropFee : 0;
    const imagePrice = fileList.length * moneyImage;

    return productPrice + dropSlowPrice + imagePrice;
  }, [isDropSlow, slowDropFee, currentProduct, fileList, moneyImage]);

  const minDropNumber = useMemo(() => {
    return currentProduct?.slowMinQuantity || 1;
  }, [currentProduct]);

  const maxDropNumber = useMemo(() => {
    return currentProduct?.slowMaxQuantity || 1;
  }, [currentProduct]);

  useEffect(() => {
    if (recommendProduct) {
      form.setFieldValue("productId", recommendProduct.id);
    }
  }, [recommendProduct]);

  const maxImageCanUpload = useMemo(() => {
    const percent = appStore.getOneConfiguration(
      ConfigurationParam.PercentImageInProject
    );
    if (currentProduct) {
      const calculated = (currentProduct.numReview * Number(percent)) / 100;
      return Math.max(1, calculated);
    }
    return 0;
  }, [currentProduct, appStore.configurations]);

  useEffect(() => {
    handleEstimate();
  }, [currentProduct, dropSlowNumber, fileAttachList, isDropSlow]);

  const handleEstimate = async () => {
    try {
      setLoadingEstimate(true);
      const payload = await getProjectPayload();
      if (isNaN(Number(dropSlowNumber))) {
        payload.project.dropSlowNumber = minDropNumber;
      }

      const { data } = await projectApi.preEstimate(payload);
      setEstimate(data);
    } catch (e) {
      console.log({ e });
    } finally {
      setLoadingEstimate(false);
    }
  };

  const placeInfoRender = useMemo(
    () =>
      placeInfo ? (
        <>
          <Typography variant="h5">{placeInfo.name}</Typography>

          <div className="flex items-center !mt-2 gap-2">
            <MapPinCheckInsideIcon className="flex-shrink-0 -translate-y-[1px]" />
            <div className="text-regular">{placeInfo.address}</div>
          </div>

          <div className="flex gap-2 mt-2 justify-between">
            <div className="flex-1">
              <div className="text-normal-semibold">{t("rateWish")}</div>
              <div className="relative flex border-[1px] border-solid border-[#e6e6e6] rounded-[10px] overflow-hidden mt-2">
                <InputNumber
                  placeholder={t("enterRateWish")}
                  className="w-full border-none"
                  controls={false}
                  size="large"
                  min={placeInfo.rate === 5 ? 5 : (placeInfo.rate ?? 0) + 0.1}
                  max={5}
                  step={0.1}
                  decimalSeparator="."
                  value={placeInfo.rateWant}
                  onChange={(value) => {
                    setPlaceInfo({
                      ...placeInfo,
                      rateWant: value ? Number(value) : 0,
                    });
                  }}
                />
                <div className="absolute right-0 h-full flex flex-col border-y-0 border-r-0 border-l-[1px] border-[#d9d9d9] border-solid">
                  <div
                    className={clsx(
                      "flex justify-center items-center flex-1 px-[7px]",
                      (placeInfo.rateWant || 0) >= 5
                        ? "cursor-not-allowed"
                        : "cursor-pointer"
                    )}
                    onClick={() => {
                      if ((placeInfo.rateWant || 0) < 5) {
                        setPlaceInfo({
                          ...placeInfo,
                          rateWant: Number(
                            ((placeInfo.rateWant || 0) + 0.1).toFixed(1)
                          ),
                        });
                      }
                    }}
                  >
                    <SlArrowUp size={7} className="text-[#00000073]" />
                  </div>
                  <Divider className="!border-[#d9d9d9]" />
                  <div
                    className={clsx(
                      "flex justify-center items-center flex-1 px-[7px]",
                      (placeInfo.rateWant || 0) <=
                        (() =>
                          placeInfo.rate === 5
                            ? 5
                            : (placeInfo.rate ?? 0) + 0.1)()
                        ? "cursor-not-allowed"
                        : "cursor-pointer"
                    )}
                    onClick={() => {
                      const min =
                        placeInfo.rate === 5 ? 5 : (placeInfo.rate ?? 0) + 0.1;
                      if ((placeInfo.rateWant || 0) > min) {
                        setPlaceInfo({
                          ...placeInfo,
                          rateWant: Number(
                            ((placeInfo.rateWant || 0.1) - 0.1).toFixed(1)
                          ),
                        });
                      }
                    }}
                  >
                    <SlArrowDown size={7} className="text-[#00000073]" />
                  </div>
                </div>
              </div>
              {placeInfo.rateWant && (placeInfo.rate ?? 0) < 5 ? (
                <>
                  <div className="flex justify-start items-center gap-1 mt-2">
                    <MessagePlusIcon className="-translate-y-[1px] size-[18px] flex-shrink-0" />
                    <div className="text-caption-no-caps-regular text-[var(--gray-2-color)]">
                      {t("packageNeededInfo", {
                        rate: placeInfo.rateWant,
                        reviews: Math.ceil(numOfReviewNeed),
                      })}
                    </div>
                  </div>
                  <div className="flex justify-start items-center gap-1 mt-2">
                    <Package2Icon className="-translate-y-[1px] size-[18px] flex-shrink-0" />
                    <div className="text-caption-no-caps-regular text-[var(--gray-2-color)]">
                      {t("packageRecommend")}: {recommendProduct?.name}
                    </div>
                  </div>
                </>
              ) : (
                <>
                  <Typography variant="body1" className="!mt-2">
                    {t("congratulationMsg")}
                  </Typography>
                </>
              )}
            </div>
            <div className="w-[140px] flex flex-col items-center pt-1">
              <span className="text-5xl font-semibold text-gray-700">
                {(placeInfo.rate || 0).toFixed(1)}
              </span>
              <Rate
                value={placeInfo.rate}
                className="text-[13px] text-yellow-400 custom-rate"
                allowHalf
                disabled
              />{" "}
              <div className="text-sm text-center text-[var(--gray-2-color)]">
                {placeInfo.totalRate} {t("reviews").toLowerCase()}
              </div>
            </div>
          </div>
          {isMd ? (
            <Divider className="!my-4" />
          ) : (
            <div className="mb-[24px]"></div>
          )}
        </>
      ) : (
        <></>
      ),
    [placeInfo, numOfReviewNeed, recommendProduct, t, isMd]
  );

  return (
    <>
      <Stack direction={{ lg: "row" }} gap={"24px"} position={"relative"}>
        <div className="flex-1">
          <Form
            form={form}
            className="custom-form"
            layout="vertical"
            initialValues={{
              lang: userStore.info.language?.toLowerCase() || "vi",
              dropSlowNumber: t("autoDistribution"),
              keywords: [],
              fileAttachList: [],
            }}
            validateTrigger={["onBlur", "onChange"]}
          // className="flex-1 bg-white p-4 shadow-md rounded-md"
          >
            {/* <Typography variant="h4">{t("projectCreate")}</Typography>
            <br /> */}
            <Form.Item
              rules={[
                {
                  required: true,
                  message: (
                    <AntFormErrorLabel label={t("pleaseEnterProjectName")} />
                  ),
                },
                {
                  max: 64,
                  message: (
                    <AntFormErrorLabel
                      label={t("maxLengthCharacters", { number: 64 })}
                    />
                  ),
                },
              ]}
              label={t("projectName")}
              name={"name"}
            >
              <Input size="large" />
            </Form.Item>
            <Form.Item name={"mapUrl"} hidden></Form.Item>
            <Form.Item name={"lat"} hidden></Form.Item>
            <Form.Item name={"long"} hidden></Form.Item>
            <Form.Item name={"address"} hidden></Form.Item>
            <Form.Item name={"addressName"} hidden></Form.Item>
            <Form.Item name={"totalStar"} hidden></Form.Item>
            <Form.Item
              label={t("projectPlaceChoose")}
              name={"placeId"}
              className="auto-height"
              rules={[
                {
                  required: true,
                  message: (
                    <AntFormErrorLabel label={t("pleaseChooseProjectPlace")} />
                  ),
                },
              ]}
            >
              {placeId && (
                <MapWithAutocomplete
                  key={"mapChoosen"}
                  coords={[{ lat, lng: long }]}
                  draggable={false}
                  defaultZoom={15}
                  noInput
                />
              )}
              <Button
                fullWidth
                className="!font-bold h-[42px]"
                size="large"
                startIcon={
                  <img src={mapPinIcon} className="w-[24px] h-[24px]" />
                }
                onClick={() => setChooseMapVisible(true)}
              >
                {t("projectPlaceChoose")}
              </Button>
            </Form.Item>
            {!isMd && placeInfoRender}
            <Form.Item
              name={"description"}
              rules={[
                {
                  required: true,
                  message: (
                    <AntFormErrorLabel
                      className={"mt-4"}
                      label={t("pleaseEnterProjectDescription")}
                    />
                  ),
                },
                {
                  message: (
                    <AntFormErrorLabel
                      className={"mt-4"}
                      label={t("projectDescriptionCannotExceedLimit", {
                        min: appStore.getOneConfiguration(
                          ConfigurationParam.MinDescriptionCreateProject
                        ),
                        max: appStore.getOneConfiguration(
                          ConfigurationParam.MaxDescriptionCreateProject
                        ),
                      })}
                    />
                  ),
                  validator: (_, value) => {
                    const min = appStore.getOneConfiguration(
                      ConfigurationParam.MinDescriptionCreateProject
                    );
                    const max = appStore.getOneConfiguration(
                      ConfigurationParam.MaxDescriptionCreateProject
                    );
                    if (
                      !value ||
                      (value.length >= min && value.length <= max)
                    ) {
                      return Promise.resolve();
                    }
                    return Promise
                      .reject
                      // new Error(
                      //   t("projectDescriptionCannotExceedLimit", {
                      //     min,
                      //     max,
                      //   })
                      // )
                      ();
                  },
                },
              ]}
              label={t("projectDescription")}
            >
              <Input.TextArea
                placeholder={t("describe")}
                onChange={(e) => {
                  const regex = /\s+/g;
                  form.setFieldValue(
                    "description",
                    e.target.value.replace(regex, " ")
                  );
                }}
                className="[&_.ant-input]:p-0"
                rows={5}
                showCount={{
                  formatter: ({ count }) =>
                    `${count}/${appStore.getOneConfiguration(
                      ConfigurationParam.MaxDescriptionCreateProject
                    )}`,
                }}
              />
            </Form.Item>
            <Row gutter={24}>
              <Col span={24} md={24}>
                <Form.Item
                  rules={[requiredRule]}
                  name={"productId"}
                  label={t("projectProductChoose")}
                >
                  <Select
                    size="large"
                    loading={loadingProduct}
                    onChange={(value, option) => {
                      const productSelected = products.find(
                        (item) => item.id === value
                      );

                      setSelectedMinKeyword(productSelected?.minKeyword || 0);

                      form.validateFields(["fileAttachList", "keywords"]);

                      if (form.getFieldValue("isDropSlow")) {
                        form.setFieldValue(
                          "dropSlowNumber",
                          productSelected?.slowMinQuantity
                        );
                      }
                    }}
                    options={products.map((p) => ({
                      label: `${p.code} - ${formatVND(p.unitPrice)}₫/${t(
                        "review"
                      )} - ${formatVND(p.numReview)} ${t(
                        "reviewTurn"
                      ).toLowerCase()}`,
                      value: p.id,
                    }))}
                  />
                </Form.Item>
              </Col>
              <Col span={24} md={8}>
                <Form.Item
                  rules={[requiredRule]}
                  name={"lang"}
                  label={
                    <div className="flex flex-col md:flex-row justify-center md:items-center gap-1">
                      {t("languagePlaceholder")}
                      <Tooltip
                        trigger={triggerType}
                        placement="top"
                        title={t("languageProjectDescription")}
                      >
                        <QuestionCircleOutlined className="translate-y-[1px] md:block hidden text-[var(--gray-7-color)] text-[16px]" />
                      </Tooltip>
                      <Typography
                        variant="caption"
                        lineHeight={1}
                        className="md:hidden"
                      >
                        {t("languageProjectDescription")}
                      </Typography>
                    </div>
                  }
                // tooltip={t("languageProjectDescription")}
                >
                  <Select
                    className="w-full"
                    value={lang}
                    options={Object.values(langResources).map((item) => {
                      return {
                        label: (
                          <React.Fragment>
                            <Image
                              preview={false}
                              src={item.icon || vietnamIcon}
                              alt=""
                              className="!w-[20px] !h-[20px] cursor-pointer -translate-y-[2px]"
                            />
                            &nbsp; {item.text2}
                          </React.Fragment>
                        ),
                        value: item.value,
                      };
                    })}
                    placeholder={
                      <React.Fragment>
                        <IoLanguageOutline />
                        &nbsp; {t("languagePlaceholder")}
                      </React.Fragment>
                    }
                    size="large"
                  ></Select>
                </Form.Item>
              </Col>
              <Col span={24} md={7} className="md:!pr-[13px]">
                <Form.Item name="isDropSlow" hidden></Form.Item>
                <div className="flex flex-row items-center gap-2 md:translate-y-[32px] justify-start mb-2">
                  <span>{t("slowDrop")}</span>
                  <Tooltip
                    trigger={triggerType}
                    placement="top"
                    title={t("slowDropTooltip")}
                  >
                    <QuestionCircleOutlined className="translate-y-[1px] text-[16px] text-[var(--gray-7-color)]" />
                  </Tooltip>
                  <Switch
                    className="large-switch light-color mb-2 translate-y-[5px] project-switch"
                    checkedChildren={t("on")}
                    unCheckedChildren={t("off")}
                    checked={form.getFieldValue("isDropSlow")}
                    onChange={(checked) => {
                      form.setFieldValue("isDropSlow", checked);
                      form.setFieldValue(
                        "dropSlowNumber",
                        checked ? minDropNumber : t("autoDistribution")
                      );
                    }}
                  />
                </div>
              </Col>
              <Col span={24} md={9} className="md:!pl-0">
                <Form.Item
                  className="mb-0"
                  name="dropSlowNumber"
                  rules={[
                    {
                      message: (
                        <AntFormErrorLabel
                          label={t("maxNumberReview", {
                            number: (currentProduct as any)?.slowMaxQuantity,
                          })}
                        />
                      ),
                      validator(rule, value, callback) {
                        if (
                          currentProduct &&
                          Number(value) >
                          (currentProduct as any)?.slowMaxQuantity
                        ) {
                          return Promise
                            .reject
                            // new Error(
                            // t("slowDropExceedPercentOfReview", {
                            //   percent: appStore.getOneConfiguration(
                            //     ConfigurationParam.MaxDropPercent
                            //   ) as number,
                            // }))
                            ();
                        }
                        return Promise.resolve();
                      },
                    },
                    {
                      message: <AntFormErrorLabel label={t("valDropNumber")} />,
                      validator(_, value) {
                        if (value === "" || value === undefined) {
                          return Promise.reject();
                        }
                        return Promise.resolve();
                      },
                    },
                  ]}
                  label={t("numberOfReviewPerDay")}
                >
                  <div className="relative flex">
                    <InputNumber
                      disabled={!isDropSlow}
                      value={form.getFieldValue("dropSlowNumber")}
                      min={minDropNumber}
                      controls={false}
                      inputMode="numeric"
                      decimalSeparator="."
                      onKeyDown={(e) => {
                        if (!/^\d*$/.test(e.key) && e.keyCode !== 8) {
                          e.preventDefault();
                        }
                      }}
                      onChange={(value) => {
                        if (value < 0) {
                          form.setFieldsValue({
                            dropSlowNumber: 0,
                          });
                          return;
                        }
                        if (value == null) {
                          form.setFieldsValue({
                            dropSlowNumber: minDropNumber,
                          });
                        } else {
                          form.setFieldsValue({ dropSlowNumber: value });
                        }
                      }}
                      className={clsx(
                        "custom-input-number",
                        isDropSlow ? "" : "no-drop-slow"
                      )}
                      style={{
                        backgroundColor: isDropSlow ? "white" : "#F6F9FC",
                      }}
                      size="large"
                      type={isDropSlow ? "number" : "text"}
                    />
                    <div
                      className={clsx(
                        "absolute right-0 h-full flex flex-col border-y-0 border-r-0 border-l-[1px] border-[#d9d9d9] border-solid",
                        isDropSlow ? "" : "hidden"
                      )}
                    >
                      <div
                        className={clsx(
                          "flex justify-center items-center flex-1 px-[7px]",
                          dropSlowNumber >= maxDropNumber
                            ? "cursor-not-allowed"
                            : "cursor-pointer"
                        )}
                        onClick={() => {
                          if (dropSlowNumber < maxDropNumber) {
                            form.setFieldValue(
                              "dropSlowNumber",
                              +dropSlowNumber + 1
                            );
                          }
                        }}
                      >
                        <SlArrowUp size={7} className="text-[#00000073]" />
                      </div>
                      <Divider className="!border-[#d9d9d9]" />
                      <div
                        className={clsx(
                          "flex justify-center items-center flex-1 px-[7px]",
                          dropSlowNumber <= minDropNumber ||
                            dropSlowNumber > maxDropNumber
                            ? "cursor-not-allowed"
                            : "cursor-pointer"
                        )}
                        onClick={() => {
                          if (
                            dropSlowNumber > minDropNumber &&
                            dropSlowNumber <= maxDropNumber
                          ) {
                            form.setFieldValue(
                              "dropSlowNumber",
                              dropSlowNumber - 1
                            );
                          }
                        }}
                      >
                        <SlArrowDown size={7} className="text-[#00000073]" />
                      </div>
                    </div>
                  </div>
                </Form.Item>
                {isDropSlow && (
                  <Form.Item>
                    <div className="">
                      <ul className="pl-6 mb-0 mt-2">
                        <li>
                          <Typography variant="caption">
                            {t("slowDropEstimate", {
                              number: formatVND(
                                estimateDayForSlowDrop.toFixed(0)
                              ),
                            })}
                          </Typography>
                        </li>
                        <li>
                          <Typography variant="caption">
                            {t("fee")}: {formatVND(slowDropFee.toFixed(0))}₫
                          </Typography>
                        </li>
                      </ul>
                    </div>
                  </Form.Item>
                )}
              </Col>
            </Row>
            {!isMd && <br />}
            <Form.Item
              name="customerId"
              label={t("customer")}
              rules={[
                {
                  required: true,
                  message: (
                    <AntFormErrorLabel label={t("pleaseChooseCustomer")} />
                  ),
                },
              ]}
            >
              <Select
                showSearch
                size="large"
                onSearch={(value) => debounceSearchCustomer(value)}
                allowClear
                options={customers.map((item) => {
                  return {
                    label: (
                      <div>
                        <span className="">
                          <span className="text-blue-500">{item.phone}</span> -{" "}
                          {item.name}
                        </span>
                      </div>
                    ),
                    value: item.id,
                  };
                })}
                onClear={() => {
                  queryCustomer.search = "";
                  fetchCustomer();
                }}
                loading={loadingCustomer}
                filterOption={false}
                placeholder={t("selectCustomer")}
              ></Select>
            </Form.Item>

            <Form.Item
              name={"keywords"}
              rules={[
                {
                  required: true,
                  message: (
                    <AntFormErrorLabel label={t("pleaseChooseKeyword")} />
                  ),
                },
                {
                  message: (
                    <AntFormErrorLabel label={t("keywordsCannotExceedLimit")} />
                  ),
                  validator: (_, value) => {
                    if (
                      value.length == 0 ||
                      value.length <= (currentProduct?.numReview || 3)

                    ) {
                      return Promise.resolve();
                    }
                    return Promise
                      .reject
                      // new Error(
                      //   t("keywordsCannotExceedLimit", {
                      //     number: currentProduct?.numReview || 3,
                      //   })
                      // )
                      ();
                  },
                },
                {
                  message: (
                    <AntFormErrorLabel label={`${t("requiredMinKeyword")} ${selectedMinKeyword ?? 0}`} />
                  ),
                  validator: (_, value) => {
                    if (
                      value.length >= (selectedMinKeyword ?? 0) ||
                      (selectedMinKeyword ?? 0) == 0
                    ) {
                      return Promise.resolve();
                    }
                    return Promise
                      .reject();
                  },
                }
              ]}
              className="[&_.ant-form-item-required]:w-full auto-height"
              label={t("keyword")}
            >
              <Button
                size="small"
                loading={loadingKeyword}
                onClick={() => {
                  generateKeywords();
                }}
                className={clsx(
                  "!absolute right-0 top-[-38px]"
                  // keywordPreset.length >= (currentProduct?.numReview || 0) &&
                  //   "invisible"
                )}
              >
                {t("genKeywords")}
              </Button>
              {keywordPreset?.filter((it) => !keywords?.includes(it))?.length >
                0 && (
                  <div className="relative mb-2 mt-1">
                    <div className="flex gap-1 flex-wrap">
                      {keywordPreset
                        ?.filter((it) => !keywords?.includes(it))
                        ?.map((keyword, i) => (
                          <Tag
                            className={clsx(
                              "text-[12px] text-[#5A6A85] !font-normal",
                              keywords.length <= (currentProduct?.numReview || 3)
                                ? "cursor-pointer"
                                : "cursor-not-allowed"
                            )}
                            key={i}
                            onClick={() => {
                              if (
                                keywords.length <=
                                (currentProduct?.numReview || 3) &&
                                !keywords.includes(keyword)
                              ) {
                                form.setFieldValue("keywords", [
                                  ...keywords,
                                  keyword,
                                ]);
                                form.validateFields(["keywords"]);
                              }
                            }}
                          >
                            {keyword}
                          </Tag>
                        ))}
                    </div>
                  </div>
                )}

              <Select
                id="keywords"
                placeholder={t("camelCase")}
                value={keywords}
                size="large"
                mode="tags"
                style={{ width: "100%" }}
                tokenSeparators={[","]}
                onChange={(value) => {
                  form.setFieldValue("keywords", value);
                }}
                className="project-create-keyword-select"
                tagRender={(props) => {
                  const { label, value, closable, onClose } = props;
                  return (
                    <Tag
                      color={
                        TAG_COLOR_PRESETS[
                        keywords.findIndex((k) => k == label) %
                        TAG_COLOR_PRESETS.length
                        ]
                      }
                      closable={closable}
                      onClose={onClose}
                      style={{ margin: 2, fontSize: 14 }}
                    >
                      {label}
                    </Tag>
                  );
                }}
              />
            </Form.Item>
            {appStore.getOneConfiguration(ConfigurationParam.ReviewImage) && (
              <Form.Item noStyle name={"isImage"}>
                <div className="flex flex-row items-center gap-2 mb-[24px]">
                  <label className="text-medium text-gray-1">
                    {t("image")}
                  </label>
                  <Switch
                    className="large-switch"
                    checkedChildren={t("yes")}
                    unCheckedChildren={t("no")}
                    checked={isImage}
                    onChange={(checked) => {
                      form.setFieldValue("isImage", checked);
                    }}
                  />
                </div>
              </Form.Item>
            )}
            {isImage && (
              <>
                <ul className="pl-6 my-0 text-[var(--gray-2-color)]">
                  <li>
                    <span>{t("projectImageInfo")}</span>
                  </li>
                  <li>
                    <span>
                      {t("projectImagePrice", {
                        number: formatVND(moneyImage),
                        percent: appStore.getOneConfiguration(
                          ConfigurationParam.PercentImageInProject
                        ),
                      })}
                    </span>
                  </li>
                </ul>
                <Form.Item noStyle shouldUpdate={true}>
                  {() => {
                    return (
                      <Form.Item
                        label={t("image")}
                        className="[&_.ant-form-item-label]:hidden auto-height"
                        style={{ marginBottom: 0 }}
                        name="fileAttachList"
                        rules={[
                          {
                            required: true,
                            message: (
                              <AntFormErrorLabel
                                label={t("pleaseChooseProjectImage")}
                              />
                            ),
                          },
                          {
                            message: (
                              <AntFormErrorLabel
                                label={t("maxAllowedImages", {
                                  number: appStore.getOneConfiguration(
                                    ConfigurationParam.PercentImageInProject
                                  ),
                                })}
                              />
                            ),
                            validator(rule, value: FileAttachRaw[], callback) {
                              if (value.length <= maxImageCanUpload) {
                                return Promise.resolve();
                              }
                              return Promise
                                .reject
                                // new Error(t("projectImageExceedLimit"))
                                ();
                            },
                          },
                        ]}
                      >
                        <FileUploadMultiple
                          maxFileSize={1024 * 1024 * 10}
                          maxFile={maxImageCanUpload + 1}
                          disabled={fileAttachList?.length > maxImageCanUpload}
                          acceptType=".jpg, .png"
                          fileList={fileList}
                          className="h-[220px]"
                          onUploadOk={(fileList) => {
                            console.log({ fileList });

                            const filePayloads = fileList.map((item) =>
                              item.id
                                ? item // Nếu có id, giữ nguyên object
                                : {
                                  url:
                                    item.url ||
                                    $url(item.response?.data?.path),
                                  name: item.name,
                                  size: item?.size,
                                  uid: item?.uid,
                                  type: item.type,
                                  path: item.response?.data?.path,
                                  destination:
                                    item.response?.data?.destination,
                                }
                            );

                            console.log(filePayloads);
                            setFileList(fileList);
                            form.setFieldsValue({
                              fileAttachList: filePayloads,
                            });
                            form.validateFields(["fileAttachList"]);
                          }}
                          onDelete={(files) => {
                            setFileList(files);
                            form.validateFields(["fileAttachList"]);
                          }}
                        />
                      </Form.Item>
                    );
                  }}
                </Form.Item>
              </>
            )}
          </Form>
        </div>
        <div className="xl:!w-[432px] md:!w-[325px] flex flex-col gap-[24px]">
          <div
            className="w-full flex bottom-0 h-fit md:p-6 px-3 py-4 border-shadow-2 rounded-lg"
          // sx={{ padding: "24px", boxShadow: "0 12px 24px -4px #919EAB1F" }}
          >
            <Box>
              {isMd && placeInfoRender}
              <h6 className="semibold">{t("slowDrop")}</h6>
              <div className="mt-[12px]">
                <span className="text-regular !leading-[130%] text-[var(--secondary-color-500)]">
                  {t("slowDropInfo", {
                    number: formatVND(moneySlowDrop),
                  })}
                </span>
                <br />
                <span className="text-regular !leading-[130%] text-[var(--secondary-color-500)]">
                  {t("slowDropRecommend", {
                    percent: appStore.getOneConfiguration(
                      ConfigurationParam.MaxDropPercent
                    ) as number,
                  })}
                </span>
              </div>
              <Divider className="!my-[24px]" />
              <h6 className="semibold">{t("keyword")}</h6>
              <div className="text-regular !leading-[130%] text-[var(--secondary-color-500)] whitespace-pre-wrap mt-[12px]">
                {t("keywordInfo1")}
              </div>
              <div className="text-regular !leading-[150%] text-[var(--secondary-color-500)] whitespace-pre-wrap mt-[12px]">
                <u>{t("keywordInfo9")}</u> {t("keywordInfo2")}{" "}
                <Tag color="blue" className="!mr-0">
                  {t("keywordInfo8")}
                </Tag>{" "}
                {t("keywordInfo3")}
              </div>
              <Typography
                variant="body1"
                className="whitespace-pre-wrap"
                sx={{ marginTop: "12px", lineHeight: "130%" }}
              >
                <ul className="italic pl-6 mt-[14px] mb-0">
                  <li>{t("keywordInfo4")}</li>
                  <li>{t("keywordInfo5")}</li>
                  <li>{t("keywordInfo6")}</li>
                  <li>{t("keywordInfo7")}</li>
                </ul>
              </Typography>
            </Box>
          </div>
          <Card
            sx={{
              padding: "24px",
              borderRadius: "8px",
              border: "3px",
              boxShadow:
                "0px 0px 2px 0px rgba(145, 158, 171, 0.20), 0px 12px 24px -4px rgba(145, 158, 171, 0.12)",
            }}
          >
            <Stack gap={"12px"} className="sticky top-[100px]">
              <Typography variant="h5">{t("overviewOrder")}</Typography>
              <Spin spinning={loadingEstimate}>
                {estimate && (
                  <Stack gap={"10px"}>
                    <Typography
                      variant="body1"
                      className="whitespace-pre-wrap flex justify-between gap-1"
                    >
                      <span>
                        {t("estimatePackage")} (x{estimate.quantityReview}){" "}
                      </span>
                      {formatVND(estimate.amount || 0)} đ
                    </Typography>
                    <Typography
                      variant="body1"
                      className="whitespace-pre-wrap flex justify-between gap-1"
                    >
                      <span>{t("estimateSlowDrop")} </span>
                      {formatVND(
                        estimate.isDropSlow ? estimate.moneyDrop.toFixed(0) : 0
                      )}{" "}
                      ₫
                    </Typography>
                    <Typography
                      variant="body1"
                      className="whitespace-pre-wrap flex justify-between gap-1"
                    >
                      <span>
                        {t("estimateImage")} (x
                        {estimate.fileAttaches?.length || 0}){" "}
                      </span>
                      {formatVND(isImage ? estimate.moneyImage : 0)} ₫
                    </Typography>
                    <Typography
                      variant="body1"
                      className="whitespace-pre-wrap flex justify-between gap-1"
                    >
                      <span>
                        {t("estimateTax")} ({estimate.tax || 0}%){" "}
                      </span>
                      {formatVND(estimate.moneyTax || 0)} ₫
                    </Typography>
                    <div className="relative">
                      <div className="w-[24px] h-[24px] bg-[#b7c1cc1f] absolute rounded-full left-0 -translate-x-[calc(100%+12px)] -top-[12px]"></div>
                      <div className="w-[24px] h-[24px] bg-[#b7c1cc1f] absolute rounded-full right-0 translate-x-[calc(100%+12px)] -top-[12px]"></div>
                      <AntdDivider dashed className="!my-0" />
                    </div>
                    <Typography
                      variant="h6"
                      className="whitespace-pre-wrap flex justify-between gap-1"
                    >
                      <span>{t("total")}</span>
                      {formatVND(estimate.moneyFinal.toFixed(0))} ₫
                    </Typography>
                  </Stack>
                )}
              </Spin>
              <AntdButton
                loading={loadingProject}
                // fullWidth
                // variant="contained"
                className="!mt-2 w-full "
                type="primary"
                size={"large"}
                onClick={() => {
                  form
                    .validateFields()
                    .then(() => {
                      Modal.confirm({
                        title: t("confirmProjectCreate"),
                        content: t("confirmProjectCreateInfo"),
                        onOk: handleSubmitForm,
                        okText: t("confirm"),
                      });
                    })
                    .catch(({ errorFields }) => {
                      if (errorFields && errorFields.length > 0) {
                        const firstErrorField = errorFields[0].name[0];

                        console.log("firstErrorField", firstErrorField);
                        form.scrollToField(firstErrorField, {
                          behavior: "smooth",
                          block: "center",
                        });

                        // const errorElement = document.querySelector(
                        //   `[id="projectForm_${firstErrorField}"], [id="${firstErrorField}"]`
                        // );

                        // console.log("errorElement", errorElement, firstErrorField);
                        // // Cuộn đến element đó với hiệu ứng mượt
                        // if (errorElement) {
                        //   errorElement.scrollIntoView({
                        //     behavior: 'smooth',
                        //     block: 'center'
                        //   });
                        // } else {
                        //   window.scrollTo({ top: 0, behavior: 'smooth' });
                        // }

                        // message.error(t("pleaseCheckFormErrors"));
                      }
                    });
                }}
              >
                {t("projectCreate")}
              </AntdButton>
            </Stack>
          </Card>
        </div>
      </Stack>
      {chooseMapVisible && (
        <GoogleLocationModal
          coord={{ address, lat, lng: long }}
          onClose={() => {
            setChooseMapVisible(false);
          }}
          onSubmitOk={(addressInfo) => {
            handleMapChoosen(addressInfo);
          }}
        />
      )}
    </>
  );
};

export default observer(ProjectCreatePage);
