import { Modal } from "antd";

import { useEffect, useState } from "react";
import "styles/googleLocationModal.scss";
// import MapWithAutocomplete from "components/Map/MapWithAutocomplete";
import { CoordAddress } from "components/Map/GoogleMapAutoComplete";
import MapWithAutocomplete from "components/Map/MapWithAutocomplete";
import { useTranslation } from "react-i18next";
import { googleMapsApi } from "api/googleMap.api";

export const GoogleLocationModal = ({
  coord,
  visible,
  onClose,
  onSubmitOk,
}: {
  visible?: boolean;
  onClose: () => void;
  onSubmitOk: (addressInfo: CoordAddress) => void;
  coord: CoordAddress;
}) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [addressInfo, setAddressInfo] = useState<CoordAddress>();
  const [originAddress, setOriginAddress] = useState<string | undefined>("");

  const handleSubmitForm = async () => {
    if (addressInfo) {
      console.log(addressInfo);
      onSubmitOk(addressInfo);
      onClose();
    }
  };

  useEffect(() => {
    if (coord.lat) {
      setOriginAddress(coord.address);
      setAddressInfo({
        address: coord.address,
        lat: coord.lat,
        lng: coord.lng,
        placeId: coord.placeId,
      });
    }
  }, [coord]);

  const handleCheckLocation = async (address: CoordAddress) => {
    try {
      setLoading(true);
      const { data } = await googleMapsApi.findAll({
        placeId: address.placeId,
      });
      setAddressInfo({
        ...address,
        rate: data.rating || 0,
        totalRate: data.user_ratings_total || 0,
        rateWant: 0,
        address: data.formatted_address,
        name: data.name,
        mapUrl: data.url,
        countReviews: data.countReviews,
      });
    } catch (error) {
      console.log({ error });
    } finally {
      setLoading(false);
    }
  };
  return (
    <Modal
      className="address-modal"
      onCancel={onClose}
      open={true}
      title={t("projectPlaceChoose")}
      confirmLoading={loading}
      onOk={handleSubmitForm}
      width={600}
      centered
      okText={t("save")}
      okButtonProps={{
        disabled: addressInfo?.address == originAddress || !addressInfo,
      }}
      cancelText={t("close")}
      //   style={}
    >
      <MapWithAutocomplete
        coords={
          addressInfo ? [{ lat: addressInfo.lat, lng: addressInfo.lng }] : []
        }
        onPlaceSelected={(address: any) => {
          console.log({ address });
          // setAddressInfo(address);
          handleCheckLocation(address);
        }}
      />
    </Modal>
  );
};
