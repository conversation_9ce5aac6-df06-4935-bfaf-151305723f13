.withdraw-status {
  height: 26px;
  font-size: 12px;
  font-weight: bold;
  display: inline-flex;
  align-items: center;

  &.PENDING {
    color: var(--orange-color-500);
    background-color: var(--orange-light-color);
    border-color: var(--orange-light-border-color);
  }

  &.PROCESSING {
    color: var(--warning-400);
    background-color: var(--warning-50);
    border-color: var(--warning-100);
  }

  &.REJECT {
    color: var(--error-color-500);
    background-color: var(--red-light-color);
    border-color: var(--red-light-border-color);
  }

  &.APPROVE {
    color: var(--success-500);
    background-color: var(--green-light-color);
    border-color: var(--success-100);
  }
}

@media screen and (max-width: 430px) {
  .payment-status {
    font-size: 10px;
    height: 24px;
  }
}
