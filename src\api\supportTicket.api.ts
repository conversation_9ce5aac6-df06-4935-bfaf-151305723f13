import { request } from "utils/request";
import { AxiosPromise } from "axios";

export const supportTicketApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/supportTicket",
      params,
    }),
  findOne: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/supportTicket/${id}`,
      // params,
    }),
  summaryStatus: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/supportTicket/summary/status",
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/supportTicket",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/supportTicket/${id}`,
      method: "patch",
      data,
    }),
  complete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/supportTicket/${id}/complete`,
      method: "patch",
    }),
  reopen: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/supportTicket/${id}/reopen`,
      method: "patch",
    }),
  reply: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/supportTicket/${id}/reply`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/supportTicket/${id}`,
      method: "delete",
    }),
};
