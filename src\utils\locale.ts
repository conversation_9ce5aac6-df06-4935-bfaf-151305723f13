import viVN from "antd/es/locale/vi_VN";
import enUS from "antd/es/locale/en_US";
import { Locale } from "antd/es/locale";

export const daysName = ["", "T.2", "T.3", "T.4", "T.5", "T.6", "T.7", "CN"];
export const viVNLocale: Locale = {
  ...viVN,
  Calendar: {
    ...viVN.Calendar,
    lang: {
      ...(viVN.Calendar?.lang ?? {}),
      placeholder: "Chọn thời gian",
      yearPlaceholder: "<PERSON>ọn năm",
      quarterPlaceholder: "Chọn quý",
      monthPlaceholder: "<PERSON>ọn tháng",
      weekPlaceholder: "Chọn tuần",
      rangePlaceholder: ["<PERSON><PERSON><PERSON> bắt đầu", "Ngày kết thúc"],
      rangeYearPlaceholder: ["<PERSON>ă<PERSON> bắt đầu", "<PERSON><PERSON><PERSON> kết thúc"],
      rangeQuarterPlaceholder: ["<PERSON><PERSON><PERSON> bắt đầu", "<PERSON><PERSON><PERSON> kết thúc"],
      rangeMonthPlaceholder: ["<PERSON><PERSON><PERSON><PERSON> bắt đầu", "<PERSON>h<PERSON><PERSON> kết thúc"],
      rangeWeekPlaceholder: ["Tuần bắt đầu", "<PERSON><PERSON>n kết thúc"],
      shortWeekDays: ["CN", "Hai", "Ba", "Tư", "Năm", "Sáu", "Bảy"],
      short<PERSON>onths: [
        "Tháng 1",
        "Tháng 2",
        "Tháng 3",
        "Tháng 4",
        "Tháng 5",
        "Tháng 6",
        "Tháng 7",
        "Tháng 8",
        "Tháng 9",
        "Tháng 10",
        "Tháng 11",
        "Tháng 12",
      ],
    },
  },
  DatePicker: {
    ...viVN.DatePicker,
    // @ts-ignore
    lang: {
      ...(viVN.DatePicker?.lang ?? {}),
      placeholder: "Chọn thời điểm",
      yearPlaceholder: "Chọn năm",
      quarterPlaceholder: "Chọn quý",
      monthPlaceholder: "Chọn tháng",
      weekPlaceholder: "Chọn tuần",
      rangePlaceholder: ["Ngày bắt đầu", "Ngày kết thúc"],
      rangeYearPlaceholder: ["Năm bắt đầu", "Năm kết thúc"],
      rangeQuarterPlaceholder: ["Quý bắt đầu", "Quý kết thúc"],
      rangeMonthPlaceholder: ["Tháng bắt đầu", "Tháng kết thúc"],
      rangeWeekPlaceholder: ["Tuần bắt đầu", "Tuần kết thúc"],
      shortWeekDays: ["CN", "Hai", "Ba", "Tư", "Năm", "Sáu", "Bảy"],
      shortMonths: [
        "Th1",
        "Th2",
        "Th3",
        "Th4",
        "Th5",
        "Th6",
        "Th7",
        "Th8",
        "Th9",
        "Th10",
        "Th11",
        "Th12",
      ],
    },
  },
  Form: {
    defaultValidateMessages: {
      required: "Hãy nhập thông tin cho trường ${label}",
    },
  },
};
