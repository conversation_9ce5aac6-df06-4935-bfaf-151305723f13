import { BookGallery } from "./book";
import { Brand } from "./brand";
import { Product } from "./product";
import { Shop } from "./shop";

export enum ProductCategoryType {
  Menu = "MENU",
  Category = "CATEGORY",
  Brand = "BRAND", //
  Taxonomy = "TAXONOMY", // ngành hàng
}

export const ProductCategoryTypeTrans = {
  [ProductCategoryType.Brand]: {
    label: "Thương hiệu",
    color: "green",
    value: ProductCategoryType.Brand,
  },
  [ProductCategoryType.Category]: {
    label: "Danh mục",
    color: "geekblue",
    value: ProductCategoryType.Category,
  },
  [ProductCategoryType.Taxonomy]: {
    label: "Ngành hàng",
    color: "blue",
    value: ProductCategoryType.Taxonomy,
  },
  [ProductCategoryType.Menu]: {
    label: "Menu",
    color: "purple",
    value: ProductCategoryType.Menu,
  },
};

export interface ProductCategory {
  id: number;
  code: string;
  createdAt: number;
  updatedAt: number;
  isDeleted: boolean;
  name: string;
  level: number;
  icon: string;
  slug: string;
  type: ProductCategoryType;
  position: number;
  description: string;
  children: ProductCategory[];
  parent: ProductCategory;
  products: Product[];
  brand: Brand; //khi chọn loại là thương hiệu
  isVisible: boolean;
  isHighlight: boolean;
}
