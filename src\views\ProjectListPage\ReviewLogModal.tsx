import { Typography } from "@mui/material";
import { Modal } from "antd";
import Column from "antd/es/table/Column";
import { Rule } from "antd/lib/form";
import { Pagination } from "components/Pagination";
import { AriviTable } from "components/Table/AriviTable";
import { useProjectLog } from "hooks/useProjectLog";
import React, { useEffect, useImperativeHandle, useState } from "react";
import { useTranslation } from "react-i18next";
import { Review } from "types/review";
import { unixToFullDate } from "utils/dateFormat";

const rules: Rule[] = [{ required: true }];

export interface ReviewLogModal {
  handleOpen: (review: Review) => void;
}
interface ReviewLogModalProps {
  onClose: () => void;
  onSubmitOk: () => void;
}

export const ReviewLogModal = React.forwardRef(
  ({ onClose, onSubmitOk }: ReviewLogModalProps, ref) => {
    const { t } = useTranslation();
    const [visible, setVisible] = useState(false);
    const [selectedReview, setSelectedReview] = useState<Review>();

    const {
      fetchProjectLog,
      loadingProjectLog,
      projectLogs,
      queryProjectLog,
      setQueryProjectLog,
      totalProjectLog,
    } = useProjectLog({ initQuery: { limit: 10, page: 1 } });

    useImperativeHandle<any, ReviewLogModal>(
      ref,
      () => ({
        async handleOpen(review) {
          setVisible(true);
          setSelectedReview(review);
          setQueryProjectLog({ ...queryProjectLog, reviewId: review.id });
        },
      }),
      []
    );

    useEffect(() => {
      fetchProjectLog();
    }, [queryProjectLog]);

    return (
      <Modal
        onCancel={() => {
          onClose?.();
          setVisible(false);
        }}
        open={visible}
        title={t("reviewLog")}
        style={{ top: 20 }}
        width={900}
        confirmLoading={loadingProjectLog}
        cancelText={t("close")}
        okButtonProps={{ style: { display: "none" } }}
      >
        <div className="bg-gray-100 rounded-[10px] p-2">
          <Typography variant="body2" className="!leading-4">
            {selectedReview?.content}
          </Typography>
        </div>
        <AriviTable dataSource={projectLogs}>
          <Column
            width={160}
            title={t("updateAt")}
            dataIndex={"createdAt"}
            render={(createdAt) => {
              return unixToFullDate(createdAt);
            }}
          />
          <Column
            title={t("partnerCode")}
            dataIndex={"partner"}
            render={(partner) => {
              return partner?.code;
            }}
          />
          <Column
            width={180}
            title={t("projectLogType")}
            dataIndex={"type"}
            render={(type) => {
              return t("projectLog" + type);
            }}
          />
          <Column
            title={t("description")}
            dataIndex={"description"}
            render={(description) => {
              return description;
            }}
          />
        </AriviTable>

        <Pagination
          defaultPageSize={queryProjectLog.limit}
          currentPage={queryProjectLog.page}
          total={totalProjectLog}
          onChange={({ limit, page }) => {
            queryProjectLog.page = page;
            queryProjectLog.limit = limit;
            setQueryProjectLog({ ...queryProjectLog });
            fetchProjectLog(queryProjectLog);
          }}
        />
      </Modal>
    );
  }
);
