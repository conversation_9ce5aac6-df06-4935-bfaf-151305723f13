import { request } from "utils/request";
import { AxiosPromise } from "axios";

export const customerWithdrawApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/customerWithdraw",
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/customerWithdraw",
      data,
      method: "post",
    }),
  getSummary: (): AxiosPromise<any> =>
    request({
      url: "/v1/admin/customerWithdraw/summary/status",

      method: "get",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/customerWithdraw/${id}`,
      method: "patch",
      data,
    }),
  approve: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/customerWithdraw/${id}/approve`,
      method: "patch",
      data,
    }),
  reject: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/customerWithdraw/${id}/reject`,
      method: "delete",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/customerWithdraw/${id}`,
      method: "delete",
    }),
};
