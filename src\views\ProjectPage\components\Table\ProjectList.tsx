import {
  DownOutlined,
  EditOutlined,
  LockOutlined,
  UnlockOutlined,
} from "@ant-design/icons";
import { Button, Popconfirm, Space, Table, Tag } from "antd";
import Column from "antd/lib/table/Column";
import { IPagination, Pagination } from "components/Pagination";
import { AriviTable } from "components/Table/AriviTable";
import DropdownCell from "components/Table/DropdownCell";
import { useTranslation } from "react-i18next";
import { GrEdit } from "react-icons/gr";
import { HiOutlineTrash } from "react-icons/hi2";
import { Project, ProjectStatus, ProjectStatusTrans } from "types/project";
import { formatVND } from "utils";
import { unixToFullDate } from "utils/dateFormat";

interface PropsType {
  dataSource: Project[];
  loading: boolean;
  loadingDelete?: boolean;
  pagination?: IPagination;
  onEdit?: (project: Project) => void;
  onDelete?: (projectId: number) => void;
  onActive?: (projectId: number) => void;
  onInactive?: (projectId: number) => void;

  // hasDeleteProjectPermission?: boolean;
  // hasUpdateProjectPermission?: boolean;
}

export const ProjectList = ({
  dataSource,
  loading,
  loadingDelete,
  pagination,
  onDelete,
  onEdit,
  onActive,
  onInactive,
}: // hasDeleteProjectPermission,
// hasUpdateProjectPermission,
PropsType) => {
  const { t } = useTranslation();

  return (
    <div>
      <AriviTable
        scroll={{
          x: "max-content",
          y: 600,
          //   y: "calc(100vh - 315px)",
        }}
        loading={loading}
        pagination={false}
        rowKey="id"
        dataSource={dataSource}
        size="small"
        className="custom-scrollbar"
        // onChange={}
      >
        <Column
          title={t("code")}
          dataIndex="code"
          key={"code"}
          render={(text, record: Project) => <span>{record.code}</span>}
        />
        <Column
          title={t("projectName")}
          dataIndex="name"
          key={"name"}
          render={(text, record: Project) => <span>{record.name}</span>}
        />
        <Column
          title={t("customer")}
          dataIndex="name"
          key={"name"}
          render={(text, record: Project) => (
            <span>{record.customer?.name}</span>
          )}
        />
        <Column
          title={t("product")}
          dataIndex="name"
          key={"name"}
          render={(text, record: Project) => (
            <span>{record.product?.name}</span>
          )}
        />
        <Column
          title={t("status")}
          dataIndex="name"
          align="center"
          key={"name"}
          render={(text, record: Project) => (
            <Tag color={ProjectStatusTrans[record.status as keyof typeof ProjectStatusTrans]?.color}>
              {t(record.status)}
            </Tag>
          )}
        />
        <Column
          title={t("createdAt")}
          dataIndex="name"
          key={"name"}
          render={(text, record: Project) => (
            <span>{unixToFullDate(record.createdAt)}</span>
          )}
        />

        <Column
          fixed="right"
          width={120}
          align="center"
          title=""
          key="action"
          dataIndex={""}
          render={(text, record: Project) => (
            //@ts-ignore
            <DropdownCell
              text={t("action")}
              items={[
                {
                  onClick: () => "",
                  // createOrderModalRef.current?.handleUpdate(record),
                  label: (
                    <Button
                      icon={<GrEdit />}
                      type="primary"
                      className="w-full justify-center !flex !items-center gap-2 !font-medium"
                      onClick={() => onEdit?.(record)}
                    >
                      {t("update")}
                    </Button>
                  ),
                  key: "update",
                  // hidden: !hasUpdateProjectPermission,
                },
                {
                  onClick: () => "",
                  // createOrderModalRef.current?.handleUpdate(record),
                  label: (
                    <Button
                      icon={
                        record.status !== ProjectStatus.Cancel &&
                        record.status !== ProjectStatus.Completed &&
                        record.status !== ProjectStatus.Paused ? (
                          <span>
                            <LockOutlined />
                          </span>
                        ) : record.status === ProjectStatus.Paused ? (
                          <span>
                            <UnlockOutlined />
                          </span>
                        ) : (
                          <></>
                        )
                      }
                      // type="primary"
                      className={`text-white w-full justify-center !flex !items-center gap-2 !font-medium ${
                        record.status !== ProjectStatus.Cancel &&
                        record.status !== ProjectStatus.Completed &&
                        record.status !== ProjectStatus.Paused
                          ? "bg-red-500"
                          : record.status === ProjectStatus.Paused
                          ? "bg-green-500"
                          : ""
                      }`}
                      onClick={() => {
                        if (
                          record.status !== ProjectStatus.Cancel &&
                          record.status !== ProjectStatus.Completed &&
                          record.status !== ProjectStatus.Paused
                        ) {
                          onInactive?.(record.id);
                        } else if (record.status === ProjectStatus.Paused) {
                          onActive?.(record.id);
                        }
                      }}
                    >
                      {record.status !== ProjectStatus.Cancel &&
                      record.status !== ProjectStatus.Completed &&
                      record.status !== ProjectStatus.Paused
                        ? t("stop")
                        : record.status === ProjectStatus.Paused
                        ? t("reopen")
                        : ""}
                    </Button>
                  ),
                  key: "updateStatus",
                  // hidden: !hasUpdateProjectPermission,
                },
                // {
                //   label: (
                //     <Popconfirm
                //       placement="topLeft"
                //       title={
                //         <div>
                //           <h1 className="text-sm">{t("confirm?")}</h1>
                //         </div>
                //       }
                //       onConfirm={() => onDelete?.(record.id)}
                //       okText={t("yes")}
                //       cancelText={t("no")}
                //     >
                //       <Button
                //         loading={loadingDelete}
                //         icon={<HiOutlineTrash className="text-lg" />}
                //         className={`w-full justify-center !flex !items-center gap-2 !text-red-500 !font-medium`}
                //       >
                //         {t("delete")}
                //       </Button>
                //     </Popconfirm>
                //   ),
                //   key: "delete",
                //   // hidden: !hasDeleteProjectPermission,
                // },
              ]}
              trigger={["click"]}
            >
              <a onClick={(e) => e.preventDefault()}>
                <Space>
                  {t("action")}
                  <DownOutlined />
                </Space>
              </a>
            </DropdownCell>
          )}
        />
      </AriviTable>
      {pagination && <Pagination {...pagination} />}
    </div>
  );
};
