import { Col, Form, Input, message, Modal, Row } from "antd";
import { Rule } from "antd/lib/form";
import { staffApi } from "api/staff.api";
import { SingleImageUpload } from "components/Upload/SingleImageUpload";
import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { ModalStatus } from "types/modal";
import { Staff } from "types/staff";
import { requiredRule } from "utils/validate-rules";

const rules: Rule[] = [{ required: true }];

export const ResetPasswordModal = ({
  visible,
  status,
  staffId,
  onClose,
  onSubmitOk,
}: {
  visible: boolean;
  status: ModalStatus;
  staffId: number;
  onClose: () => void;
  onSubmitOk: () => void;
}) => {
  const [form] = Form.useForm<{ password: string }>();
  const [loading, setLoading] = useState(false);
  const { t } = useTranslation();

  useEffect(() => {
    if (status == "create" && visible) {
      form.resetFields();
    }
  }, [visible, status]);

  const handleSubmit = async () => {
    await form.validateFields();
    const data = { password: form.getFieldValue("password") };

    setLoading(true);
    try {
      const res = await staffApi.resetPassword(staffId, data);
      message.success(t("actionSuccessfully"));
      onClose();
      onSubmitOk();
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      maskClosable={false}
      onCancel={onClose}
      visible={visible}
      title={t("resetPass")}
      style={{ top: 20 }}
      width={500}
      confirmLoading={loading}
      cancelText={t("close")}
      onOk={handleSubmit}
    >
      <Form
        layout="vertical"
        form={form}
        onFinish={handleSubmit}
        validateTrigger={["onBlur", "onChange"]}
      >
        <Form.Item
          label={t("newPass")}
          required
          name="password"
          rules={[requiredRule]}
        >
          <Input.Password placeholder="" />
        </Form.Item>
      </Form>
    </Modal>
  );
};
