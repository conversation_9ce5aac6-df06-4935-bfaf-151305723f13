import { roleApi } from "api/role.api";
import { action, makeAutoObservable, toJS } from "mobx";
import { makePersistable } from "mobx-persist-store";
import { adminRoutes, Route } from "router";
import { settings } from "settings";
import { Permission } from "types/role";

class PermissionStore {
  permissions: Permission[] = [];
  accessRoutes: Route[] = [];

  constructor() {
    makeAutoObservable(this);
    makePersistable(this, {
      name: "PermissionStore",
      properties: ["permissions"],
      storage: localStorage,
    });
  }

  fetchPermissions = async (roleId: number) => {
    const res = await roleApi.findOne(roleId);
    this.permissions = res.data.permissions;
  };

  setAccessRoutes = () => {
    if (settings.checkPermission && this.permissions.length) {
      for (const route of this.accessRoutes) {
        // const routeJs = JSON.parse(JSON.stringify(route));
        // if (route?.path?.includes("/customer")) debugger;
        if (!route.children) {
          const find = permissionStore.permissions.find(
            (e) => e.path == route.path
          );
          route.isAccess = !!find;
        } else {
          for (const childRoute of route.children) {
            const find = permissionStore.permissions.find(
              (e) => e.path == route.path + "/" + childRoute.path
            );

            childRoute.isAccess = !!find;
          }
          // const filterElementRoutes = route.children.filter(
          //   (item) => item.element
          // );
          // const findMainRoute = permissionStore.permissions.find(
          //   (e) => e.path == route.path
          // );
          // if (!filterElementRoutes.length && route.element && findMainRoute) {
          //   route.isAccess = true;
          // }
          route.isAccess = route.children.some((e) => e.isAccess);
        }
      }
    }
  };
}

const permissionStore = new PermissionStore();

export { permissionStore };
