import {
  ExportOutlined,
  ImportOutlined,
  PlusOutlined,
  SearchOutlined,
} from "@ant-design/icons";
import { Button, Card, Input, message, Popconfirm, Space } from "antd";
import { rankApi } from "api/rank.api";
import { useRank } from "hooks/useRank";
import { useEffect, useMemo, useRef, useState } from "react";
import { PermissionNames } from "router";
import { permissionStore } from "store/permissionStore";
import { Rank } from "types/rank";
import { formatVND, getTitle } from "utils";
import { checkRole } from "utils/auth";
import { unixToFullDate } from "utils/dateFormat";
import { handleExport, MyExcelColumn } from "../../utils/MyExcel";
import { RankModal, RankModalRef } from "./components/Modal/RankModal";
import { RankList } from "./components/Table/RankList";
import { TextField } from "@mui/material";
import { useTranslation } from "react-i18next";
import { ReactComponent as DownloadIcon } from "assets/svgs/download.svg";
import { ReactComponent as SearchIcon } from "assets/svgs/search-icon.svg";
import { ReactComponent as PlusIcon } from "assets/svgs/plus-icon.svg";
import "./styles/RankPage.scss";

export const RankPage = ({ title = "" }) => {
  const rankModalRef = useRef<RankModalRef>();
  const [openImport, setOpenImport] = useState(false);
  const { t } = useTranslation();

  const [loadingDelete, setLoadingDelete] = useState(false);
  const { ranks, fetchRank, loadingRank, queryRank, totalRank } = useRank({
    initQuery: {
      page: 1,
      limit: 20,
    },
  });
  // const hasRankAddPermission = checkRole(
  //   PermissionNames.consumerRankAdd,
  //   permissionStore.permissions
  // );
  // const hasRankUpdatePermission = checkRole(
  //   PermissionNames.consumerIndustryEdit,
  //   permissionStore.permissions
  // );
  // const hasIndustryDeletePermission = checkRole(
  //   PermissionNames.consumerIndustryDelete,
  //   permissionStore.permissions
  // );
  useEffect(() => {
    document.title = getTitle(t(title));
    fetchRank();
  }, []);
  const exportColumns: MyExcelColumn<Rank>[] = [
    {
      width: 30,
      header: t("rankName"),
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "name",
      render: (record: Rank) => {
        return record.name;
      },
    },
    {
      width: 30,
      header: t("rankNameEn"),
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "nameEn",
      render: (record: Rank) => {
        return record.nameEn;
      },
    },
    {
      width: 20,
      header: t("position"),
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "position",
      render: (record: Rank) => {
        return record.position;
      },
    },

    {
      width: 50,
      header: t("waitingTime"),
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "waitingTime",
      render: (record: Rank) => {
        return record.waitingTime;
      },
    },

    {
      width: 50,
      header: t("needMission"),
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "needMission",
      render: (record: Rank) => {
        return t(record.needReview);
      },
    },
    {
      width: 20,
      header: t("rewardPoint"),
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "rewardPoint",
      render: (record: Rank) => {
        return formatVND(record.rewardPoint);
      },
    },
    {
      width: 20,
      header: t("createdAt"),
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "createdAt",
      render: (record: Rank) => {
        return unixToFullDate(record.createdAt);
      },
    },
  ];

  const handleDeleteRank = async (rankId: number) => {
    try {
      setLoadingDelete(true);
      const res = await rankApi.delete(rankId);
      fetchRank();
      message.success(t("actionSuccessfully"));
    } catch (error) {
    } finally {
      setLoadingDelete(false);
    }
  };

  const handleSearch = (search: string) => {
    queryRank.search = search;
    queryRank.page = 1;
    fetchRank();
  };

  return (
    // <Card bodyStyle={{ padding: "8px 20px" }} style={{ borderRadius: "8px" }}>
    <div>
      <section className="box">
        <div className="filter-container">
          <Space wrap>
            {/* <div className="filter-item ">
              <label htmlFor="">{t("search")}</label>
              <Input.Search
                allowClear
                onChange={(ev) => {
                  if (ev.currentTarget.value) {
                    queryRank.search = ev.currentTarget.value;
                  } else {
                    queryRank.search = undefined;
                  }
                  queryRank.page = 1;

                }}
                onKeyDown={(ev) => {
                  if (ev.code == "Enter") {
                    fetchRank();
                  }
                }}
                size="large"
                className="w-full search-btn mt-1"
                // placeholder={t("productName")}
                enterButton={< SearchIcon />}
                onSearch={handleSearch}
              />
            </div> */}

            {/* <div className="filter-item btn">
              <Button
                onClick={() => fetchRank()}
                type="primary"
                icon={<SearchOutlined />}
              >
                {t("search")}
              </Button>
            </div> */}
            {/* {hasRankAddPermission && ( */}
            <div className="filter-item-btn">
              <Button
                onClick={() => {
                  rankModalRef.current?.handleCreate();
                }}
                icon={
                  <PlusIcon height={20} width={20} className="text-gray-6" />
                }
                type="primary"
                size="large"
              >
                <span className="text-regular text-action-light-4">
                  {t("create")}
                </span>
              </Button>
            </div>
            {/* )} */}
            {/* <div className="filter-item btn">
                  <Button
                    onClick={() => {
                      setOpenImport(true);
                    }}
                    type="primary"
                    icon={<PlusOutlined />}
                  >
                    Nhập excel
                  </Button>
                </div> */}
            {/* <div className="filter-item btn">
                    <Button
                      onClick={() => {
                        importModal.current?.open();
                      }}
                      type="primary"
                      icon={<ImportOutlined />}
                    >
                      Nhập excel
                    </Button>
                  </div> */}

            {/* <div className="filter-item btn">
              <Popconfirm
                title={t("exportAsk")}
                onConfirm={() =>
                  handleExport({
                    onProgress(percent) { },
                    exportColumns,
                    fileType: "xlsx",
                    dataField: "ranks",
                    query: queryRank,
                    api: rankApi.findAll,
                    fileName: t("rank"),
                    sheetName: t("rank"),
                  })
                }
                okText={t("exportExcel")}
                cancelText={t("cancel")}
              >
                <Button
                  size="large"
                  type="primary"
                  loading={false}
                  icon={<DownloadIcon />}
                >
                  {t("exportExcel")}
                </Button>
              </Popconfirm>
            </div> */}
          </Space>
        </div>

        <RankList
          onEdit={(record) => rankModalRef.current?.handleUpdate(record)}
          dataSource={ranks}
          loading={loadingRank}
          loadingDelete={loadingDelete}
          // pagination={{
          //   total: totalRank,
          //   defaultPageSize: queryRank.limit,
          //   currentPage: queryRank.page,
          //   onChange: ({ page, limit }) => {
          //     Object.assign(queryRank, {
          //       page,
          //       limit,
          //     });
          //     fetchRank();
          //   },
          // }}
          onDelete={handleDeleteRank}
          // onActive={handleActiveRank}
          // onInactive={handleInactiveRank}

          // hasDeleteRankPermission={hasRankDeletePermission}
          // hasUpdateRankPermission={hasRankUpdatePermission}
        />
      </section>

      <RankModal
        ref={rankModalRef}
        onClose={function (): void {
          throw new Error("Function not implemented.");
        }}
        onSubmitOk={fetchRank}
        // hasAddIndustryPermission={hasIndustryAddPermission}
        // hasUpdateIndustryPermission={hasIndustryUpdatePermission}
      />
      {/* </Card> */}
    </div>
  );
};
