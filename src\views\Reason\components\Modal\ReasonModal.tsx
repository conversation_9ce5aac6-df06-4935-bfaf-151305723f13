import { Col, Form, Input, message, Modal, Row } from "antd";
import { UploadFile } from "antd/lib";
import { useForm } from "antd/lib/form/Form";
import { reasonApi } from "api/reason.api";
import { InputNumber } from "components/Input/InputNumber";
import { SingleImageUpload } from "components/Upload/SingleImageUpload";
import { forwardRef, useImperativeHandle, useState } from "react";
import { useTranslation } from "react-i18next";
import { ModalStatus } from "types/modal";
import { Reason } from "types/reason";
import { requiredRule } from "utils/validate-rules";

export interface ReasonModalProps {
  onClose: () => void;
  onSubmitOk: () => void;
  // hasAddReasonPermission?: boolean;
  // hasUpdateReasonPermission?: boolean;
}

export interface ReasonModalRef {
  handleUpdate: (reason: Reason) => void;
  handleCreate: () => void;
}

export const ReasonModal = forwardRef(
  (
    {
      onSubmitOk,
    }: // hasAddReasonPermission,
    // hasUpdateReasonPermission,
    ReasonModalProps,
    ref
  ) => {
    const [form] = useForm();
    const [loading, setLoading] = useState(false);
    const [visible, setVisible] = useState<boolean>();
    const { t } = useTranslation();
    const [fileList, setFileList] = useState<UploadFile[]>([]);
    const onChangeFileList = (fileList: UploadFile[]): void => {
      setFileList(fileList);
    };
    const [selectedReason, setSelectedReason] = useState<Reason>();
    const [status, setStatus] = useState<ModalStatus>("create");

    useImperativeHandle(
      ref,
      () => ({
        handleUpdate,
        handleCreate,
      }),
      []
    );

    const handleUpdate = (reason: Reason) => {
      form.setFieldsValue({
        ...reason,
      });
      // setFileList(Reason?.fileAttaches)
      setSelectedReason(reason);
      setStatus("update");
      setVisible(true);
    };

    const handleCreate = () => {
      setStatus("create");
      setVisible(true);
    };

    const handleSubmitForm = async () => {
      await form.validateFields();
      console.log(fileList);
      const dataForm = form.getFieldsValue();
      if (dataForm.name) {
        dataForm.name = dataForm.name.trim();
      }
      const payload = {
        reason: {
          ...dataForm,
        },
      };

      try {
        setLoading(true);
        switch (status) {
          case "update":
            await reasonApi.update(selectedReason?.id || 0, payload);
            message.success(t("actionSuccessfully"));
            break;

          //create
          default:
            await reasonApi.create(payload);
            message.success(t("actionSuccessfully"));
            break;
        }
        setVisible(false);
        onSubmitOk();
        setFileList([]);
      } catch (error) {
      } finally {
        setLoading(false);
        // onSubmitOk();
      }
    };

    return (
      <Modal
        onCancel={() => {
          setVisible(false);
          setFileList([]);
        }}
        visible={visible}
        centered
        title={
          <h1 className="mb-0 text-lg text-primary font-bold">
            {status == "create" ? t("create") : t("update")}
          </h1>
        }
        confirmLoading={loading}
        destroyOnClose
        width={800}
        onOk={handleSubmitForm}
        afterClose={() => {
          form.resetFields();
        }}

        // okButtonProps={{
        //   hidden:
        //     (!hasAddReasonPermission && status == "create") ||
        //     (!hasUpdateReasonPermission && status == "update"),
        // }}
      >
        <Form
          form={form}
          layout="vertical"
          validateTrigger={["onBlur", "onChange"]}
        >
          <Row gutter={[12, 0]}>
            <Col span={24}>
              <Form.Item
                className="!mb-2"
                name="name"
                label={t("reasonName")}
                rules={[requiredRule]}
              >
                <Input placeholder={t("enterName")} size="small" />
              </Form.Item>
            </Col>

            <Col span={24}>
              <Form.Item
                className="!mb-2"
                name="nameEn"
                label={t("reasonNameEn")}
                rules={[requiredRule]}
              >
                <Input placeholder={t("enterName")} size="small" />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    );
  }
);
