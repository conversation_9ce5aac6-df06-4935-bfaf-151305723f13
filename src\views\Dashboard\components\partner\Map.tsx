import { InfoCircleOutlined } from "@ant-design/icons";
import { Card, Tooltip, Typography } from "@mui/material";
import { useTranslation } from "react-i18next";
import { useEffect, useRef, useState, useCallback } from "react";
import { debounce } from "lodash";
import { observer } from "mobx-react";
import googleMapReact from "google-map-react";

import MapWithAutocomplete, {
  MapWithAutocompleteRef,
} from "components/Map/MapWithAutocomplete";
import { usePartner } from "hooks/usePartner";
import { useProject } from "hooks/useProject";

const PartnerMap = () => {
  const { t } = useTranslation();

  const {
    fetchProject,
    projects,
    setQueryProject,
    queryProject,
    loadingProject,
  } = useProject({
    initQuery: { limit: 0, page: 1 },
  });

  const {
    fetchPartner,
    partners,
    setQueryPartner,
    queryPartner,
    loadingPartner,
  } = usePartner({
    initQuery: { limit: 0, page: 1, isHavePosition: true },
  });

  const projectMapRef = useRef<MapWithAutocompleteRef>();
  const partnerMapRef = useRef<MapWithAutocompleteRef>();

  const [projectMapLoaded, setProjectMapLoaded] = useState(false);
  const [partnerMapLoaded, setPartnerMapLoaded] = useState(false);

  useEffect(() => {
    fetchProject();
  }, [queryProject]);

  useEffect(() => {
    fetchPartner();
  }, [queryPartner]);

  useEffect(() => {
    if (projects.length && projectMapLoaded) {
      projectMapRef.current?.fitBounds();
    }
  }, [projects, projectMapLoaded]);

  useEffect(() => {
    if (partners.length && partnerMapLoaded) {
      partnerMapRef.current?.fitBounds();
    }
  }, [partners, partnerMapLoaded]);

  const debounceProjectMapChange = useCallback(
    debounce((value: googleMapReact.ChangeEventValue) => {
      setQueryProject({
        ...queryProject,
        topLeftLat: value.bounds.nw.lat,
        topLeftLong: value.bounds.nw.lng,
        bottomRightLat: value.bounds.se.lat,
        bottomRightLong: value.bounds.se.lng,
      });
    }, 300),
    [queryProject]
  );

  const debouncePartnerMapChange = useCallback(
    debounce((value: googleMapReact.ChangeEventValue) => {
      setQueryPartner({
        ...queryPartner,
        topLeftLat: value.bounds.nw.lat,
        topLeftLong: value.bounds.nw.lng,
        bottomRightLat: value.bounds.se.lat,
        bottomRightLong: value.bounds.se.lng,
      });
    }, 300),
    [queryPartner]
  );

  return (
    <Card className="mt-4">
      <h5 className="semibold !mb-6">{t("partnerDashboard")}</h5>
      <MapWithAutocomplete
        enableCluster
        ref={partnerMapRef}
        key="mapDashboardPartner"
        coords={partners.map((p) => ({
          lat: p.lat,
          lng: p.long,
          partner: p,
        }))}
        noInput
        onLoaded={() => setPartnerMapLoaded(true)}
        noAutoCenterByCoord
        viewPartner
        colorCluster="#559FFF"
        style={{ height: "600px", width: "100%" }}
      />
    </Card>
  );
};

export default observer(PartnerMap);
