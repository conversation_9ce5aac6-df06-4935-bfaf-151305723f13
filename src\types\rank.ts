import { MultipleLang } from "./faq";
import { Partner } from "./partner";
import { Staff } from "./staff";

export interface Rank {
  id: number;
  createdAt: number;
  updatedAt: number;
  isDeleted: boolean;
  name: string;
  description: string; //quyền lợi
  condition: string; //điều kiện
  image: string;
  waitingTime: number;
  numDay: number; //số ngày để lên hạng
  needReview: number; //số nhiệm dụ tối thiểu
  rewardPoint: number; // số điểm thongwr
  rewardPointByImage: number; // số điểm thongwr
  position: number;
  icon: string;
  level: number;
  isVisible: boolean;
  //CUSTOM
  nameEn: string;
  descriptionEn: string; //quyền lợi
  partners: Partner[];
  multipleLangs: MultipleLang[];
  createdStaff: Staff;
  updatedStaff: Staff;
}
