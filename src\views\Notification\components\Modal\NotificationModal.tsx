import { Button, Col, Form, Input, message, Modal, Row, Select } from "antd";
import { useForm } from "antd/lib/form/Form";
import { forwardRef, useEffect, useImperativeHandle, useState } from "react";
import { ModalStatus } from "types/modal";
import { SurveyCampaign } from "types/survey";
import { requiredRule } from "utils/validate-rules";
import { InputNumber } from "components/Input/InputNumber";
import { notificationApi } from "api/notification.api";
import { Notification, NotificationScope } from "types/notification";
import TextArea from "antd/es/input/TextArea";
import { userStore } from "store/userStore";
import { useTranslation } from "react-i18next";
import { RichTextEditorV2 } from "components/Editor/RichTextEditorV2";
import { LanguageSelector } from "../LanguageSelect";

export interface NotificationModalProps {
  onClose: () => void;
  onSubmitOk: () => void;
  scope?: NotificationScope;
  // hasAddNotificationPermission?: boolean;
  // hasUpdateNotificationPermission?: boolean;
}

export interface NotificationModalRef {
  handleUpdate: (notification: Notification) => void;
  handleCreate: () => void;
}


export const NotificationModal = forwardRef(
  (
    {
      onSubmitOk,
      scope = NotificationScope.Customer,
    }: // hasAddNotificationPermission,
      // hasUpdateNotificationPermission,
      NotificationModalProps,
    ref
  ) => {
    const [form] = useForm();
    const [loading, setLoading] = useState(false);
    const [visible, setVisible] = useState<boolean>();
    const { t } = useTranslation();
    const [content, setContent] = useState("");
    const [selectLanguage, setSelectLanguage] = useState("vi");
    const [errorLanguage, setErrorLanguage] = useState(false);

    const [selectedNotification, setSelectedNotification] =
      useState<Notification>();
    const [status, setStatus] = useState<ModalStatus>("create");

    useImperativeHandle(
      ref,
      () => ({
        handleUpdate,
        handleCreate,
      }),
      []
    );

    const handleUpdate = (notification: Notification) => {
      form.setFieldsValue({
        ...notification,
      });
      setSelectedNotification(notification);
      setStatus("update");
      setVisible(true);
    };

    const handleCreate = () => {
      setStatus("create");
      setVisible(true);
    };

    const handleSubmitForm = async () => {
      setErrorLanguage(false)
      try {
        await form.validateFields();
      } catch (e: any) {
        const dataForm = form.getFieldsValue();

        const isViFilled =
          !!dataForm.title?.trim() &&
          !!dataForm.shortContent?.trim() &&
          !!dataForm.content?.trim();

        const isEnFilled =
          !!dataForm.titleEn?.trim() &&
          !!dataForm.shortContentEn?.trim() &&
          !!dataForm.contentEn?.trim();

        if (selectLanguage === "vi" && isViFilled && !isEnFilled) {
          message.error(t("enterContentEn"));
          setErrorLanguage(true)
          return;
        }

        if (selectLanguage === "en" && isEnFilled && !isViFilled) {
          message.error(t("enterContentVi"));
          setErrorLanguage(true)
          return;
        }
        return;
      }
      const dataForm = form.getFieldsValue();
      if (dataForm.name) {
        dataForm.name = dataForm.name.trim();
      }
      const payload = {
        notification: {
          ...dataForm,
          scope,
        },
      };

      try {
        setLoading(true);
        switch (status) {
          case "update":
            await notificationApi.update(
              selectedNotification?.id || 0,
              payload
            );
            message.success(t("actionSuccessfully"));
            break;

          //create
          default:
            await notificationApi.create(payload);
            message.success(t("actionSuccessfully"));
            break;
        }
        setVisible(false);
        onSubmitOk();
      } catch (error) {
      } finally {
        setLoading(false);
        // onSubmitOk();
      }
    };

    return (
      <Modal
        onCancel={() => {
          setVisible(false);
        }}
        visible={visible}
        centered
        // title={
        //   <h1 className="mb-0 text-lg font-[600] uppercase">
        //     {status == "create" ? t("create") : t("update")}
        //   </h1>
        // }
        confirmLoading={loading}
        destroyOnClose
        width={800}
        onOk={handleSubmitForm}
        afterClose={() => {
          form.resetFields();
        }}
        okText={t("save")}
        footer={[
          <Button
            variant="outlined"
            onClick={() => {
              form.resetFields();
              setVisible(false);
            }}
            size="large"
            className="btn-cancel"
            style={{ borderColor: "#E94134", minWidth: 136, fontSize: 16, fontWeight: 600 }}
          >
            {t("cancel")}
          </Button>,
          <Button
            type="primary"
            onClick={handleSubmitForm}
            size="large"
            style={{ color: "#fff", minWidth: 136, fontSize: 16, fontWeight: 600 }}
          >
            {t("save")}
          </Button>,
        ]}
      >
        <div className="mb-3 flex justify-between items-center mt-5">
          <h1 className="mb-0 text-lg font-[600] uppercase">
            {status == "create" ? t("create") : t("update")}
          </h1>
          <div className="mt-1 -mr-1">
            <LanguageSelector
              value={selectLanguage}
              onChange={(lang: string) => {
                setSelectLanguage(lang);
              }}
            />
          </div>

        </div>
        <Form
          form={form}
          layout="vertical"
          validateTrigger={["onBlur", "onChange"]}
        >
          <Row gutter={[12, 0]} style={{ display: selectLanguage !== "vi" ? "none" : "block" }}>
            <Col span={24}>
              <Form.Item
                className="!mb-2"
                name="title"
                label={<label className="!font-[600]">{t("title")}</label>}
                rules={[{ required: true, message: t("requiredField", { field: t("title") }) }]}
              >
                <Input className="border-[#EAEFF4]" />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item
                className="!mb-2"
                name="shortContent"
                label={<label className="!font-[600]">{t("shortContent")}</label>}
                rules={[
                  { required: true, message: t("requiredField", { field: t("shortContent") }) },
                  {
                    max: 255,
                    message: t("descriptionMaxCharacters", { number: 255 }),
                  },
                ]}
              >
                <TextArea className="border-[#EAEFF4]" />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item
                className="!mb-2 rounded-[20px] rivi-small"
                name="content"
                label={<label className="!font-[600]">{t("content")}</label>}
                rules={[{ required: true, message: t("requiredField", { field: t("content") }) }]}
              >
                <RichTextEditorV2
                  onChange={(content) => {
                    setContent(content);
                    form.setFieldsValue({ value: content });
                  }}
                  content={content}
                  maxHeight={200}
                  hideUpload
                />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={[12, 0]} style={{ display: selectLanguage !== "en" ? "none" : "block" }}>
            <Col span={24}>
              <Form.Item
                className="!mb-2"
                name={"titleEn"}
                label={<label className="!font-[600]">{t("title")}</label>}
                rules={[{ required: true, message: t("requiredField", { field: t("title") }) }]}
              >
                <Input className="border-[#EAEFF4]" />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item
                className="!mb-2"
                name="shortContentEn"
                label={<label className="!font-[600]">{t("shortContent")}</label>}
                rules={[
                  { required: true, message: t("requiredField", { field: t("shortContent") }) },
                  {
                    max: 255,
                    message: t("descriptionMaxCharacters", { number: 255 }),
                  },
                ]}
              >
                <TextArea className="border-[#EAEFF4]" />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item
                className="!mb-2 rounded-[20px] rivi-small"
                name="contentEn"
                label={<label className="!font-[600]">{t("content")}</label>}
                rules={[{ required: true, message: t("requiredField", { field: t("content") }) }]}
              >
                <RichTextEditorV2
                  onChange={(content) => {
                    setContent(content);
                    form.setFieldsValue({ value: content });
                  }}
                  content={content}
                  maxHeight={200}
                  hideUpload
                />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    );
  }
);
