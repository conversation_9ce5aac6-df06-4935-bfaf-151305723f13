import {
  <PERSON><PERSON>,
  Box,
  <PERSON><PERSON><PERSON>utt<PERSON>,
  <PERSON><PERSON><PERSON>,
  Typo<PERSON>,
  useMediaQuery,
} from "@mui/material";
import { IconPower } from "@tabler/icons-react";
import { observer } from "mobx-react";
import { useTranslation } from "react-i18next";
import { Link } from "react-router-dom";
import { settings } from "settings";
import img1 from "src/assets/images/profile/user-1.jpg";
import { appStore } from "store/appStore";
import { userStore } from "store/userStore";
import { CiLogout } from "react-icons/ci";
// import logoutImg from "assets/images/logout.png";
import logoutImg from "assets/svgs/icon-logout.svg";

export const SidebarProfile = observer(() => {
  const { t } = useTranslation();

  const customizer = appStore.customizer;
  const lgUp = useMediaQuery((theme: any) => theme.breakpoints.up("lg"));
  const hideMenu = lgUp
    ? customizer.isCollapse && !customizer.isSidebarHover
    : "";

  return (
    <Box
      display={"flex"}
      alignItems="center"
      gap={2}
      sx={{
        mx: 3,
        px: hideMenu ? 0 : 2,
        py: "7px",
        bgcolor: `#F6F9FC`,
        justifyContent: hideMenu ? "center" : "",
      }}
    >
      {!hideMenu ? (
        <>
          {/* <Avatar
            className="!w-[30px] !h-[30px]"
            sizes=""
            alt={userStore.info.email}
            src={userStore.info.avatar}
          /> */}

          <Box>
            {/* <div className="break-all font-medium text-sm">
              {userStore.info.name || "Welcome"}
            </div>
            <Typography variant="caption">{userStore.info.phone}</Typography> */}
            {/* <Typography variant="h6" color="main.primary">
              {t("logout")}
            </Typography> */}
            <div className="text-[#5A6A85]">{t("logout")}</div>
          </Box>
          <Box sx={{ ml: "auto" }}>
            <Tooltip title={t("logout")} placement="top">
              <IconButton
                component={Link}
                to="/login"
                aria-label="logout"
                size="small"
                onClick={() => {
                  userStore.logout();
                }}
              >
                {/* <CiLogout size={24} className="rotate-180" /> */}
                {/* <IconPower size="20" /> */}
                <img src={logoutImg} className="w-[24px] h-[24px]" />
              </IconButton>
            </Tooltip>
          </Box>
        </>
      ) : (
        <Tooltip title={t("logout")} placement="top">
          <IconButton
            color="primary"
            component={Link}
            to="/login"
            aria-label="logout"
            size="small"
            onClick={() => {
              userStore.logout();
            }}
          >
            <CiLogout size={24} className="rotate-180" />
            {/* <IconPower size="20" /> */}
          </IconButton>
        </Tooltip>
      )}
    </Box>
  );
});
