import {
  ExportOutlined,
  ImportOutlined,
  PlusOutlined,
  SearchOutlined,
} from "@ant-design/icons";
import { Button, Card, Input, message, Popconfirm, Space } from "antd";
import { promotionApi } from "api/promotion.api";
import { usePromotion } from "hooks/usePromotion";
import { useEffect, useMemo, useRef, useState } from "react";
import { PermissionNames } from "router";
import { permissionStore } from "store/permissionStore";
import {
  Promotion,
  PromotionDiscountTypeTrans,
  PromotionStatus,
} from "types/promotion";
import { getTitle } from "utils";
import { checkRole } from "utils/auth";
import { unixToFullDate } from "utils/dateFormat";
import { handleExport, MyExcelColumn } from "../../utils/MyExcel";
import { TextField } from "@mui/material";
import { useTranslation } from "react-i18next";
import { PromotionList } from "./component/Table/PromotionList";
import {
  PromotionModal,
  PromotionModalRef,
} from "./component/Modal/PromotionModal";
import { formatDate } from "utils/date";
import { ReactComponent as DownloadIcon } from "assets/svgs/download.svg";
import { ReactComponent as SearchIcon } from "assets/svgs/search-icon.svg";
import { ReactComponent as PlusIcon } from "assets/svgs/plus-icon.svg";

export const PromotionPage = ({ title = "" }) => {
  const promotionModalRef = useRef<PromotionModalRef>();
  const [openImport, setOpenImport] = useState(false);
  const { t } = useTranslation();
  const exportColumns: MyExcelColumn<Promotion>[] = [
    {
      header: t("code"),
      headingStyle: {
        font: { bold: true },
      },
      key: "code",
      columnKey: "code",
      render: (record) => record.code,
    },
    {
      header: t("promotionName"),
      headingStyle: {
        font: { bold: true },
      },
      key: "title",
      columnKey: "title",
      render: (record) => record.title,
    },

    {
      header: t("description"),
      headingStyle: {
        font: { bold: true },
      },
      key: "description",
      columnKey: "description",
      render: (record) => record.description,
    },
    {
      header: t("discount"),
      headingStyle: {
        font: { bold: true },
      },
      key: "discountValue",
      columnKey: "discountValue",
      render: (record: Promotion) =>
        `${record.discountValue} (${PromotionDiscountTypeTrans[record.discountType]?.label
        })`,
    },
    {
      header: t("promotionQuantity"),
      headingStyle: {
        font: { bold: true },
      },
      key: "quantity",
      columnKey: "quantity",
      render: (record) => record.quantity,
    },
    {
      header: t("totalUsed"),
      headingStyle: {
        font: { bold: true },
      },
      key: "totalUsed",
      columnKey: "totalUsed",
      render: (record) => record.totalUsed,
    },
    {
      header: t("voucherPeriod"),
      headingStyle: {
        font: { bold: true },
      },
      key: "voucherPeriod",
      columnKey: "voucherPeriod",
      render: (record) =>
        `${formatDate(record.startAt)} - ${formatDate(record.endAt)}`,
    },

    {
      header: t("status"),
      headingStyle: {
        font: { bold: true },
      },
      key: "status",
      columnKey: "status",
      render: (record) => t(record.status),
    },
  ];
  const [loadingDelete, setLoadingDelete] = useState(false);
  const {
    promotions,
    fetchPromotion,
    loadingPromotion,
    queryPromotion,
    totalPromotion,
  } = usePromotion({
    initQuery: {
      page: 1,
      limit: 20,
    },
  });
  // const hasPromotionAddPermission = checkRole(
  //   PermissionNames.consumerPromotionAdd,
  //   permissionStore.permissions
  // );
  // const hasPromotionUpdatePermission = checkRole(
  //   PermissionNames.consumerIndustryEdit,
  //   permissionStore.permissions
  // );
  // const hasIndustryDeletePermission = checkRole(
  //   PermissionNames.consumerIndustryDelete,
  //   permissionStore.permissions
  // );
  useEffect(() => {
    document.title = getTitle(t(title));
    fetchPromotion();
  }, []);

  const handleDeletePromotion = async (promotionId: number) => {
    try {
      setLoadingDelete(true);
      const res = await promotionApi.delete(promotionId);
      fetchPromotion();
      message.success(t("actionSuccessfully"));
    } catch (error) {
    } finally {
      setLoadingDelete(false);
    }
  };
  const handleActivePromotion = async (promotionId: number) => {
    try {
      setLoadingDelete(true);
      const res = await promotionApi.changeStatus(promotionId, {
        status: PromotionStatus.Active,
      });
      fetchPromotion();
      message.success(t("actionSuccessfully"));
    } catch (error) {
    } finally {
      setLoadingDelete(false);
    }
  };

  const handleInactivePromotion = async (promotionId: number) => {
    try {
      setLoadingDelete(true);
      const res = await promotionApi.changeStatus(promotionId, {
        status: PromotionStatus.Inactive,
      });
      fetchPromotion();
      message.success(t("actionSuccessfully"));
    } catch (error) {
    } finally {
      setLoadingDelete(false);
    }
  };

  const handleSearch = (search: string) => {
    queryPromotion.search = search;
    queryPromotion.page = 1;
    fetchPromotion();
  };

  return (
    // <Card bodyStyle={{ padding: "8px 20px" }} style={{ borderRadius: "8px" }}>
    <div>
      <section className="box">
        <div className="filter-container">
          <Space wrap>
            <div className="filter-item">
              {/* <label htmlFor="">Tìm kiếm</label>
                <br /> */}
              <label htmlFor="">{t("search")}</label>
              <Input.Search
                allowClear
                onChange={(ev) => {
                  if (ev.currentTarget.value) {
                    queryPromotion.search = ev.currentTarget.value;
                  } else {
                    queryPromotion.search = undefined;
                  }
                  queryPromotion.page = 1;

                }}
                onKeyDown={(ev) => {
                  if (ev.code == "Enter") {
                    fetchPromotion();
                  }
                }}
                size="large"
                className="w-full search-btn mt-1"
                // placeholder={t("productName")}
                enterButton={< SearchIcon />}
                onSearch={handleSearch}
              />
              {/* <TextField
                // allowClear
                size="small"
                onChange={(ev) => {
                  const value = ev.currentTarget.value;
                  if (value) {
                    queryPromotion.page = 1;
                    queryPromotion.search = value;
                  } else {
                    queryPromotion.search = undefined;
                    fetchPromotion();
                  }
                }}
                onKeyDown={(ev) => {
                  if (ev.code == "Enter") {
                    fetchPromotion();
                  }
                }}
                label={t("promotionName")}
              /> */}
            </div>

            {/* <div className="filter-item btn">
              <Button
                onClick={() => fetchPromotion()}
                type="primary"
                icon={<SearchOutlined />}
              >
                {t("search")}
              </Button>
            </div> */}
            {/* {hasPromotionAddPermission && ( */}
            <div className="filter-item btn">
              <Button
                onClick={() => {
                  promotionModalRef.current?.handleCreate();
                }}
                icon={<PlusIcon />}
                type="primary"
                size="large"
              >
                {t("create")}
              </Button>
            </div>
            {/* )} */}
            {/* <div className="filter-item btn">
                <Button
                  onClick={() => {
                    setOpenImport(true);
                  }}
                  type="primary"
                  icon={<PlusOutlined />}
                >
                  Nhập excel
                </Button>
              </div> */}
            {/* <div className="filter-item btn">
                  <Button
                    onClick={() => {
                      importModal.current?.open();
                    }}
                    type="primary"
                    icon={<ImportOutlined />}
                  >
                    Nhập excel
                  </Button>
                </div> */}

            <div className="filter-item btn">
              <Popconfirm
                title={t("exportAsk")}
                onConfirm={() =>
                  handleExport({
                    onProgress(percent) { },
                    exportColumns,
                    fileType: "xlsx",
                    dataField: "promotions",
                    query: queryPromotion,
                    api: promotionApi.findAll,
                    fileName: t("voucherList"),
                    sheetName: t("voucherList"),
                  })
                }
                okText={t("exportExcel")}
                cancelText={t("cancel")}
              >
                <Button
                  type="primary"
                  size="large"
                  loading={false}
                  icon={<DownloadIcon />}
                >
                  {t("exportExcel")}
                </Button>
              </Popconfirm>
            </div>
          </Space>
        </div>
        <div className="b-container">
          <PromotionList
            onEdit={(record) => promotionModalRef.current?.handleUpdate(record)}
            dataSource={promotions}
            loading={loadingPromotion}
            loadingDelete={loadingDelete}
            pagination={{
              total: totalPromotion,
              defaultPageSize: queryPromotion.limit,
              currentPage: queryPromotion.page,
              onChange: ({ page, limit }) => {
                Object.assign(queryPromotion, {
                  page,
                  limit,
                });
                fetchPromotion();
              },
            }}
            onDelete={handleDeletePromotion}
            onActive={handleActivePromotion}
            onInactive={handleInactivePromotion}

          // hasDeletePromotionPermission={hasPromotionDeletePermission}
          // hasUpdatePromotionPermission={hasPromotionUpdatePermission}
          />{" "}
        </div>
      </section>

      <PromotionModal
        ref={promotionModalRef}
        onClose={function (): void {
          throw new Error("Function not implemented.");
        }}
        onSubmitOk={fetchPromotion}
      // hasAddIndustryPermission={hasIndustryAddPermission}
      // hasUpdateIndustryPermission={hasIndustryUpdatePermission}
      />
    </div>
  );
};
