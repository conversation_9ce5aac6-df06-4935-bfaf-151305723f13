import {
  App<PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Button,
  IconButton,
  Stack,
  Tool<PERSON>,
  Typo<PERSON>,
  styled,
  useMediaQuery,
} from "@mui/material";

import {
  IconMenu2,
  IconMoon,
  IconScriptPlus,
  IconSun,
} from "@tabler/icons-react";
import { observer } from "mobx-react";
import { appStore } from "store/appStore";
// import Notifications from "./Notification";
// import Profile from "./Profile";
import Language from "components/Common/Language";
import Profile from "components/Common/Profile";
import { useEffect, useMemo, useState } from "react";
import { adminRoutes, Route } from "router";
import { useTranslation } from "react-i18next";
import { settings } from "settings";
import clsx from "clsx";
import { useNavigate } from "react-router-dom";
import { IoIosArrowForward } from "react-icons/io";

const Header = () => {
  const lgUp = useMediaQuery((theme: any) => theme.breakpoints.up("lg"));
  const lgDown = useMediaQuery((theme: any) => theme.breakpoints.down("lg"));
  const { t } = useTranslation();
  const navigate = useNavigate();

  // drawer
  const customizer = appStore.customizer;

  const AppBarStyled = useMemo(
    () =>
      styled(AppBar)(({ theme }) => ({
        boxShadow: "none",
        borderBottom: "1px solid #EAEFF4",
        background: theme.palette.background.paper,
        justifyContent: "center",
        backdropFilter: "blur(4px)",
        [theme.breakpoints.up("lg")]: {
          minHeight: customizer.TopbarHeight,
        },
      })),
    [customizer]
  );
  const ToolbarStyled = useMemo(
    () =>
      styled(Toolbar)(({ theme }) => ({
        width: "100%",
        color: theme.palette.text.secondary,
      })),
    [customizer]
  );

  const [breadcrumbs, setBreadcrumbs] = useState<string[]>([]);

  useEffect(() => {
    const paths = location.pathname.split("/").filter(Boolean);
    adminRoutes.forEach((router) => {
      if (router.path == location.pathname) {
        return handleSetBreadcrumbs(router);
      } else if (router.children?.length) {
        if (paths[0] === router.path?.replace("/", "")) {
          const findChild = { ...router };
          let show = false;

          for (let i = 1; i < paths.length; i++) {
            const children = router.children?.find(
              (child) => child.path === paths[i]
            );
            if (children) {
              findChild.breadcrumb += "/" + children.breadcrumb;
              show = true;
            }
          }
          if (show) {
            return handleSetBreadcrumbs(findChild);
          }
        }
      }
    });
  }, [location.pathname]);

  const handleSetBreadcrumbs = (data: Route) => {
    if (data) {
      if (data.breadcrumb) {
        setBreadcrumbs(data.breadcrumb.split("/"));
      } else if (data.title) {
        setBreadcrumbs([data.title]);
      }
    }
  };

  return (
    <div
      className={clsx(
        "sticky flex justify-between backdrop:blur-[4px] bg-white",
        lgDown ? "px-[14.5px] py-[24px]" : "gap-[40px] px-[24px] py-[24px]"
      )}
      style={{
        borderColor: "var(--gray-4-color)",
        borderBottomWidth: 1,
        borderBottomStyle: "solid",
      }}
    >
      <div className="flex items-center flex-1 gap-2">
        {/* ------------------------------------------- */}
        {/* Toggle Button Sidebar */}
        {/* ------------------------------------------- */}
        <IconButton
          sx={{
            padding: 0,
            width: 24,
            height: 24,
            flexShrink: 0,
          }}
          color="inherit"
          aria-label="menu"
          onClick={
            lgUp
              ? () => appStore.toggleSidebar()
              : () => appStore.toggleMobileSidebar()
          }
        >
          <div className="flex md:hidden">
            <IconMenu2 size={24} />
          </div>

          <div className="hidden md:flex">
            <IconMenu2 size={24} />
          </div>
        </IconButton>
        {/* ------------------------------------------- */}
        {/* Breadcrumbs */}
        <Breadcrumbs
          separator={<IoIosArrowForward />}
          className={lgDown ? "mobile-breadcrumb-none" : ""}
        >
          {breadcrumbs.map((item, i) => (
            <span key={i} className="text-caption-caps-semibold">
              {t(item).toUpperCase()}
            </span>
          ))}
        </Breadcrumbs>

        {/* <Button
          variant="contained"
          className={clsx(
            "sm:!ml-4 ",
            lgDown && "[&_.MuiButton-icon]:!mr-0 !min-w-[0px]"
          )}
          onClick={() => {
            navigate("/project/project-create");
          }}
          startIcon={<IconScriptPlus className="w-[21px] h-[21px]" />}
        >
          {!lgDown && t("projectCreate")}
        </Button> */}
        {/* ------------------------------------------- */}
        {/* Search Dropdown */}
        {/* ------------------------------------------- */}
        {/* <Search /> */}
        {/* {lgUp ? (
          <>
            <Navigation />
          </>
        ) : null} */}
      </div>
      <div
        className={clsx(
          "flex items-center",
          lgDown ? "gap-[4px]" : "gap-[12px]"
        )}
      >
        {/* <label
            style={{
              paddingLeft: "10px",
            }}
            htmlFor=""
          >
            v{settings.version}
          </label> */}
        {/* <Language /> */}
        {/* ------------------------------------------- */}
        {/* Ecommerce Dropdown */}
        {/* ------------------------------------------- */}
        {/* <Cart /> */}
        {/* ------------------------------------------- */}
        {/* End Ecommerce Dropdown */}
        {/* ------------------------------------------- */}
        {/* <IconButton size="large" color="inherit">
            {customizer.activeMode === "light" ? (
              <IconMoon
                size="21"
                stroke="1.5"
                onClick={() => appStore.setDarkMode("dark")}
              />
            ) : (
              <IconSun
                size="21"
                stroke="1.5"
                onClick={() => appStore.setDarkMode("light")}
              />
            )}
          </IconButton> */}
        {/* <Notifications /> */}
        {/* ------------------------------------------- */}
        {/* Toggle Right Sidebar for mobile */}
        {/* ------------------------------------------- */}
        {/* {lgDown ? <MobileRightSidebar /> : null} */}
        <Profile />
      </div>
    </div>
  );
};

export default observer(Header);
