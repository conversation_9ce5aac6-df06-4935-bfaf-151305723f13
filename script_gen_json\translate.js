const { GoogleSpreadsheet } = require("google-spreadsheet");
const secret = require("./secret.json");
const fs = require("fs");
const util = require("util");

// Initialize the sheet
const doc = new GoogleSpreadsheet(
  "1_zdoxq2hnX3Zbwu7RtlKyDkGVKDMuQ3bZM-mOoDf1Mk"
);

// Initialize Auth
const init = async () => {
  await doc.useServiceAccountAuth({
    client_email: secret.client_email, //don't forget to share the Google sheet with your service account using your client_email value
    private_key: secret.private_key,
  });
};

const read = async () => {
  await doc.loadInfo(); // loads document properties and worksheets
  const sheet = doc.sheetsByTitle["Admin"]; //get the sheet by title, I left the default title name. If you changed it, then you should use the name of your sheet
  await sheet.loadHeaderRow(2); //Loads the header row (first row) of the sheet
  const colTitles = sheet.headerValues; //array of strings from cell values in the first row
  const rows = await sheet.getRows({ limit: sheet.rowCount }); //fetch rows from the sheet (limited to row count)

  let result = {};
  let key2 = "";
  //map rows values and create an object with keys as columns titles starting from the second column (languages names) and values as an object with key value pairs, where the key is a key of translation, and value is a translation in a respective language
  rows.map((row) => {
    colTitles.slice(1).forEach((title) => {
      result[title] = result[title] || [];
      const key = row[colTitles[0]];
      if (key && !row[colTitles[1]]) {
        key2 = key.split("-").pop().trim();
        result[title][key2] = result[title][key2] || [];
      }
      if (key2 === "") {
        result[title][key] = row[title] !== "" ? row[title] : undefined;
      }
      result = {
        ...result,
        [title]: {
          ...result[title],
          // [key]: row[title] !== "" && key2 === "" ? row[title] : undefined,
          [key2]:
            key2 !== ""
              ? {
                  ...result[title][key2],
                  [key]: row[title] !== "" ? row[title] : undefined,
                }
              : undefined,
        },
      };
    });
  });

  return result;
};

const write = (data) => {
  Object.keys(data).forEach((key) => {
    fs.writeFile(
      `../src/translations/${key}.json`,
      JSON.stringify(data[key], null, 2),
      (err) => {
        if (err) {
          console.error(err);
        }
      }
    );
  });
};

init()
  .then(() => read())
  .then((data) => write(data))
  .catch((err) => {});
