import {
  DownloadOutlined,
  DownOutlined,
  ExportOutlined,
  SearchOutlined,
} from "@ant-design/icons";
import {
  Button,
  Dropdown,
  Input,
  message,
  Popconfirm,
  Space,
  Table,
  Tag,
} from "antd";
import TextArea from "antd/es/input/TextArea";
import { withdrawApi } from "api/withdraw.api";
import dayjs from "dayjs";
import { useHandleCustomer } from "hooks/useHandleCustomer";
import { debounce } from "lodash";
import React, { useEffect, useMemo, useRef, useState } from "react";
import { Withdraw, WithdrawStatus, WithdrawStatusTrans } from "types/withdraw";
import { formatVND, getTitle } from "utils";
import { unixToFullDate } from "utils/dateFormat";
import { MyTableColumn } from "utils/excel";
import {
  getExportData,
  handleExport,
  MyExcelColumn,
} from "../../utils/MyExcel";
import { Pagination } from "components/Pagination";
import { WithdrawModal } from "./WithdrawModal";
import { useWithdraw } from "hooks/useWithdraw";
import { useTranslation } from "react-i18next";
import { WithdrawList } from "./WithdrawList";
import { TextField } from "@mui/material";
import { ReactComponent as DownloadIcon } from "assets/svgs/download.svg";
import "./WithdrawTab.scss";
const { ColumnGroup, Column } = Table;
interface PropTypes {
  status: WithdrawStatus;
  title?: string;
  isAgent?: boolean;
  isFocus?: boolean;
  parentLastUpdate?: number;
  onSubmitOk?: () => void;
}
export const WithdrawTab = React.memo(
  ({
    title = "",
    status,
    isAgent,
    isFocus,
    parentLastUpdate,
    onSubmitOk,
  }: PropTypes) => {
    const [note, setNote] = useState<string>();
    const [loadingConfirmWithdraw, setLoadingConfirmWithdraw] = useState(false);
    const [loadingRejectWithdraw, setLoadingRejectWithdraw] = useState(false);
    const [selectedCustomerId, setSelectedCustomerId] = useState<number>();
    const [lastUpdate, setLastUpdate] = useState<number>(0);
    const { t } = useTranslation();
    const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
    const [withdrawStatus, setWithdrawStatus] = useState<WithdrawStatus>();
    const onSelectChange = (newSelectedRowKeys: React.Key[]) => {
      setSelectedRowKeys(newSelectedRowKeys);
    };
    const rowSelection = {
      selectedRowKeys,
      onChange: onSelectChange,
      getCheckboxProps: (record: Withdraw) => {
        let disabled = true;

        if (
          record.status === WithdrawStatus.Pending ||
          record.status === WithdrawStatus.Processing
        ) {
          disabled = false;
        } else {
          disabled = true;
        }

        return {
          disabled,
        };
      },
      // getCheckboxProps: (record: FreeStock) => ({}),
      preserveSelectedRowKeys: true,
      // getCheckboxProps: (record) => {
      //   console.log(record)
      // }
    };
    console.log({ childLastUpdate: lastUpdate, status });
    const [visible, setVisible] = useState(false);

    const withdrawModalRef = useRef<WithdrawModal>(null);
    const { withdraws, fetchData, loading, query, total } = useWithdraw({
      initQuery: {
        page: 1,
        limit: 10,
        status: status === WithdrawStatus.All ? undefined : status,
      },
    });
    const exportColumns: MyExcelColumn<Withdraw>[] = [
      {
        width: 15,
        header: t("code"),
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "code",
        render: (record: Withdraw) => {
          return record.code || t("notUpdate");
        },
      },

      {
        width: 30,
        header: t("phoneNumber"),
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "phone",
        // style: { font: { color: { argb: "004e47cc" } } },
        render: (record: Withdraw) => {
          return record.partner?.phone || t("notUpdate");
        },
      },
      {
        width: 30,
        header: t("partner"),
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "name",
        // style: { font: { color: { argb: "004e47cc" } } },
        render: (record: Withdraw) => {
          return record.partner?.fullName || t("notUpdate");
        },
      },
      {
        width: 20,
        header: t("amountRequest"),
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "amount",
        render: (record: Withdraw) => {
          return formatVND(record.amount);
        },
      },

      {
        width: 20,
        header: t("bankAbbName"),
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "bankName",
        render: (record: Withdraw) =>
          record.bankName ?? (record.partner as any)?.bankName,
      },
      {
        width: 20,
        header: t("bankFullName"),
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "bankFullName",
        render: (record: Withdraw) =>
          record?.bank?.fullName ?? (record.partner as any)?.bankFullName,
      },
      {
        width: 20,
        header: t("bankNumber"),
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "bankAccountNumber",
        render: (record: Withdraw) => record?.partner?.bankAccountNumber,
      },
      {
        width: 20,
        header: t("bankAccountName"),
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "bankInfo",
        render: (record: Withdraw) => record?.partner?.bankAccountName,
      },
      {
        width: 30,

        header: t("note"),
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "note",
        render: (record: Withdraw) => t("withdraw" + record.status + "Note"),
      },
      {
        width: 20,
        header: t("inspector"),
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "inspector",
        render: (record: Withdraw) => {
          return record.status === WithdrawStatus.Pending
            ? ""
            : record?.inspecStaff
              ? record?.inspecStaff.username +
              " - " +
              record?.inspecStaff.fullName
              : "";
        },
      },
      {
        width: 20,
        header: t("inspectAt"),
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "inspectAt",
        render: (record: Withdraw) => {
          return unixToFullDate(record.inspecAt);
        },
      },
      {
        width: 30,
        header: t("status"),
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "status",
        render: (record: Withdraw) => {
          return t("withdraw" + record.status);
        },
      },
    ];
    useEffect(() => {
      // document.title = getTitle(title);
      fetchData();
    }, [query]);

    useEffect(() => {
      if (isFocus && parentLastUpdate != lastUpdate) {
        fetchData();
        if (parentLastUpdate) setLastUpdate(parentLastUpdate);
      }
    }, [parentLastUpdate, lastUpdate, isFocus]);

    const handleConfirmWithdraw = async (WithdrawId: number) => {
      try {
        setLoadingConfirmWithdraw(true);
        const res = await withdrawApi.approve(WithdrawId, {
          note: note,
        });
        message.success(t("actionSuccessfully"));
        setNote("");
        fetchData();
        onSubmitOk?.();
      } catch (error) {
      } finally {
        setLoadingConfirmWithdraw(false);
      }
    };
    const handleRejectWithdraw = async (withdrawId: number) => {
      try {
        setLoadingRejectWithdraw(true);
        const res = await withdrawApi.reject(withdrawId, {
          note: note,
        });
        message.success(t("actionSuccessfully"));
        setNote("");
        fetchData();
        onSubmitOk?.();
      } catch (error) {
      } finally {
        setLoadingRejectWithdraw(false);
      }
    };
    const debounceSearch = debounce((search) => {
      query.search = search;
      query.page = 1;
      fetchData();
    }, 500);

    const handleMenuClick = (e: any) => {
      setWithdrawStatus(e);
      setVisible(true);
    };

    const menuProps = {
      items: [
        {
          label: t("approveWithdraw"),
          key: WithdrawStatus.Processing,
          disabled:
            status !== WithdrawStatus.Pending && status !== WithdrawStatus.All,
        },
        {
          label: t("completeRequest"),
          key: WithdrawStatus.Approve,
          disabled:
            status !== WithdrawStatus.Processing &&
            status !== WithdrawStatus.All,
        },

        { label: t("reject"), key: WithdrawStatus.Reject },
      ],
      onClick: handleMenuClick,
    };
    const handleCancel = () => {
      setVisible(false);
      setNote("");
    };

    const changeStatusAll = async (
      selectedRowKeys: React.Key[],
      note: string | undefined,
      status: any
    ) => {
      try {
        console.log({ status });
        if (status.key === WithdrawStatus.Approve) {
          await withdrawApi.approveAll({
            inspecNote: note,
            withdrawIds: selectedRowKeys.map((id) => id),
          });
        } else if (status.key === WithdrawStatus.Processing) {
          await withdrawApi.processingAll({
            inspecNote: note,
            withdrawIds: selectedRowKeys.map((id) => id),
          });
        } else if (status.key === WithdrawStatus.Reject) {
          await withdrawApi.rejectAll({
            inspecNote: note,
            withdrawIds: selectedRowKeys.map((id) => id),
          });
        }

        // Chờ tất cả các Promise hoàn thành
        message.success(t("actionSuccessfully"));
        setNote("");
        fetchData();
        onSubmitOk?.();
        setSelectedRowKeys([]);
        setVisible(false);
      } catch (error) {
        setNote("");
        setSelectedRowKeys([]);
      }
    };

    return (
      <div>
        <div className="filter-container withdraw-filter">
          <Space>
            <div className="filter-item ">
              {/* <TextField
                size="small"
                onChange={(ev) => {
                  const value = ev.currentTarget.value;
                  if (value) {
                    query.page = 1;
                    query.search = value;
                  } else {
                    query.search = undefined;
                  }
                }}
                label={t("search")}
              /> */}
              <label htmlFor="">{t("searchPartnerPlaceholder")}</label>

              <Input.Search
                size="large"
                enterButton={<SearchOutlined onClick={() => fetchData()} />}
                className="w-full search-btn"
                allowClear
                onClear={() => {
                  query.search = "";
                  query.page = 1;
                  fetchData();
                }}
                onKeyDown={(ev) => {
                  if (ev.code === "Enter") {
                    query.page = 1;
                    fetchData();
                  }
                }}
                onChange={(ev) => {
                  query.search = ev.currentTarget.value;
                  query.page = 1;
                }}
              />
            </div>

            {/* <div className="filter-item btn">
              <Button
                className=""
                type="primary"
                onClick={() => {
                  withdrawModalRef.current?.handleCreate();
                }}
              >
                {t("withdrawMoney")}
              </Button>
            </div>
            <div className="filter-item btn">
              <Button
                onClick={fetchData}
                type="primary"
                icon={<SearchOutlined />}
              >
                {t("search")}
              </Button>
            </div> */}
            <div className="filter-item btn ">
              <Dropdown
                menu={menuProps}
                disabled={
                  !(
                    selectedRowKeys.length > 0 ||
                    query.status === WithdrawStatus.Pending
                  )
                }
              >
                <Button
                  className="text-regular btn-batch-action"
                  type="primary"
                >
                  <Space>{t("batchAction")}</Space>
                </Button>
              </Dropdown>
              <Popconfirm
                title={
                  <>
                    <h3>{t("confirm?")}</h3>
                    <TextArea
                      placeholder={t("enterNote")}
                      value={note}
                      onChange={(e) => setNote(e.target.value)}
                    ></TextArea>
                  </>
                }
                visible={visible}
                onConfirm={() =>
                  changeStatusAll(selectedRowKeys, note, withdrawStatus)
                }
                onCancel={handleCancel}
                okText={t("save")}
                cancelText={t("close")}
              />
            </div>

            {(status === WithdrawStatus.All ||
              status === WithdrawStatus.Processing) && (
                <div className="filter-item btn">
                  <Popconfirm
                    title={t("exportAsk")}
                    onConfirm={() =>
                      handleExport({
                        onProgress(percent) { },
                        exportColumns,
                        fileType: "xlsx",
                        dataField: "withdraws",
                        query: query,
                        api: withdrawApi.findAll,
                        params:
                          status === WithdrawStatus.All
                            ? undefined
                            : { status: status },
                        fileName: t("withdrawOrderList"),
                        sheetName: t("withdrawOrderList"),
                      })
                    }
                    okText={t("exportExcel")}
                    cancelText={t("cancel")}
                  >
                    <Button
                      className="text-regular"
                      size="large"
                      type="primary"
                      loading={false}
                      icon={<DownloadIcon />}
                      danger={status === WithdrawStatus.Processing}
                      style={
                        status === WithdrawStatus.Processing
                          ? { background: "#EB9113", borderColor: "#EB9113" }
                          : undefined
                      }
                    >
                      {t("exportExcel")}
                    </Button>
                  </Popconfirm>
                </div>
              )}
          </Space>
        </div>

        <WithdrawList
          //   onEdit={(record) =>
          //     customerWithdrawModalRef.current?.handleUpdate(record)
          //   }
          rowSelection={rowSelection}
          onRefreshData={() => {
            fetchData();
            onSubmitOk?.();
          }}
          dataSource={withdraws}
          loading={loadingConfirmWithdraw}
          //   loadingDelete={loadingDelete}
          pagination={{
            total: total,
            defaultPageSize: query.limit,
            currentPage: query.page,
            onChange: ({ page, limit }) => {
              Object.assign(query, {
                page,
                limit,
              });
              fetchData();
            },
          }}
          status={status}
        />

        <WithdrawModal
          ref={withdrawModalRef}
          onClose={function (): void { }}
          onSubmitOk={function (): void {
            fetchData();
            onSubmitOk?.();
          }}
        />
      </div>
    );
  }
);
