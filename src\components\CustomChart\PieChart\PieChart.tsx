"use client";
import * as React from "react";
import { Pie<PERSON><PERSON> } from "@mui/x-charts/PieChart";
import { Box, useMediaQuery, useTheme } from "@mui/material";
import {
  AnchorPosition,
  Direction,
} from "@mui/x-charts/ChartsLegend/legend.types";
import { useTranslation } from "react-i18next";

interface PieProps {
  data: any[];
  type?: any;
}

export default function BasicPieChart({ data, type }: PieProps) {
  const theme = useTheme();

  // 🎨 Map fullname → mã màu thực tế
  const colorMap: Record<string, string> = {
    Bạc: theme.palette.primary.main,
    Silver: theme.palette.primary.main,
    Bronze: theme.palette.secondary.main,
    Đồng: theme.palette.secondary.main,
    "Thành viên mới": theme.palette.error.main,
    "New member": theme.palette.error.main,
  };
  const { t } = useTranslation();
  const transformData = () => {
    if (!Array.isArray(data) || data.length === 0) return [];

    return data.map((item, index) => ({
      id: index,
      value: item.total,
      label: item.fullname || "Chưa có hạng",
      color: colorMap[item.fullname] || "#ccc",
    }));
  };
  type PieDataItem = {
    id: number;
    value: number;
    label: string;
    color: string;
  };
  // const transformWithdrawToPieData = (data: any): PieDataItem[] => {
  //   return [
  //     {
  //       id: 0,
  //       value: Number(data?.approve?.replace(/\s/g, "")) || 0,
  //       label: "Đã duyệt",
  //       color: theme.palette.success.main,
  //     },
  //     {
  //       id: 1,
  //       value: Number(data?.pending?.replace(/\s/g, "")) || 0,
  //       label: "Đang chờ",
  //       color: theme.palette.warning.main,
  //     },
  //     {
  //       id: 2,
  //       value: Number(data?.reject?.replace(/\s/g, "")) || 0,
  //       label: "Từ chối",
  //       color: theme.palette.error.main,
  //     },
  //   ];
  // };
  const handleReturnDataByType = (): PieDataItem[] => {
    switch (type) {
      case "NUMBER_OF_PARTNER":
        return transformData();
        break;
      case "PRODUCT_PAYMENT_IN":
        return [];
        break;
      case "WITHDRAW":
        return [];
      default:
        return [];
        break;
    }
  };
  const isMobile = useMediaQuery(theme.breakpoints.down("md")); // sm: < 600px
  return (
    <Box
      width="100%"
      display="flex"
      justifyContent="center"
      alignItems="center" // Nếu cần căn giữa cả chiều dọc
    >
      <PieChart
        width={isMobile ? 300 : 500}
        height={300}
        series={[
          {
            data: handleReturnDataByType(),
            innerRadius: isMobile ? 20 : 40,
            outerRadius: isMobile ? 60 : 80,
            paddingAngle: 5,
            cornerRadius: 4,
          },
        ]}
        slotProps={{
          legend: {
            // position: isMobile ? ("bottom" as const) : "right",
            // direction: isMobile ? ("row" as const) : "column",
            itemMarkWidth: 18,
            itemMarkHeight: 18,
            markGap: 8,
            itemGap: 12,
            labelStyle: {
              fontSize: 14,
            },
          },
        }}
      />
    </Box>
  );
}
