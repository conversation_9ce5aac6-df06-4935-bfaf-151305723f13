import { useCallback, useEffect, useMemo, useState } from "react";
import { Layout, Spin } from "antd";
import "./styles/AdminLayout.scss";
import { Outlet, NavLink } from "react-router-dom";
import { AuthProvider } from "provider/AuthProvider";
import { Navbar } from "./components/Navbar";
import { AppSuspense } from "components/App/AppSuspense";
import { AppLogo } from "components/App/AppLogo";
import { Box, Container, styled, useTheme } from "@mui/material";
import { appStore } from "store/appStore";
import { observer } from "mobx-react";
import ScrollToTop from "components/Common/ScrollToTop";
import { Sidebar } from "./components/Sidebar";
import Header from "./components/Header";
import AppContainer from "./components/AppContainer";

const MainWrapper = styled("div")(() => ({
  display: "flex",
  minHeight: "100vh",
  width: "100%",
}));

const PageWrapper = styled("div")(() => ({
  display: "flex",
  flexGrow: 1,
  flexDirection: "column",
  zIndex: 1,
  width: "0px",
  backgroundColor: "transparent",
}));

const StyledContainer = styled(Container)(({ theme }) => ({
  maxWidth: "1127px !important",
  margin: "0 auto",
  width: "100%",
}));

export const AdminLayout = observer(() => {
  const customizer = appStore.customizer;
  const theme = useTheme();

  const [collapsed, setCollapsed] = useState(false);
  const [siteLayoutMarginLeft, setSiteLayoutMarginLeft] = useState(270);
  const [selectedKey, setSelectedKey] = useState("1");

  const toggle = useCallback(() => {
    setCollapsed((prev) => !prev);
  }, []);

  useEffect(() => {
    if (collapsed) {
      setSiteLayoutMarginLeft(80);
    } else {
      setSiteLayoutMarginLeft(270);
    }
  }, [collapsed]);

  useEffect(() => {}, []);

  const noMaxWidth = useMemo(() => {
    if (location.pathname.includes("project-create")) return true;
    if (location.pathname.includes("project-list")) return true;
    return false;
  }, [location.pathname]);

  return (
    <AppSuspense>
      <AuthProvider>
        <MainWrapper
          className={
            customizer.activeMode === "dark"
              ? "darkbg mainwrapper"
              : "mainwrapper"
          }
        >
          {/* ------------------------------------------- */}
          {/* Sidebar */}
          {/* ------------------------------------------- */}
          <Sidebar />
          {/* ------------------------------------------- */}
          {/* Main Wrapper */}
          {/* ------------------------------------------- */}
          <PageWrapper
            className="page-wrapper"
            sx={{
              ...(customizer.isCollapse
                ? {
                    [theme.breakpoints.up("lg")]: {
                      ml: `${customizer.MiniSidebarWidth}px`,
                    },
                  }
                : {
                    [theme.breakpoints.up("lg")]: {
                      ml: `0px`,
                    },
                  }),
            }}
          >
            {/* ------------------------------------------- */}
            {/* Header on top */}
            {/* ------------------------------------------- */}
            <Header />
            <StyledContainer maxWidth={false}>
              {/* ------------------------------------------- */}
              {/* PageContent */}
              {/* ------------------------------------------- */}

              <Box sx={{ minHeight: "calc(100vh - 170px)" }}>
                <ScrollToTop>
                  <Outlet />
                </ScrollToTop>
              </Box>

              {/* ------------------------------------------- */}
              {/* End Page */}
              {/* ------------------------------------------- */}
            </StyledContainer>
            {/* <Customizer /> */}
          </PageWrapper>
        </MainWrapper>
        {/* <Layout>
          <Sidebar collapsed={collapsed} />

          <Layout
            className="site-layout"
            style={{ marginLeft: siteLayoutMarginLeft }}
          >
            <Navbar collapsed={collapsed} toggle={toggle} />
            <Content
              className="site-layout-background"
              style={{
                margin: "0px",
                padding: 12,
              }}
            >
              <Outlet />
            </Content>
          </Layout>
        </Layout> */}
      </AuthProvider>
    </AppSuspense>
  );
});
