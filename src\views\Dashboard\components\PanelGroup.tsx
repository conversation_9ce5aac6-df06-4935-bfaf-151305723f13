import { dashboardApi } from "api/dashboard.api";
import dayjs from "dayjs";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import DashboardInfoCard from "./DashboardInfoCard";
import totalProject from "../../../assets/images/totalProject.svg";
import unpaidProject from "../../../assets/images/unpaid.svg";
import processingProject from "../../../assets/images/processing.svg";
import stoppingProject from "../../../assets/images/stopping.svg";
import completedProject from "../../../assets/images/completed.svg";
import { PaymentStatus } from "types/payment";
import { ProjectStatus } from "types/project";

scroll;

interface Props {
  fetchByStatus?: (value?: ProjectStatus) => void;
  fetchByPaymentStatus?: (value?: PaymentStatus) => void;
}

export const PanelGroup = ({ fetchByStatus, fetchByPaymentStatus }: Props) => {
  const navigate = useNavigate();
  const { t } = useTranslation();

  const [data, setData] = useState<any>({});
  const [endDate, setEndDate] = useState(dayjs().endOf("day"));
  const [startDate, setStartDate] = useState(
    dayjs().subtract(1, "month").startOf("day")
  );
  const [summaryProject, setSummaryProject] = useState<any>();
  const [projectAndBalance, setProjectAndBalance] = useState<any>();
  const query = {
    fromAt: startDate.startOf("day").unix(),
    toAt: endDate.endOf("day").unix(),
  };

  const fetchDashboardSummary = async () => {
    const res = await dashboardApi.summary(query);
    setData(res.data);
  };
  const fetchSummaryProjectAndBalance = async () => {
    const res = await dashboardApi.summaryProjectAndBalance();
    setProjectAndBalance(res.data);
  };
  const handleFetchProject = async () => {
    try {
      const res = await dashboardApi.summaryProjectV2();
      console.log("res là", res.data);
      setSummaryProject(res.data);
    } catch (error) {
      console.log("Status project in dashboard", error);
    }
  };
  useEffect(() => {
    // fetchDashboardSummary();
    handleFetchProject();
    fetchSummaryProjectAndBalance();
  }, []);
  console.log("Project and balance", projectAndBalance);
  return (
    <div>
      <div className="card-panel">
        <div className="card-panel-description">
          <div>
            {/* <h2 className="my-0">{t("PROJECT")}</h2> */}
            <div className="grid grid-cols-2 lg:grid-cols-5 gap-[16px] lg:gap-[24px]">
              <div className="col-span-2 order-1 lg:order-none lg:col-span-1">
                <DashboardInfoCard
                  link="/project/project-list"
                  bgColor="#EFF5FF"
                  textColor="#1A73E8"
                  info={summaryProject?.total}
                  title={t("totalProject")}
                  image={totalProject}
                  onClick={() => fetchByStatus?.()}
                />
              </div>

              <div className="order-3 lg:order-none">
                <DashboardInfoCard
                  link="/project/project-list"
                  bgColor="#EAEFF4"
                  textColor="#5A6A85"
                  info={summaryProject?.totalPaymentPending || 0}
                  title={t("unpaid")}
                  image={unpaidProject}
                  onClick={() => fetchByPaymentStatus?.(PaymentStatus.Pending)}
                />
              </div>

              <div className="order-2 lg:order-none">
                <DashboardInfoCard
                  link="/project/project-list"
                  bgColor="#FFF8E3"
                  textColor="#F8C324"
                  info={summaryProject?.totalProcessing || 0}
                  title={t("onProcess")}
                  image={processingProject}
                  onClick={() => fetchByStatus?.(ProjectStatus.Processing)}
                />
              </div>

              <div className="order-4 lg:order-none">
                <DashboardInfoCard
                  link="/project/project-list"
                  bgColor="#FFEBEA"
                  textColor="#ED2012"
                  info={summaryProject?.totalPaused || 0}
                  title={t("paused")}
                  image={stoppingProject}
                  onClick={() => fetchByStatus?.(ProjectStatus.Paused)}
                />
              </div>

              <div className="order-5 lg:order-none">
                <DashboardInfoCard
                  link="/project/project-list"
                  bgColor="#E9FFEE"
                  textColor="#41D664"
                  info={summaryProject?.totalCompleted || 0}
                  title={t("completed")}
                  image={completedProject}
                  onClick={() => fetchByStatus?.(ProjectStatus.Completed)}
                />
              </div>
            </div>
          </div>
          {/* <div>
            <h2>{t("myWallet")} (VND)</h2>
            <Grid container spacing={3} textAlign="center">
              <Grid
                size={{
                  lg: 6,
                  sm: 4,
                  xs: 12,
                }}
              >
                <div
                  className="rounded-lg p-[30px] transition ease-in duration-100 text-inherit bg-[#ECF2FF] !text-[#5D87FF] cursor-pointer"
                  onClick={() => {
                    navigate("/wallet/history");
                  }}
                >
                  <Typography variant="h3">
                    {formatVND(projectAndBalance?.balance || 0)}
                  </Typography>
                  <Typography variant="h6">{t("BALANCE")}</Typography>
                </div>
              </Grid>
              <Grid
                size={{
                  lg: 6,
                  sm: 4,
                  xs: 4,
                }}
              >
                <div
                  className="rounded-lg p-[30px] transition ease-in duration-100 text-inherit bg-[#FEF5E5] !text-[#FFAE1F] cursor-pointer"
                  onClick={() => {
                    navigate("/wallet/history");
                  }}
                >
                  <Typography variant="h3">
                    {formatVND(projectAndBalance?.moneyUsed?.moneyToUse || 0)}
                  </Typography>
                  <Typography variant="h6">{t("usedMoney")}</Typography>
                </div>
              </Grid>
            </Grid>
          </div> */}
        </div>
      </div>
    </div>
  );
};
