import { Tag } from "antd";
import { useTranslation } from "react-i18next";
import "./styles/ReviewStatus.scss";
import clsx from "clsx";
import { ReviewStatus, ReviewStatusTrans } from "types/review";

interface Props {
  status: ReviewStatus;
}

export const ReviewStatusComp = ({ status }: Props) => {
  const { t } = useTranslation();

  return (
    <Tag
      className={clsx("review-status", status)}
      color={ReviewStatusTrans[status as keyof typeof ReviewStatusTrans]?.color}
    >
      {t(`review${status}`)}
    </Tag>
  );
};
