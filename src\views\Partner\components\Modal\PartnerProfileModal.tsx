import {
  Button,
  Col,
  Form,
  Image,
  Input,
  message,
  Modal,
  Popconfirm,
  Row,
  Space,
  Tabs,
} from "antd";
import { useForm } from "antd/lib/form/Form";
import { partnerApi } from "api/partner.api";
import { forwardRef, useImperativeHandle, useState } from "react";
import { Partner } from "types/partner";
import { ModalStatus } from "types/modal";
import { SurveyCampaign } from "types/survey";
import {
  emailRule,
  phoneNumberRule,
  requiredRule,
  rules,
} from "utils/validate-rules";
import { InputNumber } from "components/Input/InputNumber";
import { useTranslation } from "react-i18next";
import MissionHistoryTab from "../Tab/MissionHistoryTab";
import PartnerTransactionTab from "../Tab/PartnerTransactionTab";
import { UploadFile } from "antd/lib";
import { FileUpload } from "components/Upload/FileUpload";

enum ActiveKey {
  Info = "INFO",
  Order = "ORDER",
  Transaction = "TRANSACTION",
}

export interface PartnerProfileModalProps {
  onClose: () => void;
  onSubmitOk?: () => void;
  onVerify?: (partnerId: number) => void;
  onUnverify?: (partnerId: number) => void;
  // hasAddPartnerPermission?: boolean;
  // hasUpdatePartnerPermission?: boolean;
}

export interface PartnerProfileModalRef {
  handleUpdate: (partner: Partner) => void;
  handleCreate: () => void;
}

export const PartnerProfileModal = forwardRef(
  (
    {
      onSubmitOk,
      onUnverify,
      onVerify,
    }: // hasAddPartnerPermission,
    // hasUpdatePartnerPermission,
    PartnerProfileModalProps,
    ref
  ) => {
    const [form] = useForm();
    const [loading, setLoading] = useState(false);
    const [visible, setVisible] = useState<boolean>();
    const { t } = useTranslation();

    const [selectedPartner, setSelectedPartner] = useState<Partner>();
    const [status, setStatus] = useState<ModalStatus>("create");
    const [docFile, setDocFile] = useState<UploadFile[]>([]);

    useImperativeHandle(
      ref,
      () => ({
        handleUpdate,
        handleCreate,
      }),
      []
    );

    const handleUpdate = (partner: Partner) => {
      form.setFieldsValue({
        ...partner,
      });
      setDocFile([
        {
          uid: partner.contract || "",
          name: partner.contract || "",
        },
      ]);
      setSelectedPartner(partner);
      setStatus("update");
      setVisible(true);
    };

    const handleCreate = () => {
      setStatus("create");
      setVisible(true);
    };

    const handleSubmitForm = async () => {
      await form.validateFields();
      const dataForm = form.getFieldsValue();
      if (dataForm.name) {
        dataForm.name = dataForm.name.trim();
      }
      const payload = {
        partner: {
          ...dataForm,
        },
      };

      try {
        setLoading(true);
        switch (status) {
          case "update":
            await partnerApi.update(selectedPartner?.id || 0, payload);
            message.success(t("actionSuccessfully"));
            break;

          //create
          default:
            await partnerApi.create(payload);
            message.success(t("actionSuccessfully"));
            break;
        }
        setVisible(false);
        onSubmitOk?.();
      } catch (error) {
      } finally {
        setLoading(false);
        // onSubmitOk();
      }
    };

    return (
      <Modal
        onCancel={() => {
          setVisible(false);
        }}
        visible={visible}
        centered
        title={
          <h1 className="mb-0 text-lg text-primary font-bold">
            {t("basicInfo")}
          </h1>
        }
        confirmLoading={loading}
        destroyOnClose
        width={1100}
        onOk={handleSubmitForm}
        afterClose={() => {
          form.resetFields();
        }}
        okText={t("save")}
        cancelText={t("close")}
        footer={
          <>
            <Button onClick={() => setVisible(false)}>{t("close")}</Button>
            <Popconfirm
              title={t("confirm?")}
              onConfirm={() => {
                if (selectedPartner) onUnverify?.(selectedPartner?.id);
                setVisible(false);
              }}
              okText={t("save")}
              cancelText={t("close")}
            >
              <Button>{t("reject")}</Button>
            </Popconfirm>

            <Popconfirm
              title={t("confirm?")}
              onConfirm={() => {
                if (selectedPartner) onVerify?.(selectedPartner?.id);
                setVisible(false);
              }}
              okText={t("save")}
              cancelText={t("close")}
            >
              <Button type="primary" loading={loading}>
                {t("approve")}
              </Button>
            </Popconfirm>
          </>
        }
      >
        <Form
          form={form}
          layout="vertical"
          validateTrigger={["onBlur", "onChange"]}
        >
          <div className="flex gap-4 justify-center">
            <div className="w-[50%] flex justify-center flex-col items-center">
              <div className="font-bold mb-2 text-lg">{t("frontImg")}</div>
              <Image
                src={selectedPartner?.identityCardFront}
                width={400}
                height={250}
                className="object-cover"
              ></Image>
            </div>
            <div className="w-[50%] flex justify-center flex-col items-center">
              <div>
                {" "}
                <div className="font-bold mb-2 text-lg">{t("backImg")}</div>
              </div>
              <Image
                src={selectedPartner?.identityCardBack}
                width={400}
                height={250}
                className="object-cover"
              ></Image>
            </div>
          </div>
          {selectedPartner?.contract && (
            <>
              <div className="font-bold">{t("contract")}</div>
              <FileUpload
                fileList={docFile}
                readOnly={true}
                onDelete={() => form.setFieldsValue({ value: undefined })}
                onUploadOk={(filePath: string) => {
                  console.log(filePath);
                  form.setFieldsValue({
                    contract: filePath,
                  });
                  setDocFile([
                    {
                      uid: filePath || "",
                      name: filePath || "",
                    },
                  ]);
                }}
              />
            </>
          )}
        </Form>
      </Modal>
    );
  }
);
