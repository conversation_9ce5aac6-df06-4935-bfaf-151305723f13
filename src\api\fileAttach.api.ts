import { request } from "../utils/request";
import { AxiosPromise } from "axios";

export const fileAttachApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/fileAttach",
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/fileAttach",
      data,
      method: "post",
    }),
  upload: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/fileAttach/upload",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/fileAttach/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/fileAttach/${id}`,
      method: "delete",
    }),
};
