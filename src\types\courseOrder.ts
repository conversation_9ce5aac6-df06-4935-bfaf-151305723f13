import { Course } from "./course";
import { Customer } from "./customer";
import { Lecture } from "./lecture";
import { OnlinePayment } from "./online-payment";
import { PaymentMethod } from "./paymentMethod";
import { Staff } from "./staff";

export enum CourseOrderStatus {
  All = "ALL",
  Pending = "PENDING",
  Complete = "COMPLETE",
  Reject = "REJECT",
  Cancel = "CANCEL",
}

export const CourseOrderStatusTrans = {
  [CourseOrderStatus.All]: {
    title: "Tất cả",
    color: "purple",
    value: CourseOrderStatus.All,
  },
  [CourseOrderStatus.Pending]: {
    title: "Chờ xác nhận",
    color: "blue",
    value: CourseOrderStatus.Pending,
  },
  [CourseOrderStatus.Complete]: {
    title: "Đã hoàn tất",
    color: "green",
    value: CourseOrderStatus.Complete,
  },
  [CourseOrderStatus.Reject]: {
    title: "Đã từ chối",
    color: "red",
    value: CourseOrderStatus.Reject,
  },
  [CourseOrderStatus.Cancel]: {
    title: "Đã Huỷ",
    color: "red",
    value: CourseOrderStatus.Cancel,
  },
};

export interface CourseOrderDetail {
  id: number;
  createdAt: number;
  updatedAt: number;
  deletedBy: string;
  isDeleted: boolean;
  deletedAt: number;
  price: number;
  quantity: number;
  totalPrice: number;
  courseOrder: CourseOrder;
  course: Course;
  lecture: Lecture;
  customer: Customer;
  type: string;
}

export interface CourseOrder {
  id: number;
  createdAt: number;
  completedAt: number;
  updatedAt: number;
  deletedBy: string;
  isDeleted: boolean;
  isPrivate: boolean;
  deletedAt: number;
  code: string;
  status: CourseOrderStatus;
  reason: string;
  inspecAt: number;
  moneyProduct: number;
  moneyDiscount: number;
  moneyFinal: number;
  transferContent: string;
  onlinePayment: OnlinePayment;
  customer: Customer;
  inspecStaff: Staff;
  privateNote: string;
  paymentMethod: PaymentMethod;
  courseOrderDetails: CourseOrderDetail[];
}
