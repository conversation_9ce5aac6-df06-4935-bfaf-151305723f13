import { AxiosPromise } from "axios";
import { request } from "utils/request";

export const BankApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/bank",
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/bank",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/bank/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/bank/${id}`,
      method: "delete",
    }),
};
