import { request } from "utils/request";
import { AxiosPromise } from "axios";

export const feelingApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/feeling",
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/feeling",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/feeling/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/feeling/${id}`,
      method: "delete",
    }),
};
