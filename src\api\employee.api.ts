import { request } from "utils/request";
import { AxiosPromise } from "axios";

export const employeeApi = {
  findAllEmployee: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/employee",
      params,
    }),
  findAllEmployeeWithOwner: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/employee/withOwner",
      params,
    }),

  createEmployee: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/employee",
      data,
      method: "post",
    }),
  resetPassword: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/employee/${id}/password/reset`,
      method: "patch",
      data,
    }),

  updateEmployee: (id: number, data: any): AxiosPromise<any> => {
    return request({
      url: `/v1/admin/employee/${id}`,
      data,
      method: "patch",
    });
  },
  updateImportEmployee: (data: any): AxiosPromise<any> => {
    return request({
      url: `/v1/admin/employee/import`,
      data,
      method: "post",
    });
  },

  delete: (id: number, data?: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/employee/${id}`,
      method: "delete",
      data,
    }),
  logout: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/employee/${id}/logout`,
      method: "delete",
    }),

  blockEmployee: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/employee/${id}/block`,
      method: "patch",
      data,
    }),

  unblockEmployee: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/employee/${id}/unblock`,
      method: "patch",
    }),
  unblockOtpEmployee: (storeId: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/store/${storeId}/unblock-otp`,
      data,
      method: "post",
    }),
};
