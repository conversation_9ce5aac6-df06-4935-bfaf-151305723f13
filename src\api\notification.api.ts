import { request } from "utils/request";
import { AxiosPromise } from "axios";

export const notificationApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/notification",
      params,
    }),
  findOne: (id?: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/notification/${id}`,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/notification",
      data,
      method: "post",
    }),
  send: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/notification/send",
      data,
      method: "post",
    }),
  publish: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/notification/${id}/publish`,
      method: "patch",
      data,
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/notification/${id}`,
      method: "patch",
      data,
    }),
  read: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/notification/${id}/read`,
      method: "patch",
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/notification/${id}`,
      method: "delete",
    }),
};
