import React, { useEffect, useRef } from 'react';
import * as echarts from 'echarts';
import { useTranslation } from 'react-i18next';
import { DateType } from 'types/dashboard';
import dayjs from 'dayjs';
import { Spin } from 'antd';

interface DataItem {
  date: string;
  [key: string]: any;
}

interface SeriesItem {
  name: string;
  type?: 'line';
  data: (number | { value: number | string;[key: string]: any })[];
  [key: string]: any;
}

interface Props {
  data: DataItem[];
  series: SeriesItem[];
  type?: string;
  loading?: boolean
}

export const LineProfitChart: React.FC<Props> = ({ data, series, type = "MONTH", loading = false }) => {
  const chartRef = useRef<HTMLDivElement>(null);
  const { t } = useTranslation();

  useEffect(() => {
    if (!chartRef.current) return;

    const chart = echarts.init(chartRef.current);

    const xAxisData = data.map(item => {
      const day = dayjs(item.date);
      if (type === DateType.Day) return day.format("DD/MM");
      if (type === DateType.Month) return day.format("M/YYYY");
      if (type === DateType.Year) return day.format("YYYY");
      return item.date;
    });

    const option: echarts.EChartsOption = {
      tooltip: {
        trigger: 'axis',
        axisPointer: { type: 'line' }, // line pointer
      },
      legend: {
        top: 10,
        right: 10,
        icon: 'path://M2,2 h10 a2,2 0 0 1 2,2 v10 a2,2 0 0 1 -2,2 h-10 a2,2 0 0 1 -2,-2 v-10 a2,2 0 0 1 2,-2 z',
        itemWidth: 24,
        itemHeight: 24,
        textStyle: {
          fontSize: 14,
          fontWeight: 500,
          fontFamily: `"Plus Jakarta Sans", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif`
        },
        itemGap: 36
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: xAxisData,
        axisLabel: {
          margin: 26,
          fontWeight: 400,
          fontSize: 12,
          color: '#7C8FAC'
        },
        axisLine: {
          lineStyle: { type: 'dashed', color: '#DFE5EF' }
        },
        axisTick: { show: false },
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          margin: 16,
          fontWeight: 400,
          fontSize: 12,
          color: '#7C8FAC'
        },
        splitLine: {
          lineStyle: { type: 'dashed', color: '#DFE5EF' }
        }
      },
      series: series.map(s => ({
        ...s,
        type: 'line',
        smooth: true
      })),
    };

    chart.setOption(option);
    window.addEventListener('resize', (chart as any)?.resize);
    return () => window.removeEventListener('resize', (chart as any)?.resize);
  }, [data, series, type, t]);

  return (
    <Spin spinning={loading}>
      <div ref={chartRef} style={{ height: 400 }} />
    </Spin>
  );
};
