import { $googleApi<PERSON><PERSON> } from "constant";
import { Coords } from "google-map-react";
import React from "react";
import AutoComplete from "react-google-autocomplete";

export interface CoordAddress {
  lat: number;
  lng: number;
  address?: string;
  tmpCity?: string;
  tmpDistrict?: string;
  tmpWard?: string;
  tmpStreet?: string;
  placeId?: string;
  rate?: number;
  totalRate?: number;
  rateWant?: number;
  name?: string;
  mapUrl?: string;
  countReviews?: { count: number; star: number }[];
}

interface IGoogleMapAutoComplete {
  onPlaceSelected: (coordAddress: CoordAddress) => void;
  defaultBounds?: any;
}

export const GoogleMapAutoComplete = ({
  onPlaceSelected,
  defaultBounds,
}: IGoogleMapAutoComplete) => {
  return (
    <div>
      <AutoComplete
        apiKey={$googleApiKey}
        options={{
          bounds: defaultBounds,
          types: ["geocode"],
        }}
        style={{ width: "100%" }}
        language="ja"
        onPlaceSelected={(place) => {
          console.log("onPlaceSelected:", place);
          if (place.geometry) {
            onPlaceSelected({
              lat: place.geometry.location?.lat() || 0,
              lng: place.geometry.location?.lng() || 0,
              address: place.formatted_address || "",
            });
          }
        }}
      />
    </div>
  );
};
