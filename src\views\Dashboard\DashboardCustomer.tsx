import React, { useEffect } from 'react';
import { Row, Col, Select, Typography } from 'antd';
import ChartProject from './components/customer/ChartProject';
import { observer } from 'mobx-react';
import Map from './components/customer/Map';
import { useTranslation } from 'react-i18next';
import { getTitle } from 'utils';


export const DashboardCustomer = observer(({ title = "" }) => {
  const { t } = useTranslation();

  useEffect(() => {
    document.title = getTitle(t(title));
  }, []);

  return (
    <div className='flex flex-col gap-6 dashboard-container'>
      <ChartProject />
      <Map />
    </div>
  );
})