import { request } from "utils/request";
import { AxiosPromise } from "axios";

export const partnerApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/partner",
      params,
    }),
  findOne: (id?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/partner/" + id,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/partner",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/partner/${id}`,
      method: "patch",
      data,
    }),
  verifyStatus: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/partner/${id}/verified/status`,
      method: "patch",
      // data,
    }),
  unverifiedStatus: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/partner/${id}/unverified/status`,
      method: "patch",
      // data,
    }),
  resetPass: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/partner/${id}/password`,
      method: "patch",
      data,
    }),
  resetLoginFail: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/partner/${id}/resetLoginFail`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/partner/${id}`,
      method: "delete",
    }),
  import: (data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/partner/import`,
      method: "post",
      data,
    }),
};
