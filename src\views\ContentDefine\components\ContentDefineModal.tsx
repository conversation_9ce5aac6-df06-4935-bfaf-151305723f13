import { Col, Form, Input, message, Modal, Row, Select } from "antd";
import { Rule } from "antd/lib/form";
import TextArea from "antd/lib/input/TextArea";
import { contentDefineApi } from "api/content-define.api";
import { RichTextEditor } from "components/Editor/RichTextEditor";
import { SingleImageUpload } from "components/Upload/SingleImageUpload";
import React, { useEffect, useRef, useState } from "react";
import { ModalStatus } from "types/modal";
import { ContentDefine, ContentDefineType } from "types/content-define";
import { observer } from "mobx-react";
import { RichTextEditorV2 } from "components/Editor/RichTextEditorV2";
import { useTranslation } from "react-i18next";
import UploadVideo from "components/Upload/UploadVideo";
import { useWatch } from "antd/es/form/Form";

const rules: Rule[] = [{ required: true }];
const { Option } = Select;

export const ContentDefineModal = observer(
  ({
    visible,
    status,
    contentDefine,
    onClose,
    onSubmitOk,
  }: {
    visible: boolean;
    status: ModalStatus;
    contentDefine?: ContentDefine;
    onClose: () => void;
    onSubmitOk: () => void;
  }) => {
    const [form] = Form.useForm<ContentDefine>();
    const body = useWatch("body", form);
    const [loading, setLoading] = useState(false);
    // const [content, setContent] = useState("");
    // const editorRef = useRef<{ setContent: (content: string) => void }>(null);
    // const isEditorInit = useRef(false);
    const [content, setContent] = useState("");
    const { t } = useTranslation();

    useEffect(() => {
      if (status == "create" && visible) {
        form.resetFields();
      } else {
      }
      // setContent(contentDefine.body || "");
    }, [visible, status]);

    useEffect(() => {
      form.setFieldsValue({ ...contentDefine });
      // if (isEditorInit.current) {
      //   editorRef.current?.setContent(contentDefine.body || "");
      // }
      setContent(contentDefine?.body || "");
      // setForceUpdate({});
    }, [contentDefine]);

    const createData = async () => {
      const valid = await form.validateFields();
      const data = {
        contentDefine: {
          ...form.getFieldsValue(),
          // body: content,
        },
      };

      setLoading(true);
      try {
        const res = await contentDefineApi.create(data);
        message.success(t("actionSuccessfully"));
        onClose();
        onSubmitOk();
      } finally {
        setLoading(false);
      }
    };

    const updateData = async () => {
      const valid = await form.validateFields();

      const data = {
        contentDefine: {
          ...form.getFieldsValue(),
          // body:form.getFieldValue('body') ||  content,
        },
      };

      setLoading(true);
      try {
        const res = await contentDefineApi.update(contentDefine?.id || 0, data);
        message.success(t("actionSuccessfully"));
        onClose();
        onSubmitOk();
      } finally {
        setLoading(false);
      }
    };

    console.log({ contentDefine, body });

    const cleanQuillContent = (content: string) => {
      return content.replace(/(<p><br><\/p>\s*)+$/g, "").trim();
    }


    return (
      <Modal
        destroyOnClose
        maskClosable={false}
        onCancel={onClose}
        visible={visible}
        title={status == "create" ? t("create") : t("update")}
        style={{ top: 20 }}
        width={700}
        cancelText="Đóng"
        okText="Lưu"
        confirmLoading={loading}
        onOk={() => {
          status == "create" ? createData() : updateData();
        }}
      >
        {contentDefine && (
          <Form
            layout="vertical"
            form={form}
            validateTrigger={["onBlur", "onChange"]}
          >
            <Row gutter={16}>
              <Col span={24}>
                <Form.Item label={t("type")} name="type" rules={rules}>
                  <Select
                    disabled={status == "update"}
                    style={{ width: "100%" }}
                  >
                    {Object.values(ContentDefineType).map((e) => (
                      <Option key={e} value={e}>
                        {t(e)}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={24}>
                <Form.Item name="body" hidden>
                  <Input />
                </Form.Item>
                {[
                  ContentDefineType.ReviewVideoImageMobile,
                  ContentDefineType.ReviewVideoImagePC,
                  ContentDefineType.ReviewVideoMobile,
                  ContentDefineType.ReviewVideoPC,
                ].includes(contentDefine.type!) && body != undefined ? (
                  <div className="flex justify-center">
                    <UploadVideo
                      onUpload={(file) => {
                        console.log({ file });
                        form.setFieldValue("body", file[0]);
                      }}
                      maxCount={1}
                      fileList={[body]}
                      action={`${import.meta.env.VITE_API_URL
                        }/v1/admin/contentDefine/upload`}
                    />
                  </div>
                ) : (
                  <RichTextEditorV2
                    label={<span className="font-medium">{t("content")}</span>}
                    onChange={(rawContent) => {
                      const cleanedContent = cleanQuillContent(rawContent);
                      setContent(cleanedContent);
                      form.setFieldsValue({ body: cleanedContent });
                    }}
                    content={content}
                  />
                )}
              </Col>
            </Row>
          </Form>
        )}
      </Modal>
    );
  }
);
