import { paymentConfigApi } from "api/paymentConfig.api";
import { useState } from "react";
import { PaymentConfig } from "types/paymentConfig";
import { QueryParam } from "types/query";

export interface PaymentConfigQuery extends QueryParam {}

interface UsePaymentConfigProps {
  initQuery: PaymentConfigQuery;
}

export const usePaymentConfig = ({ initQuery }: UsePaymentConfigProps) => {
  const [data, setData] = useState<PaymentConfig[]>([]);
  const [total, setTotal] = useState(0);
  const [query, setQuery] = useState<PaymentConfigQuery>(initQuery);
  const [loading, setLoading] = useState(false);

  const fetchData = async () => {
    setLoading(true);
    try {
      const { data } = await paymentConfigApi.findAll(query);

      setData(data.paymentConfigs);
      setTotal(data.total);
    } finally {
      setLoading(false);
    }
  };

  return {
    paymentConfigs: data,
    totalPaymentConfig: total,
    fetchPaymentConfig: fetchData,
    loadingPaymentConfig: loading,
    setQueryPaymentConfig: setQuery,
    queryPaymentConfig: query,
  };
};
