import {
  Button,
  Form,
  Image,
  Input,
  message,
  Modal,
  Radio,
  RadioChangeEvent,
  Select,
  Space,
} from "antd";
import { Rule } from "antd/lib/form";
import { useForm } from "antd/lib/form/Form";
import FormItem from "antd/lib/form/FormItem";
import { onlinePaymentApi } from "api/onlinePayment.api";
import { SingleImageUpload } from "components/Upload/SingleImageUpload";
import { useBank } from "hooks/useBank";
import { debounce } from "lodash";
import { forwardRef, useEffect, useImperativeHandle, useState } from "react";
import { useTranslation } from "react-i18next";
import { ModalStatus } from "types/modal";
import {
  Bank,
  EOnlinePayment,
  EOnlinePaymentTrans,
  OnlinePayment,
} from "types/online-payment";
import { OnlinePaymentType } from "types/onlinePayment";
const rules: Rule[] = [{ required: true }];

export interface OnlinePaymentModalProps {
  onClose: () => void;
  onSubmitOk: () => void;
}

export interface OnlinePaymentModal {
  handleUpdate: (onlinePayment: OnlinePayment) => void;
  handleView: (onlinePayment: OnlinePayment) => void;
  handleCreate: () => void;
}

interface Variation {
  id: number;
  syncCode: string;
  syncName: string;
  syncPriceCode: string;
  syncValue: number;
  giftTypeId: number;
  icon: string;
}

export const OnlinePaymentModal = forwardRef(
  ({ onSubmitOk }: OnlinePaymentModalProps, ref) => {
    const [form] = useForm();
    const [loading, setLoading] = useState(false);
    const [visible, setVisible] = useState<boolean>();
    const [selectedOnlinePayment, setSelectedOnlinePayment] =
      useState<OnlinePayment>();
    const [selectedBank, setSelectedBank] = useState<Bank>();
    const [status, setStatus] = useState<ModalStatus>("create");
    const [isView, setIsView] = useState(false);
    const onlinePaymentType = Form.useWatch("onlinePaymentType", form);
    const { t } = useTranslation();

    useImperativeHandle(
      ref,
      () => ({
        handleUpdate,
        handleCreate,
        handleView,
      }),
      []
    );

    // useEffect(() => {
    //   if (visible) form.setFieldValue("icon", "");
    // }, [onlinePaymentType, visible]);

    const { banks, fetchBank, queryBank } = useBank({
      initQuery: {
        page: 1,
        limit: 100,
      },
    });

    console.log(selectedBank);

    const handleUpdate = (onlinePayment: OnlinePayment) => {
      fetchBank();
      console.log(onlinePayment);
      //fill data vào form khi ấn nút cập nhật
      form.setFieldsValue({
        ...onlinePayment,
        onlinePaymentType: onlinePayment.type,
        bankId:
          onlinePayment.type == EOnlinePayment.Bank
            ? onlinePayment.bank?.id
            : undefined,
      });
      setSelectedOnlinePayment(onlinePayment);
      setStatus("update");
      setVisible(true);
    };

    const handleView = (onlinePayment: OnlinePayment) => {
      //fill data vào form khi ấn nút cập nhật
      form.setFieldsValue({
        ...onlinePayment,
        bankId:
          onlinePayment.type == EOnlinePayment.Bank
            ? onlinePayment.bank?.id
            : undefined,
      });

      setIsView(true);
      setSelectedOnlinePayment(onlinePayment);
      setStatus("update");
      setVisible(true);
    };

    const handleCreate = () => {
      fetchBank();
      form.resetFields();
      form.setFieldsValue({ onlinePaymentType: EOnlinePayment.Bank });
      setStatus("create");
      setVisible(true);
    };

    const handleSubmitForm = async () => {
      await form.validateFields();
      const { bankId, onlinePaymentType, ...dataForm } = form.getFieldsValue();
      console.log(dataForm);
      console.log(onlinePaymentType);
      const payload = {
        onlinePayment: {
          // name:
          //   onlinePaymentType == EOnlinePayment.EWallet
          //     ? dataForm.name
          //     : banks.find((item) => item.id == bankId)?.fullName || "",
          // type: onlinePaymentType,
          name: banks.find((item) => item.id == bankId)?.fullName || "",
          type: OnlinePaymentType.Bank,
          ...dataForm,
        },
        bankId,
      };

      try {
        setLoading(true);
        switch (status) {
          case "update":
            await onlinePaymentApi.update(
              selectedOnlinePayment?.id || 0,
              payload
            );
            message.success(t("actionSuccessfully"));
            break;

          //create
          default:
            await onlinePaymentApi.create(payload);
            message.success(t("actionSuccessfully"));
            break;
        }
        onSubmitOk();
      } finally {
        setLoading(false);
        setVisible(false);
        onSubmitOk();
      }
    };

    // const onChange = (e: RadioChangeEvent) => {
    //   setOnlinePaymentType(e.target.value);
    // };

    const debounceSearchBank = debounce((value) => {
      queryBank.search = value;
      fetchBank();
    }, 300);

    return (
      <Modal
        onCancel={() => {
          setVisible(false);
        }}
        visible={visible}
        centered
        title={
          <h1 className="mb-0 text-lg text-primary font-bold">
            {isView ? (
              t("onlinePayment")
            ) : (
              <> {status == "create" ? t("create") : t("update")}</>
            )}
          </h1>
        }
        confirmLoading={loading}
        destroyOnClose
        width={500}
        // onOk={handleSubmitForm}
        afterClose={() => {
          form.resetFields();
        }}
        maskClosable={false}
        okText="Xác nhận"
        footer={
          <Space className={isView ? "none" : ""}>
            <Button onClick={() => setVisible(false)}>{t("close")}</Button>
            <Button type="primary" onClick={() => handleSubmitForm()}>
              {t("save")}
            </Button>
          </Space>
        }
      >
        <Form
          disabled={isView}
          form={form}
          layout="vertical"
          initialValues={{ isEnabled: true }}
          validateTrigger={["onBlur", "onChange"]}
        >
          {/* <div className="mb-5">
            <FormItem
              rules={rules}
              required
              label={t("paymentSource")}
              name={"onlinePaymentType"}
            >
              <Radio.Group onChange={() => form.setFieldValue("icon", "")}>
                {Object.values(EOnlinePaymentTrans).map((item) => (
                  <Radio value={item.value}>{t(item.value)}</Radio>
                ))}
              </Radio.Group>
            </FormItem>
          </div> */}

          <Form.Item shouldUpdate={true} style={{ marginBottom: 0 }}>
            {() => {
              return (
                <Form.Item
                  rules={rules}
                  style={{ marginBottom: 0 }}
                  label={<div>{t("image")}</div>}
                  name="icon"
                >
                  <SingleImageUpload
                    onUploadOk={(path: string) => {
                      form.setFieldsValue({
                        icon: path,
                      });
                    }}
                    imageUrl={form.getFieldValue("icon")}
                  />
                </Form.Item>
              );
            }}
          </Form.Item>
          {/* {onlinePaymentType == EOnlinePayment.Bank ? (
            <FormItem
              rules={rules}
              required
              label={t("onlinePaymentName")}
              name={"bankId"}
            >
              <Select
                size="large"
                onClear={() => {
                  queryBank.search = "";
                  fetchBank();
                  form.resetFields(["icon"]);
                  setSelectedBank(undefined);
                }}
                allowClear
                showSearch
                filterOption={false}
                onSearch={(value) => debounceSearchBank(value)}
                onChange={(e, option) => {
                  console.log(option);
                  //@ts-ignore
                  form.setFieldValue("icon", option.option.logo);
                  //@ts-ignore
                  setSelectedBank(option.option);
                }}
                className="!w-full"
                options={banks.map((item) => {
                  return {
                    label: (
                      <div className="flex items-center  justify-between">
                        <span>{item.fullName}</span>
                        <Image
                          preview={false}
                          className="!h-[20px] object-contain"
                          src={item.logo}
                        ></Image>
                      </div>
                    ),
                    value: item.id,
                    option: item,
                  };
                })}
                placeholder={t("selectBank")}
              />
            </FormItem>
          ) : (
            <FormItem
              rules={rules}
              required
              label={t("paymentUnit")}
              name={"name"}
            >
              <Input />
            </FormItem>
          )} */}

          <FormItem
            rules={rules}
            required
            label={t("onlinePaymentName")}
            name={"bankId"}
          >
            <Select
              size="large"
              onClear={() => {
                queryBank.search = "";
                fetchBank();
                form.resetFields(["icon"]);
                setSelectedBank(undefined);
              }}
              allowClear
              showSearch
              filterOption={false}
              onSearch={(value) => debounceSearchBank(value)}
              onChange={(e, option) => {
                console.log(option);
                //@ts-ignore
                form.setFieldValue("icon", option.option.logo);
                //@ts-ignore
                setSelectedBank(option.option);
              }}
              className="!w-full"
              options={banks.map((item) => {
                return {
                  label: (
                    <div className="flex items-center  justify-between">
                      <span>{item.fullName}</span>
                      <Image
                        preview={false}
                        className="!h-[20px] object-contain"
                        src={item.logo}
                      ></Image>
                    </div>
                  ),
                  value: item.id,
                  option: item,
                };
              })}
              placeholder={t("selectBank")}
            />
          </FormItem>

          <FormItem
            rules={rules}
            required
            label={t("ownerName")}
            name={"ownerName"}
          >
            <Input placeholder={t("enterName")} />
          </FormItem>
          <FormItem rules={rules} required label={t("stk")} name={"bankNumber"}>
            <Input placeholder={t("enterStk")} />
          </FormItem>
        </Form>
      </Modal>
    );
  }
);
