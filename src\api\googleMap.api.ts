import { request } from "utils/request";
import { AxiosPromise } from "axios";

export const googleMapsApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/googleMaps",
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/googleMaps",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/googleMaps/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/googleMaps/${id}`,
      method: "delete",
    }),
  searchPlace: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/public/googleMaps/searchPlace",
      data,
      method: "post",
    }),
  getPlaceDetail: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/public/googleMaps/getPlaceDetail",
      data,
      method: "post",
    }),
};
