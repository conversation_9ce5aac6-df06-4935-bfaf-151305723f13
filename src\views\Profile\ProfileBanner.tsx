// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
import {
  Avatar,
  Box,
  CardMedia,
  Grid2 as Grid,
  styled,
  Typography,
  useMediaQuery,
} from "@mui/material";
import { Button } from "antd";
import { observer } from "mobx-react";
import { useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { settings } from "settings";
import { userStore } from "store/userStore";
import profilecover from "../../assets/images/profilebg.jpg";
import { UpdateProfileModal } from "./UpdateProfileModal";
import clsx from "clsx";
import { formatVND } from "utils";
import emailIcon from "../../assets/svgs/mail.svg";
import phoneIcon from "../../assets/svgs/phone.svg";
import dollarIcon from "../../assets/svgs/dollar.svg";
import editIcon from "../../assets/svgs/icon-edit.svg";
import {
  ChangeAvatarModal,
  ChangeAvatarModalRef,
} from "./components/ChangeAvatarModal";
import { fileAttachApi } from "api/fileAttach.api";
import "./styles/ProfilePage.scss";

const ProfileBanner = () => {
  const { t } = useTranslation();
  const smDown = useMediaQuery((theme: any) => theme.breakpoints.down("sm"));
  const updateProfileModalRef = useRef<UpdateProfileModal>();
  const [avatarForChange, setAvatarForChange] = useState<"string">();
  const ProfileImage = styled(Box)(() => ({
    // backgroundImage: "linear-gradient(#50b2fc,#f44c66)",
    borderRadius: "50%",
    // width: "110px",
    // height: "110px",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    // margin: "0 auto",
    // position: "absolute",
    // top: -55,
    // left: 24,
  }));

  const changeAvatarModalRef = useRef<ChangeAvatarModalRef>();
  return (
    <div>
      <div className="h-full md:min-h-[400px] relative overflow-hidden border-shadow rounded-lg">
        <CardMedia
          component="img"
          image={profilecover}
          alt={profilecover}
          width="100%"
          sx={{
            width: "100%",
            height: { xs: 120, sm: 188 },
            objectFit: "cover",
          }}
        ></CardMedia>
        <div className="flex flex-col w-full">
          <div className="px-[24px] flex md:gap-6 gap-4 items-center pb-[24px]">
            <ProfileImage className="relative mt-[-65px]">
              <Avatar
                className=" avt-profile"
                src={userStore.info.avatar || settings.defaultAvatar}
                alt={userStore.info.email}
                sx={{
                  borderRadius: "50%",
                  border: "4px solid #fff",
                  width: "400px",
                  height: "400px",
                  objectFit: "cover",
                  backgroundColor: "white",
                }}
              />
              <div
                className="avt-icon bg-[#1A73E8] w-[24px] p-[5px] h-[24px] absolute rounded-full flex justify-center items-center left-[103px] top-[96px] cursor-pointer hover:shadow-md"
                onClick={() => {
                  changeAvatarModalRef.current?.handleUpdate(
                    userStore.info.avatar
                  );
                }}
              >
                <img src={editIcon} className="h-full w-full " />
              </div>
            </ProfileImage>
            <span className="text-gray-1 text-semibold">
              {userStore.info.fullName}
              {/* Nguyễn Văn A */}
            </span>
          </div>

          <div className="info-section !h-full !border-none flex flex-col gap w-full pl-3 md:pl-[24px] justify-end pb-[19px]">
            <div className="mb-[12px]">
              <h6 className="text-gray-1 semibold">{t("basicInformation")}</h6>
            </div>

            <span className="text-regular text-gray-1">
              <div className="info-text">
                <img src={emailIcon} width={20} height={20} />
                <span>
                  {t("email")}: {userStore.info.email}
                </span>
              </div>
            </span>
            <span className="text-regular text-gray-1">
              <div className="info-text">
                <img src={phoneIcon} width={20} height={20} />
                <span>
                  {t("phoneNumber")}: {userStore.info.phone}
                </span>
              </div>
            </span>
          </div>
          <div>
            <Button
              className="w-[max-content] h-[42px] mb-3 md:mb-6 mr-3 md:mr-6 float-right"
              size="large"
              type="primary"
              onClick={() =>
                updateProfileModalRef.current?.handleUpdate(userStore.info)
              }
            >
              {t("update")}
            </Button>
          </div>

        </div>
      </div>
      <UpdateProfileModal
        ref={updateProfileModalRef}
        onClose={function (): void {
          throw new Error("Function not implemented.");
        }}
        onSubmitOk={() => userStore.getProfile()}
      />
      <ChangeAvatarModal
        url={avatarForChange!}
        onClose={() => { }}
        onSubmitOk={(file: File) => {
          fileAttachApi.upload(file).then((res) => {
            setAvatarForChange(res.data.path);
          });
        }}
        //@ts-ignore
        ref={changeAvatarModalRef}
      />
    </div>
  );
};

export default observer(ProfileBanner);
