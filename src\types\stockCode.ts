import { Customer } from "./customer";
import { StockOrder } from "./stockOrder";

export interface StockCodeFavorite {
  id: number;
  createdAt: number;
  updatedAt: number;
  isDeleted: boolean;
  deletedAt: number;
  customer: Customer;
  stockCode: StockCode;
}

export interface StockCode {
  isActive: boolean;
  isBlocked: boolean;
  id: number;
  createdAt: number;
  updatedAt: number;
  isDeleted: boolean;
  deletedAt: number;
  realSellPrice: number;
  code: string;
  icon: string;
  totalFollow: number;
  totalLike: number;
  stockOrders: StockOrder[];
}
