import { Button, Col, Form, Input, message, Modal, Row } from "antd";
import { useForm } from "antd/lib/form/Form";
import { productApi } from "api/product.api";
import { forwardRef, useImperativeHandle, useState } from "react";
import { Product } from "types/product";
import { ModalStatus } from "types/modal";
import { SurveyCampaign } from "types/survey";
import { requiredRule } from "utils/validate-rules";
import { InputNumber } from "components/Input/InputNumber";
import { useTranslation } from "react-i18next";

export interface ProductModalProps {
  onClose: () => void;
  onSubmitOk: () => void;
  // hasAddProductPermission?: boolean;
  // hasUpdateProductPermission?: boolean;
}

export interface ProductModalRef {
  handleUpdate: (product: Product) => void;
  handleCreate: () => void;
}

export const ProductModal = forwardRef(
  (
    {
      onSubmitOk,
    }: // hasAddProductPermission,
      // hasUpdateProductPermission,
      ProductModalProps,
    ref
  ) => {
    const [form] = useForm();
    const [loading, setLoading] = useState(false);
    const [visible, setVisible] = useState<boolean>();

    const [selectedProduct, setSelectedProduct] = useState<Product>();
    const [status, setStatus] = useState<ModalStatus>("create");
    const { t } = useTranslation();

    useImperativeHandle(
      ref,
      () => ({
        handleUpdate,
        handleCreate,
      }),
      []
    );

    const handleUpdate = (product: Product) => {
      form.setFieldsValue({
        ...product,
      });
      setSelectedProduct(product);
      setStatus("update");
      setVisible(true);
    };

    const handleCreate = () => {
      setStatus("create");
      setVisible(true);
    };

    const handleSubmitForm = async () => {
      await form.validateFields();
      const { slowMinQuantity, ...dataForm } = form.getFieldsValue();
      if (dataForm.name) {
        dataForm.name = dataForm.name.trim();
      }
      const payload = {
        product: {
          slowMinQuantity: slowMinQuantity || 0,
          ...dataForm,
        },
      };

      console.log("payload", payload);

      try {
        setLoading(true);
        switch (status) {
          case "update":
            await productApi.update(selectedProduct?.id || 0, payload);
            message.success(t("actionSuccessfully"));
            break;

          //create
          default:
            await productApi.create(payload);
            message.success(t("actionSuccessfully"));
            break;
        }
        setVisible(false);
        onSubmitOk();
      } catch (error) {
      } finally {
        setLoading(false);
        // onSubmitOk();
      }
    };

    return (
      <Modal
        onCancel={() => {
          setVisible(false);
        }}
        visible={visible}
        centered
        title={
          <h1 className="mb-0 text-lg text-primary font-bold">
            {status == "create" ? t("create") : t("update")}
          </h1>
        }
        confirmLoading={loading}
        destroyOnClose
        width={900}
        onOk={handleSubmitForm}
        afterClose={() => {
          form.resetFields();
        }}
        okText={t("save")}
      // okButtonProps={{
      //   hidden:
      //     (!hasAddProductPermission && status == "create") ||
      //     (!hasUpdateProductPermission && status == "update"),
      // }}
      >
        <Form
          form={form}
          layout="vertical"
          validateTrigger={["onBlur", "onChange"]}
          initialValues={{
            slowMinQuantity: 0,
          }}
        >
          <Row gutter={[12, 0]}>
            <Col span={12}>
              <Form.Item
                className="!mb-2"
                name="code"
                label={t("code")}
              // rules={[requiredRule]}
              >
                <Input
                  placeholder={t("autoGen")}
                  disabled={status === "update"}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                className="!mb-2"
                name="position"
                label={t("position")}
                rules={[requiredRule]}
              >
                <InputNumber placeholder={t("enterPosition")} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                className="!mb-2"
                name="name"
                label={t("vietnameseName")}
                rules={[requiredRule]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                className="!mb-2"
                name="nameEn"
                label={t("englishName")}
                rules={[requiredRule]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                className="!mb-2"
                name="unitPrice"
                label={t("price")}
                rules={[requiredRule]}
              >
                <InputNumber />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                className="!mb-2"
                name="numReview"
                label={t("reviewQuantity")}
                rules={[requiredRule]}
              >
                <InputNumber />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                className="!mb-2"
                name="slowMinQuantity"
                label={t("slowMinQuantity")}
                dependencies={["slowMaxQuantity", "numReview"]}
                rules={[
                  ({ getFieldValue }) => ({
                    validator(_, value) {

                      const slowMaxQuantity = getFieldValue("slowMaxQuantity");
                      console.log("slowMaxQuantity", slowMaxQuantity, value)
                      if (
                        value === undefined ||
                        (slowMaxQuantity === undefined)
                      ) {
                        return Promise.resolve();
                      }

                      if (
                        (slowMaxQuantity !== undefined && Number(value) > Number(slowMaxQuantity))
                      ) {
                        return Promise.reject(
                          new Error(
                            t("slowMinQuantityMust")
                          )
                        );
                      }

                      return Promise.resolve();
                    },
                  }),
                ]}
              >
                <InputNumber />
              </Form.Item>

            </Col>
            <Col span={12}>
              <Form.Item
                className="!mb-2"
                name="slowMaxQuantity"
                label={t("slowMaxQuantity")}
                dependencies={["slowMinQuantity", "numReview"]}
                rules={[
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      const slowMinQuantity = getFieldValue("slowMinQuantity");

                      if (
                        value === undefined ||
                        (slowMinQuantity === undefined)
                      ) {
                        return Promise.resolve();
                      }

                      if (
                        (slowMinQuantity !== undefined && +value < +slowMinQuantity)
                      ) {
                        return Promise.reject(
                          new Error(
                            t("estimatedDayMust")
                          )
                        );
                      }

                      return Promise.resolve();
                    },
                  }),
                ]}
              >
                <InputNumber />
              </Form.Item>

            </Col>
            <Col span={12}>
              <Form.Item
                className="!mb-2"
                name="minKeyword"
                label={t("minKeyWordQuantity")}
                rules={[
                    requiredRule,
                ]}
              >
                <InputNumber />
              </Form.Item>

            </Col>

          </Row>
        </Form>
      </Modal>
    );
  }
);
