.review-status {
  height: 26px;
  font-size: 12px;
  font-weight: bold;
  display: inline-flex;
  align-items: center;

  // Ch<PERSON>a phân phối
  &.PENDING {
    color: var(--gray-2-color);
    background-color: var(--gray-5-color);
    border-color: var(--gray-4-color);
  }

  // Đang phân phối
  &.ASSIGNING {
    color: var(--warning-color-300);
    background-color: var(--warning-background-color-25);
    border-color: var(--warning-border-color);
  }

  // Phân phối lại
  &.WARRANTY {
    color: var(--orange-color-500);
    background-color: var(--orange-light-color);
    border-color: var(--orange-light-border-color);
  }

  // Chờ hệ thống duyệt
  &.SYSTEM_PENDING {
    color: var(--blue-color-500);
    background-color: var(--blue-light-color);
    border-color: var(--blue-light-border-color);
  }

  // Chờ nhân viên <PERSON>
  &.ADMIN_PENDING {
    color: var(--orange-color-500);
    background-color: var(--orange-light-color);
    border-color: var(--orange-light-border-color);
  }

  // Chờ bảo hành
  &.REQUEST_WARRANTY {
    color: var(--error-color-500);
    background-color: var(--red-light-color);
    border-color: var(--red-light-border-color);
  }

  // Từ chối bảo hành
  &.REJECT_WARRANTY {
    color: var(--gray-1-color);
    background-color: var(--gray-5-color);
    border-color: var(--gray-1-color);
  }

  // Đã bảo hành
  &.WARRANTY_COMPLETED {
    color: var(--success-500);
    background-color: var(--green-light-color);
    border-color: var(--success-100);
  }
}

@media screen and (max-width: 430px) {
  .review-status {
    font-size: 10px;
    height: 24px;
  }
} 