.avatar-content {
  background-color: #eff5ff;
  border-radius: 50%;

  img {
    width: 100%;
    height: 100%;
  }
}

.avatar-content-ad {
  background-color: #EFF5FF;
  border-radius: 100px;

  img {
    width: 20px;
    height: 20px;
  }
}

.support-ticket-reply {
  .name-content {
    display: inline-block;
  }

  .MuiAvatar-circular {
    width: 36px;
    height: 36px;
  }

  .reply-box {
    max-width: 65%;
    padding: 16px;
    border-radius: 8px;
    border: 1px solid #eaeff4;

    &.user {
      background-color: #eff5ff;
    }

    &.admin {
      background-color: transparent;
    }
  }

  .reply-content {
    white-space: pre-wrap;
    font-size: 14px;
    font-weight: 600;
  }

  .file-label {
    margin-top: 10px;
    font-weight: 600;
  }

  .file-list {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
  }

  .reply-time {
    margin-top: 4px;

    .MuiTypography-root {
      color: #bdc7d5;
    }
  }

  @media (max-width: 600px) {
    .name-content {
      display: none;
    }

    .MuiAvatar-circular {
      width: 20px;
      height: 20px;
    }

    .reply-box {
      max-width: 100%;
      width: 100%;
    }

    .ant-space {
      margin-left: 0 !important;
    }
  }
}