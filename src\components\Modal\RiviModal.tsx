import React from "react";
import { Modal } from "antd";

interface RiviModalProps {
  image?: string;
  title?: string;
  content?: string;
  fullContent?: React.ReactNode;
  visible: boolean;
  onClose: () => void;
}

const RiviModal: React.FC<RiviModalProps> = ({
  image,
  title,
  content,
  visible,
  fullContent,
  onClose,
}) => {
  return (
    <Modal
      zIndex={2000}
      open={visible}
      footer={null}
      onCancel={onClose}
      centered
      closable
    >
      {fullContent ? (
        fullContent
      ) : (
        <div
          className="flex flex-col items-center gap-2 mx-auto w-[90%] pt-4 pb-6"
          style={{ textAlign: "center" }}
        >
          {image && (
            <img
              src={image}
              alt="Notification"
              style={{ width: 170, marginBottom: 24 }}
            />
          )}
          <div className="font-semibold text-[18px]">{title}</div>
          <div className="text-[#7C8FAC]">{content}</div>
        </div>
      )}
    </Modal>
  );
};

export default RiviModal;
