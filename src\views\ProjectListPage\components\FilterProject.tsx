import React, { useMemo } from "react";
import { Input, Select, But<PERSON>, Popconfirm, DatePicker } from "antd";
import {
  SearchOutlined,
  PlusOutlined,
  ExportOutlined,
} from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import { PaymentStatusTrans } from "types/payment";
import { ProjectStatusTrans } from "types/project";
import { Customer } from "types/customer";
import { settings } from "settings";
import dayjs from "dayjs";
import { handleChangeRangerDateFilter } from "utils/data";

interface FilterContentProps {
  queryProject: any;
  setQueryProject: (query: any) => void;
  fetchProject: (newQuery?: any) => void;
  fetchSummary: () => void;
  debounceSearchCustomer: (value: string) => void;
  customers: Customer[];
  handleExport: (params: any) => void;
  exportColumns: any[];
  projectApi: any;
}

const FilterContent: React.FC<FilterContentProps> = ({
  queryProject,
  setQueryProject,
  fetchProject,
  fetchSummary,
  debounceSearchCustomer,
  customers,
  handleExport,
  exportColumns,
  projectApi,
}) => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  const statusOptions = useMemo(
    () => [
      { label: t("all"), value: "" },
      ...Object.values(ProjectStatusTrans).map((item: any) => ({
        label: item.value === "" ? t("all") : t("project" + item.value),
        value: item.value,
      })),
    ],
    [t]
  );

  const handleDateRangeChange = (dates: any) => {
    handleChangeRangerDateFilter({
      dates,
      query: queryProject,
      cb: (newQuery) => {
        setQueryProject(newQuery);
        fetchProject(newQuery);
        fetchSummary();
      },
    });
  };

  return (
    <div className="filter-container filter-project">
      {/* Search Input */}
      <div className="filter-item flex-2 md:flex-1 min-w-[150px] ">
        <div className="text-normal-semibold">
          {t("search")} {t("codeName")}
        </div>
        <Input.Search
          size="large"
          enterButton={<SearchOutlined onClick={() => fetchProject()} />}
          className="w-full search-btn"
          allowClear
          onClear={() => {
            setQueryProject({ ...queryProject, search: "", page: 1 });
            fetchProject();
            fetchSummary();
          }}
          // placeholder={t("nameAndCodeProject")}
          onKeyDown={(ev) => {
            if (ev.code === "Enter") {
              queryProject.page = 1;
              setQueryProject({ ...queryProject });
              fetchProject();
              fetchSummary();
            }
          }}
          onChange={(ev) => {
            queryProject.search = ev.currentTarget.value;
            queryProject.page = 1;
          }}
        />
      </div>

      {/* Project Status */}
      <div className="filter-item flex-1 min-w-[150px] max-w-[200px]">
        <div className="text-normal-semibold">{t("projectStatus")}</div>
        <Select
          value={queryProject.status ?? ""}
          size="large"
          className="w-full"
          defaultValue=""
          placeholder={t("selectStatus")}
          onChange={(value) => {
            queryProject.page = 1;
            queryProject.status = value === "" ? null : value;
            fetchProject();
            fetchSummary();
          }}
        >
          {statusOptions.map(({ label, value }) => (
            <Select.Option key={value} value={value}>
              {label}
            </Select.Option>
          ))}
        </Select>
      </div>

      {/* Payment Status */}
      <div className="filter-item flex-1 min-w-[150px] max-w-[200px]">
        <div className="text-normal-semibold">{t("paymentStatus")}</div>
        <Select
          value={queryProject.paymentStatus ?? ""}
          size="large"
          className="w-full"
          defaultValue=""
          placeholder={t("selectStatus")}
          onChange={(value) => {
            queryProject.page = 1;
            queryProject.paymentStatus = value ?? null;
            fetchProject();
            fetchSummary();
          }}
        >
          {Object.values(PaymentStatusTrans).map(({ value }) => (
            <Select.Option key={value} value={value}>
              {value === "" ? t("ALL") : t("payment" + value)}
            </Select.Option>
          ))}
        </Select>
      </div>

      {/* Customer */}
      <div className="filter-item flex-1 col-span-2 md:col-span-1">
        <div className="text-normal-semibold">{t("customer")}</div>
        <Select
          size="large"
          className="w-full"
          showSearch
          defaultValue=""
          onSearch={debounceSearchCustomer}
          onChange={(value) => {
            queryProject.page = 1;
            queryProject.customerId = value ?? null;
            fetchProject();
            fetchSummary();
          }}
          options={[{ name: t("ALL"), id: "" }, ...customers].map((item) => ({
            label: <span>{item.name}</span>,
            value: item.id,
          }))}
          filterOption={false}
          placeholder={t("selectCustomer")}
        />
      </div>

      <div className="filter-item flex-2 min-w-[120px] w-full md:max-w-[50%] md:flex-1">
        <div className="text-normal-semibold">{t("createdAt")}</div>
        <DatePicker.RangePicker
          size="large"
          className="w-full"
          format={settings.dateFormat2}
          onChange={handleDateRangeChange}
          value={
            queryProject.fromAt && queryProject.toAt
              ? [dayjs.unix(queryProject.fromAt), dayjs.unix(queryProject.toAt)]
              : undefined
          }
        />
      </div>

      <div className="filter-item flex items-end gap-2 justify-end">
        <Popconfirm
          title={t("exportAsk")}
          onConfirm={() =>
            handleExport({
              onProgress: () => { },
              exportColumns,
              fileType: "xlsx",
              dataField: "projects",
              query: queryProject,
              api: projectApi.findAll,
              fileName: t("projectList"),
              sheetName: t("projectList"),
            })
          }
          okText={t("exportExcel")}
          cancelText={t("cancel")}
        >
          <Button
            size="large"
            className="px-3"
            type="primary"
            icon={<ExportOutlined />}
          />

        </Popconfirm>
      </div>
    </div>
  );
};

export default FilterContent;
