import { useCallback, useEffect, useState } from "react";
import { Badge, Card, Tabs } from "antd";
import { getTitle } from "utils";
import {
  CustomerDepositStatus,
  CustomerDepositStatusTrans,
} from "types/customerDeposit";
import { customerDepositApi } from "api/customerDeposit.api";
import { useTranslation } from "react-i18next";
import { CustomerDepositTab } from "./CustomerDepositTab";

export const CustomerDepositPage = ({ title = "", isAgent = false }) => {
  const [tabActive, setTabActive] = useState<CustomerDepositStatus>(
    CustomerDepositStatus.All
  );
  const [summaryReceiptOfStatus, setSummaryReceiptOfStatus] = useState();
  const [lastUpdate, setLastUpdate] = useState(0);
  const { t } = useTranslation();

  console.log({ parentLastUpdate: lastUpdate });

  useEffect(() => {
    document.title = getTitle(t(title));
    fetchSummaryReceipt();
  }, []);

  const fetchSummaryReceipt = useCallback(async () => {
    const res = await customerDepositApi.getSummary();

    if (res.status) {
      setSummaryReceiptOfStatus(() => {
        const summary = res.data.reduce(
          (
            prev: any,
            curr: { status: CustomerDepositStatus; total: number }
          ) => {
            prev[curr.status] = curr.total;
            prev.ALL = (prev.ALL || 0) + curr.total;
            return prev;
          },
          { ALL: 0 }
        );

        return summary;
      });
    }
  }, [isAgent]);
  const onChangeTab = useCallback((key: CustomerDepositStatus) => {
    setTabActive(key as CustomerDepositStatus);
  }, []);

  const onLastUpdateChange = useCallback(() => {
    console.log("on last update");
    setLastUpdate((pre) => pre + 1);
  }, []);

  return (
    // <Card bodyStyle={{ padding: "8px 20px" }} style={{ borderRadius: "8px" }}>
    <div>
      <Tabs
        activeKey={tabActive}
        onChange={(key) => onChangeTab(key as CustomerDepositStatus)}
        type="line"
        animated={{ inkBar: true, tabPane: true, tabPaneMotion: {} }}
      >
        {Object.values(CustomerDepositStatusTrans).map((item) => (
          <Tabs.TabPane
            tab={
              <div className="flex items-center gap-2">
                {t(item.value)}
                {summaryReceiptOfStatus && (
                  <Badge
                    key={item.value}
                    color={CustomerDepositStatusTrans[item.value]?.color}
                    count={summaryReceiptOfStatus?.[item.value] || 0}
                  />
                )}
              </div>
            }
            key={item.value}
            tabKey={item.value}
          >
            <CustomerDepositTab
              parentLastUpdate={lastUpdate}
              isFocus={tabActive == item.value}
              status={tabActive}
              onSubmitOk={() => {
                onLastUpdateChange();
                fetchSummaryReceipt();
              }}
            />
          </Tabs.TabPane>
        ))}
      </Tabs>
      {/* </Card> */}
    </div>
  );
};
