import { Customer } from "./customer";

export enum FreeStockType {
  Sell = "SELL",
  Buy = "BUY",
}

export interface FreeStockFollow {
  id: number;
  createdAt: number;
  updatedAt: number;
  deletedBy: string;
  isDeleted: boolean;
  deletedAt: number;
  freeStock: FreeStock;
  customer: Customer;
}

export interface FreeStockFavorite {
  id: number;
  createdAt: number;
  updatedAt: number;
  deletedBy: string;
  isDeleted: boolean;
  deletedAt: number;
  customer: Customer;
  freeStock: FreeStock;
}

export interface FreeStock {
  id: number;
  createdAt: number;
  updatedAt: number;
  deletedBy: string;
  isDeleted: boolean;
  deletedAt: number;
  code: string;
  dateString: string;
  price: number;
  type: FreeStockType;
  isVisible: boolean;
  freeStockFavorites: FreeStockFavorite[];
  freeStockFollows: FreeStockFollow[];
}
