import { message, Modal } from "antd";
import { Rule } from "antd/lib/form";
import { useForm } from "antd/lib/form/Form";
import { GoogleMaps } from "components/Map/GoogleMaps";

import React, { useEffect, useRef, useState } from "react";
import "../../styles/googleLocationModal.scss";
// import MapWithAutocomplete from "components/Map/MapWithAutocomplete";
import {
  CoordAddress,
  GoogleMapAutoComplete,
} from "components/Map/GoogleMapAutoComplete";
import { useTranslation } from "react-i18next";
import MapWithAutocomplete from "components/Map/MapWithAutocomplete";
import { googleMapsApi } from "api/googleMap.api";

export const GoogleLocationModal = ({
  coord,
  visible,
  onClose,
  onSubmitOk,
}: {
  visible: boolean;
  onClose: () => void;
  onSubmitOk: (addressInfo: CoordAddress) => void;
  coord: CoordAddress;
}) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [addressInfo, setAddressInfo] = useState<CoordAddress>();
  const [originAddress, setOriginAddress] = useState<string | undefined>("");

  const handleSubmitForm = async () => {
    if (addressInfo) {
      console.log(addressInfo);
      onSubmitOk(addressInfo);
      onClose();
    }
  };

  useEffect(() => {
    if (coord) {
      setOriginAddress(coord.address);
      setAddressInfo({
        address: coord.address,
        lat: coord.lat,
        lng: coord.lng,
      });
    }
  }, [coord]);

  console.log({
    addressInfo,
    originAddress,
    isMatch: addressInfo?.address == originAddress,
  });

  const handleCheckLocation = async (address: CoordAddress) => {
    try {
      setLoading(true);
      const { data } = await googleMapsApi.findAll({
        placeId: address.placeId,
      });
      setAddressInfo({
        ...address,
        rate: data.rating || 0,
        totalRate: data.user_ratings_total || 0,
        rateWant: 0,
        address: data.formatted_address,
        name: data.name,
        mapUrl: data.url,
        countReviews: data.countReviews,
      });
    } catch (error) {
      console.log({ error });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      className="address-modal"
      onCancel={onClose}
      open={visible}
      title={t("positionChooseLabel")}
      confirmLoading={loading}
      onOk={handleSubmitForm}
      width={600}
      okText={t("save")}
      okButtonProps={{
        disabled: addressInfo?.address == originAddress || !addressInfo,
      }}
      //   style={}
    >
      <MapWithAutocomplete
        coords={[{ lat: addressInfo?.lat || 0, lng: addressInfo?.lng || 0 }]}
        onPlaceSelected={(address: CoordAddress) => {
          console.log({ address });
          // setAddressInfo(address);
          handleCheckLocation(address);
        }}
      />
    </Modal>
  );
};
