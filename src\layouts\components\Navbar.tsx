import {
  BellOutlined,
  CloseOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
} from "@ant-design/icons";
import {
  Avatar,
  Breadcrumb,
  Dropdown,
  Menu,
  Space,
  Switch,
  Tooltip,
  message,
} from "antd";
import { Header } from "antd/lib/layout/layout";
import { notificationApi } from "api/notification.api";
import { useOneSignalContext } from "context/OneSignalContext";
import { observer } from "mobx-react";
import React, { useCallback, useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { Link, useLocation } from "react-router-dom";
import { Route, adminRoutes } from "router";
import { settings } from "settings";
import { permissionStore } from "store/permissionStore";
import { userStore } from "store/userStore";
import { Notification } from "types/notification";
import { QueryParam } from "types/query";
import { iOS } from "utils/devide";
import { OneSignal } from "utils/oneSignal";
import { $url } from "utils/url";

export const Navbar = observer(
  ({ collapsed, toggle }: { collapsed: boolean; toggle: () => void }) => {
    const location = useLocation();
    const [notifications, setNotifications] = useState<Notification[]>([]);
    const [breadcrumbs, setBreadcrumbs] = useState<string[]>([]);
    const countNotificationUnread = useRef<number>(0);
    const { t } = useTranslation();

    const notificationQuery = useRef<QueryParam>({
      limit: 10,
      page: 1,
    });
    const [loading, setLoading] = useState<boolean>(false);
    const isMaxNotifications = useRef<boolean>(false);

    //Onesignal
    const [isVisibleNotiError, setIsVisibleNotiError] = useState<boolean>(true);
    const errorMessage = iOS()
      ? "Không hỗ trợ trên ios"
      : "Vui lòng cấp quyền cho phép nhận thông báo trên trình duyệt bạn đang sử dụng";

    const {
      isSubscribed,
      loading: oneSignalLoading,
      isBlocked,
      setNotificationLoading,
    } = useOneSignalContext();

    const handleChangePushNotification = useCallback(
      async (checked: boolean) => {
        // console.log(OneSignal.getId());

        try {
          setNotificationLoading?.(true);

          await OneSignal.subscribe(checked);
        } catch (error) {
          message.error(errorMessage);
        } finally {
          setNotificationLoading?.(false);
        }
      },
      []
    );

    const fetchNotification = async ({
      reset,
      readAll,
      read,
      data,
    }: {
      reset?: boolean;
      readAll?: boolean;
      read?: boolean;
      data?: number;
    }) => {
      try {
        if (reset) {
          notificationQuery.current.page = 1;
          isMaxNotifications.current = false;
        }

        setLoading(true);

        const res = await notificationApi.findAll(notificationQuery.current);
        // const unreadRes = await notificationApi.unRead();
        // countNotificationUnread.current = unreadRes.data.total;

        const notis = (await res.data?.notifications) || [];

        if (
          notis.length === 0 ||
          notis.length < notificationQuery.current.limit
        ) {
          isMaxNotifications.current = true;
        }
        if ((read && data) || readAll) {
          if (read) {
            return setNotifications((prev) => {
              const newNotis = [...prev];
              return newNotis.map((noti: Notification) =>
                noti.id == data ? { ...noti, isRead: true } : noti
              ) as Notification[];
            });
          } else {
            return setNotifications((prev) => {
              const newNotis = [...prev];
              return newNotis.map((noti: Notification) => {
                return {
                  ...noti,
                  isRead: true,
                };
              }) as Notification[];
            });
          }
        }
        if (notis.length > 0) {
          if (reset) {
            return setNotifications(notis);
          } else {
            return setNotifications((prev) => [
              ...prev,
              ...res.data.notifications,
            ]);
          }
        }
      } catch (error) {
      } finally {
        setLoading(false);
      }
    };

    // useEffect(() => {
    //   oneSignal.setReloadNotificationEvent(fetchNotification);
    //   fetchNotification({});
    // }, []);

    const handleSetBreadcrumbs = (data: Route) => {
      if (data) {
        if (data.breadcrumb) {
          setBreadcrumbs(data.breadcrumb.split("/"));
        } else if (data.title) {
          setBreadcrumbs([data.title]);
        }
      }
    };

    useEffect(() => {
      adminRoutes.forEach((router) => {
        // console.log("router", router);
        // if (router.name == "/product") {
        //   debugger;
        // }
        if (router.path == location.pathname) {
          return handleSetBreadcrumbs(router);
        } else if (router.children?.length) {
          const findChild = router.children?.find((child) => {
            // console.log("childPath", (router.path || "") + child.path);

            return (router.path || "") + "/" + child.path == location.pathname;
          });
          if (findChild) {
            // console.log("findChild", findChild);

            return handleSetBreadcrumbs(findChild);
          }
        }
      });
    }, [location.pathname]);

    // const findRoute = (routes: Route[], pathname: string) =>
    //   routes.find((route) => pathname.includes(route.path as string));

    // useEffect(() => {
    //   const currentRoute = findRoute(adminRoutes, location.pathname);
    //   const generateBreadcrumbs = (
    //     route: Route | undefined,
    //     crumbs: string[] = []
    //   ) => {
    //     if (route) {
    //       //@ts-ignore
    //       crumbs.unshift({
    //         path: route.path,
    //         breadcrumb: route.breadcrumb,
    //       });

    //       if (route.children) {
    //         const childRoute = findRoute(route.children, location.pathname);
    //         generateBreadcrumbs(childRoute, crumbs);
    //       }
    //     }
    //   };

    //   const breadcrumbList: string[] = [];
    //   generateBreadcrumbs(currentRoute, breadcrumbList);
    //   setBreadcrumbs(breadcrumbList);
    // }, [location.pathname]);

    const menu = (
      <Menu>
        {permissionStore.permissions.find(
          (role) => role.path === "/profile"
        ) && (
          <Menu.Item key={"profile"}>
            <Link to="/profile">Cá nhân</Link>
          </Menu.Item>
        )}

        <Menu.Item key={"login"}>
          <Link to={"/login"} onClick={() => userStore.logout()}>
            Đăng xuất
          </Link>
        </Menu.Item>
      </Menu>
    );

    //   const menu = React.useMemo(()=> <Menu>
    //   <Menu.Item>
    //     <Link to="/profile">Cá nhân</Link>
    //   </Menu.Item>

    //   <Menu.Item>
    //     <Link to={""}>Đăng xuất</Link>
    //   </Menu.Item>
    // </Menu>,)

    const handleOnScrollToBottom = (parent: any) => {
      if (!loading && !isMaxNotifications.current) {
        if (
          parent.scrollHeight - parent.scrollTop <=
          parent.clientHeight + 100
        ) {
          notificationQuery.current.page = notificationQuery.current.page + 1;
          fetchNotification({});
          // element is at the end of its scroll, load more content
        }
      }
    };

    return (
      <Header
        className={`site-layout-background ${collapsed ? "collapsed" : ""}`}
        style={{ padding: 0 }}
      >
        <div>
          {React.createElement(
            collapsed ? MenuUnfoldOutlined : MenuFoldOutlined,
            {
              className: "trigger",
              onClick: toggle,
            }
          )}
          <Breadcrumb
            separator={<span className="text-primary">/</span>}
            style={{ display: "inline-block" }}
            className={!collapsed ? "mobile-breadcrumb-none" : ""}
          >
            {breadcrumbs.map((item, i) => (
              <Breadcrumb.Item key={i} className="font-medium text-primary">
                {t(item)}
              </Breadcrumb.Item>
            ))}
          </Breadcrumb>
        </div>

        <Space
          size={30}
          style={{ flex: 1, justifyContent: "flex-end", marginRight: "20px" }}
        >
          {/* <div className="flex items-center">
            <Switch
              className="custom-switch"
              loading={loading}
              checked={isSubscribed}
              onChange={handleChangePushNotification}
            ></Switch>
            <span
              className="hidden md:block"
              style={{
                fontWeight: "600",
                marginLeft: 10,
              }}
            >
              Bật thông báo đẩy
            </span>
            <Tooltip title="Bật thông báo đẩy">
              <BellOutlined className="md:hidden ml-2 text-white text-xl" />
            </Tooltip>
            {isBlocked && (
              <Tooltip
                overlayClassName="notification-tooltip"
                trigger={"click"}
                visible={isVisibleNotiError}
                placement="bottom"
                overlayInnerStyle={{
                  backgroundColor: "white",
                  color: "black",
                }}
                title={
                  <div
                    style={{
                      position: "relative",
                      padding: 10,
                    }}
                  >
                    {errorMessage}
                    <div
                      onClick={() => {
                        setIsVisibleNotiError(false);
                      }}
                      className="close-btn"
                      style={{
                        position: "absolute",
                        top: 0,
                        right: 0,
                      }}
                    >
                      <CloseOutlined></CloseOutlined>
                    </div>
                  </div>
                }
              ></Tooltip>
            )}
            {/* <span
              className="noti-warning-btn"
              style={{
                cursor: "pointer",
                marginLeft: 10,
              }}
              onClick={() => notiGuide.current?.open({})}
            ></span> */}
          {/* </div> */}
          <label
            style={{
              paddingLeft: "10px",
            }}
            htmlFor=""
          >
            v{settings.version}
          </label>
          {/* <Dropdown
            trigger={["click"]}
            placement="bottomCenter"
            overlay={
              <NotificationOverlay
                onScroll={handleOnScrollToBottom}
                notifications={notifications}
                loading={loading}
                onRefreshNoti={fetchNotification}
              />
            }
          >
            <div style={{ cursor: "pointer", display: "inline-block" }}>
              <Badge count={countNotificationUnread.current}>
                <BellFilled style={{ color: "#0757A2", fontSize: "20px" }} />
              </Badge>
            </div>
          </Dropdown> */}
          <Dropdown trigger={["click"]} overlay={menu}>
            <div style={{ cursor: "pointer", display: "inline-block" }}>
              <Avatar
                size={"large"}
                src={$url(userStore.info.avatar)}
                style={{
                  color: "#f56a00",
                  backgroundColor: "#fde3cf",
                  marginBottom: "10px",
                }}
              >
                {userStore.info.fullName?.[0]}
              </Avatar>
              {/* <CaretDownOutlined style={{ color: "#fff", marginLeft: 4 }} /> */}
            </div>
          </Dropdown>
          {/* {userStore.info.avatar ? (
            <Image
              width={40}
              src={$url(userStore.info.avatar)}
              style={{ borderRadius: "50%" }}
              fallback={require("assets/images/user.png")}
            />
          ) : (
            <img
              width={40}
              alt=""
              style={{ borderRadius: "50%" }}
              src={require("assets/images/user.png")}
            />
          )} */}
        </Space>
      </Header>
    );
  }
);
