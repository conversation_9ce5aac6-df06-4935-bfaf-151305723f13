import { partnerApi } from "api/partner.api";
import { useRef, useState } from "react";
import { Partner } from "types/partner";
import { QueryParam } from "types/query";

export interface PartnerQuery extends QueryParam {}

interface UsePartnerProps {
  initQuery: PartnerQuery;
}

export const usePartner = ({ initQuery }: UsePartnerProps) => {
  const [data, setData] = useState<Partner[]>([]);
  const [total, setTotal] = useState(0);
  const [query, setQuery] = useState<PartnerQuery>(initQuery);
  const [loading, setLoading] = useState(false);
  const countFetch = useRef(0);

  const fetchData = async (newQuery?: PartnerQuery) => {
    setLoading(true);
    try {
      countFetch.current++;
      let currentFetch = countFetch.current;

      const { data } = await partnerApi.findAll({ ...query, ...newQuery });

      if (currentFetch == countFetch.current) {
        setData(data.partners);
        setTotal(data.total);
      }
    } finally {
      setLoading(false);
    }
  };

  return {
    partners: data,
    totalPartner: total,
    fetchPartner: fetchData,
    loadingPartner: loading,
    setQueryPartner: setQuery,
    queryPartner: query,
  };
};
