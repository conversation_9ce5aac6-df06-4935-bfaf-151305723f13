// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
import React, { useEffect, useState } from "react";
import {
  Box,
  Typography,
  FormGroup,
  FormControlLabel,
  Button,
  Stack,
  Divider,
} from "@mui/material";
import { Link, useNavigate } from "react-router-dom";
import AuthSocialButtons from "./AuthSocialButtons";
import CustomFormLabel from "components/CustomerInput/CustomFormLabel";
import CustomTextField from "components/CustomerInput/CustomTextField";
import CustomCheckbox from "components/CustomerInput/CustomCheckbox";
import { Form, Image, Input, message, Select } from "antd";
import { requiredRule } from "utils/validate-rules";
import { useForm } from "antd/es/form/Form";
import { appStore } from "store/appStore";
import { observer } from "mobx-react";
import { userStore } from "store/userStore";
import { permissionStore } from "store/permissionStore";
import { adminRoutes } from "router";
import { settings } from "settings";
import i18nInstance, { langResources } from "config-translation";
import { IoLanguageOutline } from "react-icons/io5";
import unitedKingdomIcon from "../../assets/images/united-kingdom.svg";
import { useTranslation } from "react-i18next";
import dayjs from "dayjs";
import { authApi } from "api/auth.api";
import { getValueFromLocalStorage } from "utils/auth";

interface loginType {
  title?: string;
  subtitle?: any | any[];
  subtext?: any | any[];
}

const AuthLogin = ({ title, subtitle, subtext }: loginType) => {
  const navigation = useNavigate();
  const [form] = useForm<{
    emailPhone: string;
    password: string;
    language: string;
  }>();
  const [loading, setLoading] = useState(false);
  // const [lang, setLang] = useState<string>(localStorage.getItem("lng") || "en");
  const { t } = useTranslation();

  const handleUpdateLanguage = async (token: string) => {
    const { language } = form.getFieldsValue();
    try {
      const res = await authApi.updateLang({ lang: language });
    } catch (error) {}
  };

  useEffect(() => {
    const _language = "vi";
    i18nInstance.changeLanguage(_language.toLowerCase());
    dayjs.locale(_language.toLowerCase());
    localStorage.setItem("lng", _language.toLowerCase());
    form.setFieldValue("language", _language.toLowerCase());
  }, []);

  const handleSubmit = async () => {
    setLoading(true);
    try {
      await form.validateFields();

      const { emailPhone, password } = form.getFieldsValue();
      await userStore.login(emailPhone, password);
      await userStore.changeLanguage("vi");
      await userStore.getProfile();
      permissionStore.accessRoutes = [...adminRoutes];
      if (settings.checkPermission && userStore.info.role) {
        await permissionStore.fetchPermissions(userStore.info.role.id);
        if (!permissionStore.permissions.length || emailPhone == "bct") {
          message.error("Không có quyền truy cập");
          throw new Error("");
        }
        permissionStore.setAccessRoutes();
        const firstPath = permissionStore.permissions[0].path;
        let allSameFirstPath: string[] = [];
        permissionStore.permissions.forEach((item) => {
          if (item.path.includes(firstPath)) {
            allSameFirstPath.push(item.path);
          }
        });
        // debugger;
        // const longestString = allSameFirstPath.reduce((longest, current) => {
        //   return current.length > longest.length ? current : longest;
        // }, "");
        // navigation("/dashboard");
        // const per = JSON.parse(JSON.stringify(permissionStore.permissions));
        // console.log("per ne", per);
        // const userP = JSON.parse(JSON.stringify(userStore.info));
        // console.log("user ne", userP);
        // navigation(permissionStore.permissions[0].path);
        const defaultPermission = permissionStore.permissions.find((item) =>
          item.path.includes("/dashboardManagement/customer")
        );

        const findDefaultPath = defaultPermission
          ? defaultPermission.path
          : "/profile";

        navigation(findDefaultPath);
      } else {
        navigation("/");
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <Form
      layout="vertical"
      form={form}
      validateTrigger={["onBlur", "onChange"]}
      // initialValues={{ language: langResources.en.value }}
    >
      {title ? (
        <Typography fontWeight="700" variant="h3" mb={1}>
          {title}
        </Typography>
      ) : null}

      {subtext}

      {/* <AuthSocialButtons title="Sign in with" /> */}
      {/* <Box mt={3}>
      <Divider>
        <Typography
          component="span"
          color="textSecondary"
          variant="h6"
          fontWeight="400"
          position="relative"
          px={2}
        >
          or sign in with
        </Typography>
      </Divider>
    </Box> */}

      <Stack className="my-8">
        <Box>
          {/* <CustomFormLabel htmlFor="username">Username</CustomFormLabel>
        <CustomTextField id="username" variant="outlined" fullWidth /> */}
          <Form.Item
            label={t("username")}
            name={"emailPhone"}
            rules={[requiredRule]}
          >
            <Input
              onKeyDown={(e) => {
                if (e.code == "Enter") {
                  handleSubmit();
                }
              }}
            />
          </Form.Item>
        </Box>
        <Box>
          {/* <CustomFormLabel htmlFor="password">Password</CustomFormLabel>
          <CustomTextField
            id="password"
            type="password"
            variant="outlined"
            fullWidth
          /> */}
          <Form.Item
            label={t("password")}
            name={"password"}
            rules={[requiredRule]}
          >
            <Input.Password
              onKeyDown={(e) => {
                if (e.code == "Enter") {
                  handleSubmit();
                }
              }}
            />
          </Form.Item>
        </Box>
        {/* <Stack
          justifyContent="space-between"
          direction="row"
          alignItems="center"
          mb={2}
        >
          <FormGroup>
            <FormControlLabel
              control={
                <CustomCheckbox
                  defaultChecked
                  onChange={(e, checked) => {
                    appStore.changeRememberThisDevice(checked);
                  }}
                />
              }
              label="Remeber this Device"
            />
          </FormGroup>
          <Typography
            component={Link}
            to="/auth/forgot-password"
            fontWeight="500"
            sx={{
              textDecoration: "none",
              color: "primary.main",
            }}
          >
            Forgot Password ?
          </Typography>
        </Stack> */}
      </Stack>

      <Box>
        <Button
          color="primary"
          variant="contained"
          size="large"
          fullWidth
          onClick={() => {
            handleSubmit();
          }}
        >
          {t("login")}
        </Button>
      </Box>

      {subtitle}
    </Form>
  );
};

export default observer(AuthLogin);
