import { Select } from "antd";
import { DefaultOptionType } from "antd/es/select";
import { SelectProps } from "antd/lib";
import { usePartner } from "hooks/usePartner";
import { debounce, uniqBy } from "lodash";
import {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
} from "react";
import { Partner } from "types/partner";
import { QueryParams2 } from "types/query";

type CustomFormItemProps = {
  value?: number | number[];
  initQuery?: QueryParams2;
  disabled?: boolean;
  selectedPartner?: Partner[];
  multiple?: boolean;
  onChange?: (id: number | any) => void;
  selectProps?: SelectProps;
  initOptionItem?: Partner | Partner[];
  valueIsOption?: boolean;
  allowClear?: boolean;
};

export interface PartnerSelector {
  refresh(): void;
}

/**
 * A demo that changes it's number value on click.
 * @param value initialValue passed by parent Form.Item.
 * @param onChange a callback for Form.Item to read its child component's value.
 * @constructor
 */
export const PartnerSelector = forwardRef(
  (
    {
      value,
      onChange,
      initQuery,
      disabled,
      multiple = false,
      selectedPartner,
      initOptionItem,
      valueIsOption,
      selectProps,
      allowClear = true,
    }: CustomFormItemProps,
    ref
  ) => {
    const { fetchPartner, queryPartner, partners, loadingPartner } = usePartner(
      {
        initQuery: { page: 1, limit: 500, ...initQuery },
      }
    );

    useImperativeHandle<any, PartnerSelector>(
      ref,
      () => ({
        refresh() {
          fetchPartner();
        },
      }),
      []
    );

    useEffect(() => {
      fetchPartner();
    }, [selectedPartner]);

    const debounceSearch = useCallback(
      debounce((keyword) => {
        queryPartner.search = keyword;
        fetchPartner();
      }, 300),
      [queryPartner]
    );

    const options = useMemo(() => {
      let data = [...partners];
      if (initOptionItem) {
        if ((initOptionItem as Partner[])?.length) {
          data = data.concat(initOptionItem);
        } else {
          data.push(initOptionItem as Partner);
        }
      }

      return uniqBy(data, (data) => data.id);
    }, [partners, initOptionItem]);

    return (
      <Select<any, DefaultOptionType>
        mode={multiple ? "multiple" : undefined}
        disabled={disabled}
        loading={loadingPartner}
        style={{ width: "100%", minWidth: 200 }}
        allowClear={allowClear}
        onSearch={debounceSearch}
        showSearch
        filterOption={false}
        value={value}
        onChange={(v, opts) => {
          if (v === undefined || v.length == 0) {
            debounceSearch(undefined);
          }
          if (valueIsOption) {
            if (opts instanceof Array) {
              onChange?.(opts.map((v) => v.item));
            } else {
              onChange?.(opts?.item);
            }
          } else {
            onChange?.(v);
          }
        }}
        {...selectProps}
      >
        {options?.map((item) => (
          <Select.Option item={item} value={item.id} key={item.id}>
            <span className="text-blue-500">{item.phone}</span> -{" "}
            {item.fullName}
          </Select.Option>
        ))}
      </Select>
    );
  }
);
