import {
  ExportOutlined,
  ImportOutlined,
  PlusOutlined,
  SearchOutlined,
} from "@ant-design/icons";
import { Button, Card, Input, message, Popconfirm, Space } from "antd";
import { useEffect, useMemo, useRef, useState } from "react";
import { PermissionNames } from "router";
import { permissionStore } from "store/permissionStore";
import { getTitle } from "utils";
import { checkRole } from "utils/auth";
import { unixToFullDate } from "utils/dateFormat";
import { handleExport, MyExcelColumn } from "../../utils/MyExcel";
import { NotificationList } from "./components/Table/NotificationList";
import { TextField } from "@mui/material";
import { Notification, NotificationScope } from "types/notification";
import { useNotification } from "hooks/useNotification";
import { useTranslation } from "react-i18next";
import { notificationApi } from "api/notification.api";
import {
  NotificationModal,
  NotificationModalRef,
} from "./components/Modal/NotificationModal";
import { ReactComponent as DownloadIcon } from "assets/svgs/download.svg";
import { ReactComponent as SearchIcon } from "assets/svgs/search-icon.svg";
import { ReactComponent as PlusIcon } from "assets/svgs/plus-icon.svg";

export const NotificationPage = ({
  title = "",
  scope = NotificationScope.Customer,
}) => {
  const notificationModalRef = useRef<NotificationModalRef>();
  const [openImport, setOpenImport] = useState(false);
  const { t } = useTranslation();
  const exportColumns: MyExcelColumn<Notification>[] = [
    {
      header: t("notificationNameVI"),
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "title",
      columnKey: "title",
      render: (record) => record.title,
    },
    {
      header: t("notificationNameEN"),
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "titleEn",
      columnKey: "content",
      render: (record) => record.titleEn,
    },
    {
      header: t("notificationContent"),
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "content",
      columnKey: "content",
      render: (record) => record.content,
    },
    {
      header: t("notificationContentEN"),
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "contentEn",
      columnKey: "contentEn",
      render: (record) => record.contentEn,
    },
    {
      header: t("notificationScope"),
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "scope",
      columnKey: "scope",
      render: (record) => record.scope,
    },
    {
      header: t("notificationPublicAt"),
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "createdDateTime",
      columnKey: "createdDateTime",
      render: (record) => unixToFullDate(record.createdAt),
    },
  ];
  const [loadingDelete, setLoadingDelete] = useState(false);
  const {
    notifications,
    fetchNotification,
    loadingNotification,
    queryNotification,
    totalNotification,
  } = useNotification({
    initQuery: {
      page: 1,
      limit: 20,
      scope,
    },
  });

  // const hasNotificationAddPermission = checkRole(
  //   PermissionNames.consumerNotificationAdd,
  //   permissionStore.permissions
  // );
  // const hasNotificationUpdatePermission = checkRole(
  //   PermissionNames.consumerIndustryEdit,
  //   permissionStore.permissions
  // );
  // const hasIndustryDeletePermission = checkRole(
  //   PermissionNames.consumerIndustryDelete,
  //   permissionStore.permissions
  // );
  useEffect(() => {
    document.title = getTitle(t(title));
    fetchNotification();
  }, []);


  const handleDeleteNotification = async (notificationId: number) => {
    try {
      setLoadingDelete(true);
      const res = await notificationApi.delete(notificationId);
      fetchNotification();
      message.success(t("actionSuccessfully"));
    } catch (error) {
    } finally {
      setLoadingDelete(false);
    }
  };

  const handleSendNotification = async (notificationId: number) => {
    try {
      setLoadingDelete(true);
      const res = await notificationApi.send({ notificationId });
      fetchNotification();
      message.success(t("actionSuccessfully"));
    } catch (error) {
    } finally {
      setLoadingDelete(false);
    }
  };

  const handleSearch = (search: string) => {
    queryNotification.search = search;
    queryNotification.page = 1;
    fetchNotification();
  };

  return (
    // <Card bodyStyle={{ padding: "8px 20px" }} style={{ borderRadius: "8px" }}>
    <div>
      <section className="box">
        <div className="filter-container">
          <Space wrap>
            <div className="filter-item">
              {/* <label htmlFor="">Tìm kiếm</label>
                    <br /> */}

              {/* <TextField
                // allowClear
                size="small"
                onChange={(ev) => {
                  const value = ev.currentTarget.value;
                  if (value) {
                    queryNotification.page = 1;
                    queryNotification.search = value;
                  } else {
                    queryNotification.search = undefined;
                    fetchNotification();
                  }
                }}
                onKeyDown={(ev) => {
                  if (ev.code == "Enter") {
                    fetchNotification();
                  }
                }}
                label={t("notificationName")}
              /> */}
              <label htmlFor="">{t("search")}</label>
              <Input.Search
                allowClear
                onChange={(ev) => {
                  if (ev.currentTarget.value) {
                    queryNotification.search = ev.currentTarget.value;
                  } else {
                    queryNotification.search = undefined;
                  }
                  queryNotification.page = 1;

                }}
                onKeyDown={(ev) => {
                  if (ev.code == "Enter") {
                    // fetchNotification();
                  }
                }}
                size="large"
                // placeholder={t("notificationName")}
                className="w-full search-btn mt-1"
                enterButton={<SearchIcon />}
                onSearch={handleSearch}
              />
            </div>

            {/* <div className="filter-item btn">
              <Button
                onClick={() => fetchNotification()}
                type="primary"
                icon={<SearchOutlined />}
              >
                {t("search")}
              </Button>
            </div> */}
            {/* {hasNotificationAddPermission && ( */}
            <div className="filter-item btn">
              <Button
                onClick={() => {
                  notificationModalRef.current?.handleCreate();
                }}
                icon={<PlusIcon />}
                type="primary"
                size="large"
              >
                {t("create")}
              </Button>
            </div>
            {/* )} */}
            {/* <div className="filter-item btn">
                    <Button
                      onClick={() => {
                        setOpenImport(true);
                      }}
                      type="primary"
                      icon={<PlusOutlined />}
                    >
                      Nhập excel
                    </Button>
                  </div> */}
            {/* <div className="filter-item btn">
                      <Button
                        onClick={() => {
                          importModal.current?.open();
                        }}
                        type="primary"
                        icon={<ImportOutlined />}
                      >
                        Nhập excel
                      </Button>
                    </div> */}

            <div className="filter-item btn">
              <Popconfirm
                title={t("exportAsk")}
                onConfirm={() =>
                  handleExport({
                    onProgress(percent) { },
                    exportColumns,
                    fileType: "xlsx",
                    dataField: "notifications",
                    query: queryNotification,
                    api: notificationApi.findAll,
                    fileName: `${t(title + "List")}`,
                    sheetName: `${t(title + "List")}`,
                    convertContent: true
                  })
                }
                okText={t("exportExcel")}
                cancelText={t("cancel")}
              >
                <Button
                  type="primary"
                  loading={false}
                  icon={<DownloadIcon />}
                  size="large"
                >
                  {t("exportExcel")}
                </Button>
              </Popconfirm>
            </div>
          </Space>
        </div>

        <NotificationList
          onEdit={(record) =>
            notificationModalRef.current?.handleUpdate(record)
          }
          dataSource={notifications}
          loading={loadingNotification}
          loadingDelete={loadingDelete}
          pagination={{
            total: totalNotification,
            defaultPageSize: queryNotification.limit,
            currentPage: queryNotification.page,
            onChange: ({ page, limit }) => {
              Object.assign(queryNotification, {
                page,
                limit,
              });
              fetchNotification();
            },
          }}
          onDelete={handleDeleteNotification}
          onSend={handleSendNotification}
        // hasDeleteNotificationPermission={hasNotificationDeletePermission}
        // hasUpdateNotificationPermission={hasNotificationUpdatePermission}
        />
      </section>

      <NotificationModal
        ref={notificationModalRef}
        onClose={function (): void {
          throw new Error("Function not implemented.");
        }}
        onSubmitOk={fetchNotification}
        scope={scope}
      // hasAddIndustryPermission={hasIndustryAddPermission}
      // hasUpdateIndustryPermission={hasIndustryUpdatePermission}
      />
      {/* </Card> */}
    </div>
  );
};
