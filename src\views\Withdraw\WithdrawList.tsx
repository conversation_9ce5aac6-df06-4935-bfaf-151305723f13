import {
  DownOutlined,
  EditOutlined,
  LockOutlined,
  RetweetOutlined,
  UnlockOutlined,
} from "@ant-design/icons";
import { Button, message, Popconfirm, Space, Table, Tag } from "antd";
import TextArea from "antd/es/input/TextArea";
import Column from "antd/lib/table/Column";
import { withdrawApi } from "api/withdraw.api";
import { IPagination, Pagination } from "components/Pagination";
import { AriviTable } from "components/Table/AriviTable";
import DropdownCell from "components/Table/DropdownCell";
import { useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { GrEdit } from "react-icons/gr";
import { HiOutlineTrash } from "react-icons/hi2";
import { Withdraw, WithdrawStatus, WithdrawStatusTrans } from "types/withdraw";
import { formatVND } from "utils";
import { unixToFullDate } from "utils/dateFormat";
import "./WithdrawList.scss";
import { WithdrawStatusComp } from "./WithdrawStatus/WithdrawStatusComp";

interface PropsType {
  dataSource: Withdraw[];
  loading: boolean;
  loadingDelete?: boolean;
  pagination?: IPagination;
  onEdit?: (withdraw: Withdraw) => void;
  onDelete?: (withdrawId: number) => void;
  onActive?: (withdrawId: number) => void;
  onInactive?: (withdrawId: number) => void;
  onRefreshData: () => void;
  status?: WithdrawStatus;
  rowSelection?: {
    selectedRowKeys: React.Key[];
    onChange: (newSelectedRowKeys: React.Key[]) => void;
    getCheckboxProps: (record: Withdraw) => {
      disabled: boolean;
    };
    preserveSelectedRowKeys: boolean;
  };
  // hasDeleteCustomerWithdrawPermission?: boolean;
  // hasUpdateCustomerWithdrawPermission?: boolean;
}

export const WithdrawList = ({
  dataSource,
  loading,
  loadingDelete,
  pagination,
  onDelete,
  onEdit,
  onActive,
  onInactive,
  onRefreshData,
  status,
  rowSelection,
}: // hasDeleteCustomerWithdrawPermission,
// hasUpdateCustomerWithdrawPermission,

PropsType) => {
  const [loadingStatus, setLoadingStatus] = useState(false);
  const noteRef = useRef<string>("");
  const { t } = useTranslation();
  const handleApprove = async (id: number, note: string) => {
    try {
      setLoadingStatus(true);
      const { data } = await withdrawApi.approve(id, { inspecNote: note });
      message.success(t("actionSuccessfully"));
      noteRef.current = "";
      onRefreshData();
    } catch (error) {
      console.log(error);
    } finally {
      setLoadingStatus(false);
    }
  };

  const handleProcessing = async (id: number, note: string) => {
    try {
      setLoadingStatus(true);
      const { data } = await withdrawApi.processing(id, { inspecNote: note });
      message.success(t("actionSuccessfully"));
      noteRef.current = "";
      onRefreshData();
    } catch (error) {
      console.log(error);
    } finally {
      setLoadingStatus(false);
    }
  };

  const handleReject = async (id: number, note: string) => {
    try {
      setLoadingStatus(true);
      const { data } = await withdrawApi.reject(id, { inspecNote: note });
      message.success(t("actionSuccessfully"));
      noteRef.current = "";

      onRefreshData();
    } catch (error) {
      console.log(error);
    } finally {
      setLoadingStatus(false);
    }
  };

  return (
    <div>
      <AriviTable
        // bordered
        rowSelection={rowSelection}
        loading={loading}
        pagination={false}
        rowKey="id"
        dataSource={dataSource}
        className="custom-scrollbar"
        scroll={{ x: "max-content", y: "calc(100vh - 370px)" }}

        // onChange={}
      >
        <Column
          title={`${t("code")}`}
          dataIndex="code"
          key="code"
          render={(text, record: Withdraw) => (
            <>
              <div>{record.code}</div>
            </>
          )}
        />
        <Column
          title={`${t("partner")}`}
          dataIndex="partner"
          key="partner"
          render={(text, record: Withdraw) => (
            <>
              <div>
                <strong>{t("fullName")}:</strong> {record.partner?.fullName}
              </div>
              <div>
                <strong>Email:</strong> {record.partner?.email}
              </div>
              <div>
                <strong>{t("phoneNumber")}:</strong> {record.partner?.phone}
              </div>
              <div>
                <strong>{t("paymentMethod")}:</strong>{" "}
                {t(record.partner?.paymentType)}
              </div>
              {record?.bankName && (
                <div>
                  <strong>{t("onlinePaymentName")}:</strong>{" "}
                  {t(record?.bankName)}
                </div>
              )}
              {record.partner?.bankAccountName && (
                <div>
                  <strong>{t("bankAccountName")}:</strong>{" "}
                  {t(record.partner?.bankAccountName)}
                </div>
              )}
              {record.partner?.bankAccountNumber && (
                <div>
                  <strong>{t("stk")}:</strong>{" "}
                  {t(record.partner?.bankAccountNumber)}
                </div>
              )}
            </>
          )}
        />
        <Column
          align="right"
          title={`${t("amountRequest")}`}
          dataIndex="amountRequest"
          key="amountRequest"
          width={160}
          render={(text, record: Withdraw) => (
            <>
              <div>{formatVND(record.amount)}</div>
            </>
          )}
        />
        {/* <Column
              title={`${t("bankInfo")}`}
              dataIndex="bankInfo"
              key="bankInfo"
              render={(text, record: CustomerWithdraw) => (
                <>
                  {record.bankName ? (
                    <div>
                      <div>{record?.bankName}</div>
                      <div>
                        {t("stk")}: {record?.bankNumber}
                      </div>
                    </div>
                  ) : (
                    <div></div>
                  )}
                </>
              )}
            /> */}
        <Column
          title={`${t("note")}`}
          dataIndex="note"
          width={200}
          key="note"
          render={(text, record: Withdraw) => (
            <>
              <div>{record.note}</div>
            </>
          )}
        />
        {status !== WithdrawStatus.Pending && (
          <>
            <Column
              title={t("inspector")}
              dataIndex="inspector"
              key="inspector"
              width={150}
              render={(text, record: Withdraw) => (
                <div>
                  {record?.inspecStaff ? `${record.inspecStaff.fullName}` : ""}
                </div>
              )}
            />
            <Column
              title={t("inspectAt")}
              dataIndex="inspectAt"
              key="inspectAt"
              width={200}
              render={(text, record: Withdraw) => (
                <div>{unixToFullDate(record.inspecAt)}</div>
              )}
            />
          </>
        )}

        <Column
          width={100}
          title={t("status")}
          dataIndex="name"
          align="center"
          key={"name"}
          render={(text, record: Withdraw) => (
            // <Tag color={WithdrawStatusTrans[record.status]?.color}>
            //   {t("withdraw" + record.status)}
            // </Tag>
            <WithdrawStatusComp status={record.status} />
          )}
        />

        <Column
          title={`${t("inspectNote")}`}
          dataIndex="inspectNote"
          width={150}
          key="inspectNote"
          render={(text, record: Withdraw) => (
            <>
              <div>{t("withdraw" + record.status + "Note")}</div>
            </>
          )}
        />
        <Column
          fixed="right"
          width={120}
          align="center"
          title=""
          key="action"
          dataIndex={""}
          render={(text, record: Withdraw) => (
            <>
              {record.status === WithdrawStatus.Pending && (
                //@ts-ignore
                <DropdownCell
                  text={t("action")}
                  items={[
                    {
                      onClick: () => "",
                      // createOrderModalRef.current?.handleUpdate(record),
                      label: (
                        // <Popconfirm
                        //   placement="topLeft"
                        //   title={
                        //     <>
                        //       <h3>{t("approveRequest")}</h3>
                        //       <TextArea
                        //         onChange={(e) => {
                        //           noteRef.current = e.target.value;
                        //         }}
                        //         placeholder={t("enterNote")}
                        //       />
                        //     </>
                        //   }
                        //   onConfirm={() =>
                        //     handleProcessing(record.id, noteRef.current)
                        //   }
                        //   okText={t("save")}
                        //   cancelText={t("close")}
                        // >
                        <Button
                          type="primary"
                          onClick={() => {
                            handleProcessing(record.id, "");
                          }}
                        >
                          {t("approveWithdraw")}
                        </Button>
                        // </Popconfirm>
                      ),
                      key: "done",
                    },
                    {
                      onClick: () => "",
                      // createOrderModalRef.current?.handleUpdate(record),
                      label: (
                        <Popconfirm
                          className="w-full "
                          placement="topLeft"
                          title={
                            <>
                              <h3>{t("rejectRequest")}</h3>
                              <TextArea
                                onChange={(e) => {
                                  noteRef.current = e.target.value;
                                }}
                                placeholder={t("enterNote")}
                              />
                            </>
                          }
                          onConfirm={() =>
                            handleReject(record.id, noteRef.current)
                          }
                          okText={t("save")}
                          cancelText={t("close")}
                        >
                          <Button danger>{t("reject")}</Button>
                        </Popconfirm>
                      ),
                      key: "reject",
                    },
                  ]}
                  trigger={["click"]}
                >
                  <a
                    className="text-medium"
                    onClick={(e) => e.preventDefault()}
                  >
                    <Space>
                      {t("action")}
                      <DownOutlined />
                    </Space>
                  </a>
                </DropdownCell>
              )}
              {record.status === WithdrawStatus.Processing && (
                //@ts-ignore
                <DropdownCell
                  text={t("action")}
                  items={[
                    {
                      onClick: () => "",
                      // createOrderModalRef.current?.handleUpdate(record),
                      label: (
                        <Popconfirm
                          placement="topLeft"
                          title={
                            <>
                              <h3>{t("doneRequest")}</h3>
                              <TextArea
                                onChange={(e) => {
                                  noteRef.current = e.target.value;
                                }}
                                placeholder={t("enterNote")}
                              />
                            </>
                          }
                          onConfirm={() =>
                            handleApprove(record.id, noteRef.current)
                          }
                          okText={t("save")}
                          cancelText={t("close")}
                        >
                          <Button type="primary">{t("completeRequest")}</Button>
                        </Popconfirm>
                      ),
                      key: "approve",
                    },
                    {
                      onClick: () => "",
                      // createOrderModalRef.current?.handleUpdate(record),
                      label: (
                        <Popconfirm
                          className="w-full "
                          placement="topLeft"
                          title={
                            <>
                              <h3>{t("rejectRequest")}</h3>
                              <TextArea
                                onChange={(e) => {
                                  noteRef.current = e.target.value;
                                }}
                                placeholder={t("enterNote")}
                              />
                            </>
                          }
                          onConfirm={() =>
                            handleReject(record.id, noteRef.current)
                          }
                          okText={t("save")}
                          cancelText={t("close")}
                        >
                          <Button danger>{t("reject")}</Button>
                        </Popconfirm>
                      ),
                      key: "reject",
                    },
                  ]}
                  trigger={["click"]}
                >
                  <a onClick={(e) => e.preventDefault()}>
                    <Space>
                      {t("action")}
                      <DownOutlined />
                    </Space>
                  </a>
                </DropdownCell>
              )}
            </>
          )}
        />
      </AriviTable>
      {pagination && <Pagination {...pagination} />}
    </div>
  );
};
