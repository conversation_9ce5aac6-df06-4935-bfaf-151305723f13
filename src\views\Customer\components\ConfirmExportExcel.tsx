import { ExportOutlined } from "@ant-design/icons";
import { <PERSON><PERSON>, Modal, Progress } from "antd";
import { customerApi } from "api/customer.api";
import { CustomerQuery } from "hooks/useHandleCustomer";
import { useEffect, useMemo, useState } from "react";
import { Card } from "types/card";
import { Customer, CustomerStatusTrans } from "types/customer";
import { MyExcelColumn, handleExport } from "utils/MyExcel";
import { unixToDate, unixToFullDate } from "utils/dateFormat";
import { handleCountRemainDays } from "./Table/CustomerTable";

interface PropsType {
  customers: Customer[];
  queryCustomer: CustomerQuery;
}

const ConfirmExportExcel = ({ customers, queryCustomer }: PropsType) => {
  const [percentExportComplete, setPercentExportComplete] = useState<number>(0);

  const [visible, setVisible] = useState<boolean>(false);

  const exportColumns: MyExcelColumn<Customer>[] = useMemo(() => {
    return [
      {
        width: 15,
        header: "Mã KH",
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "code",
        render: (record: Customer) => {
          return record.code || "Chưa cập nhật";
        },
      },

      // {
      //   width: 30,
      //   header: "Tên KH",
      //   headingStyle: {
      //     font: {
      //       bold: true,
      //     },
      //   },
      //   key: "fullName",
      //   style: { font: { color: { argb: "004e47cc" } } },
      //   render: (record: Customer) => {
      //     return record.fullName || "Chưa cập nhật";
      //   },
      // },
      {
        width: 20,
        header: "Số điện thoại",
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "phone",
        render: (record: Customer) => {
          return record.phone || "Chưa cập nhật";
        },
      },
      {
        width: 25,
        header: "Email",
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "email",
        render: (record: Customer) => {
          return record.email || "Chưa cập nhật";
        },
      },
      // {
      //   width: 20,
      //   header: "Cấp độ thành viên",
      //   headingStyle: {
      //     font: {
      //       bold: true,
      //     },
      //   },
      //   key: "customerRank",
      //   render: (record: Customer) => {
      //     return record.vipCustomerRank?.name || "Chưa cập nhật";
      //   },
      // },
      // {
      //   width: 35,
      //   header: "ID môi giới",
      //   headingStyle: {
      //     font: {
      //       bold: true,
      //     },
      //   },
      //   key: "refEmployee.name",
      //   render: (record: Customer) => {
      //     return `${
      //       record.refEmployee
      //         ? `${record.refEmployee?.name || "--"} (${
      //             record.refEmployee?.refCode
      //           })`
      //         : "Chưa có thông tin"
      //     }`;
      //   },
      // },
      // {
      //   width: 20,
      //   header: "Người duyệt",
      //   headingStyle: {
      //     font: {
      //       bold: true,
      //     },
      //   },
      //   key: "inspecStaff.name",
      //   render: (record: Customer) => {
      //     return record.inspecStaff?.name || "Chưa cập nhật";
      //   },
      // },
      {
        width: 20,
        header: "Hồ sơ",
        headingStyle: {
          font: {
            bold: true,
          },
        },

        key: "status",
        render: (record: Customer) => {
          return `${CustomerStatusTrans[record.status].label}`;
        },
      },
      // {
      //   width: 20,
      //   header: "Hoạt động",
      //   headingStyle: {
      //     font: {
      //       bold: true,
      //     },
      //   },

      //   key: "isBlocked",
      //   render: (record: Customer) => {
      //     return `${record.isBlocked ? "Ngưng hoạt động" : "Đang hoạt động"}`;
      //   },
      // },
      // {
      //   width: 30,
      //   header: "Lý do từ chối",
      //   headingStyle: {
      //     font: {
      //       bold: true,
      //     },
      //   },
      //   key: "note",
      //   render: (record: Customer) => {
      //     return record?.rejectReason || "--";
      //   },
      // },
      {
        width: 20,
        header: "Ngày đăng ký",
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "createdAt",
        render: (record: Customer) => {
          return unixToFullDate(record.createdAt);
        },
      },
      // {
      //   width: 30,
      //   header: "Ngày hết hạn gói",
      //   headingStyle: {
      //     font: {
      //       bold: true,
      //     },
      //   },
      //   key: "scanAt",
      //   render: (record: Customer) => {
      //     return record?.vipCustomerRank && record?.memberExpiredAt !== 0
      //       ? `${unixToDate(record.memberExpiredAt)} (${handleCountRemainDays(
      //           record.memberExpiredAt
      //         )} ngày)`
      //       : "Chưa có";
      //   },
      // },
    ];
  }, [customers]);

  useEffect(() => {
    if (percentExportComplete >= 100) {
      setTimeout(() => {
        setVisible(false);
      }, 500);
    }
  }, [percentExportComplete]);

  return (
    <div>
      <Button
        type="primary"
        loading={false}
        icon={<ExportOutlined />}
        disabled={!customers.length}
        onClick={() => {
          setVisible(true);
          handleExport({
            onProgress(percentComplete) {
              setPercentExportComplete(percentComplete);
            },
            exportColumns,
            fileType: "xlsx",
            dataField: "customers",
            query: queryCustomer,
            api: customerApi.findAll,
            fileName: "Danh sách Khách hàng",
            sheetName: "Danh sách Khách hàng",
            isGetFullData: true,
            limit: 50,
          });
        }}
      >
        Xuất file excel
      </Button>

      <Modal
        keyboard={false}
        closable={false}
        maskClosable={false}
        footer={null}
        onCancel={() => {
          setVisible(false);
        }}
        visible={visible}
        title={"Đang xuất excel (vui lòng không tắt trình duyệt)"}
        width={500}
        onOk={() => {}}
      >
        <Progress
          strokeColor="#0757A2"
          percent={+percentExportComplete.toFixed()}
        />
      </Modal>
    </div>
  );
};

export default ConfirmExportExcel;
