import {
  PlusOutlined,
  SearchOutlined,
} from "@ant-design/icons";
import { Button, Card, Input, message, Space } from "antd";
import { useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { getTitle } from "utils";
import { FeelingModal, FeelingModalRef } from "./components/Modal/FeelingModal";
import { FeelingList } from "./components/Table/FeelingList";
import { useFeeling } from "hooks/useFeeling";
import { feelingApi } from "api/feeling.api";

export const FeelingPage = ({ title = "" }) => {
  const feelingModalRef = useRef<FeelingModalRef>();
  const [loadingDelete, setLoadingDelete] = useState(false);
  const { t } = useTranslation();

  const {
    feelings,
    fetchFeeling,
    loadingFeeling,
    queryFeeling,
    totalFeeling,
  } = useFeeling({
    initQuery: {
      page: 1,
      limit: 20,
    },
  });

  useEffect(() => {
    document.title = getTitle(t(title));
    fetchFeeling();
  }, []);

  const handleDeleteFeeling = async (feelingId: number) => {
    try {
      setLoadingDelete(true);
      await feelingApi.delete(feelingId);
      fetchFeeling();
      message.success(t("actionSuccessfully"));
    } finally {
      setLoadingDelete(false);
    }
  };

  return (
    <Card bodyStyle={{ padding: "8px 20px" }} style={{ borderRadius: "8px" }}>
      <section className="box">
        <div className="filter-container">
          <Space wrap>
            <div className="filter-item">
              <label>{t("search")}</label>
              <Input
                allowClear
                onChange={(ev) => {
                  queryFeeling.search = ev.currentTarget.value || undefined;
                  queryFeeling.page = 1;
                  fetchFeeling();
                }}
                onKeyDown={(ev) => {
                  if (ev.code === "Enter") {
                    fetchFeeling();
                  }
                }}
                size="middle"
                placeholder={t("feeling")}
              />
            </div>

            <div className="filter-item btn">
              <Button
                onClick={() => fetchFeeling()}
                type="primary"
                icon={<SearchOutlined />}
              >
                {t("search")}
              </Button>
            </div>

            <div className="filter-item btn">
              <Button
                onClick={() => {
                  feelingModalRef.current?.handleCreate();
                }}
                icon={<PlusOutlined />}
                type="primary"
              >
                {t("create")}
              </Button>
            </div>
          </Space>
        </div>

        <FeelingList
          onEdit={(record) => feelingModalRef.current?.handleUpdate(record)}
          dataSource={feelings}
          loading={loadingFeeling}
          loadingDelete={loadingDelete}
          pagination={{
            total: totalFeeling,
            defaultPageSize: queryFeeling.limit,
            currentPage: queryFeeling.page,
            onChange: ({ page, limit }) => {
              Object.assign(queryFeeling, { page, limit });
              fetchFeeling();
            },
          }}
          onDelete={handleDeleteFeeling}
        />
      </section>

      <FeelingModal
        ref={feelingModalRef}
        onClose={() => {}}
        onSubmitOk={fetchFeeling}
      />
    </Card>
  );
};
