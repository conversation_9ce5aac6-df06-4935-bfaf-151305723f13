import {
  Button,
  Checkbox,
  Collapse,
  Space,
  Spin,
  Table,
  Typography,
  message,
} from "antd";
import { useEffect, useState } from "react";
import {
  Configuration,
  ConfigurationDataType,
  ConfigurationParam,
  DataType,
} from "types/configuration";
import { ModalStatus } from "types/modal";
import { QueryParam } from "types/query";
import { formatVND, getTitle } from "utils";
import { ConfigurationModal } from "./components/ConfigurationModal";
import { configurationApi } from "api/configuration";
import { useTranslation } from "react-i18next";
import { configurationParamMetadata } from "./components/configurationParamMetadata";
import clsx from "clsx";

const { ColumnGroup, Column } = Table;

export const ConfigurationPage = ({ title = "" }) => {
  const [query, setQuery] = useState<QueryParam>({
    page: 1,
    limit: 0,
    search: "",
  });
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [visibleModal, setVisibleModal] = useState(false);
  const [configurations, setConfigurations] = useState<Configuration[]>([]);
  const { t } = useTranslation();

  const [groupedSections, setGroupedSections] = useState<
    Record<
      string,
      {
        group: string;
        configurations: Configuration[];
      }
    >
  >({});
  const [selectedConfiguration, setSelectedConfiguration] = useState<
    Partial<
      Configuration & {
        section: {
          title: string;
          params: ConfigurationParam[];
          list: Configuration[];
          type: DataType;
        };
      }
    >
  >({});
  const [modalStatus, setModalStatus] = useState<ModalStatus>("create");

  useEffect(() => {
    document.title = getTitle(t(title));
  }, []);

  useEffect(() => {
    fetchData();
  }, [query]);

  const fetchData = async () => {
    setLoading(true);
    try {
      const res = await configurationApi.findAll();
      const allConfigs = res.data.configurations as Configuration[];

      const grouped: Record<
        string,
        { group: string; configurations: Configuration[] }
      > = {};

      for (const config of allConfigs) {
        const meta = configurationParamMetadata[config.param];
        if (!meta) continue;

        const groupName = meta.group;

        if (!grouped[groupName]) {
          grouped[groupName] = {
            group: groupName,
            configurations: [],
          };
        }

        grouped[groupName].configurations.push({
          ...config,
          dataType: meta.dataType,
          description: meta.description,
        });
      }

      setGroupedSections(grouped);
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateVisible = async (
    record: Configuration,
    isEnable: boolean
  ) => {
    const data = { configuration: { isEnable } };
    setLoading(true);
    try {
      const res = await configurationApi.update(record?.id || 0, data);
      message.success("Update staff successfully!");
      fetchData();
    } finally {
      setLoading(false);
    }
  };
  return (
    <div>
      <Spin spinning={loading}>
        <Collapse defaultActiveKey={Object.keys(groupedSections)}>
          {Object.entries(groupedSections).map(([key, section]) => (
            <Collapse.Panel header={t(key)} key={key}>
              <Table
                // showHeader={false}
                pagination={false}
                rowKey="id"
                dataSource={section.configurations}
                size="middle"
              >
                <Column
                  width={400}
                  title={t("configName")}
                  dataIndex="configName"
                  key="configName"
                  render={(text, record: Configuration) => (
                    <span className="text-primary font-medium">
                      {t(record.param)}
                    </span>
                  )}
                />
                <Column
                  width={300}
                  title={t("description")}
                  dataIndex="description"
                  key="description"
                  render={(text, record: Configuration) => (
                    <span className="text-primary font-medium">
                      {t(record.description)}
                    </span>
                  )}
                />

                <Column
                  width={400}
                  title={t("value")}
                  dataIndex="value"
                  key="value"
                  render={(text, record: Configuration) => {
                    const isPoliciesContent = [
                      ConfigurationParam.SecurityPolicy,
                      ConfigurationParam.WarrantyPolicy,
                      ConfigurationParam.PaymentPolicy,
                      ConfigurationParam.SecurityPolicyPartner,
                      ConfigurationParam.WarrantyPolicyPartner,
                      ConfigurationParam.PaymentPolicyPartner,
                    ].includes(record.param);

                    return (
                      <span
                        className={clsx(
                          "font-medium limit-lines",
                          isPoliciesContent ? "" : "text-primary"
                        )}
                      >
                        {record.dataType === DataType.Boolean ? (
                          <div>
                            {record.value === "true" ? t("on") : t("off")}
                          </div>
                        ) : record.dataType === DataType.Enum ? (
                          t(record.value)
                        ) : record.dataType === DataType.Number ? (
                          formatVND(record.value)
                        ) : isPoliciesContent ? (
                          <div>
                            {record.value ? (
                              <div className="text-green-600">
                                Đã có nội dung tiếng việt
                              </div>
                            ) : (
                              <div className="text-red-600">
                                Chưa có nội dung tiếng việt
                              </div>
                            )}
                            {record.valueEn ? (
                              <div className="text-green-600">
                                Đã có nội dung tiếng anh
                              </div>
                            ) : (
                              <div className="text-red-600">
                                Chưa có nội dung tiếng anh
                              </div>
                            )}
                          </div>
                        ) : (
                          <span
                            className="line-clamp-2"
                            dangerouslySetInnerHTML={{ __html: record.value }}
                          ></span>
                        )}
                      </span>
                    );
                  }}
                />
                <Column
                  align="right"
                  width={150}
                  title=""
                  key="action"
                  render={(text, record: Configuration) => (
                    <Button
                      type="primary"
                      onClick={() => {
                        setSelectedConfiguration({ ...record });
                        setVisibleModal(true);
                        setModalStatus("update");
                      }}
                    >
                      {t("update")}
                    </Button>
                  )}
                />
              </Table>
            </Collapse.Panel>
          ))}
        </Collapse>
      </Spin>

      <ConfigurationModal
        onSubmitOk={fetchData}
        onClose={() => setVisibleModal(false)}
        visible={visibleModal}
        configuration={selectedConfiguration}
        status={modalStatus}
      />
    </div>
  );
};
