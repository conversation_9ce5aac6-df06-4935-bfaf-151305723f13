import { Typography, useMediaQuery } from "@mui/material";
import clsx from "clsx";
import React from "react";
import { useNavigate } from "react-router-dom";
import "./DashboardInfoCard.scss";

interface DashboardInfoCardProps {
  bgColor?: string;
  textColor?: string;
  link?: string;
  title: string;
  info: React.ReactNode;
  image: string;
  className?: string;
  onClick?: () => void;
}

const DashboardInfoCard = ({
  bgColor = "#cecece",
  textColor = "#000000",
  info,
  link,
  title,
  image,
  className,
  onClick,
}: DashboardInfoCardProps) => {
  const navigate = useNavigate();
  const mdDown = useMediaQuery((theme: any) => theme.breakpoints.down("md"));

  const handleClick = () => {
    if (link) navigate(link);
    onClick?.();
  };

  const isClickable = Boolean(link || onClick);

  return (
    <div
      className={clsx(
        "dashboard-info-card",
        link ? "clickable" : "",
        className
      )}
      style={{
        backgroundColor: bgColor,
        color: textColor,
      }}
      onClick={() => {
        if (link) navigate(link);
        onClick && onClick();
      }}
    >
      <img src={image} className="dashboard-info-card__image" />
      <div className={mdDown ? "text-regular" : "text-normal-semibold"}>
        {title}
      </div>
      <div className="dashboard-info-card__info">
        {mdDown ? (
          <h6 className="semibold">{info}</h6>
        ) : (
          <h3 className="semibold">{info}</h3>
        )}
      </div>
    </div>
  );
};

export default DashboardInfoCard;
