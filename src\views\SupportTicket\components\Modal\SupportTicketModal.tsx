import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>ab,
  <PERSON>rid2,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Typo<PERSON>,
} from "@mui/material";
import { IconArrowBackUp, IconLink } from "@tabler/icons-react";
import {
  Col,
  Form,
  Input,
  message,
  Modal,
  Row,
  Spin,
  Tag,
  UploadFile,
} from "antd";
import { Rule } from "antd/lib/form";
import { fileAttachApi } from "api/fileAttach.api";
import { supportTicketApi } from "api/supportTicket.api";
import ChildCard from "components/Common/ChildCard";
import ParentCard from "components/Common/ParentCard";
import { SupportTicketStatusComp } from "components/SupportTicket/SupportTicketStatusComp";
import SupportTicketReply from "components/SupportTicketReply/SupportTicketReply";
import FileUploadItem, {
  FileAttachPayload,
  FileCustomProps,
} from "components/Upload/FileUploadItem";
import { FileUploadMultiple } from "components/Upload/FileUploadMultiple";
import { SingleImageUpload } from "components/Upload/SingleImageUpload";
import { cloneDeep } from "lodash";
import React, { useEffect, useImperativeHandle, useState } from "react";
import { useTranslation } from "react-i18next";
import { userStore } from "store/userStore";
import { FileAttachType } from "types/fileAttach";
import { ModalStatus } from "types/modal";
import {
  SupportTicket,
  SupportTicketStatus,
  SupportTicketStatusTrans,
} from "types/supportTicket";
import { $url } from "utils/url";
import { SupportTicketUpdate } from "./SupportTicketUpdate";

const rules: Rule[] = [{ required: true }];

export interface SupportTicketModalRef {
  handleCreate: () => void;
  handleUpdate: (supportTicket: SupportTicket) => void;
}
interface SupportTicketModalProps {
  onClose: () => void;
  onSubmitOk: () => void;
  idTicket?: number;
  status: string;
  setStatus: (status: ModalStatus | "list") => void
}

export const SupportTicketModal = React.forwardRef(
  ({ onClose, onSubmitOk, idTicket, setStatus, status }: SupportTicketModalProps, ref) => {
    const { t } = useTranslation();

    const [form] = Form.useForm<
      SupportTicket & { fileAttachList: FileAttachPayload[] }
    >();
    const [selectedTicket, setSelectedTicket] = useState<SupportTicket>();
    const [loading, setLoading] = useState(false);
    const [loadingReply, setLoadingReply] = useState(false);
    const [loadingChildTickets, setLoadingChildTickets] = useState(false);
    const [visible, setVisible] = useState(false);
    const [fileList, setFileList] = useState<UploadFile[]>([]);

    const [childTickets, setChildTickets] = useState<SupportTicket[]>([]);
    const [showReply, setShowReply] = useState<{
      open: boolean;
      reply: string;
      fileAttachPayloads: FileAttachPayload[];
    }>({
      open: false,
      reply: "",
      fileAttachPayloads: [],
    });

    useImperativeHandle<any, SupportTicketModalRef>(
      ref,
      () => ({
        handleCreate() {
          form.resetFields();
          setVisible(true);
          setStatus?.("create");
        },
        async handleUpdate(supportTicket: SupportTicket) {
          // form.setFieldsValue({ ...supportTicket });
          // setSelectedTicket(cloneDeep(supportTicket));
          fetchTicket(supportTicket.id);
          setVisible(true);
          setStatus?.("update");
          fetchChildTickets(supportTicket.id);
        },
      }),
      []
    );


    const fetchTicket = async (id: number) => {
      try {
        setLoading(true);
        const { data } = await supportTicketApi.findOne(id);
        form.setFieldsValue({ ...data });
        setSelectedTicket(cloneDeep(data));
      } catch (e) {
        console.log({ e });
      } finally {
        setLoading(false);
      }
    };
    const fetchChildTickets = async (parentId: number) => {
      try {
        setLoadingChildTickets(true);
        const { data } = await supportTicketApi.findAll({
          parentId,
        });
        setChildTickets(data.supportTickets);
      } catch (error) {
        console.log({ error });
      } finally {
        setLoadingChildTickets(false);
      }
    };

    useEffect(() => {
      if (!!idTicket && idTicket !== 0) {
        fetchTicket(idTicket as number);
        fetchChildTickets(idTicket as number)
      }
    }, [idTicket]);


    const getPayload = async () => {
      const { fileAttachList, ...rest } = form.getFieldsValue();
      const payload = { supportTicket: rest };
      let fileAttachIds: number[] = [];
      if (fileAttachList.length > 0) {
        const results = await Promise.allSettled(
          fileAttachList.map((file) =>
            fileAttachApi.create({
              fileAttach: {
                name: file.name,
                type: file.type.includes("image")
                  ? FileAttachType.Image
                  : file.type.includes("pdf")
                    ? FileAttachType.Pdf
                    : FileAttachType.Other,
                url: file.url,
                path: file.path,
                size: file.size,
              },
            })
          )
        );
        //@ts-ignore
        fileAttachIds = results.map((result) => result.value?.data?.id);
        if (fileAttachIds.length > 0) {
          Object.assign(payload, { fileAttachIds });
        }
      }
      return payload;
    };

    const createData = async () => {
      try {
        const valid = await form.validateFields();
        const payload = await getPayload();
        console.log({ payload });
        // const data = { supportTicket: form.getFieldsValue() };

        setLoading(true);
        const res = await supportTicketApi.create(payload);
        message.success(t("operationSuccess"));
        if (selectedTicket) fetchTicket(selectedTicket.id);
        onClose();
        onSubmitOk();
        handleClose();
      } catch (err) {
        console.log({ err });
      } finally {
        setLoading(false);
      }
    };

    // const updateData = async () => {
    //   const valid = await form.validateFields();
    //   const data = { supportTicket: form.getFieldsValue() };
    //   setLoading(true);
    //   try {
    //     const res = await supportTicketApi.update(
    //       data.supportTicket.id || 0,
    //       data
    //     );
    //     message.success(t("operationSuccess"));
    //     onClose();
    //     onSubmitOk();
    //     handleClose();
    //   } finally {
    //     setLoading(false);
    //   }
    // };

    const getReplyPayload = async () => {
      const payload = { supportTicket: { content: showReply.reply } };
      let fileAttachIds: number[] = [];
      if (showReply.fileAttachPayloads.length > 0) {
        const results = await Promise.allSettled(
          showReply.fileAttachPayloads.map((file) =>
            fileAttachApi.create({
              fileAttach: {
                name: file.name,
                type: file.type.includes("image")
                  ? FileAttachType.Image
                  : file.type.includes("pdf")
                    ? FileAttachType.Pdf
                    : FileAttachType.Other,
                url: file.url,
                path: file.path,
                size: file.size,
              },
            })
          )
        );
        //@ts-ignore
        fileAttachIds = results.map((result) => result.value?.data?.id);
        if (fileAttachIds.length > 0) {
          Object.assign(payload, { fileAttachIds });
        }
      }
      return payload;
    };

    const handleReply = async () => {
      if (selectedTicket && !loadingReply) {
        try {
          setLoadingReply(true);
          const payload = await getReplyPayload();
          console.log({ payload });
          await supportTicketApi.reply(selectedTicket.id, payload);
          await fetchChildTickets(selectedTicket.id);
          if (selectedTicket) fetchTicket(selectedTicket.id);
          onSubmitOk();
          setShowReply({ open: false, reply: "", fileAttachPayloads: [] });
          setFileList([]);
        } catch (error) {
          console.log({ error });
        } finally {
          setLoadingReply(false);
        }
      }
    };

    const handleDeleteFile = async (id: string) => {
      const filterFileList = fileList.filter((file) => file.uid != id);
      const filterFileAttachPayloads = showReply.fileAttachPayloads.filter(
        (file) => file.uid != id
      );

      setFileList(filterFileList);
      if (selectedTicket) fetchTicket(selectedTicket.id);
      onSubmitOk();
      setShowReply({
        ...showReply,
        fileAttachPayloads: filterFileAttachPayloads,
      });
    };

    const handleClose = () => {
      setVisible(false);
      form.resetFields();
      setFileList([]);
      setShowReply({ open: false, reply: "", fileAttachPayloads: [] });
      onClose?.();
    };

    return (
      // <Modal
      //   onCancel={handleClose}
      //   closeIcon
      //   open={visible}
      //   title={status == "create" ? t("add") : ""}
      //   style={{ top: 20 }}
      //   width={1000}
      //   footer={null} 
      // >

      //   {status == "create" && (
      //     <Form
      //       layout="vertical"
      //       form={form}
      //       validateTrigger={["onBlur", "onChange"]}
      //     >
      //       <Row gutter={16}>
      //         <Col span={24} md={12}>
      //           <Form.Item label={t("title")} name="title" rules={rules}>
      //             <Input placeholder="" />
      //           </Form.Item>
      //         </Col>
      //         <Col span={24} md={12}>
      //           <Form.Item
      //             label={t("department")}
      //             name="department"
      //             rules={rules}
      //           >
      //             <Input placeholder="" />
      //           </Form.Item>
      //         </Col>

      //         <Col span={24}>
      //           <Form.Item label={t("content")} name="content" rules={rules}>
      //             <Input.TextArea placeholder="" rows={4} />
      //           </Form.Item>
      //         </Col>
      //         <Col span={24}>
      //           <Form.Item shouldUpdate={true} style={{ marginBottom: 0 }}>
      //             {() => {
      //               return (
      //                 <Form.Item
      //                   label={t("fileAttach")}
      //                   style={{ marginBottom: 0 }}
      //                   name="fileAttachList"
      //                 >
      //                   <FileUploadMultiple
      //                     maxFileSize={1024 * 1024 * 10}
      //                     fileList={fileList}
      //                     onUploadOk={(fileList) => {
      //                       console.log({ fileList });
      //                       const filePayloads: FileAttachPayload[] =
      //                         fileList.map((item) => {
      //                           return {
      //                             url:
      //                               item.url || $url(item.response?.data?.path),
      //                             name: item.name,
      //                             size: item?.size,
      //                             uid: item?.uid,
      //                             type: item.type,
      //                             path: item.response?.data?.path,
      //                             destination: item.response?.data?.destination,
      //                           };
      //                         });

      //                       console.log({ filePayload: filePayloads });
      //                       setFileList(fileList);
      //                       form.setFieldsValue({
      //                         fileAttachList: filePayloads,
      //                       });
      //                     }}
      //                     onDelete={setFileList}
      //                   />
      //                 </Form.Item>
      //               );
      //             }}
      //           </Form.Item>
      //         </Col>
      //       </Row>
      //     </Form>
      //   )}
      //   {status == "update" && selectedTicket && (
      <SupportTicketUpdate
        selectedTicket={selectedTicket}
        childTickets={childTickets}
        loadingChildTickets={loadingChildTickets}
        showReply={showReply}
        setShowReply={setShowReply}
        fileList={fileList}
        setFileList={setFileList}
        handleReply={handleReply}
        setStatus={setStatus}
        t={t}
        status={status}
      />
    )
    // </Modal>
  }
);
