import { supportTicketApi } from "api/supportTicket.api";
import { useState } from "react";
import { SupportTicket } from "types/supportTicket";
import { QueryParam } from "types/query";

export interface SupportTicketQuery extends QueryParam {}

interface UseSupportTicketProps {
  initQuery: SupportTicketQuery;
}

export const useSupportTicket = ({ initQuery }: UseSupportTicketProps) => {
  const [data, setData] = useState<SupportTicket[]>([]);
  const [total, setTotal] = useState(0);
  const [query, setQuery] = useState<SupportTicketQuery>(initQuery);
  const [loading, setLoading] = useState(false);

  const fetchData = async () => {
    setLoading(true);
    try {
      const { data } = await supportTicketApi.findAll(query);

      setData(data.supportTickets);
      setTotal(data.total);
    } finally {
      setLoading(false);
    }
  };

  return {
    supportTickets: data,
    totalSupportTicket: total,
    fetchSupportTicket: fetchData,
    loadingSupportTicket: loading,
    setQuerySupportTicket: setQuery,
    querySupportTicket: query,
  };
};
