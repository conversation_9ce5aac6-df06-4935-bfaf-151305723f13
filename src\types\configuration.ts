export enum DataType {
  Number = "NUMBER",
  String = "STRING",
  Boolean = "BOOLEAN",
  Enum = "ENUM",
  File = "FILE",
  Content = "CONTENT",
}

export enum InspecReviewOption {
  SystemReview = "SYSTEM_REVIEW", // Hệ thống duyệt
  StaffReview = "STAFF_REVIEW", // Nhân viên duyệt
  AutoApproveAll = "AUTO_APPROVE_ALL", // Tự động toàn bộ
}

export const InspecReviewOptionTrans = {
  [InspecReviewOption.SystemReview]: {
    value: InspecReviewOption.SystemReview,
  },
  [InspecReviewOption.StaffReview]: {
    value: InspecReviewOption.StaffReview,
  },
  [InspecReviewOption.AutoApproveAll]: {
    value: InspecReviewOption.AutoApproveAll,
  },
};

export enum ConfigurationParam {
  InspecReview = "INSPEC_REVIEW", // Duyệt review
  ReviewImage = "REVIEW_IMAGE", // Bật/tắt tính năng đánh giá bằng ảnh khi tạo dự án
  MaxGenerateContentWithAI = "MAX_GENERATE_CONTENT_WITH_AI", // Số lượng tối đa tạo đánh giá AI trên 1 lần gọi AI (số lượng review)
  TaxPercent = "TAX_PERCENT", // Cấu hình %VAT
  ScanRadius = "SCAN_RADIUS", // Bán kính ban đầu (km)
  IntervalScanRadius = "INTERVAL_SCAN_RADIUS", // Bán kính tăng cường: trường hợp trong ngày hôm qua số lượng đánh giá không đủ thì tăng bán kính này lên theo cấu hình (km)
  MaxMinuteToCompleteReview = "MAX_MINUTE_TO_COMPLETE_REVIEW", // Thời gian tối đa hoàn thành nhiệm vụ(Tính bằng phút)
  MinuteToPenalize = "MINUTE_TO_PENALIZE", // Thời gian thời gian phạt (Tính bằng phút)

  MinDropReview = "MIN_DROP_REVIEW", // Số lượng rải tối thiểu trong ngày: khi tạo dự án số lượng rải không được nhỏ hơn tối thiểu (số lượng review)
  MaxDropReview = "MAX_DROP_REVIEW", // Số lượng rải tối thiểu trong ngày: khi tạo dự án số lượng rải không được nhỏ hơn tối thiểu (số lượng review)
  PercentImageInProject = "PERCENT_IMAGE_IN_PROJECT", // Số lượng hình tối đa của dự án: tính theo số % số lượng đánh giá của dự án
  MoneyDropInProject = "MONEY_DROP_IN_PROJECT", // Số tiền phải trả khi chọn rải chậm (VND)
  MoneyImageInProject = "MONEY_IMAGE_IN_PROJECT", // Số tiền phải trả trên một bức ảnh (VND)

  MinWithdrawAmount = "MIN_WITHDRAW_AMOUNT", // Số tiền tối thiểu có thể rút', // số tiền tối đa có thể rút
  // GEMINI
  PromptContent = "PROMPT_CONTENT", // prompt tạo nội dung cho review
  PromptKeyword = "PROMPT_KEYWORD", // prompt tạo keyword
  //
  ReviewCharacterCount = "REVIEW_CHARACTER_COUNT", // số ký tự của đánh giá (sl ký tự)
  WarrantyPeriodDays = "WARRANTY_PERIOD_DAYS", // thời gian bảo hành (ngày)
  ProjectCreationDelay = "PROJECT_CREATION_DELAY", // thời gian chờ tạo dự án (giây)
  CheckIdentityCard = "CHECK_IDENTITY_CARD", // Kiểm tra xác thực tài khoản
  ContractSampleFile = "CONTRACT_SAMPLE_FILE", // Tệp mẫu hợp đồng'
  MaxDescriptionCreateProject = "MAX_DESCRIPTION_CREATE_PROJECT",
  MinDescriptionCreateProject = "MIN_DESCRIPTION_CREATE_PROJECT",
  TimeRecheckReview = "TIME_RECHECK_REVIEW",
  MaxDropPercent = "MAX_DROP_PERCENT", // số lượng rải chậm tối đa (%)
  SecurityPolicy = "SECURITY_POLICY", // cs bảo mật
  WarrantyPolicy = "WARRANTY_POLICY", // cs bảo hành
  PaymentPolicy = "PAYMENT_POLICY", // cs thanh toán
  autoCheckWarranty = "AUTO_CHECK_WARRANTY", //Tự động kiểm tra bảo hành
  MaxCheckFailReview = "MAX_CHECK_FAIL_REVIEW", // số lần check tối đa của 1 review
  MinDepositAmount = "MIN_DEPOSIT_AMOUNT", // nạp tối thiểu
  //Chính sách dành cho đối tác
  SecurityPolicyPartner = "SECURITY_POLICY_PARTNER", // cs bảo mật
  WarrantyPolicyPartner = "WARRANTY_POLICY_PARTNER", // cs bảo hành
  PaymentPolicyPartner = "PAYMENT_POLICY_PARTNER", // cs thanh toán
}

export const ConfigurationParamTrans = {
  [ConfigurationParam.InspecReview]: {
    value: ConfigurationParam.InspecReview,
  },
  [ConfigurationParam.ReviewImage]: {
    label: "Link youtube",
    value: ConfigurationParam.ReviewImage,
  },
  [ConfigurationParam.MaxGenerateContentWithAI]: {
    label: "Link google",
    value: ConfigurationParam.MaxGenerateContentWithAI,
  },
  [ConfigurationParam.TaxPercent]: {
    label: "Link tiktok",
    value: ConfigurationParam.TaxPercent,
  },
  [ConfigurationParam.ScanRadius]: {
    label: "ScanRadius",
    value: ConfigurationParam.ScanRadius,
  },
  [ConfigurationParam.IntervalScanRadius]: {
    label: "Số ngày tự hủy yêu cầu đổi VIP",
    value: ConfigurationParam.IntervalScanRadius,
  },
  [ConfigurationParam.MaxMinuteToCompleteReview]: {
    label: "Tên công ty",
    value: ConfigurationParam.MaxMinuteToCompleteReview,
  },
  [ConfigurationParam.MinDropReview]: {
    label: "Mã số thuế",
    value: ConfigurationParam.MinDropReview,
  },
  [ConfigurationParam.MaxDropReview]: {
    label: "Địa chỉ công ty",
    value: ConfigurationParam.MaxDropReview,
  },
  [ConfigurationParam.PercentImageInProject]: {
    label: "Banner trang chủ (trên)",
    value: ConfigurationParam.PercentImageInProject,
  },
  [ConfigurationParam.MinuteToPenalize]: {
    label: "Banner trang chủ (trên)",
    value: ConfigurationParam.MinuteToPenalize,
  },
  [ConfigurationParam.MoneyDropInProject]: {
    label: "Banner trang chủ (trên)",
    value: ConfigurationParam.MoneyDropInProject,
  },
  [ConfigurationParam.MoneyImageInProject]: {
    label: "Banner trang chủ (trên)",
    value: ConfigurationParam.MoneyImageInProject,
  },
  [ConfigurationParam.PromptContent]: {
    label: "Banner trang chủ (trên)",
    value: ConfigurationParam.PromptContent,
  },
  [ConfigurationParam.PromptKeyword]: {
    label: "Banner trang chủ (trên)",
    value: ConfigurationParam.PromptKeyword,
  },
};

export const ConfigSlugsNote = {};

export enum ConfigurationDataType {
  Number = "NUMBER",
  String = "STRING",
  Boolean = "BOOLEAN",
}

export interface Configuration {
  id: number;
  createdAt: number;
  updatedAt: number;
  isDeleted: boolean;
  param: ConfigurationParam;
  value: string;
  valueEn: string;
  dataType: DataType;
  title: string;
  description: string;
}
