import { StopOutlined } from "@ant-design/icons";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Typo<PERSON>,
} from "@mui/material";
import { useMediaQuery } from "@mui/system";
import { Button, Collapse, message, Modal, Popconfirm, Progress, Table } from "antd";
import { projectApi } from "api/project.api";
import empty3Img from "assets/images/empty3.png";
import { ReactComponent as CreditSvg } from "assets/svgs/credit.svg";
import { ReactComponent as FolderSvg } from "assets/svgs/folder.svg";
import { ReactComponent as IconlyPro } from "assets/svgs/iconly-pro.svg";
import { ReactComponent as InputSeparator } from "assets/svgs/input-seperator.svg";
import { ReactComponent as LocationSvg } from "assets/svgs/location.svg";
import { ReactComponent as PlayIcon } from "assets/svgs/play.svg";
import { PaymentStatusComp } from "components/PaymentStatus/PaymentStatusComp";
import { ProjectStatusComp } from "components/ProjectStatus/ProjectStatusComp";
import { AriviTable } from "components/Table/AriviTable";
import { useHandleCustomer } from "hooks/useHandleCustomer";
import { useInfiniteScroll } from "hooks/useInfiniteScroll";
import { useProduct } from "hooks/useProduct";
import { useProject } from "hooks/useProject";
import { observer } from "mobx-react";
import { useCallback, useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { FiEdit, FiPause, FiTrash } from "react-icons/fi";
import { IoPlayOutline } from "react-icons/io5";
import { useNavigate, useSearchParams } from "react-router-dom";
import { PaymentStatus } from "types/payment";
import { Project, ProjectStatus } from "types/project";
import { formatVND, getTitle } from "utils";
import { unixToFullDate } from "utils/dateFormat";
import { handleExport, MyExcelColumn } from "utils/MyExcel";
import { PanelGroup } from "views/Dashboard/components/PanelGroup";
import FilterContent from "./components/FilterProject";
import { ProjectModal, ProjectModalRef } from "./ProjectModal";
import "./styles/ProjectListPage.scss";
import { MdOutlineAttachMoney } from "react-icons/md";
import { ProjectPaymentModal } from "./ProjectPaymentModal";
import { CustomerModal } from "views/Customer/components/Modal/CustomerModal";
import Invoice from "components/Invoice/Invoice";

const { ColumnGroup, Column } = Table;

const ProjectListPage = ({ title = "" }) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [loadingPause, setLoadingPause] = useState(false);
  const [loadingCancel, setLoadingCancel] = useState(false);
  const isMd = useMediaQuery((theme: any) => theme.breakpoints.up("md"));

  const [searchParams, setSearchParams] = useSearchParams();
  const [loadingPay, setLoadingPay] = useState(false);
  const [invoice, setInvoice] = useState(false);
  const [infoInvoice, setInfoInvoice] = useState();

  // const [expireWarrantyAt, setExpireWarrantyAt] = useState(0);
  const expireWarrantyAt = useRef(0);
  const customerModal = useRef<CustomerModal>();
  const {
    fetchProject,
    loadingProject,
    projects,
    setQueryProject,
    totalProject,
    queryProject,
    fetchSummary,
    summaryData,
    debounceSearchProject,
    fetchMore,
    hasMoreData,
  } = useProject({
    initQuery: { limit: 10, page: 1 },
  });
  const {
    customers,
    fetchCustomer,
    queryCustomer,
    totalCustomer,
    loadingCustomer,
    debounceSearchCustomer,
  } = useHandleCustomer({
    initQuery: {
      page: 1,
      limit: 50,
    },
  });

  const { fetchProduct, products } = useProduct({
    initQuery: {
      limit: 0,
      page: 1,
      queryObject: JSON.stringify([
        {
          type: "sort",
          field: "product.price",
          value: "ASC",
        },
      ]),
    },
  });

  const [selected, setSelected] = useState<string | undefined>();

  const modalRef = useRef<ProjectModalRef>(null);
  const mdDown = useMediaQuery((theme: any) => theme.breakpoints.down("sm"));

  useEffect(() => {
    document.title = getTitle(t(title));
    fetchProject();
    fetchSummary();
    fetchCustomer();
    fetchProduct();
  }, []);

  const handleLoadMore = useCallback(() => {
    fetchMore(queryProject); // Pass a flag to append data
  }, [fetchMore, queryProject, setQueryProject]);

  const { loadMoreRef } = useInfiniteScroll({
    loading: loadingProject,
    hasMore: hasMoreData,
    onLoadMore: handleLoadMore,
  });

  const handlePayAgain = async (projectId: number) => {
    try {
      setLoadingPay(true);
      const { data } = await projectApi.payAgain(projectId);
      message.success(t("operationSuccess"));
      fetchProject();
      // fetchSummary();
    } catch (error) {
    } finally {
      setLoadingPay(false);
    }
  };

  // const handleActiveProject = async (projectId: number) => {
  //   try {
  //     setLoadingPause(true);
  //     const res = await projectApi.reopen(projectId);
  //     fetchProject();
  //     fetchSummary();

  //     message.success(t("actionSuccessfully"));
  //   } catch (error) {
  //   } finally {
  //     setLoadingPause(false);
  //   }
  // };

  // const handleInactiveProject = async (projectId: number) => {
  //   try {
  //     setLoadingPause(true);
  //     const res = await projectApi.pause(projectId);
  //     fetchProject();
  //     fetchSummary();

  //     message.success(t("actionSuccessfully"));
  //   } catch (error) {
  //   } finally {
  //     setLoadingPause(false);
  //   }
  // };

  const handlePauseProject = async (id: number, isPause: boolean) => {
    try {
      setLoadingPause(true);
      if (isPause) {
        await projectApi.pause(id);
      } else {
        await projectApi.reopen(id);
      }
      message.success(t("actionSuccessfully"));
      fetchProject();
      fetchSummary();
    } catch (e) {
      console.log({ e });
    } finally {
      setLoadingPause(false);
    }
  };

  const handleCancelProject = async (projectId: number) => {
    try {
      setLoadingCancel(true);
      const res = await projectApi.cancel(projectId);
      fetchProject();
      fetchSummary();

      message.success(t("actionSuccessfully"));
    } catch (error) {
    } finally {
      setLoadingCancel(false);
    }
  };
  const handleWarrantyProject = async (projectId: number) => {
    try {
      setLoadingPause(true);
      console.log({ expireWarrantyAtInFn: expireWarrantyAt.current });
      const res = await projectApi.warranty(projectId, {
        expireWarrantyAt: expireWarrantyAt.current,
      });
      fetchProject();
      fetchSummary();
      expireWarrantyAt.current = 0;
      message.success(t("actionSuccessfully"));
    } catch (error) {
    } finally {
      setLoadingPause(false);
    }
  };

  const handleRejectWarrantyProject = async (projectId: number) => {
    try {
      setLoadingPause(true);
      console.log({ expireWarrantyAtInFn: expireWarrantyAt.current });
      const res = await projectApi.rejectWarranty(projectId, {
        expireWarrantyAt: expireWarrantyAt.current,
      });
      fetchProject();
      fetchSummary();
      expireWarrantyAt.current = 0;
      message.success(t("actionSuccessfully"));
    } catch (error) {
    } finally {
      setLoadingPause(false);
    }
  };

  useEffect(() => {
    handleOpenProjectDetail();
  }, [projects, searchParams]);

  const handleOpenProjectDetail = async () => {
    const projectId = searchParams.get("projectId");
    const isOpen = searchParams.get("isOpen");

    if (projects.length > 0 && projectId) {
      const project = projects.find((p) => p.id == Number(projectId));
      if (project) {
        modalRef.current?.handleUpdate(
          project,
          isOpen === "false" ? false : true
        );
      } else {
        const { data } = await projectApi.findOne(Number(projectId));
        modalRef.current?.handleUpdate(data, isOpen === "false" ? false : true);
      }
    }
  };

  const exportColumns: MyExcelColumn<Project>[] = [
    {
      header: t("code"),
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "code",
      columnKey: "code",
      render: (record: Project) => {
        return record.code;
      },
    },
    {
      header: t("projectName"),
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "projectName",
      columnKey: "projectName",
      render: (record: Project) => {
        return record.name;
      },
    },
    {
      header: t("customer"),
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "name",
      columnKey: "name",
      render: (record: Project) => record.customer?.name,
    },
    {
      header: t("address"),
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "address",
      columnKey: "address",
      render: (record) => record.address,
    },
    {
      header: t("package"),
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "packageName",
      columnKey: "packageName",
      render: (record: Project) => record.product.name,
    },
    {
      header: t("moneyFinal"),
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "moneyFinal",
      columnKey: "moneyFinal",
      render: (record) => formatVND(record.moneyFinal),
    },
    {
      header: t("promotion"),
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "promotion",
      columnKey: "promotion",
      render: (record) => record.promotion?.code,
    },
    {
      header: t("progress"),
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "progress",
      columnKey: "progress",
      render: (record) =>
        `${record.currentCompleteReview}/${record.quantityReview}`,
    },
    {
      header: t("status"),
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "status",
      columnKey: "status",
      render: (record) => t("project" + record.status),
    },
    {
      header: t("paymentStatus"),
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "paymentStatus",
      columnKey: "paymentStatus",
      render: (record) => t(record.paymentStatus),
    },
    {
      width: 20,
      header: t("createdAt"),
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "createdAt",
      render: (record: Project) => {
        return unixToFullDate(record.createdAt);
      },
    },
  ];

  const getTitleMoney = (id: number) => {
    if (id && Array.isArray(products)) {
      const product = products.find((item: any) => item.id === id);
      if (product)
        return t("moneyTitle", {
          name: product?.name,
          money: formatVND((product.price / product.numReview).toFixed(1)),
          total: product.maxLengthCharReview,
        });
    }
    return "";
  };

  const fetchByStatus = (value?: ProjectStatus) => {
    if (selected !== value) {
      setSelected(value);
      queryProject.page = 1;
      queryProject.status = value ?? "";
      queryProject.paymentStatus = "";
      setQueryProject({ ...queryProject });
      fetchProject({ ...queryProject });
      fetchSummary();
    }
  };

  const fetchByPaymentStatus = (value?: PaymentStatus) => {
    if (selected !== value) {
      setSelected(value ?? "");
      queryProject.page = 1;
      queryProject.paymentStatus = value ?? "";
      queryProject.status = "";
      setQueryProject({ ...queryProject });
      fetchProject({ ...queryProject });
      fetchSummary();
    }
  };


  return (
    <div>
      <PanelGroup
        fetchByStatus={fetchByStatus}
        fetchByPaymentStatus={fetchByPaymentStatus}
      />
      <div className="md:sticky top-[58px] md:top-[70px] rounded-md z-10 bg-white my-[24px]">
        {mdDown ? (
          <Collapse>
            <Collapse.Panel header={t("filter")} key="1">
              <FilterContent
                queryProject={queryProject}
                setQueryProject={setQueryProject}
                fetchProject={fetchProject}
                fetchSummary={fetchSummary}
                debounceSearchCustomer={debounceSearchCustomer}
                customers={customers}
                handleExport={handleExport}
                exportColumns={exportColumns}
                projectApi={projectApi}
              />
            </Collapse.Panel>
          </Collapse>
        ) : (
          <FilterContent
            queryProject={queryProject}
            setQueryProject={setQueryProject}
            fetchProject={fetchProject}
            fetchSummary={fetchSummary}
            debounceSearchCustomer={debounceSearchCustomer}
            customers={customers}
            handleExport={handleExport}
            exportColumns={exportColumns}
            projectApi={projectApi}
          />
        )}
      </div>

      {mdDown ? (
        <>
          <div className="space-y-4">
            {projects.length === 0 ? (
              <div className="flex flex-col gap-4 items-center my-10">
                <img src={empty3Img} className="w-[178px] h-[184px]" />
                <div className="text-[18px] font-semibold text-[#2A3547]">
                  {t("noProjectYet")}
                </div>
                <div className="w-[300px] text-[12px] text-[#5A6A85]">
                  {t("noProjectYet2")}
                </div>
                <MuiButton
                  variant="contained"
                  onClick={() => {
                    navigate("/project/project-create");
                  }}
                >
                  {t("projectCreate")}
                </MuiButton>
              </div>
            ) : (
              projects.map((record: Project, index: number) => (
                <div
                  key={record.id}
                  className="bg-white border rounded-lg p-4 shadow-sm"
                >
                  {/* Header */}

                  <div className="font-semibold text-[#2A3547] mb-4 text-[16px]">
                    {record.name}
                  </div>
                  <div className="flex gap-1 justify-center items-center">
                    <div className="flex flex-col gap-1 w-full">
                      <div className="flex justify-between items-start">
                        <div className="flex-1 items-center">
                          <div className="text-[#5A6A85] text-xs truncate flex">
                            <div className="w-[16px]">
                              <IconlyPro />
                            </div>
                            <div className="pt-0.5 pl-1">{record.code}</div>
                          </div>
                        </div>
                        <div className="flex gap-1 ml-2">
                          <ProjectStatusComp status={record.status} />
                        </div>
                      </div>

                      <div className="text-[#5A6A85] text-xs truncate mt-1 flex">
                        <div className="w-[16px]">
                          <FolderSvg />
                        </div>
                        <div className="pt-0.5 pl-1">
                          {getTitleMoney(record?.product?.id)}
                        </div>
                      </div>
                      <div className="text-[#5A6A85] text-xs truncate mt-1 flex">
                        <div className="w-[16px]">
                          <CreditSvg />
                        </div>
                        <div className="pt-0.5 pl-1">
                          {formatVND(record.moneyFinal)}₫
                        </div>
                      </div>
                      <div className="text-[#5A6A85] text-xs truncate mt-1 flex">
                        <div className="w-[16px]">
                          <LocationSvg />
                        </div>
                        <div className="pt-0.5 pl-1">{record.address}</div>
                      </div>
                      {record.paymentStatus !== PaymentStatus.Pending && (
                        <div className="text-[#5A6A85] text-md truncate mt-1">
                          {t("progress")} (
                          {record.quantityReview - record.totalContentEmpty}/
                          {record.quantityReview})
                        </div>
                      )}
                      {/* Progress */}
                      <div className="mb-1">
                        {record.paymentStatus === PaymentStatus.Pending ? (
                          <div className="text-xs">
                            <Typography
                              // color={
                              //   record.totalContentEmpty === 0
                              //     ? "green"
                              //     : "error"
                              // }
                              color="#12B76A"
                              variant="caption"
                              fontSize={14}
                            >
                              {t("createdContent")}:{" "}
                              {record.quantityReview - record.totalContentEmpty}
                              /{record.quantityReview}
                            </Typography>
                          </div>
                        ) : (
                          <div>
                            <Progress
                              percent={record.completePercent}
                              size="small"
                              status="active"
                            />
                          </div>
                        )}
                      </div>

                      <div className="flex justify-between items-start mb-1">
                        <div className="flex-1">
                          <div className="text-[#5A6A85] text-xs truncate">
                            {t("paymentStatus")}
                          </div>
                        </div>
                        <div className="flex gap-1">
                          <PaymentStatusComp status={record.paymentStatus} />
                        </div>
                      </div>

                      <div className="flex items-center">
                        <div className="flex-1 min-w-0">
                          <div className="text-[#5A6A85] text-xs truncate">
                            {t("createdAt")}
                            <br />
                            <span>{unixToFullDate(record.createdAt)}</span>
                          </div>
                        </div>
                        <div className="w-[50px] flex items-center justify-center">
                          <InputSeparator />
                        </div>

                        <div className="flex-1 min-w-0">
                          <div className="text-[#5A6A85] text-xs truncate">
                            {t("completedAt")}
                            <br />
                            <span>
                              {unixToFullDate((record as any).completedAt)}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex gap-2 justify-between mt-2">
                    {/* Detail Button - Always visible */}
                    {record.paymentStatus === PaymentStatus.Complete && (
                      <Button
                        size="small"
                        type="primary"
                        onClick={() => {
                          setSearchParams(`projectId=${record.id}`);
                        }}
                        // style={{ww}}
                        className="flex-1 h-[36px]"
                      >
                        {t("detail")}
                      </Button>
                    )}
                    {record.paymentStatus === PaymentStatus.Pending && (
                      <div className="flex gap-2 w-full">
                        <Button
                          size="small"
                          type="primary"
                          onClick={() => {
                            setSearchParams(`projectId=${record.id}`);
                          }}
                          // style={{ww}}
                          className="flex-2 h-[36px] w-1/2"
                        >
                          {t("detail")}
                        </Button>
                        <Popconfirm
                          title={t("confirm?")}
                          onConfirm={() => handlePayAgain(record.id)}
                          okText={t("yes")}
                          cancelText={t("no")}
                        >
                          <Button
                            disabled={record.totalContentEmpty !== 0}
                            size="small"
                            className="h-[36px] w-1/2 text-orange-500 border border-orange-300 bg-orange-50 hover:bg-orange-100 rounded-lg font-semibold"
                            color="orange"
                          >
                            {t("payAgain")}
                          </Button>
                        </Popconfirm>
                      </div>
                    )}

                    {/* Warranty Actions */}
                    {record.status === ProjectStatus.RequestWarranty && (
                      <>
                        <Popconfirm
                          title={t("confirm?")}
                          onConfirm={() => handleWarrantyProject(record.id)}
                          okText={t("save")}
                          cancelText={t("close")}
                        >
                          <Button
                            size="small"
                            type="primary"
                            className="flex-1"
                          >
                            {t("approveWarranty")}
                          </Button>
                        </Popconfirm>
                        <Popconfirm
                          title={t("confirm?")}
                          onConfirm={() =>
                            handleRejectWarrantyProject(record.id)
                          }
                          okText={t("save")}
                          cancelText={t("close")}
                        >
                          <Button size="small" danger className="flex-1">
                            {t("rejectWarranty")}
                          </Button>
                        </Popconfirm>
                      </>
                    )}

                    {/* Pause/Resume Actions */}
                    <div className="flex gap-1 f-ull">
                      {record.status !== ProjectStatus.Completed &&
                        record.status == ProjectStatus.Processing && (
                          <Popconfirm
                            title={t("actionConfirm")}
                            onConfirm={() =>
                              handlePauseProject(record.id, true)
                            }
                          >
                            <Button
                              size="small"
                              loading={loadingPause}
                              danger
                              className="w-[36px] !p-0 h-[36px]"
                            >
                              <FiPause />
                            </Button>
                          </Popconfirm>
                        )}

                      {record.status !== ProjectStatus.Completed &&
                        record.status == ProjectStatus.Paused && (
                          <Popconfirm
                            title={t("actionConfirm")}
                            onConfirm={() =>
                              handlePauseProject(record.id, false)
                            }
                          >
                            <Button
                              size="small"
                              loading={loadingPause}
                              className="w-[36px] !p-0 h-[36px] border-none"
                            >
                              <PlayIcon />
                            </Button>
                          </Popconfirm>
                        )}

                      {record.status !== ProjectStatus.Completed &&
                        record.paymentStatus == PaymentStatus.Pending && (
                          <>
                            <Popconfirm
                              title={t("actionConfirm")}
                              onConfirm={() => handleCancelProject(record.id)}
                            >
                              <Button
                                size="small"
                                danger
                                loading={loadingCancel}
                                className="w-[36px] !p-0 h-[36px]"
                              >
                                <StopOutlined />
                              </Button>
                            </Popconfirm>
                          </>
                        )}
                    </div>

                    {/* Cancel Action */}
                  </div>
                </div>
              ))
            )}

            {/* Load More Ref */}
            <div ref={loadMoreRef} style={{ height: 1 }} />
          </div>
        </>
      ) : (
        <AriviTable
          pagination={false}
          rowKey="id"
          dataSource={projects}
          scroll={{
            x: "max-content",
            y: projects.length > 0 ? "calc(100vh - 320px)" : undefined,
          }}
          loading={loadingProject}
          locale={{
            emptyText: (
              <div className="flex flex-col gap-4 items-center my-10">
                <img src={empty3Img} className="w-[178px] h-[184px]" />
                <div className="text-[18px] font-semibold text-[#2A3547]">
                  {t("noProjectYet")}
                </div>
                <div className="w-[300px] text-[12px] text-[#5A6A85]">
                  {t("noProjectYet2")}
                </div>
                <MuiButton
                  variant="contained"
                  onClick={() => {
                    navigate("/project/project-create");
                  }}
                  className="hover:opacity-80 transition-all"
                >
                  {t("projectCreate")}
                </MuiButton>
              </div>
            ),
          }}
          components={{
            body: {
              wrapper: (props: any) => (
                <>
                  <tbody {...props} />
                  <tr>
                    <td colSpan={1}>
                      <div ref={loadMoreRef} style={{ height: 1 }} />
                    </td>
                  </tr>
                </>
              ),
            },
          }}
        >
          <Column
            width={60}
            title={t("stt")}
            dataIndex={"stt"}
            align="center"
            render={(_, __, index) => {
              return index + 1;
            }}
          />
          <Column
            width={300}
            title={t("info")}
            dataIndex="info"
            key="info"
            render={(_, record: Project) => {
              return (
                <>
                  <div className="whitespace-pre-wrap">
                    <strong>{t("code")}</strong> : {record.code}
                  </div>
                  <div className="whitespace-pre-wrap">
                    <strong>{t("projectName")}</strong> : {record.name}
                  </div>
                  <div className="whitespace-pre-wrap">
                    <strong>{t("customer")}</strong>: <label
                      className="text-primary hover:cursor-pointer"
                      onClick={() => {
                        console.log("record.customer", record);
                        customerModal.current?.handleUpdate(record.customer as any)
                      }}>
                      {record.customer?.name}
                    </label>
                  </div>

                  <div className="whitespace-pre-wrap">
                    <strong>{t("package")}</strong>: {record.product?.name}
                  </div>
                  <div className="whitespace-pre-wrap">
                    <strong>{t("address")}</strong>: {record.address}
                  </div>
                </>
              );
            }}
          />
          <Column
            width={200}
            title={t("feature")}
            dataIndex="feature"
            key="feature"
            render={(_, record: Project) => {
              const currProduct = record.product;
              let estimateDayForSlowDrop = 0;
              if (currProduct) {
                estimateDayForSlowDrop =
                  currProduct.numReview / (record?.dropSlowNumber || 1);
              }

              return (
                <>
                  <Typography variant="body1">
                    <b>{t("slowDrop")}:</b>{" "}
                    {record.isDropSlow ? t("yes") : t("no")}
                  </Typography>
                  {record.isDropSlow && (
                    <>
                      <Typography variant="body1">
                        <b>{t("slowDropPerDay")}:</b>{" "}
                        {formatVND(record.dropSlowNumber)}
                      </Typography>
                      <Typography variant="body1">
                        {t("slowDropEstimate", {
                          number: Math.ceil(estimateDayForSlowDrop),
                        })}
                      </Typography>
                    </>
                  )}
                  <Divider className="!my-2" />
                  <Typography variant="body1">
                    <b>{t("image")}:</b>{" "}
                    {record.fileAttaches?.length > 0
                      ? `${record.fileAttaches?.length}`
                      : t("no")}
                  </Typography>
                </>
              );
            }}
          />
          <Column
            width={210}
            title={t("progress")}
            dataIndex="name"
            key="title"
            render={(_, record: Project) => {
              return (
                <div>
                  {record.paymentStatus === PaymentStatus.Pending ? (
                    <div>
                      <Typography
                        // color={
                        //   record.totalContentEmpty === 0 ? "green" : "error"
                        // }
                        color="#12B76A"
                      >
                        {t("createdContent")}:{" "}
                        {record.quantityReview - record.totalContentEmpty}/
                        {record.quantityReview}
                      </Typography>
                    </div>
                  ) : (
                    <div>
                      <Progress
                        percent={record.completePercent}
                        size="default"
                        status="active"
                      />
                      <div className="text-xs text-gray-500 mt-1 text-center">
                        {record.currentCompleteReview}/{record.quantityReview}
                      </div>
                    </div>
                  )}
                </div>
              );
            }}
          />

          <Column
            width={140}
            align="center"
            title={t("status")}
            dataIndex="status"
            key="status"
            render={(status: ProjectStatus, record: Project) => {
              return (
                <>
                  <ProjectStatusComp status={status} />
                </>
              );
            }}
          />
          <Column
            align="center"
            title={t("checkout")}
            dataIndex="paymentStatus"
            key="paymentStatus"
            render={(paymentStatus: PaymentStatus, record: Project) => {
              return (
                <>
                  <PaymentStatusComp status={paymentStatus} />
                  <br />
                  {paymentStatus == PaymentStatus.Complete && (
                    <>
                      <Typography variant="caption">
                        <b>{t("fee")}:</b> {formatVND(record.moneyFinal)}₫
                      </Typography>
                      <br />
                      <span className="text-primary hover:cursor-pointer" onClick={() => {
                        // @ts-ignore
                        setInfoInvoice({ ...record })
                        setInvoice(true);

                      }}>{t("invoice")}</span>
                    </>
                  )}
                </>
              );
            }}
          />
          <Column
            title={t("createdAt")}
            dataIndex="createdAt"
            key="createdAt"
            render={(createdAt) => {
              return unixToFullDate(createdAt);
            }}
          />
          <Column
            width={170}
            title={t("completedAt")}
            dataIndex="completedAt"
            key="completedAt"
            render={(completedAt) => {
              return unixToFullDate(completedAt);
            }}
          />
          <Column
            width={100}
            title={t("action")}
            key="action"
            fixed="right"
            render={(text, record: Project) => {
              return (
                <Stack gap={1}>
                  <Tooltip title={t("detail")}>
                    <Button
                      size="small"
                      type="primary"
                      onClick={() => {
                        setSearchParams(`projectId=${record.id}`);
                      }}
                      className={`${isMd ? "" : "!w-[50px] mx-auto"}`}
                    >
                      {isMd ? (
                        <span className="text-normal-semibold">
                          {t("detail")}
                        </span>
                      ) : (
                        <FiEdit />
                      )}
                    </Button>
                  </Tooltip>
                  {record.status !== ProjectStatus.Completed &&
                    record.status == ProjectStatus.Processing && (
                      <Popconfirm
                        title={t("actionConfirm")}
                        onConfirm={() => handlePauseProject(record.id, true)}
                      >
                        <Tooltip title={t("projectPAUSE")}>
                          <Button
                            size="small"
                            loading={loadingPause}
                            danger
                            className={`${isMd ? "" : "!w-[50px] mx-auto"}`}
                          >
                            {isMd ? (
                              <span className="text-normal-semibold">
                                {t("PAUSE")}
                              </span>
                            ) : (
                              <FiPause />
                            )}
                          </Button>
                        </Tooltip>
                      </Popconfirm>
                    )}
                  {record.status !== ProjectStatus.Completed &&
                    record.status == ProjectStatus.Paused && (
                      <Popconfirm
                        title={t("actionConfirm")}
                        onConfirm={() => handlePauseProject(record.id, false)}
                      >
                        <Tooltip title={t("continue")}>
                          <Button
                            size="small"
                            loading={loadingPause}
                            className={`btn-outline-blue ${isMd ? "" : "!w-[50px] mx-auto"
                              }`}
                          >
                            {isMd ? (
                              <span className="text-normal-semibold">
                                {t("continue")}
                              </span>
                            ) : (
                              <IoPlayOutline />
                            )}
                          </Button>
                        </Tooltip>
                      </Popconfirm>
                    )}
                  {record.status == ProjectStatus.RequestWarranty && (
                    <>
                      <Popconfirm
                        title={t("confirm?")}
                        onConfirm={() => handleWarrantyProject(record.id)}
                        okText={t("save")}
                        cancelText={t("close")}
                      >
                        <Button size="small" type="primary">
                          {t("approveWarranty")}
                        </Button>
                      </Popconfirm>
                      <Popconfirm
                        title={t("confirm?")}
                        onConfirm={() => handleRejectWarrantyProject(record.id)}
                        okText={t("save")}
                        cancelText={t("close")}
                      >
                        <Button size="small">{t("rejectWarranty")}</Button>
                      </Popconfirm>
                    </>
                  )}
                  {record.paymentStatus !== PaymentStatus.Complete &&
                    record.paymentStatus == PaymentStatus.Pending && (
                      <>
                        {/* <Tooltip title={t("payment")}>
                          <Button
                            disabled={
                              Math.max(0, record.totalContentEmpty) !== 0
                            }
                            size="small"
                            className={`btn-payment ${
                              isMd ? "" : "!w-[50px] mx-auto"
                            }`}
                            onClick={() => {
                              paymentModalRef.current?.handleOpen(record);
                            }}
                          >
                            {isMd ? (
                              t("payment")
                            ) : (
                              <MdOutlineAttachMoney size={20} />
                            )}
                          </Button>
                        </Tooltip> */}
                        <Popconfirm
                          title={t("actionConfirm")}
                          onConfirm={() => handleCancelProject(record.id)}
                        >
                          <Tooltip title={t("delete")}>
                            <Button
                              size="small"
                              danger
                              loading={loadingCancel}
                              className={`${isMd ? "" : "!w-[50px] mx-auto"}`}
                            >
                              {isMd ? (
                                <span className="text-normal-semibold">
                                  {t("delete")}
                                </span>
                              ) : (
                                <FiTrash />
                              )}
                            </Button>
                          </Tooltip>
                        </Popconfirm>
                      </>
                    )}
                </Stack>
              );
            }}
          />
        </AriviTable>
      )}
      {/* <Pagination
        defaultPageSize={queryProject.limit}
        currentPage={queryProject.page}
        total={totalProject}
        onChange={({ limit, page }) => {
          queryProject.page = page;
          queryProject.limit = limit;
          setQueryProject({ ...queryProject });
          fetchProject();
          fetchSummary();
        }}
      /> */}

      {/* <div ref={loadMoreRef} style={{ textAlign: "center", padding: "20px" }}>
      </div> */}

      <ProjectModal
        onSubmitOk={() => {
          fetchProject();
          fetchSummary();
        }}
        onClose={() => {
          if (queryProject.page == 1) {
            fetchProject();
            fetchSummary();
          } else {
            fetchProject({
              ...queryProject,
              ...{ page: 1, limit: queryProject.page * 10 },
            });
            fetchSummary();
          }
          setSearchParams("");
        }}
        ref={modalRef}
      />

      <CustomerModal
        onClose={() => { }}
        onSubmit={() => { }}
        ref={customerModal}
      ></CustomerModal>

      <Modal
        className="my-custom-modal"
        onCancel={() => {
          setInvoice(false);
        }}
        // closeIcon={false}
        centered
        open={invoice}
        title={""}
        style={{ top: 20 }}
        width={540}
        cancelText={t("close")}
        cancelButtonProps={{
          className: "hidden",
        }}
        okButtonProps={{
          className: "hidden",
        }}
      >
        <div className="mb-20">
          <Invoice data={infoInvoice as any} />
        </div>

      </Modal>
    </div>
  );
};

export default observer(ProjectListPage);
