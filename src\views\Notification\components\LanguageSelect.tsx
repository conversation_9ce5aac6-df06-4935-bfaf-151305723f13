import React, { useState } from "react";
import {
  Avatar,
  IconButton,
  Menu,
  MenuItem,
  Stack,
  Typography,
} from "@mui/material";
import { langResources } from "config-translation";

interface LanguageSelectorProps {
  value: string; // current language code, e.g. 'en'
  onChange: (lang: string) => void;
}

export const LanguageSelector: React.FC<LanguageSelectorProps> = ({
  value,
  onChange
}) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  const currentLang =
    langResources[value.toLowerCase() as keyof typeof langResources] ||
    langResources.en;

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleSelectLang = (lang: string) => {
    onChange(lang);
    handleClose();
  };

  return (
    <div style={{ borderColor: "#E94134" }}>
      <IconButton
        onClick={handleClick}
        sx={{
          gap: "8px",
          border: "1px solid #EAEFF4",
          borderRadius: "100px",

        }}
      >
        <Avatar
          src={currentLang.icon}
          alt={currentLang.value}
          sx={{ width: 20, height: 20 }}
        />
        <Typography variant="body2" className="hidden md:block">
          {currentLang.text}
        </Typography>

      </IconButton>

      <Menu
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        sx={{ "& .MuiMenu-paper": { width: 200 } }}
      >
        {Object.values(langResources).map((lang) => (
          <MenuItem
            key={lang.value}
            onClick={() => handleSelectLang(lang.value)}
            sx={{ py: 2, px: 3 }}
          >
            <Stack direction="row" spacing={1} alignItems="center">
              <Avatar src={lang.icon} alt={lang.value} sx={{ width: 20, height: 20 }} />
              <Typography variant="body2">{lang.text}</Typography>
            </Stack>
          </MenuItem>
        ))}
      </Menu>

    </div>
  );
};
