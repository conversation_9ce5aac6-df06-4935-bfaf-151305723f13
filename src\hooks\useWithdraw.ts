import { withdrawApi } from "api/withdraw.api";
import { useState } from "react";
import { Withdraw } from "types/withdraw";
import { QueryParam } from "types/query";

export interface WithdrawQuery extends QueryParam {}

interface UseWithdrawProps {
  initQuery: WithdrawQuery;
}

export const useWithdraw = ({ initQuery }: UseWithdrawProps) => {
  const [data, setData] = useState<Withdraw[]>([]);
  const [total, setTotal] = useState(0);
  const [query, setQuery] = useState<WithdrawQuery>(initQuery);
  const [loading, setLoading] = useState(false);

  const fetchData = async () => {
    setLoading(true);
    try {
      const { data } = await withdrawApi.findAll(query);

      setData(data.withdraws);
      setTotal(data.total);
    } finally {
      setLoading(false);
    }
  };

  return {
    withdraws: data,
    total,
    fetchData,
    loading,
    setQuery,
    query,
  };
};
