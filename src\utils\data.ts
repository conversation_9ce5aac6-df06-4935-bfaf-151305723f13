import { Dayjs } from "dayjs";
import { QueryParam } from "types/query";
import { MyTableColumn } from "./excel";
import React from "react";
import { onRangeChangeUnix } from "./dateFormat";

export const getRangerDateFilter = (dates: Dayjs[]) => {
  return {
    fromDate: dates[0].startOf("day").format("YYYY-MM-DD"),
    toDate: dates[1].endOf("day").format("YYYY-MM-DD"),
  };
};

export const getVisibleTableColumns = (columns: MyTableColumn[]) => {
  return columns.filter((c) => !c.hidden);
};

export const isObjectExist = (obj: Record<string, any>) => {
  return !!Object.keys(obj).length;
};

export const addBoldToStringAtIndex = ({
  matchs,
  str,
}: {
  str: string;
  matchs: {
    length: number;
    offset: number;
  }[];
}) => {
  let resultStr = str;

  const sortedMatches = [...matchs].sort((a, b) => b.offset - a.offset);

  for (const { offset, length } of sortedMatches) {
    if (offset < 0 || length <= 0 || offset + length > resultStr.length) {
      continue; // Skip invalid matches
    }

    resultStr =
      resultStr.slice(0, offset) +
      `<b>` +
      resultStr.slice(offset, offset + length) +
      `</b>` +
      resultStr.slice(offset + length);
  }

  return stringToJSX(resultStr);
};

let getNodes = (str: string) =>
  new DOMParser().parseFromString(str, "text/html").body.childNodes;
let createJSX = (nodeArray: any) => {
  return nodeArray.map((node: any) => {
    let attributeObj: any = {};
    const { attributes, localName, childNodes, nodeValue } = node;
    if (attributes) {
      Array.from(attributes).forEach((attribute: any) => {
        if (attribute.name === "style") {
          let styleAttributes = attribute.nodeValue.split(";");
          let styleObj: any = {};
          styleAttributes.forEach((attribute: any) => {
            let [key, value] = attribute.split(":");
            styleObj[key] = value;
          });
          attributeObj[attribute.name] = styleObj;
        } else {
          attributeObj[attribute.name] = attribute.nodeValue;
        }
      });
    }
    return localName
      ? React.createElement(
        localName,
        attributeObj,
        childNodes && Array.isArray(Array.from(childNodes))
          ? createJSX(Array.from(childNodes))
          : []
      )
      : nodeValue;
  });
};

export const stringToJSX = (domString: string) => {
  return createJSX(Array.from(getNodes(domString)));
};

export const handleChangeRangerDateFilter = ({
  query,
  dates,
  queryKey = { fromKey: "fromAt", toKey: "toAt" },
  isString = false,
  cb,
}: {
  query: QueryParam;
  dates: Dayjs[];
  queryKey?: { fromKey: string; toKey: string };
  isString?: boolean;
  cb?: (newQuery: QueryParam) => void;
}) => {
  const { fromKey, toKey } = queryKey;

  const newQuery: QueryParam = {
    ...query,
    page: 1,
    [fromKey]: undefined,
    [toKey]: undefined,
  };

  if (dates?.length === 2) {
    if (isString) {
      const { fromDate, toDate } = getRangerDateFilter(dates);
      newQuery[fromKey] = fromDate;
      newQuery[toKey] = toDate;
    } else {
      const [fromAt, toAt] = onRangeChangeUnix(dates);
      newQuery[fromKey] = fromAt;
      newQuery[toKey] = toAt;
    }
  }

  cb?.(newQuery);
};
