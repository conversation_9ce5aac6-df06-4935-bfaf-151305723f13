import {
  CloseOutlined,
  DownOutlined,
  EditFilled,
  ExportOutlined,
  KeyOutlined,
  LockOutlined,
  PlusOutlined,
  StopOutlined,
  UnlockOutlined,
} from "@ant-design/icons";
import {
  Avatar,
  Button,
  Card,
  Image,
  Input,
  Popconfirm,
  Space,
  Switch,
  Table,
  Tag,
  message,
} from "antd";
import { staffApi } from "api/staff.api";
import { Pagination } from "components/Pagination";
import { StaffQuery, useStaff } from "hooks/useStaff";
import { debounce } from "lodash";
import { observer } from "mobx-react-lite";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { ModalStatus } from "types/modal";
import { QueryParam } from "types/query";
import { QueryObject } from "types/queryObject";
import { Staff } from "types/staff";
import { getTitle } from "utils";
import { MyExcelColumn, handleExport } from "utils/MyExcel";
import { checkRoles } from "utils/auth";
import { unixToFullDate } from "utils/dateFormat";
import { ResetPasswordModal } from "./components/ResetPasswordModal";
import { StaffModal } from "./components/StaffModal";
// import { AreaSelector } from "components/AreaSelector/AreaSelector";
import DropdownCell from "components/Table/DropdownCell";
import { settings } from "settings";
import { VisibleStatusTrans } from "types/common";
import { TextField } from "@mui/material";
import { useTranslation } from "react-i18next";
import { AriviTable } from "components/Table/AriviTable";
import { ReactComponent as SearchIcon } from "assets/svgs/search-icon.svg";
import { ReactComponent as DownloadIcon } from "assets/svgs/download.svg";
const { Column } = Table;

export const StaffPage = observer(
  ({
    title = "",
    initQuery,
  }: {
    title?: string;
    initQuery?: QueryParam & {
      storeId: number;
    };
  }) => {
    const { fetchStaff, staffs, loadingStaff, deleteStaff, totalStaffs } =
      useStaff();
    const [queryStaff, setQueryStaff] = useState<StaffQuery>({
      page: 1,
      limit: 20,
    });
    const { t } = useTranslation();

    const exportColumns: MyExcelColumn<Staff>[] = [
      // {
      //   header: t("createdAt"),
      //   headingStyle: {
      //     font: {
      //       bold: true,
      //     },
      //   },
      //   key: "createdDateTime",
      //   columnKey: "createdDateTime",
      //   render: (record) => unixToFullDate(record.createdAt),
      // },
      {
        header: t("fullName"),
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "fullName",
        columnKey: "fullName",
        render: (record) => record.fullName,
      },
      {
        header: "Username",
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "username",
        columnKey: "username",
      },
      {
        header: "Email",
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "email",
        columnKey: "email",
        render: (record) => record.email,
      },
      {
        header: t("phoneNumber"),
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "phone",
        columnKey: "phone",
        render: (record) => record.phone,
      },

      {
        header: t("status"),
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "isBlocked",
        columnKey: "isBlocked",
        render: (record) => (record.isBlocked ? t("blocked") : t("open")),
      },
      {
        header: t("role"),
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "role",
        columnKey: "role",
        render: (record) => record.role.name,
      },
    ];
    const [visibleModal, setVisibleModal] = useState(false);
    const [selectedStaff, setSelectedStaff] = useState<Partial<Staff>>({});
    const [modalStatus, setModalStatus] = useState<ModalStatus>("create");
    const [loadingExport, setLoadingExport] = useState(false);
    const [visibleResetPasswordModal, setVisibleResetPasswordModal] =
      useState(false);

    useEffect(() => {
      document.title = getTitle(t(title));
    }, []);

    useEffect(() => {
      fetchStaff(queryStaff);
    }, [queryStaff]);

    const roles = useMemo(
      () =>
        checkRoles<{
          block: string;
          resetPassword: string;
        }>({
          create: "role-create-staff",
          detail: "role-detail-staff",
          block: "role-block-staff",
          resetPassword: "role-reset-pass-staff",
          update: "role-update-staff",
          delete: "role-delete-staff",
        }),
      []
    );

    const blockStaff = async (staff: Staff) => {
      await staffApi.block(staff.id, {
        isBlocked: !staff.isBlocked,
      });

      message.success(
        `${staff.isBlocked ? "Mở khóa" : "Khóa"} người dùng thành công!`
      );
      fetchStaff(queryStaff);
    };

    const handleTableChange = (
      newPagination: any,
      filters: any,
      sorter: any,
      extra: any
    ) => {
      let queryObject: QueryObject[] = [];

      if (filters["notification.type"]) {
        queryObject.push({
          type: "multi-filter",
          field: `notification.type`,
          value: filters["notification.type"],
        });
      }

      if (sorter.order) {
        queryObject.push({
          type: "sort",
          field: `${sorter.columnKey}`,
          value: sorter.order == "ascend" ? "ASC" : "DESC",
        });
      }

      setQueryStaff({
        ...queryStaff,
        queryObject: JSON.stringify(queryObject),
      });
    };

    const debounceSearch = useCallback(
      debounce(
        (keyword) => setQueryStaff({ ...queryStaff, search: keyword, page: 1 }),
        300
      ),
      []
    );

    // const handleExport = async () => {
    //   setLoadingExport(true);
    //   const { data } = await staffApi.findAll({
    //     ...queryStaff,
    //     page: 1,
    //     limit: 50,
    //   });

    //   let columns = [...exportColumns];

    //   import("../../utils/MyExcel").then((file) => {
    //     return file.MyExcel.export<Staff>({
    //       sheetName: `Danh sách nhân viên`,
    //       columns: columns,
    //       fileName: `Danh sách nhân viên`,
    //       fileType: "xlsx",
    //       data: data.staffs,
    //     });
    //   });

    //   setTimeout(() => {
    //     setLoadingExport(false);
    //   }, 500);
    // };

    const toggleVisible = async (id: number, visibleOnWeb?: boolean) => {
      await staffApi.update(id, { staff: { visibleOnWeb } });
      message.success("Thao tác thành công");
      fetchStaff(queryStaff);
    };

    const handleSearch = (search: string) => {
      queryStaff.search = search;
      queryStaff.page = 1;
      fetchStaff(queryStaff);
    };

    return (
      // <Card bodyStyle={{ padding: "8px 20px" }} style={{ borderRadius: "8px" }}>
      <div>
        <div className="filter-container">
          <Space>
            <div className="filter-item">
              {/* <label htmlFor="">Tìm kiếm</label> */}
              {/* <TextField
                // allowClear
                onKeyDown={(ev) => {
                  if (ev.code == "Enter") {
                    setQueryStaff({ ...queryStaff });
                  }
                }}
                onChange={(e) => {
                  queryStaff.search = e.target.value;

                  debounceSearch(e.target.value);
                }}
                size="small"
                label={t("nameAndPhone")}
              /> */}
              <label htmlFor="">{t("search")}</label>
              <Input.Search
                allowClear
                onChange={(e) => {
                  queryStaff.search = e.target.value;
                  queryStaff.page = 1;
                  // debounceSearch(e.target.value);
                }}
                onKeyDown={(ev) => {
                  if (ev.code == "Enter") {
                    // setQueryStaff({ ...queryStaff });
                    // fetchStaff(queryStaff);
                  }
                }}
                size="large"
                // placeholder={t("nameAndPhone")}
                className="w-full search-btn mt-1"
                enterButton={<SearchIcon onClick={() => {}} />}
                onSearch={handleSearch}
              />
            </div>
            {/* <AreaSelector
              onChange={(areaId) =>
                setQueryStaff({ ...queryStaff, areaId, page: 1 })
              }
            /> */}
            {
              <div className="filter-item btn">
                <Button
                  size="large"
                  onClick={() => {
                    setVisibleModal(true);
                    setModalStatus("create");
                    setSelectedStaff({});
                  }}
                  type="primary"
                  icon={<PlusOutlined />}
                >
                  {t("create")}
                </Button>
              </div>
            }
            {
              <div className="filter-item btn">
                <Popconfirm
                  title={t("exportAsk")}
                  onConfirm={() =>
                    handleExport({
                      onProgress(percent) {},
                      exportColumns,
                      fileType: "xlsx",
                      dataField: "staffs",
                      query: queryStaff,
                      api: staffApi.findAll,
                      fileName: t("staffList"),
                      sheetName: t("staffList"),
                    })
                  }
                  okText={t("exportExcel")}
                  cancelText={t("cancel")}
                >
                  <Button
                    size="large"
                    type="primary"
                    loading={false}
                    icon={<DownloadIcon />}
                  >
                    {t("exportExcel")}
                  </Button>
                </Popconfirm>
              </div>
            }
          </Space>
        </div>

        <AriviTable
          size="small"
          className="table-striped-rows"
          loading={loadingStaff}
          pagination={false}
          rowKey="id"
          dataSource={staffs}
          onChange={handleTableChange}
          scroll={{ x: "max-content", y: "calc(100vh - 380px)" }}
        >
          <Column
            title={t("fullName")}
            dataIndex="name"
            key="staff.name"
            render={(text, record: Staff) => (
              <>
                {record.isBlocked ? (
                  <>
                    <div className="flex items-center gap-2">
                      <Avatar
                        className="!w-8 !h-8"
                        src={record.avatar}
                        icon={
                          <Image
                            preview={false}
                            src={settings.defaultAvatar}
                            className="!w-8 !h-8"
                          />
                        }
                      />
                      <div>
                        <span style={{ color: "red" }}>{record?.fullName}</span>{" "}
                        <StopOutlined style={{ color: "red" }} />
                      </div>
                    </div>
                  </>
                ) : (
                  <div className="flex items-center gap-2">
                    <Avatar
                      src={record.avatar}
                      icon={
                        <Image
                          preview={false}
                          src={settings.defaultAvatar}
                          className="!w-8 !h-8"
                        />
                      }
                    />
                    <span>{record?.fullName}</span>
                  </div>
                )}
              </>
            )}
          />
          {/* <Column
            title="Ảnh profile"
            key="staff.profile"
            dataIndex="profile"
            render={(text, record: Staff) => (
              <>
                {record.profileImage ? (
                  <Image
                    src={record.profileImage}
                    className="object-cover !h-[40px] !w-[70px]"
                  />
                ) : (
                  <span className="text-red-500">Chưa cập nhật</span>
                )}
              </>
            )}
          /> */}
          <Column title="Username" dataIndex="username" key="staff.username" />
          <Column title="Email" dataIndex="email" key={"staff.email"} />
          <Column
            title={t("phoneNumber")}
            dataIndex="phone"
            key={"staff.phone"}
          />
          {/* <Column title="Chức vụ" dataIndex="position" key={"staff.position"} /> */}

          {/* <Column
            align="center"
            title="Ẩn hiện trên web"
            dataIndex="isVisible"
            key="isVisible"
            width={150}
            render={(text, record: Staff) => (
              <span style={{ color: "black" }}>
                <Popconfirm
                  onConfirm={async () => {
                    await toggleVisible(record.id, !record.visibleOnWeb);
                  }}
                  title="Bạn có chắc chắn muốn thực hiện thao tác này?"
                >
                  <Switch
                    checked={record.visibleOnWeb}
                    className="new-visible-"
                    checkedChildren={VisibleStatusTrans.true.label}
                    unCheckedChildren={VisibleStatusTrans.false.label}
                  ></Switch>
                </Popconfirm>

              </span>
            )}
          /> */}
          <Column
            width={150}
            align="center"
            title={t("status")}
            dataIndex="isBlocked"
            key={"staff.isBlocked"}
            render={(text, record: Staff) =>
              record?.isBlocked ? (
                <>
                  <Tag color="red">{t("locked")}</Tag>
                </>
              ) : (
                <>
                  <Tag color="green">{t("open")}</Tag>
                </>
              )
            }
          />
          {/* <Column
              key={"staff.area"}
              title="Khu vực"
              render={(text, record: Staff) => <span>{record.area?.name}</span>}
            /> */}
          <Column
            key={"staff.role"}
            title={t("role")}
            render={(text, record: Staff) => <span>{record.role?.name}</span>}
          />

          <Column
            fixed="right"
            width={120}
            align="center"
            title=""
            key="moneyCommission"
            dataIndex={"moneyCommission"}
            render={(text, record: Staff) => (
              //@ts-ignore
              <DropdownCell
                text={t("action")}
                items={[
                  {
                    onClick: () => "",
                    // createOrderModalRef.current?.handleUpdate(record),
                    label: (
                      <Button
                        icon={<EditFilled />}
                        type="primary"
                        className="w-full !font-medium"
                        onClick={() => {
                          setSelectedStaff(record);
                          setVisibleModal(true);
                          setModalStatus("update");
                        }}
                      >
                        {t("update")}
                      </Button>
                    ),
                    key: "update",
                  },
                  {
                    onClick: () => "",
                    // createOrderModalRef.current?.handleUpdate(record),
                    label: (
                      <Button
                        // block
                        // ghost
                        onClick={() => {
                          setSelectedStaff(record);
                          setVisibleResetPasswordModal(true);
                        }}
                        icon={<KeyOutlined />}
                      >
                        {t("resetPass")}
                      </Button>
                    ),
                    key: "reset",
                  },

                  {
                    label: (
                      <Popconfirm
                        placement="topLeft"
                        title={
                          <div>
                            <h1 className="text-sm">{t("confirm?")}</h1>
                          </div>
                        }
                        onConfirm={() => blockStaff(record)}
                        okText={t("yes")}
                        cancelText={t("no")}
                      >
                        <Button
                          icon={
                            record.isBlocked ? (
                              <UnlockOutlined />
                            ) : (
                              <LockOutlined />
                            )
                          }
                          // type="ghost"
                          className={`w-full !text-white ${
                            record.isBlocked ? "!bg-green-500" : "!bg-amber-500"
                          } !font-medium`}
                        >
                          {record.isBlocked ? t("unlock") : t("lock")}
                        </Button>
                      </Popconfirm>
                    ),
                    key: "blockStaff",
                    hidden: record.role.isAdmin,
                  },

                  {
                    label: (
                      <Popconfirm
                        placement="topLeft"
                        title={
                          <div>
                            <h1 className="text-sm">{t("confirm?")}</h1>
                          </div>
                        }
                        onConfirm={async () => {
                          // if (!reason) {
                          //   message.error("Vui lòng nhập lý do xóa khách hàng");
                          //   return;
                          // } else {
                          // }
                          await deleteStaff?.(record.id);
                          fetchStaff(queryStaff);
                        }}
                        okText={t("yes")}
                        cancelText={t("no")}
                      >
                        <Button
                          icon={<CloseOutlined />}
                          className="w-full !text-white !bg-red-500 !font-medium"
                        >
                          {t("delete")}
                        </Button>
                      </Popconfirm>
                    ),
                    key: "delete",
                  },
                  // {
                  //   label: (
                  //     <Popconfirm
                  //       placement="topLeft"
                  //       title={`Xác nhận xóa mã chứng khoán này?`}
                  //       onConfirm={() => onDelete(record.id)}
                  //       okText="Xác nhận"
                  //       cancelText="Không"
                  //     >
                  //       <Button
                  //         icon={<DeleteOutlined />}
                  //         className="w-full !text-white !bg-red-500 !font-medium"
                  //       >
                  //         Xóa mã
                  //       </Button>
                  //     </Popconfirm>
                  //   ),
                  //   key: "delete",
                  // },
                ]}
                trigger={["click"]}
              >
                <a onClick={(e) => e.preventDefault()}>
                  <Space>
                    {t("action")}
                    <DownOutlined />
                  </Space>
                </a>
              </DropdownCell>
              // <div className="flex flex-col gap-2">

              // </div>
            )}
          />

          {/* <Column
              width={200}
              title="Thao tác"
              key="action"
              render={(text, record: Staff) => (
                <span>
                  <Space direction="vertical">
                    <Button
                      icon={<ClockCircleFilled />}
                      block
                      type="primary"
                      onClick={() => {
                        loginLogRef.current?.handleOpen(record);
                      }}
                    >
                      Lịch sử đăng nhập
                    </Button>
                    {
                      <Button
                        icon={<EditOutlined />}
                        block
                        type="primary"
                        onClick={() => {
                          setSelectedStaff(record);
                          setVisibleModal(true);
                          setModalStatus("update");
                        }}
                      >
                        Cập nhật
                      </Button>
                    }
                    <Button
                      block
                      type="ghost"
                      onClick={() => {
                        setSelectedStaff(record);
                        setVisibleResetPasswordModal(true);
                      }}
                      icon={<KeyOutlined />}
                    >
                      Reset mật khẩu
                    </Button>

                    {record.id !== userStore.info.id && (
                      <Popconfirm
                        placement="topLeft"
                        title={`Xác nhận xóa nhân viên này?`}
                        onConfirm={async () => {
                          await deleteStaff(record.id);
                          fetchStaff(queryStaff);
                        }}
                        okText="Ok"
                        cancelText="Không"
                      >
                        <Button block danger icon={<DeleteFilled />}>
                          Xóa
                        </Button>
                      </Popconfirm>
                    )}

                    <Popconfirm
                      placement="topLeft"
                      title={`Xác nhận ${
                        record.isBlocked ? "Bỏ chặn" : "Chặn"
                      } nhân viên này?`}
                      onConfirm={() => blockStaff(record)}
                      okText="Ok"
                      cancelText="Không"
                    >
                      <Button
                        icon={<StopOutlined />}
                        block
                        type="primary"
                        danger
                      >
                        {record.isBlocked ? "Bỏ chặn" : "Chặn"}
                      </Button>
                    </Popconfirm>
                  </Space>
                </span>
              )}
            /> */}
        </AriviTable>

        {queryStaff.limit < totalStaffs && (
          <Pagination
            total={totalStaffs}
            currentPage={queryStaff.page}
            onChange={({ page, limit }) => {
              queryStaff.page = page;
              queryStaff.limit = limit;
              setQueryStaff({ ...queryStaff });
            }}
            defaultPageSize={queryStaff.limit}
          />
        )}

        <ResetPasswordModal
          onSubmitOk={() => {}}
          status={"create"}
          staffId={selectedStaff.id || 0}
          visible={visibleResetPasswordModal}
          onClose={() => {
            setVisibleResetPasswordModal(false);
          }}
        />

        <StaffModal
          onSubmitOk={() => fetchStaff(queryStaff)}
          onClose={() => setVisibleModal(false)}
          visible={visibleModal}
          staff={selectedStaff}
          status={modalStatus}
        />
      </div>
    );
  }
);
