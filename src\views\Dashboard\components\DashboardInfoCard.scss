.dashboard-info-card {
  border-radius: 8px;
  min-height: 136px;
  width: 100%;
  transition: all 0.1s ease-in;
  text-decoration: inherit;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 4px; // Tailwind: gap-2
  margin-left: auto;
  margin-right: auto;

  @media (min-width: 768px) {
    min-height: 155px;
  }

  &.clickable {
    cursor: pointer;
  }

  &__image {
    width: 48px;
    height: 48px;

    // @media (min-width: 768px) {
    //   width: 48px;
    //   height: 48px;
    // }
  }

  &__title {
    font-weight: 400;
    font-size: 12px;

    @media (min-width: 768px) {
      font-size: 14px;
      font-weight: 600;
    }
  }

  &__info {
    font-weight: 600;
    font-size: 16px;
    line-height: 120%;

    @media (min-width: 768px) {
      font-size: 24px;
    }
  }
}
