.ant-form-item-label label {
  font-weight: 500;
}

.ant-breadcrumb a {
  color: #e3e3e373 !important;
}

.sidebar {
  &.ant-menu {
    overflow-y: auto;
    overflow-x: hidden;
    padding: 0 12px;
    font-weight: 500;
    max-height: calc(100vh - 70px);
  }
}

.ant-menu-item,
.ant-menu-submenu {
  border-bottom: 1px solid #f0f0f0;
}

.ant-menu-item-only-child {
  border: none;
}

.ant-menu:not(.ant-menu-horizontal) .ant-menu-item-selected {
  background-color: #fdeeff !important;
  border-radius: 8px !important;
}

.ant-menu-item:active {
  border-radius: 8px !important;
}

//border input
.ant-select:not(.ant-select-customize-input) .ant-select-selector,
.ant-input,
.ant-picker,
.ant-input-number,
.ant-input-affix-wrapper {
  // border-radius: 8px !important;
}

.ant-breadcrumb-separator {
  color: #e3e3e373 !important;
}

.ant-breadcrumb>span:last-child {
  color: #fff !important;
}

// .ant-modal-content {
.ant-btn {
  // height: 40px !important;
  border-radius: 8px !important;

  &.ant-btn-lg {

    // height: 42px;
    .ant-btn-icon {
      display: flex;
      align-items: center;
    }
  }
}

.ant-layout-header {
  background: #fff !important;
}

.ant-layout-sider {
  background: #fff !important;
}

.ant-btn-primary {
  color: #fff;
  // background-color: #101928 !important;
  // border-color: #101928 !important;
  -webkit-transition: all 0.3s;
  font-weight: 700;
  font-size: 15px;
  padding: 0 15px;

  transition: all 0.3s;

  //` &:hover {
  //   background-color: #4f5b6e !important;
  //   border-color: #4f5b6e;
  //   color: white;
  // }
}

// .ant-form-item {
//   margin-bottom: 12px !important;
// }

.ant-tag {
  border-radius: 8px !important;
  margin-inline-end: 0; // Fix right margin of tag (on mobile)
  font-weight: bold;
}

.ant-modal {
  border-radius: 8px !important;
  overflow: hidden;
}

.ant-btn:hover,
.ant-btn:focus {
  border-color: transparent !important;
}

//upload
.ant-upload-wrapper .ant-upload-drag {
  background-color: var(--gray-5-color);
}

.ant-upload-picture-card-wrapper {
  text-align: center;
}

.campaign-modal {

  .ant-picker,
  .ant-input-number {
    width: 100%;
  }

  .ant-input-number-group-wrapper {
    width: 100%;
  }

  .add-product-btn {
    margin-bottom: 10px;
  }
}

//fix mask background action multiple upload
.ant-upload-list-picture-card .ant-upload-list-item-info::before {
  left: 0 !important;
}

.ant-upload-list-item svg {
  font-size: 18px;
}

.ant-upload-list-item .ant-upload-list-item-name {
  font-size: 16px;
}

.row-dragging {
  background: #fafafa;
  border: 1px solid #ccc;
  z-index: 9999;
}

.ant-descriptions-row>th,
.ant-descriptions-row>td {
  padding-bottom: 5px !important;
}

.row-dragging td {
  padding: 16px;
}

.row-dragging .drag-visible {
  visibility: visible;
}

.drag-icon {
  padding: 0 !important;
}

.ant-descriptions-header {
  margin-bottom: 5px !important;
}

.image-box {
  overflow: hidden;
  position: relative;
  max-height: 120px;
  box-shadow: rgba(0, 0, 0, 0.02) 0px 1px 3px 0px,
    rgba(27, 31, 35, 0.15) 0px 0px 0px 1px;
  border-radius: 7px;
  background-color: #fff;
  display: flex;
  justify-content: center;
  cursor: pointer;

  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -o-user-select: none;
  user-select: none;

  &:hover {
    .copy-wrapper {
      opacity: 1;
    }
  }

  .checkbox-absolute {
    position: absolute;
    right: 5px;
    top: 5px;
    z-index: 99;
  }

  .image-name {
    position: absolute;
    bottom: 0;
    z-index: 5;
    padding: 0 10px;
    width: 100%;
    background-color: rgba($color: #000000, $alpha: 0.7);
    margin-bottom: 0;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
    line-height: 200%;
    height: 30px;
    text-overflow: ellipsis;
    color: #fff;
  }

  img {
    height: 120px !important;
    margin: auto;
  }

  .copy-wrapper {
    opacity: 0;
    position: absolute;
    width: 100%;
    height: 100%;
    background-color: rgba($color: #fff, $alpha: 0.5);
    z-index: 9;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: 0.4s;
  }
}

.giftType-stats-table {
  tbody {
    display: none;
  }
}

.ant-select.readOnly {
  pointer-events: none;
}

// .ant-table-thead > tr > th {
//   background: #faf5ff !important;
// }

.ant-input[disabled] {
  color: black !important;
}

// .ant-table-cell {
//   padding: 4px 8px !important;
// }

// .ant-input-number-handler-wrap {
//   visibility: hidden;
// }

.custom-rate :where(.ant-rate-star) {
  margin-right: 2px !important;
}

.ant-form-vertical .ant-form-item-label>label {
  width: 100%;
  font-weight: bold;
  font-size: 0.875rem;
}

.ant-form-item-control-input-content {
  .ant-input-outlined {
    padding: 8px 10px;
  }
}

.ant-form {
  .ant-form-item {
    .ant-form-item-required {
      &::before {
        display: none !important;
      }

      &::after {
        display: inline-block;
        margin-inline-end: 4px;
        color: red !important;
        font-size: 14px;
        font-family: SimSun, sans-serif;
        line-height: 1;
        content: "*";
        visibility: visible !important;
      }
    }
  }
}

.create-project {
  .ant-form {
    .ant-form-item {
      .ant-form-item-required {
        &::before {
          display: none !important;
        }

        &::after {
          display: inline-block;
          margin-inline-end: 4px;
          color: red;
          font-size: 14px;
          font-family: SimSun, sans-serif;
          line-height: 1;
          content: "*";
          visibility: visible !important;
        }
      }
    }
  }
}

.ant-form-item-required {
  font-weight: 600 !important;
  font-size: 14px !important;
  line-height: 140% !important;
  letter-spacing: -0.14px !important;
  color: #2a3547 !important;
}

.ant-select-arrow {
  top: 50%;
  transform: translateY(-20%);
  right: 12px;

  width: 20px !important;
  height: 20px !important;

  background-image: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' width='20' height='20' fill='none'><path d='M5 7.5L10 12.5L15 7.5' stroke='%23BDC7D5' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/></svg>");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  position: absolute;

  &>.anticon {
    display: none;
  }
}

.ant-modal-root {
  // .ant-modal-mask,
  // .ant-modal-wrap {
  //   z-index: 1201;
  // }

  .ant-modal {
    .ant-btn {
      &.ant-btn-variant-outlined {
        border-color: #e94134;
        color: #e94134;

        &:hover {
          background-color: #e94134;
          color: white;
        }
      }
    }
  }
}

// .ant-select-dropdown,
// .ant-popover,
// .ant-tooltip {
//   z-index: 1202 !important;
// }

// .ant-image-preview-root {
//   .ant-image-preview-mask,
//   .ant-image-preview-wrap {
//     z-index: 1202 !important;
//   }
// }

.ant-popover {
  .ant-btn {
    &.ant-btn-variant-outlined {
      border-color: #fa896b;
      color: #fa896b;

      &:hover {
        background-color: #fa896b;
        color: white;
      }
    }
  }
}

.arivi-table {
  .ant-table-thead>tr>th {
    background: #ffffff !important;
  }

  .ant-table-cell {
    &::before {
      display: none;
    }

    &.ant-table-cell-fix-right {
      right: -2px !important;
    }
  }

  .ant-table {
    scrollbar-color: unset;

    .ant-table-body {

      // Scrollbar track
      &::-webkit-scrollbar {
        width: 6px;
        height: 6px;
      }

      &::-webkit-scrollbar-track {
        background: #f1f1f1; // Customize track background
        border-radius: 10px;
      }

      // Scrollbar thumb
      &::-webkit-scrollbar-thumb {
        background-color: #cecece; // Customize thumb color
        border-radius: 4px;
        transition: all 0.2s linear;
      }

      &::-webkit-scrollbar-thumb:hover {
        background-color: #818181; // On hover
      }
    }
  }
}

.arivi-pagination {
  justify-content: center;

  .ant-pagination-item {
    border-radius: 100%;

    &.ant-pagination-item-active {
      background-color: var(--color-primary);

      a {
        color: white;
      }
    }
  }
}

.large-switch {
  // transform: scale(1.3);
  // transform-origin: left center;
  display: inline-block;
  height: 28px;
  width: fit-content;

  &.light-color {
    .ant-switch-inner {
      background-color: #bdc7d5;

      .ant-switch-inner-checked,
      .ant-switch-inner-unchecked {
        color: #ffffff;
      }
    }
  }

  &.ant-switch-checked {
    .ant-switch-inner {
      background-color: unset;
      padding-inline-end: 28px;
      padding-inline-start: 6px;

      .ant-switch-inner-checked,
      .ant-switch-inner-unchecked {
        color: #fff;
      }
    }

    .ant-switch-handle {
      inset-inline-start: calc(100% - 26px);
    }

    .ant-switch-inner-unchecked {
      margin-inline-start: calc(100% - 22px + 52px) !important;
    }
  }

  .ant-switch-handle {
    width: 24px;
    height: 24px;

    &::before {
      border-radius: 100%;
    }
  }

  .ant-switch-inner {
    padding-inline-start: 30px;
    background-color: #bdc7d5;

    .ant-switch-inner-checked,
    .ant-switch-inner-unchecked {
      font-size: 14px;
    }

    .ant-switch-inner-checked {
      margin-top: 2px;
      margin-inline-end: calc(100% - 22px + 54px);
      margin-inline-start: calc(-100% + 22px - 54px);
    }
  }
}

.custom-form {
  .ant-form-item-with-help {
    .ant-form-item-margin-offset {
      margin-bottom: unset !important;
    }
  }

  .ant-form-item {
    &.auto-height {
      .ant-form-item-control-input {
        height: unset;
      }
    }

    .ant-form-item-control-input {
      height: 42px;
      gap: 16px;
      align-self: stretch;

      &:has(textarea) {
        height: unset;
      }
    }
  }
}

.custom-input-number {
  background-color: #eaeff4;
  width: 100%;

  &.ant-input-number-focused {}

  &.no-drop-slow {
    input {
      text-align: left !important;
      color: var(--gray-3-color) !important;
    }
  }

  .ant-input-number-handler-wrap {
    width: 22px !important;
    opacity: 1 !important;
    background-color: transparent;
    display: flex !important;
    min-height: 40px;
  }

  input {
    text-align: center !important;
  }
}

.search-btn-full {
  .ant-input-affix-wrapper {
    height: 56px !important;
    display: flex;
    align-items: center;
  }

  .ant-input {
    height: 56px !important;
    line-height: 56px !important;
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }

  .ant-btn {
    height: 56px !important;
  }
}

.tabs-hide .ant-tabs-nav {
  display: none;
}

.ant-btn-color-dangerous.ant-btn-variant-solid {
  background-color: #e94134;
}

.ant-btn-primary:disabled {
  color: #00000040 !important;
  background-color: #0000000a !important;
  opacity: 1 !important;
  border-color: #d9d9d9 !important;

  span {
    color: #00000040 !important;
  }
}

.address-modal .ant-modal-footer button {
  min-height: 42px;
}

.ant-input[disabled] {
  color: #8a8a8a !important;
}

.count-navbar {
  .MuiChip-label {
    padding-left: 6px;
    padding-right: 6px;
  }
}