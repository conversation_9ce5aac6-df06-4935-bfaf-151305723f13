import { DownOutlined } from "@ant-design/icons";
import { <PERSON><PERSON>, <PERSON>confirm, Space } from "antd";
import Column from "antd/lib/table/Column";
import { IPagination, Pagination } from "components/Pagination";
import { AriviTable } from "components/Table/AriviTable";
import DropdownCell from "components/Table/DropdownCell";
import { useTranslation } from "react-i18next";
import { GrEdit } from "react-icons/gr";
import { HiOutlineTrash } from "react-icons/hi2";
import { Tone } from "types/tone";
import { unixToFullDate } from "utils/dateFormat";

interface PropsType {
  dataSource: Tone[];
  loading: boolean;
  loadingDelete?: boolean;
  pagination?: IPagination;
  onEdit?: (tone: Tone) => void;
  onDelete?: (toneId: number) => void;
  onActive?: (toneId: number) => void;
  onInactive?: (toneId: number) => void;
}

export const ToneList = ({
  dataSource,
  loading,
  loadingDelete,
  pagination,
  onDelete,
  onEdit,
  onActive,
  onInactive,
}: PropsType) => {
  const { t } = useTranslation();

  return (
    <div>
      <AriviTable
        loading={loading}
        pagination={false}
        rowKey="id"
        dataSource={dataSource}
        size="small"
        className="custom-scrollbar"
        scroll={{ x: "max-content", y: "calc(100vh - 380px)" }}
      >
        <Column
          title={t("content")}
          dataIndex="name"
          key="name"
          render={(text, record: Tone) => <div>{record.name}</div>}
        />

        <Column
          title={t("createdAt")}
          dataIndex="createdAt"
          key="createdAt"
          render={(_, record: Tone) => (
            <span>{unixToFullDate(record.createdAt)}</span>
          )}
        />

        <Column
          fixed="right"
          width={120}
          align="center"
          title=""
          key="action"
          render={(text, record: Tone) => (
            //@ts-ignore
            <DropdownCell
              text={t("action")}
              items={[
                {
                  onClick: () => onEdit?.(record),
                  label: (
                    <Button
                      icon={<GrEdit />}
                      type="primary"
                      className="w-full justify-center !flex !items-center gap-2 !font-medium"
                    >
                      {t("update")}
                    </Button>
                  ),
                  key: "update",
                },
                {
                  label: (
                    <Popconfirm
                      placement="topLeft"
                      title={<h1 className="text-sm">{t("confirm?")}</h1>}
                      onConfirm={() => onDelete?.(record.id)}
                      okText={t("yes")}
                      cancelText={t("no")}
                    >
                      <Button
                        loading={loadingDelete}
                        icon={<HiOutlineTrash className="text-lg" />}
                        className="w-full justify-center !flex !items-center gap-2 !text-red-500 !font-medium"
                      >
                        {t("delete")}
                      </Button>
                    </Popconfirm>
                  ),
                  key: "delete",
                },
              ]}
              trigger={["click"]}
            >
              <a onClick={(e) => e.preventDefault()}>
                <Space>
                  {t("action")}
                  <DownOutlined />
                </Space>
              </a>
            </DropdownCell>
          )}
        />
      </AriviTable>
      {pagination && <Pagination {...pagination} />}
    </div>
  );
};
