import { DownOutlined, PlusOutlined, SearchOutlined } from "@ant-design/icons";
import {
  Button,
  Card,
  Checkbox,
  Col,
  Form,
  Input,
  Popconfirm,
  Row,
  Select,
  Space,
  Spin,
  Table,
  message,
} from "antd";
import { useEffect, useRef, useState } from "react";
import { formatVND, getTitle } from "utils";
import { MyTableColumn } from "utils/excel";
import { useForm } from "antd/es/form/Form";
import { usePartner } from "hooks/usePartner";
import { partnerApi } from "api/partner.api";
import { InputNumber } from "components/Input/InputNumber";
import { CustomerDeposit } from "types/customerDeposit";
import { Customer } from "types/customer";
import { customerDepositApi } from "api/customerDeposit.api";
import { CustomerSelector } from "components/Selector/CustomerSelector";
import { rules } from "utils/validate-rules";
import { AriviTable } from "components/Table/AriviTable";
import Column from "antd/es/table/Column";
import { useTranslation } from "react-i18next";
import { GrEdit } from "react-icons/gr";
import { HiOutlineTrash } from "react-icons/hi2";
import DropdownCell from "components/Table/DropdownCell";

const { TextArea } = Input;

export const CreateCustomerDepositPage = ({ title = "" }) => {
  const [form] = useForm();

  const [selectCustomer, setSelectCustomer] = useState<Customer>();
  const [loadingCustomer, setLoadingCustomer] = useState(false);
  const [loadingCreate, setLoadingCreate] = useState(false);
  const { t } = useTranslation();

  useEffect(() => {
    document.title = getTitle(t(title));
  }, []);

  const handleCreateCommand = async () => {
    setLoadingCreate(true);
    try {
      await form.validateFields();
      const formData = form.getFieldsValue();

      await customerDepositApi.create({
        ...formData,
      });

      message.success(t("actionSuccessfully"));
      form.resetFields();
      // handleSelectPartner(partnerId);
    } catch (error) {
    } finally {
      setLoadingCreate(false);
    }
  };

  const handleSelectCustomer = async (value: Customer) => {
    if (value) {
      try {
        setLoadingCustomer(true);
        // const { data } = await customerApi.findOne(value);
        setSelectCustomer(value);
        form.setFieldValue("customerId", value.id);
      } catch (error) {
      } finally {
        setLoadingCustomer(false);
      }
    } else {
      setSelectCustomer(undefined);
    }
  };

  return (
    <Card bodyStyle={{ padding: "8px 20px" }} style={{ borderRadius: "8px" }}>
      {" "}
      <div className="filter-container">
        <Space wrap>
          <div className="filter-item">
            <label htmlFor="">{t("selectCustomer")}</label>
            <br />
            <CustomerSelector
              // query={{ limit: 1000, page: 1, type: 0 }}
              onChange={handleSelectCustomer}
              valueIsOption
            />
          </div>

          {/* <div className="filter-item btn">
            <Button
              onClick={() => {
                modalRef.current?.handleCreate();
              }}
              type="primary"
              icon={<PlusOutlined />}
            >
              Tạo lệnh
            </Button>
          </div> */}
        </Space>
      </div>
      {/* <AriviTable
        pagination={false}
        rowKey="id"
        dataSource={selectCustomer ? [selectCustomer] : []}
        loading={loadingCustomer}
        scroll={{ x: "max-content" }}
      >
        <Column
          title={"Tên KH"}
          dataIndex="name"
          width={500}
          key={"name"}
          render={(text, record: Customer) => <div>{record.name}</div>}
        />

        <Column
          fixed="right"
          width={120}
          align="center"
          title=""
          key="action"
          dataIndex={""}
          render={(text, record: Customer) => (
            //@ts-ignore
            <DropdownCell
              text={t("action")}
              items={[
                {
                  onClick: () => "",
                  // createOrderModalRef.current?.handleUpdate(record),
                  label: (
                    <Button
                      icon={<GrEdit />}
                      type="primary"
                      className="w-full justify-center !flex !items-center gap-2 !font-medium"
                      // onClick={() => onEdit?.(record)}
                    >
                      {t("update")}
                    </Button>
                  ),
                  key: "update",
                  // hidden: !hasUpdateFaqPermission,
                },

                {
                  label: (
                    <Popconfirm
                      placement="topLeft"
                      title={
                        <div>
                          <h1 className="text-sm">{t("confirm?")}</h1>
                        </div>
                      }
                      // onConfirm={() => onDelete?.(record.id)}
                      okText={t("yes")}
                      cancelText={t("no")}
                    >
                      <Button
                        // loading={loadingDelete}
                        icon={<HiOutlineTrash className="text-lg" />}
                        className={`w-full justify-center !flex !items-center gap-2 !text-red-500 !font-medium`}
                      >
                        {t("delete")}
                      </Button>
                    </Popconfirm>
                  ),
                  key: "delete",
                  // hidden: !hasDeleteFaqPermission,
                },
              ]}
              trigger={["click"]}
            >
              <a onClick={(e) => e.preventDefault()}>
                <Space>
                  {t("action")}
                  <DownOutlined />
                </Space>
              </a>
            </DropdownCell>
          )}
        />
      </AriviTable> */}
      {/* <Pagination
          currentPage={query.page}
          total={total}
          defaultPageSize={query.limit}
          onChange={({ limit, page }) => {
            query.page = page;
            query.limit = limit;
            fetchData();
          }}
        /> */}
      {selectCustomer && (
        <div className="w-1/2 bg-white p-2 mt-2 rounded-[5px]">
          <div className="text-[16px] text-blue-700 font-bold uppercase">
            {t("createOrderFor")}: {selectCustomer.name || ""}
          </div>
          <br />
          <Form
            layout="vertical"
            form={form}
            validateTrigger={["onBlur", "onChange"]}
          >
            <Row gutter={16}>
              <Form.Item name="customerId" hidden>
                <Input placeholder="" />
              </Form.Item>
              {/* <Col span={8}>
              <Form.Item label="Chọn đối tác" name="LOGIN_NAME" rules={rules}>
                <Input placeholder="" disabled={status === "update"} />
              </Form.Item>
            </Col> */}
              <Col span={24}>
                <Form.Item
                  label={t("amountRequest")}
                  name="amount"
                  rules={rules}
                >
                  <InputNumber className="w-full" placeholder="" />
                </Form.Item>
              </Col>

              <Col span={24}>
                <Form.Item label={t("note")} name="note">
                  <TextArea placeholder="" rows={4} />
                </Form.Item>
              </Col>
            </Row>
          </Form>
          <div className="flex justify-end">
            <Button
              onClick={handleCreateCommand}
              type="primary"
              className=""
              loading={loadingCreate}
            >
              {t("create")}
            </Button>
          </div>
        </div>
      )}
    </Card>
  );
};
