.site-layout-background .anticon.trigger {
  color: var(--color-primary);
}

.ant-layout {
  .trigger {
    padding: 0 24px;
    font-size: 18px;
    line-height: 64px;
    cursor: pointer;
    transition: color 0.3s;

    &:hover {
      color: #1890ff;
    }
  }

  .logo {
    width: 100%;
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: center;

    // background: rgba(255, 255, 255, 0.3);

    img {
      width: 70px;
      border-radius: 5px;
    }
  }
}

// header
.ant-layout-header {
  position: fixed;
  width: calc(100% - 270px);
  top: 0;
  right: 0;
  z-index: 100;
  display: flex;
  justify-content: space-between;
  transition: width 0.2s;
  &.collapsed {
    width: calc(100% - 80px);
  }
}

// content
.ant-layout-content {
  margin-top: 64px !important;
  min-height: calc(100vh - 64px);

  overflow: auto;
}

// .ant-layout-sider {
//   flex: 0 0 250px !important;
//   max-width: 250px !important;
//   min-width: 250px !important;
//   width: 250px !important;
// }

// .ant-layout-sider-collapsed {
//   flex: 0 0 80px !important;
//   max-width: 80px !important;
//   min-width: 80px !important;
//   width: 80px !important;
// }

.site-layout {
  transition: margin-left 0.2s;
  // margin-left: 250px !important;
}

// sider
.ant-layout-sider {
  position: fixed;
  height: 100vh;
}

.ant-menu-root {
  li:first-child {
    margin-top: 0;
  }
}

@media screen and (max-width: 500px) {
  .mobile-breadcrumb-none {
    display: none !important;
  }
}
