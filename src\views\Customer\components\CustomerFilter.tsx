import { DatePicker, Input, Select, Popconfirm, Button, Space } from "antd";
import { useTranslation } from "react-i18next";
import { CustomerStatus } from "types/customer";
import { handleExport, MyExcelColumn } from "utils/MyExcel";
import { Customer } from "types/customer";
import { customerApi } from "api/customer.api";
import dayjs from "dayjs";
import { ReactComponent as DownloadIcon } from "assets/svgs/download.svg";
import { ReactComponent as SearchIcon } from "assets/svgs/search-icon.svg";

interface CustomerFilterProps {
  debounceSearchCustomer: (value: string) => void;
  queryCustomer: any;
  fetchCustomer: () => void;
  exportColumns: MyExcelColumn<Customer>[];
  hideBtnExportExcel?: boolean
}

export const CustomerFilter: React.FC<CustomerFilterProps> = ({
  debounceSearchCustomer,
  queryCustomer,
  fetchCustomer,
  exportColumns,
  hideBtnExportExcel = false
}) => {
  const { t } = useTranslation();

  const optionStatus = [
    { value: "ALL", label: t("ALL") },
    { value: CustomerStatus.Active, label: t(CustomerStatus.Active) },
    { value: CustomerStatus.Blocked, label: t(CustomerStatus.Blocked) },
  ];

  const handleSearch = (search: string) => {
    queryCustomer.search = search;
    queryCustomer.page = 1;
    fetchCustomer();
  };


  return (
    <div className="filter-container w-full">
      <div className="flex flex-wrap gap-3 items-end w-full !text-sm">
        {/* Search Input - Flexible width */}
        <div className="filter-item flex-1 min-w-[376px] ">
          <label className="block mb-1 ">{t("searchCustomer")}</label>
          <Input.Search
            allowClear
            size="large"
            className="w-full search-btn"
            // placeholder={t("searchAllParam")}
            onChange={(ev) => {
              queryCustomer.search = ev.currentTarget.value;
              queryCustomer.page = 1;
            }}
            enterButton={<SearchIcon onClick={() => {

            }} />}
            onKeyDown={(ev) => {
              if (ev.code == "Enter") {
                // fetchCustomer();
              }
            }}
            onSearch={handleSearch}
          />
        </div>

        {/* Date Range Picker - Max 50% width */}
        <div className="filter-item flex-1 max-w-[50%] min-w-[280px]">
          <label className="block mb-1">{t("createdAt")}</label>
          <DatePicker.RangePicker
            size="large"
            className="w-full"
            allowClear
            value={[
              queryCustomer.fromAt ? dayjs.unix(queryCustomer.fromAt) : null,
              queryCustomer.toAt ? dayjs.unix(queryCustomer.toAt) : null,
            ]}
            onChange={(value) => {
              if (value) {
                queryCustomer.fromAt = dayjs(value[0]).startOf("day").unix();
                queryCustomer.toAt = dayjs(value[1]).endOf("day").unix();
              } else {
                delete queryCustomer.fromAt;
                delete queryCustomer.toAt;
              }
              queryCustomer.page = 1;
              fetchCustomer();
            }}
            format="DD/MM/YYYY"
          />
        </div>

        {/* Status Select - Fixed width */}
        <div className="filter-item flex-1 md:max-w-[300px]">
          <label className="block mb-1">{t("accoutStatus")}</label>
          <Select
            size="large"
            className="w-full"
            defaultValue="ALL"
            placeholder={t("selectStatus")}
            onChange={(value) => {
              queryCustomer.page = 1;
              queryCustomer.status =
                value === undefined || value === "ALL" ? null : value;
              fetchCustomer();
            }}
          >
            {optionStatus.map((opt) => (
              <Select.Option key={opt.value} value={opt.value}>
                {opt.label}
              </Select.Option>
            ))}
          </Select>
        </div>

        {/* Export Button - Max content width */}
        {!hideBtnExportExcel && (
          <div className="filter-item">
            <Popconfirm
              title={t("exportAsk")}
              onConfirm={() =>
                handleExport({
                  onProgress() { },
                  exportColumns,
                  fileType: "xlsx",
                  dataField: "customers",
                  query: queryCustomer,
                  api: customerApi.findAll,
                  fileName: t("customerList"),
                  sheetName: t("customerList"),
                })
              }
              okText={t("exportExcel")}
              cancelText={t("cancel")}
            >
              <Button
              
                size="large"
                type="primary"
                icon={<DownloadIcon />}
                className="w-max"
              >
                <p className="text-sm !font-[400]">{t("exportExcel")}</p>
              </Button>
            </Popconfirm>
          </div>
        )}
      </div>
    </div>
  );
};