export enum FeatureType {
  StoreScanQR = "STORE_SCAN_QR", //cửa hàng quét QR
  CustomerScanQR = "CUSTOMER_SCAN_QR", //Bé quét QR
  StoreRequestGift = "STORE_REQUEST_GIFT", //Cửa hàng đổi quà
  CustomerRequestGift = "CUSTOMER_REQUEST_GIFT", //bé đổi quà
  CustomerSpin = "CUSTOMER_SPIN", //bé chơi game vòng xoay
  StoreHistory = "STORE_HISTORY", //Cửa hàng xem lịch sử
  CustomerHistory = "CUSTOMER_HISTORY", //Bé xem lịch sử
}
export interface FeatureConfig {
  id: number;
  createdAt: number;
  updatedAt: number;
  isDeleted: boolean;
  title: string;
  type: FeatureType;
  isEnable: boolean;
}
