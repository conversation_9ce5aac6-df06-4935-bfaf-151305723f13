import { Customer } from "./customer";
import { Employee } from "./employee";
import { Staff } from "./staff";

export enum ChangeEmployeeHistoryStatus {
  All = "ALL",
  Pending = "PENDING",
  Complete = "COMPLETE",
  Reject = "REJECT",
}

export const ChangeEmployeeHistoryStatusTran = {
  [ChangeEmployeeHistoryStatus.All]: {
    label: "Tất cả",
    color: "purple",
    value: ChangeEmployeeHistoryStatus.All,
  },
  [ChangeEmployeeHistoryStatus.Pending]: {
    label: "Đang duyệt",
    color: "blue",
    value: ChangeEmployeeHistoryStatus.Pending,
  },
  [ChangeEmployeeHistoryStatus.Complete]: {
    label: "Đã duyệt",
    color: "green",
    value: ChangeEmployeeHistoryStatus.Complete,
  },
  [ChangeEmployeeHistoryStatus.Reject]: {
    label: "Từ chối",
    color: "red",
    value: ChangeEmployeeHistoryStatus.Reject,
  },
};

export interface ChangeEmployeeHistory {
  id: number;
  createdAt: number;
  updatedAt: number;
  deletedBy: string;
  isDeleted: boolean;
  deletedAt: number;
  status: ChangeEmployeeHistoryStatus;
  inspecAt: number;
  actionBy: string;
  reason: string;
  fromEmployee: Employee;
  toEmployee: Employee;
  customer: Customer;
  staff: Staff;
}
