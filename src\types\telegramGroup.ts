import { Bot } from "./bot";
import { CustomerRank } from "./customer-rank";

export enum TelegramGroupType {
  PS = "PS", //phải sinh
  CS = "CS", //cơ sở
}

export const TelegramGroupTypeTrans = {
  [TelegramGroupType.CS]: "Chứng khoán cơ sở",
  [TelegramGroupType.PS]: "Chứng khoán phái sinh",
};

export interface TelegramGroup {
  id: number;
  createdAt: number;
  updatedAt: number;
  isDeleted: boolean;
  isActive: boolean;
  deletedAt: number;
  name: string;
  groupId: string;
  bot: Bot;
  type: TelegramGroupType;
  customerRanks: CustomerRank[];
  icon: string;
}
