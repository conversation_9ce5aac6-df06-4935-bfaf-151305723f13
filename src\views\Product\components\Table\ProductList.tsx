import {
  DownOutlined,
  EditOutlined,
  LockOutlined,
  UnlockOutlined,
} from "@ant-design/icons";
import { Button, Popconfirm, Space, Table, Tag } from "antd";
import Column from "antd/lib/table/Column";
import { IPagination, Pagination } from "components/Pagination";
import { AriviTable } from "components/Table/AriviTable";
import DropdownCell from "components/Table/DropdownCell";
import { useTranslation } from "react-i18next";
import { GrEdit } from "react-icons/gr";
import { HiOutlineTrash } from "react-icons/hi2";
import { Product, ProductStatus, ProductStatusTrans } from "types/product";
import { formatVND } from "utils";
import { unixToFullDate } from "utils/dateFormat";

interface PropsType {
  dataSource: Product[];
  loading: boolean;
  loadingDelete?: boolean;
  pagination?: IPagination;
  onEdit?: (product: Product) => void;
  onDelete?: (productId: number) => void;
  onActive?: (productId: number) => void;
  onInactive?: (productId: number) => void;

  // hasDeleteProductPermission?: boolean;
  // hasUpdateProductPermission?: boolean;
}

export const ProductList = ({
  dataSource,
  loading,
  loadingDelete,
  pagination,
  onDelete,
  onEdit,
  onActive,
  onInactive,
}: // hasDeleteProductPermission,
  // hasUpdateProductPermission,

  PropsType) => {
  const { t } = useTranslation();

  return (
    <div style={{ overflowX: "auto" }}>
      <AriviTable
        // bordered
        scroll={{ x: "max-content", y: "calc(100vh - 310px)" }}
        loading={loading}
        pagination={false}
        rowKey="id"
        dataSource={dataSource}
        className="custom-scrollbar"
      // onChange={}
      >
        <Column
          title={t("code")}
          dataIndex="code"
          key={"code"}
          render={(text, record: Product) => <span>{record.code}</span>}
        />
        <Column
          title={t("productName")}
          dataIndex="name"
          key={"name"}
          render={(text, record: Product) => (
            <div>
              <div>
                <span className="font-bold">{t("vietnamese")}</span>:{" "}
                {record.name}
              </div>
              <div>
                <span className="font-bold">{t("english")}</span>:{" "}
                {record.nameEn}
              </div>
            </div>
          )}
        />
        <Column
          width={100}
          title={t("position")}
          dataIndex="position"
          key={"position"}
          render={(_, record: Product) => (
            <div>
              {record.position}
            </div>
          )}
        />
        <Column
          width={100}
          title={t("status")}
          dataIndex="name"
          align="center"
          key={"name"}
          render={(text, record: Product) => (
            <Tag color={ProductStatusTrans[record.status]?.color}>
              {t(record.status)}
            </Tag>
          )}
        />
        <Column
          align="right"
          width={200}
          title={t("price")}
          dataIndex="unitPrice"
          key={"unitPrice"}
          render={(text, record: Product) => (
            <span className="">{formatVND(record.unitPrice)}</span>
          )}
        />
        <Column
          align="right"
          width={200}
          title={t("reviewQuantity")}
          dataIndex="numReview"
          key={"numReview"}
          render={(text, record: Product) => (
            <span className="">{formatVND(record.numReview)}</span>
          )}
        />
        <Column
          align="right"
          width={220}
          title={t("slowMinQuantity")}
          dataIndex="slowMinQuantity"
          key={"slowMinQuantity"}
          render={(text, record: Product) => (
            <span className="">{formatVND(record.slowMinQuantity)}</span>
          )}
        />
        <Column
          align="right"
          width={220}
          title={t("slowMaxQuantity")}
          dataIndex="slowMaxQuantity"
          key={"slowMaxQuantity"}
          render={(text, record: Product) => (
            <span className="">{formatVND((record as any).slowMaxQuantity)}</span>
          )}
        />
        {/* <Column
          align="right"
          width={400}
          title={t("estimatedDay")}
          dataIndex="estimatedDay"
          key={"estimatedDay"}
          render={(text, record: Product) => (
            <span className="">{formatVND(record.estimatedDay)}</span>
          )}
        />
        <Column
          align="right"
          width={300}
          title={t("maxLengthCharReview")}
          dataIndex="maxLengthCharReview"
          key={"maxLengthCharReview"}
          render={(text, record: Product) => (
            <span className="">{formatVND(record.maxLengthCharReview)}</span>
          )}
        /> */}
        <Column
          fixed="right"
          width={120}
          align="center"
          title=""
          key="action"
          dataIndex={""}
          render={(text, record: Product) => (
            //@ts-ignore
            <DropdownCell
              text={t("action")}
              items={[
                {
                  onClick: () => "",
                  // createOrderModalRef.current?.handleUpdate(record),
                  label: (
                    <Button
                      icon={<GrEdit />}
                      type="primary"
                      className="w-full justify-center !flex !items-center gap-2 !font-medium"
                      onClick={() => onEdit?.(record)}
                    >
                      {t("update")}
                    </Button>
                  ),
                  key: "update",
                  // hidden: !hasUpdateProductPermission,
                },
                {
                  onClick: () => "",
                  // createOrderModalRef.current?.handleUpdate(record),
                  label: (
                    <Button
                      icon={
                        record.status === ProductStatus.Active ? (
                          <span>
                            <LockOutlined />
                          </span>
                        ) : (
                          <span>
                            <UnlockOutlined />
                          </span>
                        )
                      }
                      type="primary"
                      className="w-full justify-center !flex !items-center gap-2 !font-medium"
                      onClick={() => {
                        if (record.status === ProductStatus.Active) {
                          onInactive?.(record.id);
                        } else {
                          onActive?.(record.id);
                        }
                      }}
                    >
                      {record.status === ProductStatus.Active
                        ? t("INACTIVE")
                        : t("ACTIVE")}
                    </Button>
                  ),
                  key: "updateStatus",
                  // hidden: !hasUpdateProductPermission,
                },
                {
                  label: (
                    <Popconfirm
                      placement="topLeft"
                      title={
                        <div>
                          <h1 className="text-sm">{t("confirm?")}</h1>
                        </div>
                      }
                      onConfirm={() => onDelete?.(record.id)}
                      okText={t("yes")}
                      cancelText={t("no")}
                    >
                      <Button
                        loading={loadingDelete}
                        icon={<HiOutlineTrash className="text-lg" />}
                        className={`w-full justify-center !flex !items-center gap-2 !text-red-500 !font-medium`}
                      >
                        {t("delete")}
                      </Button>
                    </Popconfirm>
                  ),
                  key: "delete",
                  // hidden: !hasDeleteProductPermission,
                },
              ]}
              trigger={["click"]}
            >
              <a onClick={(e) => e.preventDefault()}>
                <Space>
                  {t("action")}
                  <DownOutlined />
                </Space>
              </a>
            </DropdownCell>
          )}
        />
      </AriviTable>
      {pagination && <Pagination {...pagination} />}
    </div>
  );
};
