import React, { useEffect, useRef } from 'react';
import * as echarts from 'echarts';
import { Card, Spin } from 'antd';
import { useTranslation } from 'react-i18next';
import { DateType } from 'types/dashboard';
import dayjs from 'dayjs';

interface DataItem {
  date: string;
  cost: number;
  profit: number;
}

interface Props {
  data: any[];
  type?: string;
  loading?: boolean
}

export const CostProfitChart: React.FC<Props> = ({ data, type = "MONTH", loading = false }) => {
  const chartRef = useRef<HTMLDivElement>(null);
  const { t } = useTranslation();

  useEffect(() => {
    if (!chartRef.current) return;

    const chart = echarts.init(chartRef.current);

    const option: echarts.EChartsOption = {
      tooltip: {
        trigger: 'axis',
        axisPointer: { type: 'shadow' },
      },
      legend: {
        data: [t("cost"), t("profit")],
        top: 10,
        right: 10,
        icon: 'path://M2,2 h10 a2,2 0 0 1 2,2 v10 a2,2 0 0 1 -2,2 h-10 a2,2 0 0 1 -2,-2 v-10 a2,2 0 0 1 2,-2 z',
        itemWidth: 24,
        itemHeight: 24,
        textStyle: {
          fontSize: 14,
          fontWeight: 500,
          fontFamily: `"Plus Jakarta Sans", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;`
        },
        itemGap: 36
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true,
      },

      xAxis: {
        type: 'category',
        data: data.map(item => {
          const day = dayjs(item.date);
          if (type === DateType.Day) return day.format("DD/MM");
          if (type === DateType.Month) return day.format("M/YYYY");
          if (type === DateType.Year) return day.format("YYYY");
          return item.date;
        }),

        axisLabel: {

          margin: 26, fontWeight: 400, fontSize: 12, color: '#7C8FAC'
        },
        axisLine: {
          lineStyle: { type: 'dashed', color: '#DFE5EF' }
        },
        axisTick: { show: false },
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          margin: 16, fontWeight: 400, fontSize: 12, color: '#7C8FAC'
        },
        splitLine: {
          lineStyle: { type: 'dashed', color: '#DFE5EF' }
        }
      },
      smooth: true,
      series: [
        {
          name: t("cost"),
          type: 'bar',
          stack: 'total',
          itemStyle: { color: '#1A73E8' },
          data: data.map((item) => item.cost),
        },
        {
          name: t("profit"),
          type: 'bar',
          stack: 'total',
          barMaxWidth: 40,
          itemStyle: { color: '#00ABFF' },
          data: data.map((item) => ({
            value: item.profit,
            itemStyle: {
              color: '#00ABFF',
              borderRadius: item.profit > 0 ? [50, 50, 0, 0] : 0,
            },
          })),
        },
      ],
    };

    chart.setOption(option);
    window.addEventListener('resize', (chart as any).resize);
    return () => window.removeEventListener('resize', (chart as any).resize);
  }, [data]);

  return (
    <Spin spinning={loading}>
      <div ref={chartRef} style={{ height: 400 }} />
    </Spin>

  );
};
