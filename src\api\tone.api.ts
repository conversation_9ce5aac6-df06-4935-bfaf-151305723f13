import { request } from "utils/request";
import { AxiosPromise } from "axios";

export const toneApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/tone",
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/tone",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/tone/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/tone/${id}`,
      method: "delete",
    }),
};
