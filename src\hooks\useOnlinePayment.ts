import { onlinePaymentApi } from "api/onlinePayment.api";
import { debounce } from "lodash";
import { useCallback, useState } from "react";
import { OnlinePayment } from "types/online-payment";
import { QueryParam } from "types/query";

export interface OnlinePaymentQuery extends QueryParam {
  storeId?: number;
}

interface OnlinePaymentProps {
  initQuery: OnlinePaymentQuery;
}

export const useOnlinePayment = ({ initQuery }: OnlinePaymentProps) => {
  const [data, setData] = useState<OnlinePayment[]>([]);
  const [query, setQuery] = useState<OnlinePaymentQuery>(initQuery);
  const [loading, setLoading] = useState<boolean>(false);
  const [total, setTotal] = useState(0);

  const fetchOnlinePayment = useCallback(
    async (newQuery?: OnlinePaymentQuery) => {
      try {
        setLoading(true);
        const { data } = await onlinePaymentApi.findAll(newQuery ?? query);
        setTotal(data.total);
        setData(data.onlinePayments);
      } finally {
        setLoading(false);
      }
    },
    [query]
  );

  const debounceFetchOnlinePayment = useCallback(() => {
    debounce(() => fetchOnlinePayment(), 1000);
  }, []);

  return {
    onlinePayments: data,
    totalOnlinePayment: total,
    fetchOnlinePayment,
    queryOnlinePayment: query,
    setQueryOnlinePayment: setQuery,
    loadingOnlinePayment: loading,
    debounceFetchOnlinePayment,
  };
};
