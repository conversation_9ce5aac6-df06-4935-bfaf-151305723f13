.project-status {
  height: 26px;
  font-size: 12px;
  font-weight: bold;
  display: inline-flex;
  align-items: center;

  &.PROCESSING {
    color: var(--warning-color-300);
    background-color: var(--warning-background-color-25);
    border-color: var(--warning-border-color);
  }
  &.PENDING {
    color: var(--orange-color-500);
    background-color: var(--orange-light-color);
    border-color: var(--orange-light-border-color);
  }
  &.COMPLETE {
    color: var(--blue-color-500);
    background-color: var(--blue-light-color);
    border-color: var(--blue-light-border-color);
  }

  &.PAUSE {
    color: var(--error-color-500);
    background-color: var(--red-light-color);
    border-color: var(--red-light-border-color);
  }

  &.NEW {
    color: var(--success-500);
    background-color: var(--green-light-color);
    border-color: var(--success-100);
  }
}

@media screen and (max-width: 430px) {
  .project-status {
    font-size: 10px;
    height: 24px;
  }
}
