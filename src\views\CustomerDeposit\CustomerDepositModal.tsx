import { Col, Form, Input, message, Modal, Row, Select } from "antd";
import { Rule } from "antd/lib/form";
import { customerDepositApi } from "api/customerDeposit.api";

import React, { useEffect, useImperativeHandle, useState } from "react";
import { ModalStatus } from "types/modal";
import { CustomerDeposit } from "types/customerDeposit";
import { Customer } from "types/customer";
import { InputNumber } from "components/Input/InputNumber";
import { useHandleCustomer } from "hooks/useHandleCustomer";
import { CustomerSelector } from "components/Selector/CustomerSelector";
import { useTranslation } from "react-i18next";
import TextArea from "antd/es/input/TextArea";

const rules: Rule[] = [{ required: true }];

export interface CustomerDepositModal {
  handleCreate: () => void;
}

interface CustomerDepositModalProps {
  onClose: () => void;
  onSubmitOk: () => void;
  isAgent?: boolean;
}

export const CustomerDepositModal = React.forwardRef(
  ({ onClose, onSubmitOk, isAgent }: CustomerDepositModalProps, ref) => {
    const [form] = Form.useForm();
    const [loading, setLoading] = useState(false);
    const [selectedCustomerId, setSelectedCustomerId] = useState<number>();
    const [visible, setVisible] = useState(false);
    const [idCustomer, setIdCustomer] = useState<number>();
    const [status, setStatus] = useState<ModalStatus>("create");
    const { t } = useTranslation();

    useImperativeHandle<any, CustomerDepositModal>(
      ref,
      () => ({
        handleCreate() {
          form.resetFields();
          setVisible(true);
          setStatus("create");
        },
      }),
      []
    );
    const createData = async () => {
      try {
        await form.validateFields();
        const { customerId, ...formData } = form.getFieldsValue();
        setLoading(true);

        const res = await customerDepositApi.create({
          ...formData,
          customerId,
        });
        message.success(t("actionSuccessfully"));
        onClose();
        setVisible(false);
        onSubmitOk();
      } finally {
        setLoading(false);
      }
    };

    return (
      <Modal
        onCancel={() => {
          onClose?.();
          setVisible(false);
        }}
        visible={visible}
        title={t("depositMoney")}
        style={{ top: 20 }}
        width={700}
        confirmLoading={loading}
        onOk={() => createData()}
      >
        <Form
          layout="vertical"
          form={form}
          validateTrigger={["onBlur", "onChange"]}
        >
          <Row>
            <Col span={24}>
              <Form.Item
                label={t("selectCustomer")}
                name="customerId"
                rules={rules}
              >
                {/* <Select
                  placeholder="Vui lòng chọn Khách hàng"
                  size="middle"
                  style={{ width: "100%", minWidth: 150, marginTop: "5px" }}
                  allowClear
                  showSearch
                  loading={loadingCustomer}
                >
                  {customers?.map((item) => (
                    <Select.Option value={item.id}>
                      {item.name} - {item.phone}
                    </Select.Option>
                  ))}
                </Select> */}
                <CustomerSelector
                  initQuery={{ isAgent }}
                  value={form.getFieldValue("customerId")}
                  onChange={(customerId) => {
                    form.setFieldValue("customerId", customerId);
                  }}
                />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label={t("amountRequest")} name="amount" rules={rules}>
                <InputNumber
                  className="w-full"
                  // placeholder="Nhập số điểm muốn nạp"
                />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label={t("note")} name="note">
                <TextArea />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    );
  }
);
