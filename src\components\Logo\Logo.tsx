import { FC } from "react";
import { Link } from "react-router-dom";
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
import { ReactComponent as LogoDark } from "src/assets/images/logos/dark-logo.svg";
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
import { ReactComponent as LogoDarkRTL } from "src/assets/images/logos/dark-rtl-logo.svg";
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
import { ReactComponent as LogoLight } from "src/assets/images/logos/light-logo.svg";
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
import { ReactComponent as LogoLightRTL } from "src/assets/images/logos/light-logo-rtl.svg";
import { styled } from "@mui/material";
import { appStore } from "store/appStore";
import { settings } from "settings";
import clsx from "clsx";

interface Props {
  className?: string;
}

const Logo = ({ className }: Props) => {
  const customizer = appStore.customizer;

  const isSideBarCollapsed =
    customizer.isCollapse && !!!customizer.isSidebarHover;

  const LinkStyled = styled(Link)(() => ({
    // height: customizer.TopbarHeight,
    width: isSideBarCollapsed ? "40px" : "130px",
    height: "44px",
    overflow: "hidden",
    display: "block",
  }));

  if (customizer.activeDir === "ltr") {
    return (
      <LinkStyled
        to="/"
        style={{
          display: "flex",
          alignItems: "center",
        }}
      >
        {/* {customizer.activeMode === "dark" ? <LogoLight /> : <LogoDark />} */}
        <img
          src={settings.logo}
          className={clsx(className, isSideBarCollapsed ? "!w-[130px]" : "")}
        />
      </LinkStyled>
    );
  }

  return (
    <LinkStyled
      to="/"
      style={{
        display: "flex",
        alignItems: "center",
      }}
    >
      {/* {customizer.activeMode === "dark" ? <LogoDarkRTL /> : <LogoLightRTL />} */}
      <img
        src={settings.logo}
        className={clsx(className, isSideBarCollapsed ? "!w-[130px]" : "")}
      />
    </LinkStyled>
  );
};

export default Logo;
