export enum MessageTemplateType {
  //main type
  CloseStockOrder = "CLOSE_STOCK_ORDER", //template gửi khi đóng lệnh CS có lãi
  CloseDerivative = "CLOSE_DERIVATIVE", //template gửi khi đóng lệnh PS có lãi
}

export const MessageTemplateTypeTrans = {
  [MessageTemplateType.CloseDerivative]: "<PERSON><PERSON>i sinh",
  [MessageTemplateType.CloseStockOrder]: "Cơ sở",
};

export interface MessageTemplate {
  id: number;
  createdAt: number;
  updatedAt: number;
  isDeleted: boolean;
  deletedAt: number;
  //main type
  description: string;
  type: MessageTemplateType;
  profitPercent: number;
}
