import { Area } from "./area";
import { Product } from "./product";
import { Shop } from "./shop";

export enum PopupType {
  Content = "CONTENT", // N.dung cứng
  Link = "LINK", // L.kết ngoài
  Shop = "Shop", // Cửa hàng
  Product = "PRODUCT", // s.phẩm
}

export enum PopupPlatform {
  Web = "WEB",
  Mobile = "MOBILE",
}

export const PopupTypeTrans = {
  [PopupType.Content]: "Nội dung",
  [PopupType.Link]: "Đường link",
  [PopupType.Product]: "Sản phẩm",
  [PopupType.Shop]: "Cửa hàng",
};

export interface Popup {
  id: number;
  createdAt: number;
  updatedAt: number;
  isDeleted: boolean;
  title: string;
  content: string;
  platform: PopupPlatform;
  image: string;
  type: PopupType;
  link: string;
  isVisible: boolean;
  area: Area;
  product: Product;
  shop: Shop;
}
