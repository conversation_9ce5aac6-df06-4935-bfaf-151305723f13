import { request } from "utils/request";
import { AxiosPromise } from "axios";

export const dashboardApi = {
  summaryProductQuantity: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/dashboard/product/summary-quantity",
      params,
    }),
  summaryPoint: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/dashboard/summary/point",
      params,
    }),
  summaryRequestGift: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/dashboard/summary/request-gift",
      params,
    }),
  summarySale: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/dashboard/sale",
      params,
    }),
  topProduct: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/dashboard/top/product",
      params,
    }),
  topStockOrder: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/dashboard/top/stockOrder",
      params,
    }),
  topStockCode: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/dashboard/top/stockCode",
      params,
    }),
  summary: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/dashboard/summary",
      params,
    }),

  summaryOrder: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/dashboard/order/byDate",
      params,
    }),
  summaryOrderSale: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/dashboard/order/sale/byDate",
      params,
    }),
  summaryCustomer: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/dashboard/admin/byDate",
      params,
    }),
  summaryShop: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/dashboard/shop",
      params,
    }),
  summaryRank: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/dashboard/summary/rank",
      params,
    }),
  summaryProjectAndBalance: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/dashboard/summary/total",
      params,
    }),
  statusProject: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/dashboard/summary/status/project",
      params,
    }),
  summaryProjectV2: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/project/summary",
      params,
    }),
  summaryReview: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/dashboard/summary/total/review",
      params,
    }),
  customerCount: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/dashboard/summary/total/customer",
      params,
    }),
  partnerCount: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/dashboard/summary/total/partner",
      params,
    }),
  cashFlow: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/dashboard/partner/cash/flow",
      params,
    }),
  topPartner: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/dashboard/top/partner",
      params,
    }),
  topCustomer: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/dashboard/top/customer",
      params,
    }),
  partnerRank: (params?: any, lang?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/dashboard/partner/rank",
      params,
      headers: {
        lang: lang,
      },
    }),
  moneyProjectPayment: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/dashboard/summary/project",
      params,
    }),
  moneyPartnerWithdraw: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/dashboard/summary/withdraw",
      params,
    }),
  summaryTotal: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/dashboard/summary/total/project/date",
      params,
    }),
  summaryStatistical: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/dashboard/total/profit",
      params,
    }),
  summaryProfit: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/dashboard/summary/profit",
      params,
    }),

  totalMoneyPartnerWithdraw: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/dashboard/total/withdraw",
      params,
    }),

  summaryDeposit: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/dashboard/summary/deposit",
      params,
    }),
  summaryCustomerTransaction: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/dashboard/summary/customer/transaction",
      params,
    }),
  summaryCustomerBalance: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/dashboard/summary/customer/balance",
      params,
    }),

};
