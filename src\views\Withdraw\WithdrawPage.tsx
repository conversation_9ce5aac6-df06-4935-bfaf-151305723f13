import { useCallback, useEffect, useState } from "react";
import { Badge, Card, Tabs } from "antd";
import { getTitle } from "utils";
import { WithdrawStatus, WithdrawStatusTrans } from "types/withdraw";
import { withdrawApi } from "api/withdraw.api";
import { useTranslation } from "react-i18next";
import { WithdrawTab } from "./WithdrawTab";
import "./WithdrawPage.scss";
import clsx from "clsx";

export const WithdrawPage = ({ title = "", isAgent = false }) => {
  const [tabActive, setTabActive] = useState<WithdrawStatus>(
    WithdrawStatus.All
  );
  const [summaryReceiptOfStatus, setSummaryReceiptOfStatus] = useState();
  const [lastUpdate, setLastUpdate] = useState(0);
  const { t } = useTranslation();

  console.log({ parentLastUpdate: lastUpdate });

  useEffect(() => {
    document.title = getTitle(t(title));
    fetchSummaryReceipt();
  }, []);

  const fetchSummaryReceipt = useCallback(async () => {
    const res = await withdrawApi.getSummary();

    if (res.status) {
      setSummaryReceiptOfStatus(() => {
        const summary = res.data.reduce(
          (prev: any, curr: { status: WithdrawStatus; total: number }) => {
            prev[curr.status] = curr.total;
            prev.ALL = (prev.ALL || 0) + curr.total;
            return prev;
          },
          { ALL: 0 }
        );

        return summary;
      });
    }
  }, [isAgent]);
  const onChangeTab = useCallback((key: WithdrawStatus) => {
    setTabActive(key as WithdrawStatus);
  }, []);

  const onLastUpdateChange = useCallback(() => {
    console.log("on last update");
    setLastUpdate((pre) => pre + 1);
  }, []);

  return (
    <Card bodyStyle={{ padding: "8px 20px" }} style={{ borderRadius: "8px" }}>
      <Tabs
        activeKey={tabActive}
        onChange={(key) => onChangeTab(key as WithdrawStatus)}
        type="line"
        animated={{ inkBar: true, tabPane: true, tabPaneMotion: {} }}
      >
        {Object.values(WithdrawStatusTrans).map((item) => {
          let count = summaryReceiptOfStatus?.[item.value] || 0;

          return (
            <Tabs.TabPane
              tab={
                <div className="flex items-center gap-2">
                  {t(item.value)}
                  {summaryReceiptOfStatus && (
                    <Badge
                      key={item.value}
                      color={WithdrawStatusTrans[item.value]?.color}
                      count={summaryReceiptOfStatus?.[item.value] || 0}
                    />
                  )}
                </div>
              }
              key={item.value}
              tabKey={item.value}
            >
              <WithdrawTab
                parentLastUpdate={lastUpdate}
                isFocus={tabActive == item.value}
                status={tabActive}
                onSubmitOk={() => {
                  onLastUpdateChange();
                  fetchSummaryReceipt();
                }}
              />
            </Tabs.TabPane>
          );
        })}
      </Tabs>
    </Card>
  );
};
