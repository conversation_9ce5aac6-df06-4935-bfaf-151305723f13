import { customerDepositApi } from "api/customerDeposit.api";
import { useState } from "react";
import { CustomerDeposit } from "types/customerDeposit";
import { QueryParam } from "types/query";

export interface CustomerDepositQuery extends QueryParam {}

interface UseCustomerDepositProps {
  initQuery: CustomerDepositQuery;
}

export const useCustomerDeposit = ({ initQuery }: UseCustomerDepositProps) => {
  const [data, setData] = useState<CustomerDeposit[]>([]);
  const [total, setTotal] = useState(0);
  const [query, setQuery] = useState<CustomerDepositQuery>(initQuery);
  const [loading, setLoading] = useState(false);

  const fetchData = async () => {
    setLoading(true);
    try {
      const { data } = await customerDepositApi.findAll(query);

      setData(data.customerDeposits);
      setTotal(data.total);
    } finally {
      setLoading(false);
    }
  };

  return { customerDeposits: data, total, fetchData, loading, setQuery, query };
};
