import { useState } from "react";
import { QueryParam } from "types/query";
import { toneApi } from "api/tone.api";
import { Tone } from "types/tone";

export interface ToneQuery extends QueryParam {}

interface UseToneProps {
  initQuery: ToneQuery;
}

export const useTone = ({ initQuery }: UseToneProps) => {
  const [data, setData] = useState<Tone[]>([]);
  const [total, setTotal] = useState(0);
  const [query, setQuery] = useState<ToneQuery>(initQuery);
  const [loading, setLoading] = useState(false);

  const fetchData = async () => {
    setLoading(true);
    try {
      const { data } = await toneApi.findAll(query);

      setData(data.tones);
      setTotal(data.total);
    } finally {
      setLoading(false);
    }
  };

  return {
    tones: data,
    totalTone: total,
    fetchTone: fetchData,
    loadingTone: loading,
    setQueryTone: setQuery,
    queryTone: query,
  };
};
