import { Button, Col, Form, Input, message, Modal, Row, Select } from "antd";
import { useForm } from "antd/lib/form/Form";
import { forwardRef, useImperativeHandle, useState } from "react";
import { ModalStatus } from "types/modal";
import { SurveyCampaign } from "types/survey";
import { requiredRule } from "utils/validate-rules";
import { InputNumber } from "components/Input/InputNumber";
import { faqCategoryApi } from "api/faqCategory.api";
import { FaqCategory, FaqCategoryType } from "types/faq";
import { useTranslation } from "react-i18next";

export interface FaqCategoryModalProps {
  onClose: () => void;
  onSubmitOk: () => void;
  type?: FaqCategoryType;
  // hasAddFaqCategoryPermission?: boolean;
  // hasUpdateFaqCategoryPermission?: boolean;
}

export interface FaqCategoryModalRef {
  handleUpdate: (faqCategory: FaqCategory) => void;
  handleCreate: () => void;
}

export const FaqCategoryModal = forwardRef(
  (
    {
      onSubmitOk,
      type = FaqCategoryType.Customer,
    }: // hasAddFaqCategoryPermission,
      // hasUpdateFaqCategoryPermission,
      FaqCategoryModalProps,
    ref
  ) => {
    const [form] = useForm();
    const [loading, setLoading] = useState(false);
    const [visible, setVisible] = useState<boolean>();
    const { t } = useTranslation();

    const [selectedFaqCategory, setSelectedFaqCategory] =
      useState<FaqCategory>();
    const [status, setStatus] = useState<ModalStatus>("create");

    useImperativeHandle(
      ref,
      () => ({
        handleUpdate,
        handleCreate,
      }),
      []
    );

    const handleUpdate = (faqCategory: FaqCategory) => {
      form.setFieldsValue({
        ...faqCategory,
      });
      setSelectedFaqCategory(faqCategory);
      setStatus("update");
      setVisible(true);
    };

    const handleCreate = () => {
      setStatus("create");
      setVisible(true);
    };

    const handleSubmitForm = async () => {
      await form.validateFields();
      const dataForm = form.getFieldsValue();
      if (dataForm.name) {
        dataForm.name = dataForm.name.trim();
      }
      const payload = {
        faqCategory: {
          ...dataForm,
          type,
        },
      };

      try {
        setLoading(true);
        switch (status) {
          case "update":
            await faqCategoryApi.update(selectedFaqCategory?.id || 0, payload);
            message.success(t("actionSuccessfully"));
            break;

          //create
          default:
            await faqCategoryApi.create(payload);
            message.success(t("actionSuccessfully"));
            break;
        }
        setVisible(false);
        onSubmitOk();
      } catch (error) {
      } finally {
        setLoading(false);
        // onSubmitOk();
      }
    };

    return (
      <Modal
        onCancel={() => {
          setVisible(false);
        }}
        visible={visible}
        centered
        title={
          <h1 className="mb-0 text-lg text-primary font-bold">
            {status == "create" ? t("create") : t("update")}
          </h1>
        }
        confirmLoading={loading}
        destroyOnClose
        width={800}
        onOk={handleSubmitForm}
        afterClose={() => {
          form.resetFields();
        }}
        footer={[
          <Button
            key="cancel"
            onClick={() => {
              form.resetFields();
              setVisible(false);
            }}
            size="large"
            style={{ borderColor: "#E94134", minWidth: 136, fontSize: 16, fontWeight: 600 }}
          >
            {t("close")}
          </Button>,
          <Button
            key="submit"
            type="primary"
            onClick={handleSubmitForm}
            loading={loading}
            size="large"
            style={{ minWidth: 136, fontSize: 16, fontWeight: 600 }}
          >
            {t("save")}
          </Button>,
        ]}
        okText={t("save")}
        cancelText={t("close")}
      // okButtonProps={{
      //   hidden:
      //     (!hasAddFaqCategoryPermission && status == "create") ||
      //     (!hasUpdateFaqCategoryPermission && status == "update"),
      // }}
      >
        <Form
          form={form}
          layout="vertical"
          validateTrigger={["onBlur", "onChange"]}
        >
          <Row gutter={[12, 0]}>
            <Col span={24}>
              <Form.Item
                className="!mb-2"
                name="position"
                label={t("position")}
                rules={[requiredRule]}
              >
                <InputNumber placeholder={t("enterPosition")} />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item
                className="!mb-2"
                name="name"
                label={t("vietnameseName")}
                rules={[requiredRule]}
              >
                <Input placeholder={t("enterName")} />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item
                className="!mb-2"
                name="nameEn"
                label={t("englishName")}
                rules={[requiredRule]}
              >
                <Input placeholder={t("enterName")} />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    );
  }
);
