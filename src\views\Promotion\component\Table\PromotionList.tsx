import {
  DownOutlined,
  EditOutlined,
  LockOutlined,
  UnlockOutlined,
} from "@ant-design/icons";
import { Button, Popconfirm, Space, Table, Tag } from "antd";
import Column from "antd/lib/table/Column";
import { IPagination, Pagination } from "components/Pagination";
import { AriviTable } from "components/Table/AriviTable";
import DropdownCell from "components/Table/DropdownCell";
import { useTranslation } from "react-i18next";
import { GrEdit } from "react-icons/gr";
import { HiOutlineTrash } from "react-icons/hi2";
import {
  Promotion,
  PromotionDiscountTypeTrans,
  PromotionStatus,
  PromotionStatusTrans,
} from "types/promotion";
import { formatVND } from "utils";
import { formatDate } from "utils/date";
import { unixToFullDate } from "utils/dateFormat";

interface PropsType {
  dataSource: Promotion[];
  loading: boolean;
  loadingDelete?: boolean;
  pagination?: IPagination;
  onEdit?: (promotion: Promotion) => void;
  onDelete?: (promotionId: number) => void;
  onActive?: (promotionId: number) => void;
  onInactive?: (promotionId: number) => void;

  // hasDeletePromotionPermission?: boolean;
  // hasUpdatePromotionPermission?: boolean;
}

export const PromotionList = ({
  dataSource,
  loading,
  loadingDelete,
  pagination,
  onDelete,
  onEdit,
  onActive,
  onInactive,
}: // hasDeletePromotionPermission,
// hasUpdatePromotionPermission,

PropsType) => {
  const { t } = useTranslation();

  return (
    <div>
      <AriviTable
        // bordered
        scroll={{ x: "max-content", y: "calc(100vh - 310px)" }}
        loading={loading}
        pagination={false}
        rowKey="id"
        dataSource={dataSource}
        className="custom-scrollbar"
        // onChange={}
      >
        <Column
          title={t("code")}
          dataIndex="code"
          key={"code"}
          render={(text, record: Promotion) => <span>{record.code}</span>}
        />
        <Column
          title={t("promotionName")}
          dataIndex="name"
          key={"name"}
          render={(text, record: Promotion) => <span>{record.title}</span>}
        />
        <Column
          title={t("description")}
          dataIndex="description"
          key={"description"}
          render={(text, record: Promotion) => (
            <span>{record.description}</span>
          )}
        />
        <Column
          title={t("discount")}
          dataIndex="discount"
          key={"discount"}
          align="right"
          render={(text, record: Promotion) => (
            <span>
              {formatVND(record.discountValue)} (
              {`${PromotionDiscountTypeTrans[record.discountType]?.label}`})
            </span>
          )}
        />
        <Column
          title={t("promotionQuantity")}
          width={200}
          dataIndex="quantity"
          key={"quantity"}
          align="right"
          render={(text, record: Promotion) => <span>{record.quantity} </span>}
        />
        <Column
          title={t("totalUsed")}
          width={150}
          dataIndex="totalUsed"
          key={"totalUsed"}
          align="right"
          render={(text, record: Promotion) => <span>{record.totalUsed} </span>}
        />
        <Column
          title={t("voucherPeriod")}
          dataIndex="quantity"
          key={"quantity"}
          render={(text, record: Promotion) => (
            <span>
              {`${formatDate(record.startAt)} - ${formatDate(record.endAt)}`}{" "}
            </span>
          )}
        />
        <Column
          width={100}
          title={t("status")}
          dataIndex="name"
          align="center"
          key={"name"}
          render={(text, record: Promotion) => (
            <Tag color={PromotionStatusTrans[record.status]?.color}>
              {t(record.status)}
            </Tag>
          )}
        />

        <Column
          fixed="right"
          width={120}
          align="center"
          title=""
          key="action"
          dataIndex={""}
          render={(text, record: Promotion) => (
            //@ts-ignore
            <DropdownCell
              text={t("action")}
              items={[
                {
                  onClick: () => "",
                  // createOrderModalRef.current?.handleUpdate(record),
                  label: (
                    <Button
                      icon={<GrEdit />}
                      type="primary"
                      className="w-full justify-center !flex !items-center gap-2 !font-medium"
                      onClick={() => onEdit?.(record)}
                    >
                      {t("update")}
                    </Button>
                  ),
                  key: "update",
                  // hidden: !hasUpdatePromotionPermission,
                },
                {
                  onClick: () => "",
                  // createOrderModalRef.current?.handleUpdate(record),
                  label: (
                    <Button
                      icon={
                        record.status === PromotionStatus.Active ? (
                          <span>
                            <LockOutlined />
                          </span>
                        ) : (
                          <span>
                            <UnlockOutlined />
                          </span>
                        )
                      }
                      type="primary"
                      className="w-full justify-center !flex !items-center gap-2 !font-medium"
                      onClick={() => {
                        if (record.status === PromotionStatus.Active) {
                          onInactive?.(record.id);
                        } else {
                          onActive?.(record.id);
                        }
                      }}
                    >
                      {record.status === PromotionStatus.Active
                        ? t("INACTIVE")
                        : t("ACTIVE")}
                    </Button>
                  ),
                  key: "updateStatus",
                  // hidden: !hasUpdatePromotionPermission,
                },
                {
                  label: (
                    <Popconfirm
                      placement="topLeft"
                      title={
                        <div>
                          <h1 className="text-sm">{t("confirm?")}</h1>
                        </div>
                      }
                      onConfirm={() => onDelete?.(record.id)}
                      okText={t("yes")}
                      cancelText={t("no")}
                    >
                      <Button
                        loading={loadingDelete}
                        icon={<HiOutlineTrash className="text-lg" />}
                        className={`w-full justify-center !flex !items-center gap-2 !text-red-500 !font-medium`}
                      >
                        {t("delete")}
                      </Button>
                    </Popconfirm>
                  ),
                  key: "delete",
                  // hidden: !hasDeletePromotionPermission,
                },
              ]}
              trigger={["click"]}
            >
              <a onClick={(e) => e.preventDefault()}>
                <Space>
                  {t("action")}
                  <DownOutlined />
                </Space>
              </a>
            </DropdownCell>
          )}
        />
      </AriviTable>
      {pagination && <Pagination {...pagination} />}
    </div>
  );
};
