import * as ExcelJS from "exceljs";
import saveAs from "file-saver";
import dayjs from "dayjs";
import { Customer } from "types/customer";
import {
  ProductCategory,
  ProductCategoryTypeTrans,
} from "types/product-category";
// import { Customer, Lang } from "types/customer";
// import { Language } from "types/language";

const borderStyles = {
  top: { style: "thin" },
  left: { style: "thin" },
  bottom: { style: "thin" },
  right: { style: "thin" },
};

const fontRed = {
  color: { argb: "ff0000" },
  bold: true,
};

const bgYellow = {
  type: "pattern",
  pattern: "solid",
  fgColor: { argb: "ffff00" },
};

const bgRed = {
  type: "pattern",
  pattern: "solid",
  fgColor: { argb: "ff7373" },
};

const fontOdd = {
  color: { argb: "0070c0" },
  bold: true,
};
const fontEven = {
  color: { argb: "c00066" },
  bold: true,
};

export const exportCategory = (categoryData: ProductCategory[]) => {
  const title =
    "Danh sách danh mục - ngành hàng - " + dayjs().format("HH:mm DD/MM/YYYY");
  const headerTitle = ["Code", "Tên", "Slug", "Loại", "Thuộc ngành hàng"];
  //Khai bao file
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet("New Sheet", {
    pageSetup: { fitToPage: true, fitToHeight: 5, fitToWidth: 7 },
  });

  const titleRow = worksheet.getRow(1);
  titleRow.getCell(1).value = title;
  titleRow.getCell(1).alignment = { horizontal: "center" };
  titleRow.getCell(1).font = { size: 15, bold: true };

  const headerRow = worksheet.getRow(2);
  for (let i = 0; i < headerTitle.length; i++) {
    const title = headerTitle[i];
    headerRow.getCell(i + 1).value = title;
    headerRow.getCell(i + 1).font = { bold: true };
  }

  let startContentRow = 3;

  for (let i = 0; i < categoryData.length; i++) {
    const category = categoryData[i];

    const row = worksheet.getRow(startContentRow);
    row.getCell(1).value = category.code;
    row.getCell(2).value = category.name;
    row.getCell(3).value = category.slug;
    row.getCell(4).value = ProductCategoryTypeTrans[category.type]?.label;
    row.getCell(5).value = category?.parent?.name;

    startContentRow++;
  }

  const columnCount = worksheet.columnCount;
  worksheet.mergeCells(1, 1, 1, columnCount);

  worksheet.columns.forEach(function (column, i) {
    var maxLength = 0;
    //@ts-ignore
    column["eachCell"]({ includeEmpty: true }, function (cell) {
      var columnLength = cell.value ? cell.value.toString().length : 10;
      if (columnLength > maxLength) {
        maxLength = columnLength;
      }
    });
    column.width = maxLength < 10 ? 10 : maxLength - 10;
  });

  workbook.xlsx.writeBuffer().then(function (buffer: any) {
    saveAs(
      new Blob([buffer], { type: "application/octet-stream" }),
      title + ".xlsx"
    );
  });
};

// const autoWidth = (
//   worksheet: ExcelJS.Worksheet,
//   columnPaths: number[],
//   minimalWidth = 0
// ) => {
//   let maxColumnLength = 0;
//   columnPaths.forEach((columnPath) => {
//     const column = worksheet.getColumn(columnPath);
//     //@ts-ignore
//     column.eachCell({ includeEmpty: true }, (cell) => {
//       maxColumnLength = Math.max(
//         maxColumnLength,
//         minimalWidth,
//         cell.value ? cell.value.toString().length : 0
//       );
//     });
//     column.width = maxColumnLength;
//   });
// };
