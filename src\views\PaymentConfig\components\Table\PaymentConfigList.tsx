import {
  DownOutlined,
  EditOutlined,
  LockOutlined,
  UnlockOutlined,
} from "@ant-design/icons";
import { Button, Image, message, Popconfirm, Space, Switch, Table } from "antd";
import Column from "antd/lib/table/Column";
import { paymentConfigApi } from "api/paymentConfig.api";
import { IPagination, Pagination } from "components/Pagination";
import { AriviTable } from "components/Table/AriviTable";
import DropdownCell from "components/Table/DropdownCell";
import { useTranslation } from "react-i18next";
import { GrEdit } from "react-icons/gr";
import { HiOutlineTrash } from "react-icons/hi2";
import { PaymentConfig } from "types/paymentConfig";
import { formatVND } from "utils";
import { unixToFullDate } from "utils/dateFormat";

interface PropsType {
  dataSource: PaymentConfig[];
  loading: boolean;
  loadingDelete?: boolean;
  pagination?: IPagination;
  onEdit?: (paymentConfig: PaymentConfig) => void;
  onDelete?: (paymentConfigId: number) => void;
  onActive?: (paymentConfigId: number) => void;
  onInactive?: (paymentConfigId: number) => void;
  onSubmitOk: () => void;

  // hasDeletePaymentConfigPermission?: boolean;
  // hasUpdatePaymentConfigPermission?: boolean;
}

export const PaymentConfigList = ({
  dataSource,
  loading,
  loadingDelete,
  pagination,
  onDelete,
  onEdit,
  onActive,
  onInactive,
  onSubmitOk,
}: // hasDeletePaymentConfigPermission,
// hasUpdatePaymentConfigPermission,
PropsType) => {
  const { t } = useTranslation();
  const toggleEnable = async (id: number, isEnabled?: boolean) => {
    await paymentConfigApi.update(id, { paymentConfig: { isEnabled } });
    message.success(t("actionSuccessfully"));
    onSubmitOk();
  };
  return (
    <div>
      <AriviTable
        scroll={{ x: "max-content", y: "calc(100vh - 380px)" }}
        loading={loading}
        pagination={false}
        rowKey="id"
        dataSource={dataSource}
        size="small"
        className="custom-scrollbar"
        // onChange={}
      >
        <Column
          title={"Logo"}
          dataIndex="name"
          key={"name"}
          render={(text, record: PaymentConfig) => (
            <div>
              <Image
                src={record.logo}
                width={50}
                height={50}
                className="rounded"
              />
            </div>
          )}
        />

        <Column
          title={t("paymentConfigName")}
          dataIndex="name"
          key={"name"}
          render={(text, record: PaymentConfig) => (
            <div>
              <div>
                <span className="font-bold">{t("vietnamese")}</span>:{" "}
                {record.name}
              </div>
              <div>
                <span className="font-bold">{t("english")}</span>:{" "}
                {record.nameEn}
              </div>
            </div>
          )}
        />
        <Column
          title={t("status")}
          align="center"
          width={150}
          dataIndex="isEnabled"
          key="isEnabled"
          render={(text, record: PaymentConfig) => (
            <span>
              <Popconfirm
                onConfirm={async () => {
                  await toggleEnable(record.id, !record.isEnabled);
                }}
                title={t("confirm?")}
              >
                <Switch
                  checked={record.isEnabled}
                  // disabled={record?.isDefault}
                  checkedChildren={t("on")}
                  unCheckedChildren={t("off")}
                ></Switch>
              </Popconfirm>
            </span>
          )}
        />

        <Column
          title={t("createdAt")}
          dataIndex="name"
          key={"name"}
          render={(text, record: PaymentConfig) => (
            <span>{unixToFullDate(record.createdAt)}</span>
          )}
        />

        {/* <Column
          fixed="right"
          width={120}
          align="center"
          title=""
          key="action"
          dataIndex={""}
          render={(text, record: PaymentConfig) => (
            //@ts-ignore
            <DropdownCell
              text={t("action")}
              items={[
                {
                  onClick: () => "",
                  // createOrderModalRef.current?.handleUpdate(record),
                  label: (
                    <Button
                      icon={<GrEdit />}
                      type="primary"
                      className="w-full justify-center !flex !items-center gap-2 !font-medium"
                      onClick={() => onEdit?.(record)}
                    >
                      {t("update")}
                    </Button>
                  ),
                  key: "update",
                  // hidden: !hasUpdatePaymentConfigPermission,
                },

                // {
                //   label: (
                //     <Popconfirm
                //       placement="topLeft"
                //       title={
                //         <div>
                //           <h1 className="text-sm">{t("confirm?")}</h1>
                //         </div>
                //       }
                //       onConfirm={() => onDelete?.(record.id)}
                //       okText={t("yes")}
                //       cancelText={t("no")}
                //     >
                //       <Button
                //         loading={loadingDelete}
                //         icon={<HiOutlineTrash className="text-lg" />}
                //         className={`w-full justify-center !flex !items-center gap-2 !text-red-500 !font-medium`}
                //       >
                //         {t("delete")}
                //       </Button>
                //     </Popconfirm>
                //   ),
                //   key: "delete",
                //   // hidden: !hasDeletePaymentConfigPermission,
                // },
              ]}
              trigger={["click"]}
            >
              <a onClick={(e) => e.preventDefault()}>
                <Space>
                  {t("action")}
                  <DownOutlined />
                </Space>
              </a>
            </DropdownCell>
          )}
        /> */}
      </AriviTable>
      {pagination && <Pagination {...pagination} />}
    </div>
  );
};
