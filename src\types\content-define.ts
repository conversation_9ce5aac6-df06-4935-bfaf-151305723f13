export enum ContentDefineType {
  Ios = "IOS",
  Android = "ANDROID",
  ReviewVideoImagePC = "REVIEW_VIDEO_IMAGE_PC",
  ReviewVideoPC = "REVIEW_VIDEO_PC",
  ReviewVideoImageMobile = "REVIEW_VIDEO_IMAGE_MOBILE",
  ReviewVideoMobile = "REVIEW_VIDEO_MOBILE",
}

export interface ContentDefine {
  id: number;
  createdAt: number;
  updatedAt: number;
  isDeleted: boolean;
  body: string;
  type: ContentDefineType;
}
