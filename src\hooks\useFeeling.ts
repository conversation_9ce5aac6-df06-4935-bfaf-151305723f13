import { useState } from "react";
import { QueryParam } from "types/query";
import { Feeling } from "types/feeling";
import { feelingApi } from "api/feeling.api";

export interface FeelingQuery extends QueryParam {}

interface UseFeelingProps {
  initQuery: FeelingQuery;
}

export const useFeeling = ({ initQuery }: UseFeelingProps) => {
  const [data, setData] = useState<Feeling[]>([]);
  const [total, setTotal] = useState(0);
  const [query, setQuery] = useState<FeelingQuery>(initQuery);
  const [loading, setLoading] = useState(false);

  const fetchData = async () => {
    setLoading(true);
    try {
      const { data } = await feelingApi.findAll(query);
      setData(data.feelings);
      setTotal(data.total);
    } finally {
      setLoading(false);
    }
  };

  return {
    feelings: data,
    totalFeeling: total,
    fetchFeeling: fetchData,
    loadingFeeling: loading,
    setQueryFeeling: setQuery,
    queryFeeling: query,
  };
};
