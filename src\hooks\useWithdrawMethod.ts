import { withdrawMethodApi } from "api/withdrawMethod.api";
import { useState } from "react";
import { WithdrawMethod } from "types/withdrawMethod";
import { QueryParam } from "types/query";

export interface WithdrawMethodQuery extends QueryParam {}

interface UseWithdrawMethodProps {
  initQuery: WithdrawMethodQuery;
}

export const useWithdrawMethod = ({ initQuery }: UseWithdrawMethodProps) => {
  const [data, setData] = useState<WithdrawMethod[]>([]);
  const [total, setTotal] = useState(0);
  const [query, setQuery] = useState<WithdrawMethodQuery>(initQuery);
  const [loading, setLoading] = useState(false);

  const fetchData = async () => {
    setLoading(true);
    try {
      const { data } = await withdrawMethodApi.findAll(query);

      setData(data.withdrawMethods);
      setTotal(data.total);
    } finally {
      setLoading(false);
    }
  };

  return { withdrawMethods: data, total, fetchData, loading, setQuery, query };
};
