import { <PERSON>, Button, Container, Typography } from "@mui/material";
import { Button as AntdButton } from "antd";
import React from "react";
import { Link, useNavigate } from "react-router-dom";
import notFoundImg from "assets/images/notFoundImg.svg";

export const NotFoundPage = () => {
  const navigate = useNavigate();

  return (
    <Box
      display="flex"
      flexDirection="column"
      height="100vh"
      textAlign="center"
      justifyContent="center"
    >
      <Container maxWidth="md">
        <img src={notFoundImg} alt="404" />
        <Typography align="center" variant="h1" mb={4}>
          Opps!!!
        </Typography>
        <Typography align="center" variant="h4" mb={4}>
          This page you are looking for could not be found.
        </Typography>
        <Button
          color="primary"
          variant="contained"
          component={Link}
          to="/"
          className="hover:opacity-80 opacity-100 transition-opacity duration-300"
        >
          Go Back to Home
        </Button>
      </Container>
    </Box>
  );

  // return (
  //   <Result
  //     status="404"
  //     title="404"
  //     subTitle="Sorry, the page you visited does not exist."
  //     extra={
  //       <Button
  //         onClick={() => {
  //           navigate("/");
  //         }}
  //         type="primary"
  //       >
  //         Về trang chủ
  //       </Button>
  //     }
  //   />
  // );
};
