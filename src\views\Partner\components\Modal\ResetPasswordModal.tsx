import { Col, Form, Input, message, Modal, Row } from "antd";
import { Rule } from "antd/lib/form";
import { partnerApi } from "api/partner.api";
import { SingleImageUpload } from "components/Upload/SingleImageUpload";
import React, { useEffect, useImperativeHandle, useState } from "react";
import { useTranslation } from "react-i18next";
import { ModalStatus } from "types/modal";
import { Partner } from "types/partner";

const rules: Rule[] = [{ required: true }];

export interface ResetPasswordModalRef {
  handleCreate: () => void;
  handleUpdate: (partner: Partner) => void;
}
interface ResetPasswordModalProps {
  onClose: () => void;
  onSubmitOk: () => void;
}

export const ResetPasswordModal = React.forwardRef(
  ({ onClose, onSubmitOk }: ResetPasswordModalProps, ref) => {
    const [form] = Form.useForm();
    const [loading, setLoading] = useState(false);
    const [visible, setVisible] = useState(false);
    const [status, setStatus] = useState<ModalStatus>("create");
    const { t } = useTranslation();

    const [selectedPartner, setSelectedPartner] = useState<Partner>();
    useImperativeHandle<any, ResetPasswordModalRef>(
      ref,
      () => ({
        handleCreate() {
          form.resetFields();
          setVisible(true);
          setStatus("create");
        },
        handleUpdate(partner: Partner) {
          //   form.setFieldsValue({ ...partner });
          setSelectedPartner(partner);
          setVisible(true);
          setStatus("update");
        },
      }),
      []
    );

    const createData = async () => {
      const valid = await form.validateFields();
      const data = { resetPassword: form.getFieldsValue() };

      setLoading(true);
      try {
        // const res = await resetPasswordApi.create(data);
        message.success("Create ResetPassword successfully!");
        onClose();
        onSubmitOk();
      } finally {
        setLoading(false);
      }
    };

    const updateData = async () => {
      const valid = await form.validateFields();
      const data = form.getFieldsValue();
      setLoading(true);
      try {
        const res = await partnerApi.resetPass(selectedPartner?.id || 0, data);
        message.success(t("actionSuccessfully"));
        onClose();
        onSubmitOk();
        setVisible(false);
      } finally {
        setLoading(false);
      }
    };

    return (
      <Modal
        onCancel={() => {
          onClose?.();
          setVisible(false);
        }}
        afterClose={() => {
          form.resetFields();
        }}
        destroyOnClose
        visible={visible}
        title={status == "create" ? "Create ResetPassword" : t("resetPass")}
        style={{ top: 20 }}
        width={600}
        confirmLoading={loading}
        onOk={() => {
          status == "create" ? createData() : updateData();
        }}
        cancelText={t("close")}
      >
        <Form
          layout="vertical"
          form={form}
          validateTrigger={["onBlur", "onChange"]}
        >
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item label={t("password")} name="password" rules={rules}>
                <Input.Password placeholder="" className="w-full" />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    );
  }
);
