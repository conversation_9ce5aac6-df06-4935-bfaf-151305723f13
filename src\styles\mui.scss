.MuiButtonBase-root {
  &.<PERSON><PERSON><PERSON><PERSON>on-colorPrimary {
    height: 42px !important;

    &:not(.MuiButton-outlinedPrimary):not(.Mu<PERSON>Button-textPrimary) {
      background-color: var(--primary-color) !important;
      color: var(--action-light-4-color) !important;
    }

    &.MuiButton-sizeSmall {
      height: unset !important;
    }
  }
  &:disabled {
    color: #00000040 !important;
    background-color: #0000000a !important;
    opacity: 1 !important;
    border-color: #d9d9d9 !important;
  }
}

.MuiContainer-root {
  max-width: 1127px;
  padding: 24px 0;
}

@media screen and (min-width: 1390px) {
  .MuiContainer-root {
    padding: 24px 0px !important;
  }
}

@media screen and (max-width: 430px) {
  .MuiContainer-root {
    padding: 24px 15px;
  }
}
