import {
  DownloadOutlined,
  ExportOutlined,
  SearchOutlined,
} from "@ant-design/icons";
import {
  Button,
  Input,
  message,
  Popconfirm,
  Select,
  Space,
  Table,
  Tabs,
  Tag,
  DatePicker,
  Modal,
} from "antd";
import TextArea from "antd/es/input/TextArea";
import dayjs from "dayjs";
import { useHandleCustomer } from "hooks/useHandleCustomer";
import { debounce } from "lodash";
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";

import { formatVND, getTitle } from "utils";
import { unixToFullDate } from "utils/dateFormat";
import { MyTableColumn } from "utils/excel";

import { Pagination } from "components/Pagination";
import {
  SupportTicket,
  SupportTicketCreatedBy,
  SupportTicketStatus,
} from "types/supportTicket";
import { useSupportTicket } from "hooks/useSupportTicket";
import { useTranslation } from "react-i18next";
import { TextField } from "@mui/material";
import { handleExport, MyExcelColumn } from "utils/MyExcel";
import { supportTicketApi } from "api/supportTicket.api";
import { Review, ReviewStatus, ReviewStatusTrans } from "types/review";
import { useReview } from "hooks/useReview";
import { TaskList } from "./Table/TaskList";
import { reviewApi } from "api/review.api";
import { useProject } from "hooks/useProject";
import { usePartner } from "hooks/usePartner";
import { ReactComponent as DownloadIcon } from "assets/svgs/download.svg"
import { formatDate } from "utils/date";
import { settings } from "settings";

const { ColumnGroup, Column } = Table;
const { RangePicker } = DatePicker;

interface PropTypes {
  status: ReviewStatus;
  title?: string;
  isFocus?: boolean;
  parentLastUpdate?: number;
  onSubmitOk?: (projectId?: number) => void;
  selectedProjectId?: number;
  selectedParnerId?: number;
  onSubmitParnertOk?: (partnerId?: number) => void;
  valueStatus?: string[],
  missionDisplay?: string;
}

export const TaskListTab = React.memo(
  ({
    title = "",
    status,
    isFocus,
    parentLastUpdate,
    selectedProjectId,
    selectedParnerId,
    onSubmitOk,
    onSubmitParnertOk,
    valueStatus = [
      ReviewStatus.Pending,
      ReviewStatus.SystemPending,
      ReviewStatus.Warranty,
      ReviewStatus.Complete,
      ReviewStatus.Reject,
    ],
    missionDisplay = "list"
  }: PropTypes) => {
    const [note, setNote] = useState<string>();
    const [loadingConfirmDeposit, setLoadingConfirmDeposit] = useState(false);
    const [loadingRejectDeposit, setLoadingRejectDeposit] = useState(false);
    const [selectedCustomerId, setSelectedCustomerId] = useState<number>();
    const [lastUpdate, setLastUpdate] = useState(0);
    const { t } = useTranslation();
    const [activeTabKey, setActiveTabKey] = useState("1");

    // State for date range export modal
    const [showExportModal, setShowExportModal] = useState(false);
    const [exportDateRange, setExportDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs] | null>(null);
    const [exportLoading, setExportLoading] = useState(false);

    const {
      fetchProject,
      projects,
      totalProject,
      setQueryProject,
      queryProject,
      loadingProject,
    } = useProject({
      initQuery: {
        page: 1,
        limit: 100,
      },
    });

    const {
      fetchPartner,
      loadingPartner,
      partners,
      queryPartner
    } = usePartner({
      initQuery: {
        page: 1,
        limit: 100,
      },
    });

    const [selectType, setSelectType] = useState()

    const optionsType = [
      {
        value: "",
        label: t("allTypeReview")
      },
      {
        value: false,
        label: t("contentOnly")
      },
      {
        value: true,
        label: t("withPictures")
      },
    ]

    const debounceSearchProject = useCallback(
      debounce((keyword) => {
        console.log(keyword);
        (queryProject.limit = 100), (queryProject.page = 1);
        queryProject.search = keyword;
        fetchProject();
      }, 300),
      []
    );

    const debounceSearchParnert = useCallback(
      debounce((keyword) => {
        console.log(keyword);
        (queryPartner.limit = 100), (queryPartner.page = 1);
        queryPartner.search = keyword;
        fetchPartner();
      }, 300),
      []
    );

    console.log({ childLastUpdate: lastUpdate, status });

    const exportColumns: MyExcelColumn<Review>[] = [
      {
        width: 15,
        header: t("idNv"),
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "id",
        render: (record: Review) => {
          return record.id?.toString() || '';
        },
      },
      {
        width: 25,
        header: t("projectCode"),
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "projectCode",
        render: (record: Review) => {
          return record.project?.code || '';
        },
      },
      {
        width: 25,
        header: t("projectName"),
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "projectName",
        render: (record: Review) => {
          return record.project?.name || '';
        },
      },
      {
        width: 25,
        header: t("location"),
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "location",
        isHyperLink: true,
        render: (record: Review) => {
          if (record.project?.mapUrl) {
            return {
              text: "Xem vị trí",
              hyperlink: record.project.mapUrl,
              styles: {
                font: {
                  color: {
                    argb: "#1890ff", // Màu xanh như Ant Design
                  },
                  underline: true,
                },
              },
            };
          }
          return '';
        },
      },
      {
        width: 25,
        header: t("createdAt"),
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "createdAt",
        render: (record: Review) => {
          return formatDate(record.createdAt, settings.fullDateFormat)
        },
      },
      {
        width: 25,
        header: t("timeDelivery"),
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "timeDelivery",
        render: (record: Review) => {
          return record.acceptAt !== 0 && record.acceptAt !== undefined ? formatDate(record.acceptAt, settings.fullDateFormat) : "--";
        },
      },
      {
        width: 40,
        header: t("content"),
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "content",
        render: (record: Review) => {
          return record.content || '';
        },
      },
      {
        width: 25,
        header: t("image"),
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "isExistImage",
        isHyperLink: true,
        render: (record: Review) => {
          if (record.isExistImage && record.fileAttaches?.[0]?.url) {
            // If has image and review link, create hyperlink to review URL
            return {
              text: "Xem hình ảnh",
              hyperlink: record.fileAttaches?.[0]?.url,
              styles: {
                font: {
                  color: {
                    argb: "#1890ff", // Màu xanh như Ant Design
                  },
                  underline: true,
                },
              },
            };
          }
          return t("no");

        },
      },
      {
        width: 20,
        header: t("partnerCode"),
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "partnerCode",
        render: (record: Review) => {
          return record.partner?.code ?? t("missionPending");
        },
      },
      {
        width: 25,
        header: t("partnerName"),
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "partnerName",
        render: (record: Review) => {
          return record.partner?.fullName || '';
        },
      },
      {
        width: 30,
        header: t("rank"),
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "rank",
        render: (record: Review) => {
          return record.partner?.rank?.name || '';
        },
      },
      {
        width: 30,
        header: t("linkReview"),
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "linkReview",
        isHyperLink: true,
        render: (record: Review) => {
          if (record.reviewUrl) {
            return {
              text: "Xem đánh giá",
              hyperlink: record.reviewUrl,
              styles: {
                font: {
                  color: {
                    argb: "#1890ff",
                  },
                  underline: true,
                },
              },
            };
          }
          return '';
        },
      },
      {
        width: 20,
        header: t("status"),
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "status",
        render: (record: Review) => {
          return t(`review${record.status}`);
        },
      },
      {
        width: 20,
        header: t("revenue"),
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "revenue",
        render: (record: Review) => {
          return `${formatVND(record.price ?? 0)}₫`;
        },
      },
      {
        width: 20,
        header: t("fee"),
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "fee",
        render: (record: Review) => {
          return `${formatVND(record.rewardPoint ?? 0)}₫`;
        },
      },
      {
        width: 20,
        header: t("profit"),
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "profit",
        render: (record: Review) => {
          return `${formatVND(record.profit ?? 0)}₫`;
        },
      },
    ];

    const {
      reviews,
      fetchReview,
      fetchDataReject,
      loadingReview,
      queryReview,
      totalReview,
      setQueryReview,
    } = useReview({
      initQuery: {
        page: 1,
        limit: 20,
        status: status === ReviewStatus.All ? undefined : status,
        queryObject: ReviewStatus.All && JSON.stringify([
          {
            type: "multi-filter",
            field: "review.status",
            value: valueStatus,
          },
          {
            type: "filter",
            field: "reviews.isExistImage"
          }
        ])
      },
    });

    const cleanedFilter = (rawFilter?: string) => {
      if (!rawFilter) return [];
      const filters = JSON.parse(rawFilter);

      return filters.map((filter: any) => {
        if (filter.type === "multi-filter" && filter.field === "review.status") {
          return {
            ...filter,
            value: filter.value.filter((v: string) => v !== "PENDING"),
          };
        }
        return filter;
      });
    };


    // Handle export with date range
    const handleExportWithDateRange = async () => {
      if (!exportDateRange) {
        message.error(t("pleaseSelectDateRange"));
        return;
      }

      setExportLoading(true);
      try {
        // Create export query with date range

        const query = {
          ...queryReview,
          queryObject: JSON.stringify(cleanedFilter(queryReview.queryObject)),
        }

        const exportQuery = {
          ...query,
          fromAt: exportDateRange[0].startOf('day').unix(),
          toAt: exportDateRange[1].endOf('day').unix(),
          // Remove pagination for export
          page: 1,
          limit: 999999,
        };

        await handleExport({
          onProgress(percent) { },
          exportColumns,
          fileType: "xlsx",
          dataField: "reviews",
          query: exportQuery,
          api: reviewApi.findAll,
          fileName: `${t("approveProject")}_${exportDateRange[0].format('DD-MM-YYYY')}_${exportDateRange[1].format('DD-MM-YYYY')}`,
          sheetName: `${t("approveProject")}`,
        });

        message.success(t("exportSuccess"));
        setShowExportModal(false);
        setExportDateRange(null);
      } catch (error) {
        message.error(t("exportFailed"));
        console.error("Export error:", error);
      } finally {
        setExportLoading(false);
      }
    };

    useEffect(() => {
      if (selectedProjectId !== undefined) {
        queryReview.projectId = selectedProjectId;
        fetchReview();
      }
    }, [selectedProjectId]);

    useEffect(() => {
      queryReview.page = 1;
      if (status === ReviewStatus.Reject) {
        fetchDataReject();
      } else {
        fetchReview();
      }
    }, [activeTabKey]);

    useEffect(() => {
      if (status === ReviewStatus.Reject) {
        fetchDataReject();
      } else {
        fetchReview();
      }
      fetchPartner();
    }, [queryReview]);

    useEffect(() => {
      if (isFocus && parentLastUpdate != lastUpdate) {
        if (status === ReviewStatus.Reject) {
          fetchDataReject();
        } else {
          fetchReview();
        }
        if (parentLastUpdate) setLastUpdate(parentLastUpdate);
      }
    }, [parentLastUpdate, lastUpdate, isFocus]);

    const debounceSearch = debounce((search) => {
      queryReview.search = search;
      queryReview.page = 1;
      if (status === ReviewStatus.Reject) {
        fetchDataReject();
      } else {
        fetchReview();
      }
    }, 500);

    useEffect(() => {
      fetchProject();
    }, []);

    return (
      <div>
        <div className="filter-container w-full">
          <div className="flex flex-wrap gap-4 w-full">
            <div className="filter-item flex-1 min-w-[274px] !h-[56px]">
              <Input.Search
                size="large"
                style={{ minHeight: 56, width: '100%' }}
                enterButton={<SearchOutlined style={{ fontSize: 20 }} onClick={() => {
                  if (status === ReviewStatus.Reject) {
                    fetchDataReject();
                  } else {
                    fetchReview();
                  }
                }} />}
                className="w-full search-btn !h-[56px] search-btn-full"
                allowClear
                onClear={() => {
                  setQueryReview({ ...queryReview, search: "", page: 1 });
                  if (status === ReviewStatus.Reject) {
                    fetchDataReject();
                  } else {
                    fetchReview();
                  }
                }}
                placeholder={t("searchIdMission")}
                onKeyDown={(ev) => {
                  if (ev.code === "Enter") {
                    queryReview.page = 1;
                    setQueryReview({ ...queryReview });
                  }
                }}
                onChange={(ev) => {
                  queryReview.search = ev.currentTarget.value;
                  queryReview.page = 1;
                }}
              />
            </div>

            <div className="filter-item flex-1 min-w-[180px]">
              <Select
                showSearch
                value={selectedProjectId}
                defaultValue={"" as any}
                onSearch={(value) => {
                  console.log("first");
                  debounceSearchProject(value);
                }}
                style={{ height: 60, width: '100%' }}
                placeholder={t("allProject")}
                className="w-full !h-[56px]"
                onChange={(value) => {
                  queryReview.projectId = value;
                  queryReview.page = 1;
                  if (status === ReviewStatus.Reject) {
                    fetchDataReject();
                  } else {
                    fetchReview();
                  }
                  onSubmitOk?.(value);
                }}
                onClear={() => {
                  queryReview.projectId = undefined;
                  onSubmitOk?.();
                  fetchProject();
                }}
                filterOption={false}
              >
                {[{ id: "", name: t("allProject"), code: "" }, ...projects].map(
                  (project) => (
                    <Select.Option key={project.id} value={project.id}>
                      <div>{project.name}</div>{" "}
                      <div className="text-primary">{project.code}</div>
                    </Select.Option>
                  )
                )}
              </Select>
            </div>

            <div className="filter-item flex-1 min-w-[180px]">
              <Select
                showSearch
                value={selectedParnerId}
                defaultValue={"" as any}
                onSearch={(value) => {
                  console.log("first");
                  debounceSearchParnert(value);
                }}
                style={{ height: 60, width: '100%' }}
                placeholder={t("allPartner")}
                className="w-full !h-[56px]"
                onChange={(value) => {
                  queryReview.partnerId = value;
                  queryReview.page = 1;
                  if (status === ReviewStatus.Reject) {
                    fetchDataReject();
                  } else {
                    fetchReview();
                  }
                  onSubmitParnertOk?.(value)
                }}
                onClear={() => {
                  queryReview.partnerId = undefined;
                  onSubmitParnertOk?.()
                }}
                filterOption={false}
              >
                {[{ id: "", fullName: t("allPartner"), code: "" }, ...partners].map(
                  (partners) => (
                    <Select.Option key={partners.id} value={partners.id}>
                      <span>{(partners as any).fullName}</span>{" "}
                      <div className="text-[#1A73E8]">{partners.code}</div>
                    </Select.Option>
                  )
                )}
              </Select>
            </div>

            <div className="filter-item flex-1 min-w-[180px]">
              <Select
                showSearch
                value={selectType}
                defaultValue={"" as any}
                onSearch={(value) => {
                  console.log("first");
                }}
                style={{ height: 60, width: '100%' }}
                placeholder={t("allTypeReview")}
                className="w-full !h-[56px]"
                onChange={(value) => {
                  queryReview.page = 1;
                  if (value === "") {
                    (queryReview as any).queryObject = JSON.stringify([
                      {
                        type: "multi-filter",
                        field: "review.status",
                        value: valueStatus,
                      }])
                  } else {
                    (queryReview as any).queryObject = JSON.stringify([
                      {
                        type: "multi-filter",
                        field: "review.status",
                        value: valueStatus,
                      },
                      {
                        type: "multi-filter",
                        field: "review.isExistImage",
                        value: [value]
                      }
                    ])
                  }

                  if (status === ReviewStatus.Reject) {
                    fetchDataReject();
                  } else {
                    fetchReview();
                  }
                }}
                onClear={() => {
                  (queryReview as any).queryObject = JSON.stringify([
                    {
                      type: "multi-filter",
                      field: "review.status",
                      value: valueStatus,
                    },
                    {
                      type: "multi-filter",
                      field: "review.isExistImage",
                      value: []
                    }
                  ])
                  if (status === ReviewStatus.Reject) {
                    fetchDataReject();
                  } else {
                    fetchReview();
                  }
                }}
                filterOption={false}
              >
                {optionsType.map(
                  (opt, index) => (
                    <Select.Option key={index} value={opt.value}>
                      <div>{opt.label}</div>
                    </Select.Option>
                  )
                )}
              </Select>
            </div>

            <div className="filter-item flex-shrink-0">
              <Button
                type="primary"
                loading={false}
                icon={<DownloadIcon />}
                style={{ height: 56 }}
                onClick={() => setShowExportModal(true)}
              >
                <span className="text-[14px] font-[400] text-[#EFF5FF]">{t("exportExcel")}</span>
              </Button>
            </div>
          </div>
        </div>

        <TaskList
          onRefreshData={(projectId?: number) => {
            fetchReview();
            onSubmitOk?.(projectId);
          }}
          dataSource={reviews}
          loading={loadingReview}
          pagination={{
            total: totalReview,
            defaultPageSize: queryReview.limit,
            currentPage: queryReview.page,
            onChange: ({ page, limit }) => {
              Object.assign(queryReview, {
                page,
                limit,
              });
              if (status === ReviewStatus.Reject) {
                fetchDataReject();
              } else {
                fetchReview();
              }
            },
          }}
          missionDisplay={missionDisplay}
        />

        {/* Export Date Range Modal */}
        <Modal
          title={t("selectExportDateRange")}
          open={showExportModal}
          onOk={handleExportWithDateRange}
          onCancel={() => {
            setShowExportModal(false);
            setExportDateRange(null);
          }}
          confirmLoading={exportLoading}
          okText={t("exportExcel")}
          cancelText={t("cancel")}
          width={500}
        >
          <div className="py-4">
            <div className="mb-4">
              <RangePicker
                value={exportDateRange}
                onChange={(dates) => setExportDateRange(dates as any)}
                style={{ width: '100%', height: 40 }}
                format="DD/MM/YYYY"
                placeholder={[t("startDate"), t("endDate")]}
                allowClear
              />
            </div>
          </div>
        </Modal>
      </div>
    );
  }
);