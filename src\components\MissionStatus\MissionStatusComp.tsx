
import { Tag } from 'antd'
import { useTranslation } from 'react-i18next';
import "./styles/MissionStatus.scss";
import clsx from 'clsx';
import { ReviewStatus, ReviewStatusTrans } from 'types/review';

interface Props {
  status: ReviewStatus
}

export const MissionStatusComp = ({ status }: Props) => {
  const { t } = useTranslation();

  return (
    <Tag
      className={clsx("project-status", status)}
      color={ReviewStatusTrans[status as keyof typeof ReviewStatusTrans]?.color}
    >
      {t(`review${status}`)}
    </Tag>
  )
}
