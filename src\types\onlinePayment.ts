import { Bank } from "./customer";

export enum OnlinePaymentType {
  Bank = "BANK",
  EWallet = "E-WALLET",
}

export const OnlinePaymentTypeTrans = {
  [OnlinePaymentType.Bank]: {
    //   label: "<PERSON><PERSON> hàng",
    value: OnlinePaymentType.Bank,
  },
  [OnlinePaymentType.EWallet]: {
    //   label: "Ví điện tử",
    value: OnlinePaymentType.EWallet,
  },
};

export interface OnlinePayment {
  id: number;
  createdAt: number;
  updatedAt: number;
  isDeleted: boolean;
  name: string;
  type: OnlinePaymentType;
  icon: string;
  ownerName: string;
  bankNumber: string;
  isDirect: boolean; // trực tiếp, không cần duyệt thủ công khi nạp qua online payment này
  qrCode: string; // code hoặc hình ảnh QR
  isEnabled: boolean;
  bank: Bank;
}
