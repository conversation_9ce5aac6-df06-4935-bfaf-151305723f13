export enum ProductStatus {
  Active = "ACTIVE",
  Inactive = "INACTIVE",
}

export const ProductStatusTrans = {
  [ProductStatus.Active]: {
    label: "Hiện",
    value: ProductStatus.Active,
    color: "green",
  },
  [ProductStatus.Inactive]: {
    label: "Ẩn",
    value: ProductStatus.Active,
    color: "red",
  },
};

export interface Product {
  id: number;
  createdAt: number;
  updatedAt: number;
  isDeleted: boolean;
  name: string;
  code: string;
  icon: string;
  note: string;
  price: number;
  unitPrice: number;
  numReview: number; //sl đánh giá
  estimatedDay: number;
  maxLengthCharReview: number; //sl ký tự đánh giá
  status: ProductStatus;
  nameEn: string;
  slowMinQuantity: number
  slowMaxQuantity: number;
  position?: number;
  minKeyword?: number; // Minimum number of keywords required for the product
}
