import {
  DownOutlined,
  EditOutlined,
  LockOutlined,
  RetweetOutlined,
  UnlockOutlined,
} from "@ant-design/icons";
import { Button, message, Popconfirm, Space, Table, Tag } from "antd";
import TextArea from "antd/es/input/TextArea";
import Column from "antd/lib/table/Column";
import { customerWithdrawApi } from "api/customerWithdraw.api";
import { IPagination, Pagination } from "components/Pagination";
import { AriviTable } from "components/Table/AriviTable";
import DropdownCell from "components/Table/DropdownCell";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { GrEdit } from "react-icons/gr";
import { HiOutlineTrash } from "react-icons/hi2";
import {
  CustomerWithdraw,
  CustomerWithdrawStatus,
  CustomerWithdrawStatusTrans,
} from "types/customerWithdraw";
import { formatVND } from "utils";
import { unixToFullDate } from "utils/dateFormat";

interface PropsType {
  dataSource: CustomerWithdraw[];
  loading: boolean;
  loadingDelete?: boolean;
  pagination?: IPagination;
  onEdit?: (customerWithdraw: CustomerWithdraw) => void;
  onDelete?: (customerWithdrawId: number) => void;
  onActive?: (customerWithdrawId: number) => void;
  onInactive?: (customerWithdrawId: number) => void;
  onRefreshData: () => void;
  // hasDeleteCustomerWithdrawPermission?: boolean;
  // hasUpdateCustomerWithdrawPermission?: boolean;
}

export const CustomerWithdrawList = ({
  dataSource,
  loading,
  loadingDelete,
  pagination,
  onDelete,
  onEdit,
  onActive,
  onInactive,
  onRefreshData,
}: // hasDeleteCustomerWithdrawPermission,
// hasUpdateCustomerWithdrawPermission,

PropsType) => {
  const [loadingStatus, setLoadingStatus] = useState(false);
  const [note, setNote] = useState("");
  const { t } = useTranslation();
  const handleApprove = async (id: number) => {
    try {
      setLoadingStatus(true);
      console.log(note);
      const { data } = await customerWithdrawApi.approve(id, { note });
      message.success(t("actionSuccessfully"));
      setNote("");
      onRefreshData();
    } catch (error) {
      console.log(error);
    } finally {
      setLoadingStatus(false);
    }
  };

  const handleReject = async (id: number) => {
    try {
      setLoadingStatus(true);
      const { data } = await customerWithdrawApi.reject(id, { note });
      message.success(t("actionSuccessfully"));
      setNote("");

      onRefreshData();
    } catch (error) {
      console.log(error);
    } finally {
      setLoadingStatus(false);
    }
  };

  return (
    <div>
      <AriviTable
        // bordered

        loading={loading}
        pagination={false}
        rowKey="id"
        dataSource={dataSource}
        className="custom-scrollbar"
        scroll={{ x: "max-content" }}

        // onChange={}
      >
        <Column
          title={`${t("code")}`}
          dataIndex="code"
          key="code"
          render={(text, record: CustomerWithdraw) => (
            <>
              <div>{record.code}</div>
            </>
          )}
        />
        <Column
          title={`${t("customer")}`}
          dataIndex="customer"
          key="customer"
          render={(text, record: CustomerWithdraw) => (
            <>
              <div>
                {t("fullName")}: {record.customer?.name}
              </div>
              <div>Email: {record.customer?.email}</div>
              <div>
                {t("phoneNumber")}: {record.customer?.phone}
              </div>
            </>
          )}
        />
        <Column
          align="right"
          title={`${t("amountRequest")}`}
          dataIndex="amountRequest"
          key="amountRequest"
          render={(text, record: CustomerWithdraw) => (
            <>
              <div>{formatVND(record.amount)}</div>
            </>
          )}
        />
        {/* <Column
            title={`${t("bankInfo")}`}
            dataIndex="bankInfo"
            key="bankInfo"
            render={(text, record: CustomerWithdraw) => (
              <>
                {record.bankName ? (
                  <div>
                    <div>{record?.bankName}</div>
                    <div>
                      {t("stk")}: {record?.bankNumber}
                    </div>
                  </div>
                ) : (
                  <div></div>
                )}
              </>
            )}
          /> */}
        <Column
          title={`${t("note")}`}
          dataIndex="note"
          width={250}
          key="note"
          render={(text, record: CustomerWithdraw) => (
            <>
              <div>{record.note}</div>
            </>
          )}
        />
        <Column
          title={`${t("inspector")}`}
          dataIndex="inspector"
          key="inspector"
          render={(text, record: CustomerWithdraw) => (
            <>
              <div>
                {record.status === CustomerWithdrawStatus.Pending
                  ? ""
                  : record?.inspecStaff
                  ? record?.inspecStaff.username +
                    " - " +
                    record?.inspecStaff.fullName
                  : ""}
              </div>
            </>
          )}
        />
        <Column
          title={`${t("inspectAt")}`}
          dataIndex="inspectAt"
          width={250}
          key="inspectAt"
          render={(text, record: CustomerWithdraw) => (
            <>
              <div>{unixToFullDate(record.inspecAt)}</div>
            </>
          )}
        />
        <Column
          width={100}
          title={t("status")}
          dataIndex="name"
          align="center"
          key={"name"}
          render={(text, record: CustomerWithdraw) => (
            <Tag color={CustomerWithdrawStatusTrans[record.status]?.color}>
              {t(record.status)}
            </Tag>
          )}
        />

        <Column
          title={`${t("inspectNote")}`}
          dataIndex="inspectNote"
          width={150}
          key="inspectNote"
          render={(text, record: CustomerWithdraw) => (
            <>
              <div>{record.inspecNote}</div>
            </>
          )}
        />
        <Column
          fixed="right"
          width={120}
          align="center"
          title=""
          key="action"
          dataIndex={""}
          render={(text, record: CustomerWithdraw) => (
            <>
              {record.status === CustomerWithdrawStatus.Pending && (
                //@ts-ignore
                <DropdownCell
                  text={t("action")}
                  items={[
                    {
                      onClick: () => "",
                      // createOrderModalRef.current?.handleUpdate(record),
                      label: (
                        <Popconfirm
                          placement="topLeft"
                          title={
                            <>
                              <h3>{t("approveRequest")}</h3>
                              <TextArea
                                onChange={(e) => {
                                  setNote(e.target.value);
                                  console.log(e.target.value);
                                }}
                                placeholder={t("enterNote")}
                              />
                            </>
                          }
                          onConfirm={() => handleApprove(record?.id)}
                          okText={t("save")}
                          cancelText={t("close")}
                        >
                          <Button type="primary">{t("approve")}</Button>
                        </Popconfirm>
                      ),
                      key: "approve",
                      hidden: record.status !== CustomerWithdrawStatus.Pending,
                    },
                    {
                      onClick: () => "",
                      // createOrderModalRef.current?.handleUpdate(record),
                      label: (
                        <Popconfirm
                          className="w-full "
                          placement="topLeft"
                          title={
                            <>
                              <h3>{t("rejectRequest")}</h3>
                              <TextArea
                                onChange={(e) => setNote(e.target.value)}
                                placeholder={t("enterNote")}
                              />
                            </>
                          }
                          onConfirm={() => handleReject(record?.id)}
                          okText={t("save")}
                          cancelText={t("close")}
                        >
                          <Button danger>{t("reject")}</Button>
                        </Popconfirm>
                      ),
                      key: "reject",
                      hidden: record.status !== CustomerWithdrawStatus.Pending,
                    },
                  ]}
                  trigger={["click"]}
                >
                  <a onClick={(e) => e.preventDefault()}>
                    <Space>
                      {t("action")}
                      <DownOutlined />
                    </Space>
                  </a>
                </DropdownCell>
              )}
            </>
          )}
        />
      </AriviTable>
      {pagination && <Pagination {...pagination} />}
    </div>
  );
};
