// SurveyActivityLogTab.js
import { Table, Tag } from "antd";
import { FormInstance } from "antd/es/form";
import Column from "antd/lib/table/Column";

import { ModalStatus } from "types/modal";
import { unixToFullDate } from "utils/dateFormat";
import { useEffect } from "react";
import { Pagination } from "components/Pagination";
import { usePromotion } from "hooks/usePromotion";
import { Promotion } from "types/promotion";
import { Project, ProjectStatusTrans } from "types/project";
import { useTranslation } from "react-i18next";
import { useProject } from "hooks/useProject";

interface PropTypes {
  promotionId?: number;
}
const PromotionProjectTab = ({ promotionId }: PropTypes) => {
  const { projects, fetchProject, loadingProject, queryProject, totalProject } =
    useProject({
      initQuery: {
        page: 1,
        limit: 50,
        promotionId,
      },
    });
  const { t } = useTranslation();

  useEffect(() => {
    fetchProject();
  }, []);

  return (
    <div>
      <Table
        bordered
        scroll={{
          x: "max-content",
          y: "400px",
        }}
        loading={loadingProject}
        pagination={false}
        rowKey="id"
        dataSource={projects}
        size="small"
        className="custom-scrollbar"
        // onChange={}
      >
        <Column
          title={t("code")}
          dataIndex="code"
          key={"code"}
          render={(text, record: Project) => <span>{record.code}</span>}
        />
        <Column
          title={t("projectName")}
          dataIndex="name"
          key={"name"}
          render={(text, record: Project) => <span>{record.name}</span>}
        />
        <Column
          title={t("customer")}
          dataIndex="name"
          key={"name"}
          render={(text, record: Project) => (
            <span>{record.customer?.name}</span>
          )}
        />
        <Column
          title={t("product")}
          dataIndex="name"
          key={"name"}
          render={(text, record: Project) => (
            <span>{record.product?.name}</span>
          )}
        />
        <Column
          title={t("status")}
          dataIndex="name"
          align="center"
          key={"name"}
          render={(text, record: Project) => (
            <Tag color={ProjectStatusTrans[record.status as keyof typeof ProjectStatusTrans]?.color}>
              {t(record.status)}
            </Tag>
          )}
        />
        <Column
          title={t("createdAt")}
          dataIndex="name"
          key={"name"}
          render={(text, record: Project) => (
            <span>{unixToFullDate(record.createdAt)}</span>
          )}
        />
      </Table>
      <Pagination
        total={totalProject}
        defaultPageSize={queryProject.limit}
        currentPage={queryProject.page}
        onChange={({ page, limit }) => {
          Object.assign(queryProject, {
            page,
            limit,
          });
          fetchProject();
        }}
      />
    </div>
  );
};

export default PromotionProjectTab;
