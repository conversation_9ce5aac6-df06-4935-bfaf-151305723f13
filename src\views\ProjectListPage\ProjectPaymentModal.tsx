import {
  <PERSON>,
  <PERSON><PERSON>,
  Di<PERSON>r,
  <PERSON>ack,
  Typography,
  useTheme,
} from "@mui/material";
import { Form, Input, message, Modal, Select } from "antd";
import { Rule } from "antd/lib/form";
import React, { useImperativeHandle, useState } from "react";
import { useTranslation } from "react-i18next";
import { ModalStatus } from "types/modal";
import { Project } from "types/project";
import bgImg from "assets/svgs/bgr-vi.svg";
import { settings } from "settings";
import { userStore } from "store/userStore";
import { formatVND } from "utils";
import { useForm } from "antd/es/form/Form";
import { requiredRule } from "utils/validate-rules";
import { projectApi } from "api/project.api";
import { appStore } from "store/appStore";
import { ConfigurationParam } from "types/configuration";
import { GoPlus } from "react-icons/go";
import { IoRefresh } from "react-icons/io5";
import { observer } from "mobx-react";
import { useNavigate } from "react-router-dom";
import { PaymentType } from "types/payment";
import clsx from "clsx";
import { FaCircleCheck } from "react-icons/fa6";
import RiviModal from "components/Modal/RiviModal";
import celebrationImg from "assets/svgs/celebration.svg";

const rules: Rule[] = [{ required: true }];

export interface ProjectPaymentModalRef {
  handleOpen: (project: Project) => void;
}
interface ProjectPaymentModalProps {
  onClose: () => void;
  onSubmitOk: () => void;
}

export const ProjectPaymentModal = observer(
  React.forwardRef(({ onClose, onSubmitOk }: ProjectPaymentModalProps, ref) => {
    const { t } = useTranslation();
    const theme = useTheme();
    const navigate = useNavigate();

    const [form] = useForm<{
      paymentType: PaymentType;
      promotionCode: string;
    }>();
    const [visible, setVisible] = useState(false);
    const [status, setStatus] = useState<ModalStatus>("create");
    const [loadingEstimate, setLoadingEstimate] = useState(false);
    const [selectedProject, setSelectedProject] = useState<Project>();
    const [estimate, setEstimate] = useState<{
      moneyDiscount: number;
      moneyDrop: number;
      moneyFinal: number;
      moneyImage: number;
      moneyTax: number;
      moneyTotal: number;
      isError?: boolean;
    }>();
    const [donePayment, setDonePayment] = useState(false);

    useImperativeHandle<any, ProjectPaymentModalRef>(
      ref,
      () => ({
        handleOpen(project) {
          setVisible(true);
          setStatus("create");
          setSelectedProject(project);
          estimateProject(project);
        },
      }),
      []
    );
    const handlePayment = async () => {
      if (!selectedProject) return;
      try {
        setLoadingEstimate(true);
        await projectApi.payment(selectedProject.id);
        userStore.getProfile();
        // message.success(t("operationSuccess"));
        onSubmitOk();
        setDonePayment(true);
        setVisible(false);
      } catch (e) {
        console.log({ e });
      } finally {
        setLoadingEstimate(false);
      }
    };
    const estimateProject = async (project: Project) => {
      try {
        setLoadingEstimate(true);
        const { data } = await projectApi.estimate({
          projectId: project.id,
          promotionCode: form.getFieldValue("promotionCode"),
        });
        setEstimate({
          moneyDiscount: data.moneyDiscount || 0,
          moneyDrop: data.moneyDrop || 0,
          moneyFinal: data.moneyFinal || 0,
          moneyImage: data.moneyImage || 0,
          moneyTax: data.moneyTax || 0,
          moneyTotal: data.moneyTotal || 0,
        });
      } catch (e) {
        console.log({ e });
      } finally {
        setLoadingEstimate(false);
      }
    };

    const handleClose = () => {
      form.resetFields();
      setVisible(false);
      onClose?.();
      setSelectedProject(undefined);
      setEstimate(undefined);
      setDonePayment(false);
    };

    return (
      <>
        <Modal
          onCancel={() => {
            handleClose();
          }}
          closeIcon={false}
          open={visible}
          title={t("projectPayment") + " - " + selectedProject?.code}
          style={{ top: 20 }}
          width={700}
          // onOk={() => {
          //   status == "create" ? createData() : null;
          // }}
          onOk={() => {
            if (donePayment) {
              handleClose();
            } else {
              Modal.confirm({
                title: t("actionConfirm"),
                onOk: handlePayment,
                okText: t("confirm"),
                centered: true,
                width: "90%",
                style: {
                  maxWidth: 400,
                  margin: "0 auto",
                },
              });
            }
          }}
          confirmLoading={loadingEstimate}
          cancelText={t("cancel")}
          okText={donePayment ? "OK" : t("payNow")}
          cancelButtonProps={{
            className: "hidden",
          }}
          okButtonProps={{
            disabled:
              (estimate?.moneyFinal || 1) >
                (selectedProject?.customer?.balance || 0) || estimate?.isError,
            className: "w-full !m-0 h-[42px]",
          }}
        >
          <Form
            layout="vertical"
            form={form}
            initialValues={{ paymentType: PaymentType.Balance }}
            validateTrigger={["onBlur", "onChange"]}
          >
            <Form.Item name={"paymentType"} hidden></Form.Item>

            <Divider className="!mb-4" />
            {/* {estimate && selectedProject && (
              <div>
                <Typography variant="h6">{t("paymentInfo")}</Typography>
                <Box>
                  <Typography
                    variant="body1"
                    className="whitespace-pre-wrap flex justify-between"
                  >
                    <div>
                      {t("estimatePackage")} (x
                      {selectedProject.product?.numReview} {t("reviews")})
                    </div>
                    <div>
                      {formatVND(selectedProject.product?.price || 0)} VND
                    </div>
                  </Typography>
                  <Typography
                    variant="body1"
                    className="whitespace-pre-wrap flex justify-between"
                  >
                    <div>{t("estimateSlowDrop")}</div>
                    <div>{formatVND(estimate.moneyDrop)} VND</div>
                  </Typography>
                  <Typography
                    variant="body1"
                    className="whitespace-pre-wrap flex justify-between"
                  >
                    <div>
                      {t("estimateImage")} (x
                      {selectedProject.fileAttaches.length})
                    </div>
                    <div>{formatVND(estimate.moneyImage)} VND</div>
                  </Typography>
                  {estimate.moneyDiscount ? (
                    <Typography
                      variant="body1"
                      className="whitespace-pre-wrap flex justify-between"
                    >
                      <div>{t("promotionPrice")}</div>
                      <div>-{formatVND(estimate.moneyDiscount)} VND</div>
                    </Typography>
                  ) : null}
                </Box>
                <Divider className="!my-4" />
                <Box>
                  <Typography
                    variant="body1"
                    className="whitespace-pre-wrap flex justify-between"
                  >
                    <div>{t("estimatePrice")}</div>
                    <div>{formatVND(estimate.moneyTotal)} VND</div>
                  </Typography>
                  <Typography
                    variant="body1"
                    className="whitespace-pre-wrap flex justify-between"
                  >
                    <div>
                      {t("estimateTax")} (
                      {appStore.getOneConfiguration(
                        ConfigurationParam.TaxPercent
                      )}
                      %)
                    </div>
                    <div>{formatVND(estimate.moneyTax)} VND</div>
                  </Typography>
                </Box>
                <Divider className="!my-4" />
                <Box>
                  <Typography
                    variant="h6"
                    className="whitespace-pre-wrap flex justify-between"
                  >
                    <div>{t("totalPrice")}</div>
                    <div>{formatVND(estimate.moneyFinal)} VND</div>
                  </Typography>
                </Box>
              </div>
            )} */}
            {estimate && selectedProject && (
              <div>
                <div className="font-medium text-lg mb-[12px]">
                  {t("overviewOrder")}
                </div>
                <Box>
                  <div className="whitespace-pre-wrap flex justify-between mb-[10px]">
                    <div>
                      {t("estimatePackage")} (x
                      {selectedProject.product?.numReview})
                    </div>
                    <div>
                      {formatVND(selectedProject.product?.price || 0)} ₫
                    </div>
                  </div>
                  <div className="whitespace-pre-wrap flex justify-between mb-[10px]">
                    <div>{t("estimateSlowDrop")}</div>
                    <div>{formatVND(estimate.moneyDrop)} ₫</div>
                  </div>
                  <div className="whitespace-pre-wrap flex justify-between mb-[10px]">
                    <div>
                      {t("estimateImage")} (x
                      {selectedProject.fileAttaches.length})
                    </div>
                    <div>{formatVND(estimate.moneyImage)} ₫</div>
                  </div>
                  {estimate.moneyDiscount ? (
                    <div className="whitespace-pre-wrap flex justify-between mb-[10px]">
                      <div>{t("promotionCode")}</div>
                      <div
                        className={`text-[${theme.palette.primary.main}] font-bold`}
                      >
                        -{formatVND(estimate.moneyDiscount)} ₫
                      </div>
                    </div>
                  ) : null}
                </Box>
                <Box>
                  <div className="whitespace-pre-wrap flex justify-between mb-[10px]">
                    <div>{t("estimatePrice")}</div>
                    <div>{formatVND(estimate.moneyTotal)} ₫</div>
                  </div>
                  <div className="whitespace-pre-wrap flex justify-between">
                    <div>
                      {t("estimateTax")} (
                      {appStore.getOneConfiguration(
                        ConfigurationParam.TaxPercent
                      )}
                      %)
                    </div>
                    <div>{formatVND(estimate.moneyTax)} ₫</div>
                  </div>
                </Box>
                <Divider className="!my-4" sx={{ borderStyle: "dashed" }} />
                <Box>
                  <Typography
                    variant="h6"
                    className="whitespace-pre-wrap flex justify-between"
                  >
                    <div>{t("total")}</div>
                    <div>{formatVND(estimate.moneyFinal)} ₫</div>
                  </Typography>
                </Box>
              </div>
            )}
          </Form>
        </Modal>
        <RiviModal
          image={celebrationImg}
          visible={donePayment}
          fullContent={
            <div
              className="flex flex-col items-center mx-auto w-[90%] pt-4 pb-6"
              style={{ textAlign: "center" }}
            >
              <img
                src={celebrationImg}
                alt="Notification"
                style={{ width: 170 }}
              />
              <div className="font-semibold text-[18px] mt-[15.43px]">
                {t("paymentSuccess")}
              </div>
              <div className="text-[#7C8FAC] mt-[4px]">
                {t("paymentSuccess2")}
              </div>
            </div>
          }
          onClose={() => {
            setDonePayment(false);
          }}
        />
      </>
    );
  })
);
