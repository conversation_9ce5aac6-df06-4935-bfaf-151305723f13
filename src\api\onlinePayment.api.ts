import { request } from "utils/request";
import { AxiosPromise } from "axios";

export const onlinePaymentApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/onlinePayment",
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/onlinePayment",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/onlinePayment/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/onlinePayment/${id}`,
      method: "delete",
    }),
};
