import { Card, Typography } from "@mui/material";
import { useTranslation } from "react-i18next";
import { useEffect, useRef, useState, useCallback } from "react";
import { observer } from "mobx-react";
import { debounce } from "lodash";
import { useProject } from "hooks/useProject";
import MapWithAutocomplete, {
  MapWithAutocompleteRef,
} from "components/Map/MapWithAutocomplete";
import googleMapReact from "google-map-react";
import { Spin } from "antd";

const DashboardMap = () => {
  const { t } = useTranslation();

  const { fetchProject, projects, queryProject, loadingProject } = useProject({
    initQuery: {
      limit: 0,
      page: 1,
    },
  });

  const projectMapRef = useRef<MapWithAutocompleteRef>();
  const [projectMapLoaded, setProjectMapLoaded] = useState(false);

  useEffect(() => {
    fetchProject();
  }, [queryProject]);

  useEffect(() => {
    if (projects.length > 0 && projectMapLoaded) {
      projectMapRef.current?.fitBounds();
    }
  }, [projects, projectMapLoaded]);

  return (
    <Card>
      <h5 className="semibold !mb-6">{t("customerDashboard")}</h5>
      <Spin spinning={loadingProject}>
        <MapWithAutocomplete
          enableCluster
          ref={projectMapRef}
          key={"mapDashboardCustomer"}
          coords={projects.map((p) => ({
            lat: p.lat,
            lng: p.long,
            project: p,
          }))}
          noInput
          onLoaded={() => {
            setProjectMapLoaded(true);
          }}
          noAutoCenterByCoord
          colorCluster="#FF766B"
          style={{ height: "600px", width: "100%" }}
        />
      </Spin>
    </Card>
  );
};

export default observer(DashboardMap);
