export const getValueFromArrOfKeys = <T>(
  data: T,
  stringKeys: string[]
): any => {
  // if (data.id && data.id === 9) {
  //   debugger;
  // }
  let _stringKeys = [...stringKeys];
  if (!data) return "";
  if (!_stringKeys.length) return data;
  const key = _stringKeys.shift();
  return getValueFromArrOfKeys(data[key as keyof T], _stringKeys);
};

export const isPropInObjectArrayAscending = <T>(array: T[], prop: keyof T) => {
  for (let i = 0; i < array.length - 1; i++) {
    if (array[i][prop] > array[i + 1][prop]) {
      return false;
    }
  }
  return true;
};

export const isPropInObjectArrayDescending = <T>(array: T[], prop: keyof T) => {
  for (let i = 0; i < array.length - 1; i++) {
    if (array[i][prop] < array[i + 1][prop]) {
      return false;
    }
  }
  return true;
};

export const uniqueArrayByKey = <T, K extends keyof T>(array: T[], key: K): T[] => {
  const seen = new Set<T[K]>();
  return array.filter((item) => {
    const keyValue = item[key];
    if (seen.has(keyValue)) {
      return false;
    } else {
      seen.add(keyValue);
      return true;
    }
  });
};
