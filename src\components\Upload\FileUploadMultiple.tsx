import React, { ReactNode, useEffect, useState } from "react";
import {
  InboxOutlined,
  LinkOutlined,
  PaperClipOutlined,
  UploadOutlined,
} from "@ant-design/icons";
import type { UploadFile, UploadProps } from "antd";
import { Button, message, Row, Upload } from "antd";
import { Button as <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@mui/material";
import uploadPlaceholder from "assets/images/uploadPlaceholder.png";
import { $url } from "utils/url";
import { getToken } from "utils/auth";
import { settings } from "settings";
import FileUploadItem, { FileCustomProps } from "./FileUploadItem";
import { useTranslation } from "react-i18next";
import { useTheme } from "@mui/material";
import clsx from "clsx";
import { formatVND } from "utils";

const { Dragger } = Upload;

export const FileUploadMultiple = ({
  onUploadOk,
  onDelete,
  onBefore,
  fileList,
  fileTypes,
  fileTypeText = "PDF",
  placeholder,
  acceptType,
  readOnly = false,
  uploadFileComment,
  uploadUrl = import.meta.env.VITE_API_URL + "/v1/admin/fileAttach/upload",
  id,
  disabled,
  className,
  draggerContent,
  variant = "compact",
  maxFile = 5,
  maxFileSize = 1024 * 1024 * 10,
}: {
  fileList: UploadFile[];
  fileTypeText?: string;
  fileTypes?: string[];
  placeholder?: string;
  uploadUrl?: string;
  acceptType?: string;
  readOnly?: boolean;
  uploadFileComment?: boolean;
  disabled?: boolean;
  id?: string;
  className?: string;
  draggerContent?: ReactNode;
  variant?: "compact" | "detailed";
  maxFile?: number;
  maxFileSize?: number;
  onUploadOk: (file: Array<any>) => void;
  onDelete: (file: Array<any>) => void;
  onBefore?: () => Promise<boolean>;
}) => {
  const { t } = useTranslation();
  const theme = useTheme();
  const [duplicateImage, setDuplicateImage] = useState(false);

  // const [fileListPaste, setFileListPaste] = useState<UploadFile[]>();

  // useEffect(() => {
  //   setFileListPaste(() => fileList.map((e) => ({ ...e })));
  // }, [fileList]);

  const props: UploadProps = {
    accept: acceptType,
    id: id,
    fileList,
    name: "file",
    multiple: true,
    action: uploadUrl,
    className: clsx("inline-block w-full mt-2", className),
    disabled,
    beforeUpload(info) {
      setDuplicateImage(false);

      if (maxFileSize && info.size > maxFileSize) {
        message.error(
          t("fileSizeInvalid", {
            number: formatVND(Number((maxFileSize / 1024 / 1024).toFixed(2))),
          })
        );
        return false;
      }

      if (fileList?.length > 0 && info) {
        const value = fileList.find(
          (item) => item.name === info.name && item.size === info.size
        );

        if (value) {
          setDuplicateImage(true);
          message.error(t("duplicateImage"));
          return;
        }
      }
    },
    onChange(info) {
      if (duplicateImage) return;
      const currentFiles = info.fileList;

      if (currentFiles.length > maxFile) {
        message.warning(`${t("maxFile")}: ${maxFile}`);
        return;
      }
      // console.log("fileListOrigin", fileListOrigin);
      if (info.file.status === "uploading") {
        info.file.status = "done";
        // setLoading(true); // Đang trong quá trình tải file lên
      }

      if (info.file.status === "done") {
        // Khi file tải lên thành công
        const updatedFileList = info.fileList.map((file) => {
          if (file.uid === info.file.uid && info.file.response) {
            // Gán URL từ response vào file item
            return {
              ...file,
            };
          }
          return file;
        });
        // setFileListPaste(updatedFileList);
        onUploadOk(updatedFileList);
        // setFileListOrigin(updatedFileList); // Cập nhật danh sách file với URL mới
        // onUploadOk(updatedFileList); // Gọi hàm onUploadOk sau khi tải xong với URL đã được gán

        // setLoading(false); // Dừng loading
      }
    },
    onDrop(e) {
      console.log("Dropped files", e.dataTransfer.files);
    },
  };
  const handleDelete = (id: string) => {
    // console.log("fileListOrigin", fileListOrigin);
    const filterFile = fileList?.filter((item) => {
      return item.uid !== id;
    });
    // setFileListOrigin(filterFile);
    // @ts-ignore
    onUploadOk(filterFile);
  };
  const handleDeleteAll = () => {
    onUploadOk([]);
  };
  const uploadFileToServer = async (file: File) => {
    const formData = new FormData();
    formData.append("file", file); // Thêm file vào FormData

    const response = await fetch(uploadUrl, {
      method: "POST",
      body: formData,
      headers: {
        token: getToken() || "", // Nếu cần thêm header
      },
    });

    if (!response.ok) {
      throw new Error("Upload failed");
    }

    return response.json(); // Giả sử server trả về JSON
  };
  const handleUpload = (file: File, responseData: { path: string }) => {
    const newFile: UploadFile<any> = {
      uid: Math.floor(Math.random() * 1000).toString(),
      name: file.name,
      size: file.size,
      url: `${import.meta.env.VITE_IMG_URL}${responseData.path}`,
      type: file.type,
    };
    // setFileListPaste((prevFileList) => {
    //   const updatedFileList = [...(prevFileList || []), newFile];
    //   onUploadOk(updatedFileList);
    //   return updatedFileList;
    // });
  };

  const handlePasteImage = async (e: ClipboardEvent) => {
    const target = e.target as HTMLElement;

    // Kiểm tra nếu sự kiện xảy ra bên trong textarea hoặc input
    if (target.tagName === "TEXTAREA" || target.tagName === "INPUT") {
      return; // Bỏ qua xử lý sự kiện paste
    }
    const items = e.clipboardData?.items;
    console.log(items);
    if (items) {
      const fileArray: File[] = Array.from(items)
        .map((item) => item.getAsFile())
        .filter((file) => file) as File[];

      console.log(fileArray);
      // Xử lý tất cả các file ảnh dán
      for (const file of fileArray) {
        const response = await uploadFileToServer(file); // Upload ảnh lên server
        handleUpload(file, response.data); // Thêm ảnh vào danh sách
      }
    }
  };
  useEffect(() => {
    const pasteHandler = handlePasteImage as unknown as EventListener;
    window.addEventListener("paste", pasteHandler);

    return () => {
      window.removeEventListener("paste", pasteHandler);
    };
  }, [handlePasteImage]);

  return (
    <>
      {uploadFileComment ? (
        <>
          <Upload
            fileList={fileList}
            headers={{ token: getToken() || "" }}
            showUploadList={false}
            {...props}
          >
            <Button icon={<PaperClipOutlined />}></Button>
          </Upload>
          <Row gutter={[6, 6]}>
            {fileList &&
              fileList.map((item, index) => {
                const file: FileCustomProps = {
                  id: item.uid,
                  fileName: item.name || item.response?.data?.originalname,
                  fileSize: item.size || item.response?.data?.size,
                  fileUrl: item.url || $url(item.response?.data?.path),
                };
                return (
                  <FileUploadItem
                    key={index}
                    file={file}
                    onDelete={handleDelete}
                  />
                );
              })}
          </Row>
        </>
      ) : (
        <>
          <div className="rounded-md transition-all">
            <Dragger
              headers={{ token: getToken() || "" }}
              showUploadList={false}
              {...props}
            >
              {draggerContent ? (
                draggerContent
              ) : (
                <>
                  <p className="ant-upload-drag-icon">
                    <img
                      src={uploadPlaceholder}
                      className="w-[119px] h-[119px]"
                    />
                  </p>
                  <p className="ant-upload-text">{t("clickOrDragToUpload")}</p>
                </>
              )}

              {/* <p className="ant-upload-drag-icon">
                <InboxOutlined />
              </p> */}
            </Dragger>
          </div>
          {fileList && fileList.length > 0 && (
            <div className="relative mt-3">
              {fileList.length > 0 && (
                <Button
                  className="w-fit text-center font-bold"
                  danger
                  onClick={handleDeleteAll}
                >
                  {t("deleteAll")}
                </Button>
              )}
              <div className="flex flex-wrap gap-4 mt-3">
                {fileList.map((item, index) => {
                  const file: FileCustomProps = {
                    id: item.uid,
                    fileType: item.type,
                    fileName: item.name || item.response?.data?.originalname,
                    fileSize: item.size || item.response?.data?.size,
                    fileUrl: item.url || $url(item.response?.data?.path),
                  };
                  return (
                    <FileUploadItem
                      key={index}
                      file={file}
                      onDelete={handleDelete}
                    />
                  );
                })}
              </div>
            </div>
          )}
        </>
      )}
    </>
  );
};
