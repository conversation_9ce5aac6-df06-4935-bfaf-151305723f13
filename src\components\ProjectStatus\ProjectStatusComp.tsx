
import { Tag } from 'antd'
import { useTranslation } from 'react-i18next';
import "./styles/ProjectStatus.scss";
import clsx from 'clsx';
import { ProjectStatus, ProjectStatusTrans } from 'types/project';

interface Props {
  status: ProjectStatus
}

export const ProjectStatusComp = ({ status }: Props) => {
  const { t } = useTranslation();

  return (
    <Tag
      className={clsx("project-status", status)}
      color={ProjectStatusTrans[status as keyof typeof ProjectStatusTrans]?.color}
    >
      {t(`project${status}`)}
    </Tag>
  )
}
