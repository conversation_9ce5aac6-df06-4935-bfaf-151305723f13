import { Customer } from "./customer";
import { StockCode } from "./stockCode";
import { TelegramGroup } from "./telegramGroup";

export interface StockOrderFollow {
  id: number;
  createdAt: number;
  updatedAt: number;
  isDeleted: boolean;
  deletedAt: number;
  isFollow: boolean;
  //   status: StockOrderFollowStatus;
  stockOrder: StockOrder;
  customer: Customer;
}

export enum StockOrderType {
  Sell = "SELL",
  Buy = "BUY",
  Modify = "MODIFY",
  PartSell = "PART_SELL",
  PreModify = "PRE_MODIFY",
}

export const CloseStockOrderStatusTrans = {
  true: {
    value: true,
    label: "Đóng",
    color: "orange",
  },
  false: {
    value: false,
    label: "Chưa đóng",
    color: "blue",
  },
};

export const StockOrderTypeTrans = {
  [StockOrderType.Sell]: {
    label: "Bán",
    value: StockOrderType.Sell,
    color: "green",
    className: "text-green-500",
  },
  [StockOrderType.Buy]: {
    label: "Mua",
    value: StockOrderType.Buy,
    color: "red",
    className: "text-red-500",
  },
  [StockOrderType.Modify]: {
    label: "Điều chỉnh",
    value: StockOrderType.Modify,
    color: "blue",
    className: "text-blue-500",
  },
};

export interface StockOrder {
  id: number;
  createdAt: number;
  closedAt: number;
  followAt: number;
  updatedAt: number;
  isDeleted: boolean;
  deletedAt: number;
  code: string;
  type: StockOrderType;
  dateString: string; //format: YYYY-MM-DD
  closeDate: string; //format: YYYY-MM-DD
  buyPrice: number;
  takeProfitPrice: number; //giá chốt lời
  takeLossPrice: number; //giá bán lỗ lỗ
  isVisible: boolean;
  isRoot: true;
  text: string;
  followNote: string;
  closeNote: string;
  totalChild: number; //tổng lệnh con
  totalLike: number; //tổng lệnh con
  totalFollow: number; //tổng lệnh con
  stockCode: StockCode;
  telegramGroup: TelegramGroup;
  stockOrderFollows: StockOrderFollow[];
  children: StockOrder[];
  parent: StockOrder;
  sellType: string;
  profitPercent: number;
  isPublic: boolean;
}

export enum StockOrderTypeCustom {
  Follow = "isFollow",
  Favorite = "isLiked",
  // Close = "isClosed",
  // GetNotification = "isGetNotification",
}

export const StockOrderTypeCustomTrans = {
  [StockOrderTypeCustom.Follow]: {
    title: "KH theo dõi lệnh",
    value: StockOrderTypeCustom.Follow,
  },
  [StockOrderTypeCustom.Favorite]: {
    title: "KH yêu thích mã này",
    value: StockOrderTypeCustom.Favorite,
  },
  // [StockOrderTypeCustom.GetNotification]: {
  //   title: "KH cần gửi thông báo",
  //   value: StockOrderTypeCustom.GetNotification,
  // },
  // [StockOrderTypeCustom.Close]: {
  //   title: "KH đóng lệnh",
  //   value: StockOrderTypeCustom.Close,
  // },
};
