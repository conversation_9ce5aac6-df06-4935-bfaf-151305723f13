import momoLogo from "assets/images/momo.png";
import onepayLogo from "assets/images/onepay.png";
import bankLogo from "assets/images/bank.png";
import walletLogo from "assets/images/wallet.png";

export enum PaymentStatus {
  Pending = "PENDING",
  Complete = "COMPLETE",
  Fail = "FAIL",
  All = "ALL"
}

export const PaymentStatusTrans = {
  [PaymentStatus.All]: {
    value: "",
    color: "",
    label: "all"
  },
  [PaymentStatus.Pending]: {
    value: PaymentStatus.Pending,
    color: "orange",
    label: `payment${PaymentStatus.Pending}`
  },
  [PaymentStatus.Complete]: {
    value: PaymentStatus.Complete,
    color: "blue",
    label: `payment${PaymentStatus.Complete}`
  },
  // [PaymentStatus.Fail]: {
  //   value: PaymentStatus.Fail,
  //   color: "red",
  // },
};

export enum PaymentType {
  Manual = "MANUAL", //admin nạp tay
  Momo = "MOMO",
  OnePay = "ONE_PAY",
  Bank = "BANK",
  Balance = "BALANCE",
}

export const PaymentTypeTrans = {
  [PaymentType.Momo]: {
    value: PaymentType.Momo,
    logo: momoLogo,
    disabled: false,
  },
  [PaymentType.OnePay]: {
    value: PaymentType.OnePay,
    logo: onepayLogo,
    disabled: true,
  },
  [PaymentType.Bank]: {
    value: PaymentType.Bank,
    logo: bankLogo,
    disabled: true,
  },
  [PaymentType.Balance]: {
    value: PaymentType.Balance,
    logo: walletLogo,
    disabled: false,
  },
};
