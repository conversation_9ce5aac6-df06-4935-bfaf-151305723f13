import { Customer } from "types/customer";
import { formatDate } from "utils/date";

export const nameWithExpiredDay = (
  rankName: string,
  expiresInDay: number,
  expiredAt?: number,
  showExpiredDay?: boolean
) => {
  let message = `${rankName}`;
  if (expiresInDay) {
    message += `${expiresInDay} ngày`;
    if (showExpiredDay && !!expiredAt) {
      message += `- Hết hạn: ${formatDate(expiredAt)}`;
    }
  } else if (expiredAt == -1) {
    message += `(Vĩnh viễn)`;
  } else {
    message += `(<PERSON><PERSON><PERSON> du<PERSON>t)`;
  }

  return message;
};

/**
 * get rank name by level
 */
// export const getRankName = (customer: Customer) => {
//   const {
//     vipCustomerRank,
//     vipProduct,
//     freeCustomerRank,
//     csCustomerRank,
//     csProduct,
//     psCustomerRank,
//     psProduct,
//     freeProduct,
//     memberExpiredAt,
//     csExpiredAt,
//     psExpiredAt,
//     freeExpiredAt,
//   } = customer;
//   let message = "";
//   // check goi vip
//   if (vipCustomerRank) {
//     message = nameWithExpiredDay(
//       vipCustomerRank.name,
//       vipProduct?.expiresInDay,
//       memberExpiredAt,
//       true
//     );
//     message += "\n";
//   }
//   // check goi cs
//   if (csCustomerRank) {
//     message += nameWithExpiredDay(
//       csCustomerRank.name,
//       csProduct?.expiresInDay,
//       csExpiredAt,
//       true
//     );
//     message += "\n";
//   }

//   // check goi ps
//   if (psCustomerRank) {
//     message += nameWithExpiredDay(
//       psCustomerRank.name,
//       psProduct?.expiresInDay,
//       psExpiredAt,
//       true
//     );
//     message += "\n";
//   }
//   // check goi free
//   if (freeCustomerRank) {
//     message += nameWithExpiredDay(
//       freeCustomerRank.name,
//       freeProduct?.expiresInDay,
//       freeExpiredAt,
//       true
//     );
//   }

//   return message;
// };
