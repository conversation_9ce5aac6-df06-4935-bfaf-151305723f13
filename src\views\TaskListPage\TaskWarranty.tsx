import { useCallback, useEffect, useState } from "react";
import { <PERSON><PERSON>, Card, Tabs } from "antd";
import { getTitle } from "utils";
import {
  SupportTicketCreatedBy,
  SupportTicketStatus,
  SupportTicketStatusTrans,
} from "types/supportTicket";
import { supportTicketApi } from "api/supportTicket.api";
import { useTranslation } from "react-i18next";
import { reviewApi } from "api/review.api";
import { MisionListTrans, MisionWarningTrans, MissionTabType, ReviewStatus, ReviewStatusTrans } from "types/review";
import { TaskListTab } from "./components/TaskListTab";

export const TaskWarranty = ({ title = "" }) => {
  const [tabActive, setTabActive] = useState<ReviewStatus>(ReviewStatus.All);
  const [summaryReceiptOfStatus, setSummaryReceiptOfStatus] = useState();
  const [lastUpdate, setLastUpdate] = useState<number>(0);
  const [selectedProjectId, setSelectedProjectId] = useState<
    number | undefined
  >();

  const [selectedPartnertId, setSelectedPartnertId] = useState<
    number | undefined
  >();


  console.log({ parentLastUpdate: lastUpdate });
  const { t } = useTranslation();

  useEffect(() => {
    document.title = getTitle(t(title));
    fetchSummary();
  }, []);

  const fetchSummary = useCallback(async (projectId?: number) => {
    const res = await reviewApi.summaryStatus({ projectId });

    if (res.status) {
      setSummaryReceiptOfStatus(() => {
        const summary = res.data.reduce(
          (prev: any, curr: { status: ReviewStatus; total: number }) => {
            prev[curr.status] = curr.total;
            prev.ALL = (prev.ALL || 0) + curr.total;
            return prev;
          },
          { ALL: 0 }
        );

        return summary;
      });
    }
  }, []);
  const onChangeTab = useCallback((key: ReviewStatus) => {
    setTabActive(key as ReviewStatus);
  }, []);

  const onLastUpdateChange = useCallback(() => {
    console.log("on last update");
    setLastUpdate((pre) => pre + 1);
  }, []);

  return (
    // <Card bodyStyle={{ padding: "8px 20px" }} style={{ borderRadius: "8px" }}>
    <div>
      <Tabs
        activeKey={tabActive}
        onChange={(key) => onChangeTab(key as ReviewStatus)}
        type="line"
        animated={{ inkBar: true, tabPane: true, tabPaneMotion: {} }}
      >
        {Object.values(MisionWarningTrans).map((item) => (
          <Tabs.TabPane
            tab={
              <div className="flex items-center gap-2">
                {t(`review${item.value}`)}
                {summaryReceiptOfStatus && (
                  <Badge
                    key={item.value}
                    color={
                      ReviewStatusTrans[
                        item.value as keyof typeof ReviewStatusTrans
                      ]?.color
                    }
                    count={summaryReceiptOfStatus?.[item.value] || 0}
                    style={{ fontSize: 10 }}
                  />
                )}
              </div>
            }
            key={item.value}
            tabKey={item.value}
          >
            <TaskListTab
              title={t(item.value)}
              parentLastUpdate={lastUpdate}
              isFocus={tabActive == item.value}
              status={tabActive}
              onSubmitOk={(projectId?: number) => {
                onLastUpdateChange();
                fetchSummary(projectId);
                setSelectedProjectId(projectId);
              }}
              selectedProjectId={selectedProjectId}
              selectedParnerId={selectedPartnertId}
              onSubmitParnertOk={(partnerId?: number) => {
                onLastUpdateChange();
                setSelectedPartnertId(partnerId);
              }}
              valueStatus={[
                ReviewStatus.RejectWarranty,
                ReviewStatus.RequestWarranty
              ]}
              missionDisplay={MissionTabType.Warranty}
            />
          </Tabs.TabPane>
        ))}
      </Tabs>
      {/* </Card> */}
    </div>
  );
};
