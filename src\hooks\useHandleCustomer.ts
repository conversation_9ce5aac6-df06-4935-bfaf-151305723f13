import { customerApi } from "api/customer.api";
import { debounce } from "lodash";
import { useRef, useState } from "react";
import { Customer } from "types/customer";
import { QueryParam } from "types/query";

export interface CustomerQuery extends QueryParam {
  stockOrderId?: number;
  stockCodeId?: number;
  isFollow?: boolean;
  isClosed?: boolean;
  isGetNotification?: boolean;
  isBlocked?: boolean | null;
  isFavorite?: boolean;
}

interface CustomerProps {
  initQuery: CustomerQuery;
}

export const useHandleCustomer = ({ initQuery }: CustomerProps) => {
  const [data, setData] = useState<Customer[]>([]);
  const query = useRef<CustomerQuery>(initQuery);
  const [loading, setLoading] = useState<boolean>(false);
  const [isFindOneCustomerLoading, setIsFindOneCustomerLoading] =
    useState<boolean>(false);

  const [total, setTotal] = useState(0);

  const fetchCustomer = async (newQuery?: CustomerQuery) => {
    try {
      setLoading(true);
      const { data } = await customerApi.findAll({
        ...query.current,
        ...newQuery,
      });
      setTotal(data.total);
      setData(data.customers);
      setLoading(false);
      console.log(data.customers);
      return data.customers;
    } catch (error) {}
  };

  const fetchOneCustomer = async (id: number) => {
    try {
      setIsFindOneCustomerLoading(true);
      const { data } = await customerApi.findOne(id);
      return data as Customer;
    } finally {
      setIsFindOneCustomerLoading(false);
    }
  };

  const debounceSearchCustomer = debounce((search: string) => {
    query.current.page = 1;
    query.current.search = search;
    fetchCustomer();
  }, 400);

  return {
    customers: data,
    totalCustomer: total,
    fetchCustomer,
    queryCustomer: query.current,
    loadingCustomer: loading,
    fetchOneCustomer,
    isFindOneCustomerLoading,
    debounceSearchCustomer,
  };
};
