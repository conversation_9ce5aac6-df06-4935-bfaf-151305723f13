import { customerTransactionApi } from "api/customerTransaction.api";
import { useState } from "react";
import { CustomerTransaction } from "types/customerTransaction";
import { QueryParam } from "types/query";

export interface CustomerTransactionQuery extends QueryParam {}

interface UseCustomerTransactionProps {
  initQuery: CustomerTransactionQuery;
}

export const useCustomerTransaction = ({
  initQuery,
}: UseCustomerTransactionProps) => {
  const [data, setData] = useState<CustomerTransaction[]>([]);
  const [total, setTotal] = useState(0);
  const [query, setQuery] = useState<CustomerTransactionQuery>(initQuery);
  const [loading, setLoading] = useState(false);

  const fetchData = async () => {
    setLoading(true);
    try {
      const { data } = await customerTransactionApi.findAll(query);

      setData(data.customerTransactions);
      setTotal(data.total);
    } finally {
      setLoading(false);
    }
  };

  return {
    customerTransactions: data,
    total,
    fetchData,
    loading,
    setQuery,
    query,
  };
};
