import {
  ExportOutlined,
  FilterOutlined,
  PlusOutlined,
  SearchOutlined,
} from "@ant-design/icons";
import {
  Button,
  Image,
  Input,
  message,
  Modal,
  Popconfirm,
  Popover,
  Radio,
  Select,
  Space,
  Spin,
  Table,
  Tag,
} from "antd";
import { reviewApi } from "api/review.api";
import { Pagination } from "components/Pagination";
import { useProject } from "hooks/useProject";
import { useReason } from "hooks/useReason";
import { useReview } from "hooks/useReview";
import { cloneDeep } from "lodash";
import React, { useEffect, useState, useRef, useMemo } from "react";
import { useTranslation } from "react-i18next";
import { ModalStatus } from "types/modal";
import { Project } from "types/project";
import { QueryParam } from "types/query";
import { Review, ReviewStatus, ReviewStatusTrans } from "types/review";
import { formatVND, getTitle } from "utils";
import { unixToFullDate } from "utils/dateFormat";
import { handleExport, MyExcelColumn } from "utils/MyExcel";
import { $url } from "utils/url";

const { ColumnGroup, Column } = Table;

export const ApproveProjectPage = ({ title = "" }) => {
  const [isRejectModalOpen, setIsRejectModalOpen] = useState(false);
  const [note, setNote] = useState("");
  const {
    projects,
    fetchProject,
    loadingProject,
    queryProject,
    totalProject,
    fetchSummary,
    summaryData,
    debounceSearchProject,
  } = useProject({
    initQuery: {
      page: 1,
      limit: 50,
    },
  });
  const {
    reviews: reviewData,
    fetchReview,
    loadingReview: loadingReviews,
    queryReview,
    setQueryReview,
    totalReview,
  } = useReview({
    initQuery: {
      page: 1,
      limit: 100,
      status: ReviewStatus.AdminPending,
      sortType: "ASC",
      sortBy: "assignAt",
    },
  });

  const {
    fetchReason,
    loadingReason,
    queryReason,
    reasons,
    setQueryReason,
    totalReason,
  } = useReason({ initQuery: { page: 1, limit: 100 } });

  const [loadingReview, setLoadingReview] = useState(false);
  const { t } = useTranslation();
  const [selectedProject, setSelectedProject] = useState<Project>();
  const [selectedReview, setSelectedReview] = useState<Review>();
  const [loadingAction, setLoadingAction] = useState(false);
  const [filterOpen, setFilterOpen] = useState(false);

  const exportColumns: MyExcelColumn<Review>[] = [
    {
      width: 15,
      header: t("projectName"),
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "projectName",
      render: (record: Review) => {
        return record.project.name;
      },
    },

    {
      width: 200,
      header: t("content"),
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "content",
      render: (record: Review) => {
        return record.content || t("notUpdate");
      },
    },
    {
      header: t("image"),
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "fileAttaches",
      render: (record: Review) => {
        return record.fileAttaches[0]?.url || t("notUpdate");
      },
    },

    {
      width: 20,
      header: t("partner"),
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "partner",
      render: (record: Review) => {
        return record.partner?.fullName || t("notUpdate");
      },
    },
    {
      width: 30,
      header: t("profit"),
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "profit",
      render: (record: Review) => {
        return formatVND(record.profit)
          ? formatVND(record?.profit)
          : t("missionNotCompleted");
      },
    },
    {
      width: 30,
      header: t("status"),
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "status",
      render: (record: Review) => {
        return t(record.status);
      },
    },
    {
      width: 20,
      header: t("createdAt"),
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "createdAt",
      render: (record: Review) => {
        return unixToFullDate(record.createdAt);
      },
    },
  ];
  const handleOpenChange = (newOpen: boolean) => {
    setFilterOpen(newOpen);
  };
  const getDataReview = async () => {
    try {
      setLoadingReview(true);
      const { data } = await fetchReview();
      console.log(data);
    } catch (error) {
    } finally {
      setLoadingReview(false);
    }
  };

  const getOneReview = async (reviewId: number) => {
    try {
      setLoadingReview(true);
      const { data } = await reviewApi.findOne(reviewId);
      setSelectedReview(data);
      fetchReview();
      console.log(data);
    } catch (error) {
    } finally {
      setLoadingReview(false);
    }
  };

  const handleComplete = async (reviewId: number) => {
    try {
      setLoadingAction(true);
      const data = await reviewApi.complete(reviewId);
      getOneReview(reviewId);
      fetchReview();
      message.success(t("actionSuccessfully"));
    } catch (error) {
    } finally {
      setLoadingAction(false);
    }
  };
  const handlePending = async (reviewId: number) => {
    try {
      setLoadingAction(true);
      const data = await reviewApi.pending(reviewId);
      getOneReview(reviewId);
      fetchReview();
      message.success(t("actionSuccessfully"));
    } catch (error) {
    } finally {
      setLoadingAction(false);
    }
  };

  const handleReject = async (reviewId: number, note: string) => {
    try {
      setLoadingAction(true);
      const data = await reviewApi.reject(reviewId, { note });
      getOneReview(reviewId);
      fetchReview();

      message.success(t("actionSuccessfully"));
    } catch (error) {
    } finally {
      setLoadingAction(false);
    }
  };

  const handleRejectWarranty = async (reviewId: number) => {
    if (!note.trim()) {
      message.warning(t("pleaseEnterNote"));
      return;
    }

    try {
      setLoadingAction(true);
      await reviewApi.rejectWarranty(reviewId, { note });
      getOneReview(reviewId);
      fetchReview();
      message.success(t("actionSuccessfully"));
      setIsRejectModalOpen(false);
      setNote("");
    } catch (error) {
      message.error(t("actionFailed"));
    } finally {
      setLoadingAction(false);
    }
  };

  const handleApproveWarranty = async (reviewId: number) => {
    try {
      setLoadingAction(true);
      const data = await reviewApi.approveWarranty(reviewId);
      getOneReview(reviewId);
      fetchReview();

      message.success(t("actionSuccessfully"));
    } catch (error) {
    } finally {
      setLoadingAction(false);
    }
  };

  useEffect(() => {
    queryReview.projectId = selectedProject?.id;
    getDataReview();
  }, [selectedProject]);

  useEffect(() => {
    document.title = getTitle(t(title));
    fetchProject();
    fetchReason();
  }, []);
  const filterContent = (
    <div className="w-[300px] space-y-4">
      {/* Đại lý */}
      <div>
        <div className="font-medium mb-1">{t("status")}</div>
        <Select
          style={{ width: "100%" }}
          allowClear
          onClear={() => {
            delete queryReview.status;
            setQueryReview({ ...queryReview });
          }}
          value={queryReview.status}
          placeholder={t("selectStatus")}
          onChange={(value) => {
            queryReview.page = 1;
            if (value === undefined) {
              delete queryReview.status;
            } else {
              queryReview.status = value;
            }
            setQueryReview({ ...queryReview });
            fetchReview();
          }}
        >
          {Object.values(ReviewStatusTrans)
            .filter((it) => it.value !== ReviewStatus.Inactive)
            .map(({ value }) => (
              <Select.Option key={value} value={value}>
                {t(value)}
              </Select.Option>
            ))}
        </Select>
        <div className="font-medium mb-1 mt-4">{t("project")}</div>
        <Select
          showSearch
          allowClear
          onSearch={(value) => {
            console.log("first");
            debounceSearchProject(value);
          }}
          style={{ height: 60 }}
          placeholder={t("selectProject")}
          // optionFilterProp="children"
          className="w-full  mb-4"
          onChange={(value) => {
            const project = projects.find((p) => p.id === value);
            setSelectedProject(project);
            setSelectedReview(undefined);

            // delete queryReview.status;
            queryReview.projectId = value;
            queryReview.page = 1;
            setQueryReview({ ...queryReview });

            fetchReview();
          }}
          onClear={() => {
            queryProject.search = undefined;
            queryReview.projectId = undefined;
            fetchProject();
            // fetchBrand();
          }}
          value={selectedProject?.id}
          filterOption={false}
        >
          {projects.map((project) => (
            <Select.Option key={project.id} value={project.id}>
              <div>{project.name}</div>{" "}
              <div className="text-primary">{project.code}</div>
            </Select.Option>
          ))}
        </Select>
      </div>

      {/* Nút Xoá filter */}
      <Button
        block
        type="primary"
        danger
        onClick={() => {
          delete queryReview.status; // reset query
          queryReview.page = 1;
          fetchReview();
          setFilterOpen(false);
          setQueryReview({ ...queryReview });
        }}
      >
        {t("clearFilter")}
      </Button>
    </div>
  );

  const filterCount = useMemo(() => {
    const validKeys = Object.keys(queryReview).filter(
      (key) => queryReview[key] !== undefined
    );
    return Math.max(validKeys.length - 4, 0);
  }, [queryReview]);

  return (
    <div>
      <div className="flex gap-6">
        <div className="w-1/4 p-4 bg-white rounded shadow-md">
          <Space wrap>
            <Popover
              content={filterContent}
              title="Filter"
              trigger="click"
              open={filterOpen}
              onOpenChange={handleOpenChange}
              placement="bottom"
              className="my-2"
            >
              <Button type="primary" icon={<FilterOutlined />}>
                {t("filter")}{" "}
                {filterCount > 0 ? <div>({filterCount})</div> : ""}
              </Button>
            </Popover>
            <div className={`filter-item btn `}>
              <Popconfirm
                title={t("exportAsk")}
                onConfirm={() =>
                  handleExport({
                    onProgress(percent) {},
                    exportColumns,
                    fileType: "xlsx",
                    dataField: "reviews",
                    query: queryReview,
                    api: reviewApi.findAll,
                    fileName: `${t("approveProject")} `,
                    sheetName: `${t("approveProject")} `,
                  })
                }
                okText={t("exportExcel")}
                cancelText={t("cancel")}
              >
                <Button
                  type="primary"
                  loading={false}
                  icon={<ExportOutlined />}
                >
                  {t("exportExcel")}
                </Button>
              </Popconfirm>
            </div>
          </Space>
          <div className="flex justify-between items-center mb-2">
            <h3 className="text-lg font-bold">{t("selectReview")}</h3>
          </div>

          {selectedProject && (
            <div className="text-lg font-bold ">
              {t("projectName")}: {selectedProject.name}
            </div>
          )}
          <div className="space-y-4 max-h-[calc(100vh-360px)] overflow-auto">
            {reviewData.length > 0 ? (
              reviewData.map((review, index) => (
                <div
                  key={index}
                  onClick={() => {
                    getOneReview(review.id);
                  }}
                  className="p-4 bg-white rounded shadow border border-gray-200 cursor-pointer"
                >
                  <p className="text-sm text-gray-700">
                    <strong>{t("review")}:</strong> {review.content}
                  </p>
                  <p className="text-sm text-gray-700">
                    <strong>{t("status")}:</strong>{" "}
                    <Tag
                      color={
                        ReviewStatusTrans[
                          review.status as keyof typeof ReviewStatusTrans
                        ]?.color
                      }
                    >
                      {t(review.status)}
                    </Tag>
                  </p>
                  {/* <p className="text-xs text-gray-500">
                    {unixToFullDate(review.createdAt)}
                  </p> */}
                </div>
              ))
            ) : (
              <p></p>
            )}
          </div>
        </div>

        <div className="flex-1 bg-white rounded shadow-md p-6 space-y-4">
          {selectedReview ? (
            <div className="">
              <h2 className="text-xl font-bold">{t("missionDetail")}</h2>
              <div>
                <p className="text-md font-bold">{t("projectName")}:</p>
                <p className="font-medium">{selectedReview?.project?.name}</p>
              </div>
              <div>
                <p className="text-md font-bold">{t("mission")}:</p>

                <p className="font-medium">{selectedReview?.content}</p>
                {selectedReview.fileAttaches.length > 0 && (
                  <>
                    {selectedReview.fileAttaches?.map((it) => (
                      <Image src={it.url} width={160} height={160} />
                    ))}
                  </>
                )}
              </div>
              <div>
                <p className="text-md font-bold">
                  <p className="text-md font-bold">{t("partner")}:</p>
                </p>
                <p className="font-medium">
                  {selectedReview?.partner?.fullName || t("notYet")}
                </p>
              </div>
              <div>
                <p className="text-md font-bold">{t("status")}:</p>
                <p className="font-medium">
                  <Tag
                    color={
                      ReviewStatusTrans[
                        selectedReview.status as keyof typeof ReviewStatusTrans
                      ]?.color
                    }
                  >
                    {t(selectedReview.status)}
                  </Tag>
                </p>
              </div>
              {selectedReview.status === ReviewStatus.Reject && (
                <div>
                  <p className="text-md font-bold">{t("note")}:</p>
                  <p className="font-medium">{selectedReview.note}</p>
                </div>
              )}
              <div>
                <p className="text-md font-bold">
                  <p className="text-md font-bold">{t("profit")}:</p>
                </p>
                <p className="font-medium">
                  {selectedReview.status === ReviewStatus.Complete
                    ? `${formatVND(selectedReview?.profit)} VND`
                    : t("missionNotCompleted")}
                </p>
              </div>

              {/* <div>
                <p className="text-gray-500 text-sm">Lịch sử duyệt</p>
                <p className="font-medium">
                  {selectedReview?.reviewNote || "Chưa có ghi chú"}
                </p>
              </div> */}
              {/* <div className="text-right text-sm text-gray-400">
                {unixToFullDate(selectedReview?.createdAt)}
              </div> */}
              <div className="flex gap-2">
                {selectedReview.reviewUrl && (
                  <Button
                    onClick={() =>
                      window.open(selectedReview.reviewUrl, "_blank")
                    }
                  >
                    {t("seeReview")}
                  </Button>
                )}
                {(selectedReview.status === ReviewStatus.AdminPending ||
                  selectedReview.status === ReviewStatus.SystemPending) && (
                  <>
                    <Popconfirm
                      title={t("confirm?")}
                      //   description={t("confirmWrongPhoto")}
                      onConfirm={() =>
                        handleReject(selectedReview.id, t("wrongPhoto"))
                      }
                      okText={t("save")}
                      cancelText={t("close")}
                    >
                      <Button type="primary" danger loading={loadingAction}>
                        {t("wrongPhoto")}
                      </Button>
                    </Popconfirm>

                    <Popconfirm
                      title={t("confirm?")}
                      onConfirm={() =>
                        handleReject(selectedReview.id, t("noReview"))
                      }
                      okText={t("save")}
                      cancelText={t("close")}
                    >
                      <Button type="primary" danger loading={loadingAction}>
                        {t("noReview")}
                      </Button>
                    </Popconfirm>

                    <Popconfirm
                      title={t("confirm?")}
                      onConfirm={() => handleComplete(selectedReview.id)}
                      okText={t("save")}
                      cancelText={t("close")}
                    >
                      <Button type="primary" loading={loadingAction}>
                        {t("approve")}
                      </Button>
                    </Popconfirm>
                  </>
                )}
                {selectedReview.status === ReviewStatus.RequestWarranty && (
                  <div className="w-full">
                    <Button
                      className="w-full"
                      type="primary"
                      danger
                      onClick={() => setIsRejectModalOpen(true)}
                    >
                      {t("reject")}
                    </Button>

                    <Popconfirm
                      title={t("confirm?")}
                      onConfirm={() => handleApproveWarranty(selectedReview.id)}
                      okText={t("save")}
                      cancelText={t("close")}
                    >
                      <Button
                        type="primary"
                        className="w-full"
                        loading={loadingAction}
                      >
                        {t("approve")}
                      </Button>
                    </Popconfirm>
                  </div>
                )}
                {selectedReview.status === ReviewStatus.Reject && (
                  <Popconfirm
                    title={t("confirm?")}
                    onConfirm={() => handlePending(selectedReview.id)}
                    okText={t("save")}
                    cancelText={t("close")}
                  >
                    <Button type="primary" loading={loadingAction}>
                      {t("reviewAgain")}
                    </Button>
                  </Popconfirm>
                )}
              </div>
            </div>
          ) : (
            <div className="text-center text-gray-500 mt-10">
              {t("pleaseSelectMission")}
            </div>
          )}
        </div>
      </div>
      <Modal
        title={t("confirm?")}
        open={isRejectModalOpen}
        onOk={() => {
          if (selectedReview) {
            handleRejectWarranty(selectedReview?.id);
          }
        }}
        onCancel={() => setIsRejectModalOpen(false)}
        okText={t("save")}
        cancelText={t("close")}
        confirmLoading={loadingAction}
      >
        {/* <Input.TextArea
          rows={4}
          placeholder={t("pleaseEnterNote")}
          value={note}
          onChange={(e) => setNote(e.target.value)}
        /> */}
        <Radio.Group
          className="!flex !flex-col gap-2"
          onChange={(e) => setNote(e.target.value)}
          value={note}
        >
          {reasons.map((reason) => (
            <Radio key={reason.name} value={reason.name}>
              {reason.name}
            </Radio>
          ))}
        </Radio.Group>
      </Modal>
    </div>
  );
};
