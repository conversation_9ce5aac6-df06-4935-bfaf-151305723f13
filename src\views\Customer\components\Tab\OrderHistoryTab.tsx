// SurveyActivityLogTab.js
import {
  Button,
  Input,
  message,
  Popconfirm,
  Progress,
  Select,
  Space,
  Table,
  Tag,
} from "antd";
import { FormInstance } from "antd/es/form";
import Column from "antd/lib/table/Column";

import { unixToFullDate } from "utils/dateFormat";
import { useEffect, useRef, useState } from "react";
import { Pagination } from "components/Pagination";
import { useProject } from "hooks/useProject";
import { Project, ProjectStatus, ProjectStatusTrans } from "types/project";
import { useTranslation } from "react-i18next";
import { AriviTable } from "components/Table/AriviTable";
import { PaymentStatus, PaymentStatusTrans } from "types/payment";
import { useHandleCustomer } from "hooks/useHandleCustomer";
import {
  DownOutlined,
  LockOutlined,
  PlusOutlined,
  SearchOutlined,
  StopOutlined,
  UnlockOutlined,
} from "@ant-design/icons";
import { projectApi } from "api/project.api";
import { formatVND } from "utils";
import { useSearchParams } from "react-router-dom";
import {
  ProjectModal,
  ProjectModalRef,
} from "views/ProjectListPage/ProjectModal";
import DropdownCell from "components/Table/DropdownCell";
import { ReactComponent as SearchIcon } from "assets/svgs/search-icon.svg";
import { ProjectStatusComp } from "components/ProjectStatus/ProjectStatusComp";
import { PaymentStatusComp } from "components/PaymentStatus/PaymentStatusComp";

interface PropTypes {
  customerId?: number;
}
const OrderHistoryTab = ({ customerId }: PropTypes) => {
  const {
    projects,
    fetchProject,
    loadingProject,
    queryProject,
    totalProject,
    setQueryProject,
    fetchSummary,
  } = useProject({
    initQuery: {
      page: 1,
      limit: 5,
      customerId: customerId ? customerId : undefined,
    },
  });
  const { t } = useTranslation();
  const {
    customers,
    fetchCustomer,
    queryCustomer,
    totalCustomer,
    loadingCustomer,
    debounceSearchCustomer,
  } = useHandleCustomer({
    initQuery: {
      page: 1,
      limit: 50,
    },
  });
  const [loadingPay, setLoadingPay] = useState(false);
  const [loadingDelete, setLoadingDelete] = useState(false);
  const [searchParams, setSearchParams] = useSearchParams();
  const modalRef = useRef<ProjectModalRef>(null);

  const handlePayAgain = async (projectId: number) => {
    try {
      setLoadingPay(true);
      const { data } = await projectApi.payAgain(projectId);
      message.success(t("operationSuccess"));
      fetchProject();
      fetchSummary();
    } catch (error) {
    } finally {
      setLoadingPay(false);
    }
  };

  const handleActiveProject = async (projectId: number) => {
    try {
      setLoadingDelete(true);
      const res = await projectApi.reopen(projectId);
      fetchProject();
      fetchSummary();

      message.success(t("actionSuccessfully"));
    } catch (error) {
    } finally {
      setLoadingDelete(false);
    }
  };

  const handleInactiveProject = async (projectId: number) => {
    try {
      setLoadingDelete(true);
      const res = await projectApi.pause(projectId);
      fetchProject();
      fetchSummary();

      message.success(t("actionSuccessfully"));
    } catch (error) {
    } finally {
      setLoadingDelete(false);
    }
  };
  const handleCancelProject = async (projectId: number) => {
    try {
      setLoadingDelete(true);
      const res = await projectApi.cancel(projectId);
      fetchProject();
      fetchSummary();

      message.success(t("actionSuccessfully"));
    } catch (error) {
    } finally {
      setLoadingDelete(false);
    }
  };

  useEffect(() => {
    fetchProject();
    fetchCustomer();
  }, []);
  useEffect(() => {
    const projectId = searchParams.get("projectId");
    if (projects.length > 0 && projectId) {
      const project = projects.find((p) => p.id == Number(projectId));
      project && modalRef.current?.handleUpdate(project);
    }
  }, [searchParams, projects]);

  const handleSearch = (search: string) => {
    queryProject.search = search;
    queryProject.page = 1;
    fetchCustomer();
    fetchProject();
  };

  return (
    <div>
      <div className="filter-container">
        <Space wrap>
          <div className="filter-item">
            <label htmlFor="">{t("search")}</label>

            <Input.Search
              allowClear
              size="large"
              className="w-full search-btn"
              placeholder={t("projectName")}
              onChange={(ev) => {
                queryProject.search = ev.currentTarget.value;
                queryProject.page = 1;
              }}
              enterButton={<SearchIcon onClick={() => {

              }} />}
              onKeyDown={(ev) => {
                if (ev.code == "Enter") {
                  // fetchCustomer();
                }
              }}
              onSearch={handleSearch}
            />
          </div>
          <div className="filter-item">
            <label htmlFor="">{t("projectStatus")}</label>
            <br />
            <Select
              size="large"
              style={{ width: 170 }}
              // allowClear
              defaultValue=""
              placeholder={t("selectStatus")}
              onChange={(value) => {
                queryProject.page = 1;
                if (value === undefined) {
                  queryProject.status = null;
                } else {
                  queryProject.status = value;
                }
                fetchProject();
                fetchSummary();
              }}
            >
              {[{ value: "", label: "all" }, ...Object.values(ProjectStatusTrans)].map(({ value, label }) => (
                <Select.Option key={value} value={value}>
                  {t(label)}
                </Select.Option>
              ))}
            </Select>
          </div>
          <div className="filter-item">
            <label htmlFor="">{t("paymentStatus")}</label>
            <br />
            <Select
              size="large"
              style={{ width: 170 }}
              // allowClear
              placeholder={t("selectStatus")}
              defaultValue={""}
              onChange={(value) => {
                queryProject.page = 1;
                if (value === undefined) {
                  queryProject.paymentStatus = "";
                } else {
                  queryProject.paymentStatus = value;
                }
                fetchProject();
                fetchSummary();
              }}
            >
              {Object.values(PaymentStatusTrans).map(({ value, label }) => (
                <Select.Option key={value} value={value}>
                  {t(label)}
                </Select.Option>
              ))}
            </Select>
          </div>
          {/* <div className="filter-item ml-2 md:ml-0">
            <label htmlFor="">{t("customer")}</label>
            <br />
            <Select
              showSearch
              onSearch={(value) => debounceSearchCustomer(value)}
              style={{ width: 250 }}
              allowClear
              onChange={(value) => {
                queryProject.page = 1;
                if (value === undefined) {
                  queryProject.customerId = null;
                } else {
                  queryProject.customerId = value;
                }
                fetchProject();
                fetchSummary();
              }}
              options={customers?.map((item) => {
                return {
                  label: (
                    <div>
                      <span className="">{item.name}</span>
                    </div>
                  ),
                  value: item.id,
                };
              })}
              filterOption={false}
              placeholder={t("selectCustomer")}
            />
          </div> */}
          {/* <div className="filter-item btn">
            <Button
              onClick={() => fetchProject()}
              type="primary"
              icon={<SearchOutlined />}
            >
              {t("search")}
            </Button>
          </div> */}

          {/* <div className="filter-item btn">
            <Button
              onClick={() => {
                navigate("/project/project-create");
              }}
              type="primary"
              icon={<PlusOutlined />}
            >
              {t("create")}
            </Button>
          </div> */}
        </Space>
      </div>

      <AriviTable
        pagination={false}
        rowKey="id"
        dataSource={projects}
        scroll={{ x: "max-content" }}
        loading={loadingProject}
      >
        {/* <Column
          width={50}
          title={t("stt")}
          dataIndex={"stt"}
          align="center"
          render={(_, __, index) => {
            if (queryProject.page == 1) {
              return index + 1;
            }
            return (queryProject.page - 1) * 10 + index + 1;
          }}
        /> */}
        {/* <Column title={t("code")} dataIndex="code" key="code" /> */}
        <Column
          width={300}
          title={t("project")}
          dataIndex="name"
          key="title"
          render={(address, record: Project) => {
            return (
              <>
                <div className="whitespace-pre-wrap">
                  <strong>{t("code")}</strong> : {record.code}
                </div>
                <div className="whitespace-pre-wrap">
                  <strong>{t("projectName")}</strong> : {record.name}
                </div>
                <div className="whitespace-pre-wrap">
                  <strong>{t("package")}</strong>: {record.product?.name}
                </div>
                <div className="whitespace-pre-wrap">
                  <strong>{t("customer")}</strong>: {record.customer?.name}
                </div>
              </>
            );
          }}
        />
        {/* <Column
          width={150}
          title={t("customer")}
          dataIndex="customer"
          key="customer"
          render={(address, record: Project) => {
            return (
              <div className="whitespace-pre-wrap">{record.customer?.name}</div>
            );
          }}
        /> */}
        <Column
          width={250}
          title={t("address")}
          dataIndex="address"
          key="address"
          render={(address) => {
            return <div className="whitespace-pre-wrap">{address}</div>;
          }}
        />
        {/* <Column
          // width={300}
          title={t("package")}
          dataIndex="product"
          key="product"
          render={(product: Product) => {
            return <div className="whitespace-pre-wrap">{product.name}</div>;
          }}
        /> */}
        <Column
          // width={300}
          title={t("moneyFinal")}
          dataIndex="moneyFinal"
          align="right"
          key="moneyFinal"
          render={(_, project: Project) => {
            return <div>{formatVND(project.moneyFinal || 0)}</div>;
          }}
        />
        <Column
          width={300}
          title={t("progress")}
          dataIndex="name"
          key="title"
          render={(_, record: Project) => {
            return (
              <div>
                <Progress
                  percent={record.completePercent}
                  size="default"
                  status="active"
                />
                <div className="text-xs text-gray-500 mt-1 text-center">
                  {record.currentCompleteReview}/{record.quantityReview}
                </div>
              </div>
            );
          }}
        />

        <Column
          title={t("status")}
          align="center"
          dataIndex="status"
          key="status"
          render={(status: ProjectStatus) => {
            return (
              <ProjectStatusComp status={status} />
            );
          }}
        />
        <Column
          align="center"
          title={t("paymentStatus")}
          dataIndex="paymentStatus"
          key="paymentStatus"
          render={(paymentStatus: PaymentStatus) => {
            return (
              <PaymentStatusComp status={paymentStatus} />
            );
          }}
        />
        <Column
          title={t("createdAt")}
          dataIndex="createdAt"
          key="createdAt"
          render={(createdAt) => {
            return unixToFullDate(createdAt);
          }}
        />
        <Column
          width={120}
          title={""}
          key="action"
          fixed="right"
          render={(text, record: Project) => (
            //@ts-ignore
            <DropdownCell
              text={t("action")}
              items={[
                {
                  onClick: () => "",
                  // createOrderModalRef.current?.handleUpdate(record),
                  label: (
                    <Button
                      className="w-full"
                      type="primary"
                      onClick={() => {
                        modalRef.current?.handleUpdate(record);
                        setSearchParams(`projectId=${record.id}`);
                      }}
                    >
                      {t("detail")}
                    </Button>
                  ),
                  key: "view",
                  // hidden: !hasUpdateProductPermission,
                },
                {
                  onClick: () => "",
                  // createOrderModalRef.current?.handleUpdate(record),
                  label: (
                    <>
                      {(record.status === ProjectStatus.Processing ||
                        record.status === ProjectStatus.Paused) && (
                          <Button
                            icon={
                              record.status === ProjectStatus.Processing ? (
                                <span>
                                  <LockOutlined />
                                </span>
                              ) : record.status === ProjectStatus.Paused ? (
                                <span>
                                  <UnlockOutlined />
                                </span>
                              ) : (
                                <></>
                              )
                            }
                            // type="primary"
                            className={`text-white w-full justify-center !flex !items-center gap-2 !font-medium ${record.status === ProjectStatus.Processing
                              ? "bg-red-500"
                              : record.status === ProjectStatus.Paused
                                ? "bg-green-500"
                                : ""
                              }`}
                            onClick={() => {
                              if (record.status === ProjectStatus.Processing) {
                                handleInactiveProject?.(record.id);
                              } else if (record.status === ProjectStatus.Paused) {
                                handleActiveProject?.(record.id);
                              }
                            }}
                          >
                            {record.status === ProjectStatus.Processing
                              ? t("stop")
                              : record.status === ProjectStatus.Paused
                                ? t("reopen")
                                : ""}
                          </Button>
                        )}
                    </>
                  ),
                  key: "updateStatus",
                  hidden: !(
                    record.status === ProjectStatus.Processing ||
                    record.status === ProjectStatus.Paused
                  ),
                },
                {
                  onClick: () => "",
                  // createOrderModalRef.current?.handleUpdate(record),
                  label: (
                    <>
                      {record.status === ProjectStatus.New && (
                        <Popconfirm
                          title={t("confirm?")}
                          // description={t("are_you_sure_to_cancel_this_project")}
                          okText={t("save")}
                          cancelText={t("close")}
                          onConfirm={() => handleCancelProject?.(record.id)}
                        >
                          <Button
                            icon={
                              <span>
                                <StopOutlined />
                              </span>
                            }
                            className={`text-white w-full justify-center !flex !items-center gap-2 !font-medium ${record.status === ProjectStatus.New &&
                              "bg-red-500"
                              }`}
                          >
                            {t("cancel")}
                          </Button>
                        </Popconfirm>
                      )}
                    </>
                  ),
                  key: "updateStatus2",
                  hidden: record.status !== ProjectStatus.New,
                },
                {
                  label: (
                    <>
                      {record.paymentStatus !== PaymentStatus.Complete && (
                        <Popconfirm
                          title={t("confirm?")}
                          onConfirm={() => handlePayAgain(record.id)}
                          okText={t("yes")}
                          cancelText={t("no")}
                        >
                          <Button
                            className=" w-full"
                            type="primary"
                            loading={loadingPay}
                          >
                            {t("payAgain")}
                          </Button>
                        </Popconfirm>
                      )}
                    </>
                  ),
                  key: "delete",
                  hidden: record.paymentStatus === PaymentStatus.Complete,
                },
              ].filter((item) => !item.hidden)}
              trigger={["click"]}
            >
              <a onClick={(e) => e.preventDefault()}>
                <Space>
                  {t("action")}
                  <DownOutlined />
                </Space>
              </a>
            </DropdownCell>
          )}
        />
      </AriviTable>

      <Pagination
        defaultPageSize={queryProject.limit}
        currentPage={queryProject.page}
        total={totalProject}
        onChange={({ limit, page }) => {
          queryProject.page = page;
          queryProject.limit = limit;
          setQueryProject({ ...queryProject });
          fetchProject();
          fetchSummary();
        }}
      />

      <ProjectModal
        onSubmitOk={fetchProject}
        onClose={() => {
          setSearchParams("");
        }}
        ref={modalRef}
      />
    </div>
  );
};

export default OrderHistoryTab;
