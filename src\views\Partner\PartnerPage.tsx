import {
  ExportOutlined,
  ImportOutlined,
  PlusOutlined,
  SearchOutlined,
} from "@ant-design/icons";
import { <PERSON><PERSON>, Card, DatePicker, Input, message, Popconfirm, Select, Space } from "antd";
import { partnerApi } from "api/partner.api";
import { usePartner } from "hooks/usePartner";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { PermissionNames } from "router";
import { permissionStore } from "store/permissionStore";
import {
  Partner,
  PartnerStatus,
  PartnerStatusTrans,
  PartnerVerifyStatusTrans,
} from "types/partner";
import { getTitle } from "utils";
import { checkRole } from "utils/auth";
import { unixToFullDate } from "utils/dateFormat";
import { handleExport, MyExcelColumn } from "../../utils/MyExcel";
import { PartnerModal, PartnerModalRef } from "./components/Modal/PartnerModal";
import { PartnerList } from "./components/Table/PartnerList";
import {
  ResetPasswordModal,
  ResetPasswordModalRef,
} from "./components/Modal/ResetPasswordModal";
import { TextField } from "@mui/material";
import { useTranslation } from "react-i18next";
import partnerExportDemo from "./components/partnerExportDemo";
import ImportPartner, {
  ImportPartnerModal,
} from "./components/Modal/ImportPartner";
import { removeSubstringFromKeys } from "types/common";
import { useSearchParams } from "react-router-dom";
import dayjs from "dayjs";
import { debounce } from "lodash";
import { ReactComponent as DownloadIcon } from "assets/svgs/download.svg";
import { ReactComponent as SearchIcon } from "assets/svgs/search-icon.svg";

export const PartnerPage = ({ title = "" }) => {
  const [searchParams, setSearchParams] = useSearchParams();

  const partnerModalRef = useRef<PartnerModalRef>();
  const resetModalRef = useRef<ResetPasswordModalRef>();
  const { t } = useTranslation();
  const importModal = useRef<ImportPartnerModal>();

  const [openImport, setOpenImport] = useState(false);

  const [loadingDelete, setLoadingDelete] = useState(false);
  const {
    partners,
    fetchPartner,
    loadingPartner,
    queryPartner,
    totalPartner,
    setQueryPartner,
  } = usePartner({
    initQuery: {
      page: 1,
      limit: 20,
    },
  });
  const [searchState, setSearchState] = useState("");
  // const hasPartnerAddPermission = checkRole(
  //   PermissionNames.consumerPartnerAdd,
  //   permissionStore.permissions
  // );
  // const hasPartnerUpdatePermission = checkRole(
  //   PermissionNames.consumerIndustryEdit,
  //   permissionStore.permissions
  // );
  // const hasIndustryDeletePermission = checkRole(
  //   PermissionNames.consumerIndustryDelete,
  //   permissionStore.permissions
  // );
  useEffect(() => {
    document.title = getTitle(t(title));
    console.log({ dayjs: dayjs().valueOf() });
    const search = searchParams.get("search");
    if (search) {
      queryPartner.search = search;
      setQueryPartner({ ...queryPartner });
      setSearchState(search);
      setSearchParams("");
    }
    fetchPartner(queryPartner);
  }, []);

  useEffect(() => {
    fetchPartner();
  }, [queryPartner]);

  const exportColumns: MyExcelColumn<Partner>[] = [
    {
      header: t("code"),
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "code",
      columnKey: "code",
      render: (record) => record.code,
    },
    {
      header: t("partnerName"),
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "fullName",
      columnKey: "fullName",
      render: (record) => record.fullName,
    },
    {
      header: "Email",
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "email",
      columnKey: "email",
      render: (record) => record.email,
    },
    {
      header: t("phoneNumber"),
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "phone",
      columnKey: "phone",
      render: (record) => record.phone,
    },
    {
      header: t("balance"),
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "balance",
      columnKey: "balance",
      render: (record) => record.balance,
    },
    {
      header: t("status"),
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "isBlocked",
      columnKey: "isBlocked",
      render: (record) => (record.isBlocked ? t("locked") : t("open")),
    },
    {
      header: t("verifyStatus"),
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "verifyStatus",
      columnKey: "verifyStatus",
      render: (record) => t(record.verifyStatus),
    },
  ];

  const handleOnUploadedFile = async (excelData: any, setData: any) => {
    const { results } = excelData;

    console.log("results", results);
    const importData = results?.map((item: any) => {
      const refineRow = removeSubstringFromKeys(item, " (*)");
      const refineDate = removeSubstringFromKeys(item, " (*)(DD/MM/YYYY)");
      const fullName = refineRow[t("partnerName")];
      return {
        fullName,

        rowNum: item.__rowNum__,
        email: refineRow["Email"],
        phone: refineRow[t("phoneNumber")],
        password: refineRow[t("password")],
      };
    });
    console.log("importData", importData);

    // const dataPost = postData.map((item: any) => item.store);
    // setData(dataPost);
    // console.log("dataPost ne", dataPost);

    setData(importData);
  };

  const handleDeletePartner = async (partnerId: number) => {
    try {
      setLoadingDelete(true);
      const res = await partnerApi.delete(partnerId);
      fetchPartner();
      message.success(t("actionSuccessfully"));
    } catch (error) {
    } finally {
      setLoadingDelete(false);
    }
  };
  const handleActivePartner = async (partnerId: number) => {
    try {
      setLoadingDelete(true);
      const res = await partnerApi.update(partnerId, {
        partner: { isBlocked: false },
      });
      fetchPartner();
      message.success(t("actionSuccessfully"));
    } catch (error) {
    } finally {
      setLoadingDelete(false);
    }
  };
  const handleVerifyPartner = async (partnerId: number) => {
    try {
      setLoadingDelete(true);
      const res = await partnerApi.verifyStatus(partnerId);
      fetchPartner();
      message.success(t("actionSuccessfully"));
    } catch (error) {
    } finally {
      setLoadingDelete(false);
    }
  };
  const handleRejectPartner = async (partnerId: number) => {
    try {
      setLoadingDelete(true);
      const res = await partnerApi.unverifiedStatus(partnerId);
      fetchPartner();
      message.success(t("actionSuccessfully"));
    } catch (error) {
    } finally {
      setLoadingDelete(false);
    }
  };

  const handleInactivePartner = async (partnerId: number) => {
    try {
      setLoadingDelete(true);
      const res = await partnerApi.update(partnerId, {
        partner: { isBlocked: true },
      });
      fetchPartner();
      message.success(t("actionSuccessfully"));
    } catch (error) {
    } finally {
      setLoadingDelete(false);
    }
  };

  const debounceSearch = useCallback(
    debounce((keyword) => {
      return setQueryPartner((pre) => ({ ...pre, page: 1, search: keyword }));
    }, 300),
    []
  );

  const optionStatus = [
    {
      value: "ALL",
      label: t("ALL")
    },
    {
      value: false,
      label: t("open")
    },
    {
      value: true,
      label: t("locked")

    }
  ]

  const handleSearch = (search: string) => {
    queryPartner.search = search;
    queryPartner.page = 1;
    fetchPartner();
  };

  return (
    <div>
      {/* <Card bodyStyle={{ padding: "8px 20px" }} style={{ borderRadius: "8px" }}> */}
      <section className="box">
        <div className="filter-container" style={{ width: '100%' }}>
          <div style={{ width: '100%', display: 'flex', gap: '16px', alignItems: 'end' }}>
            <div className="filter-item" style={{ flex: 1, minWidth: '350px' }}>
              <label htmlFor="">{t("searchPartner")}</label>
              <Input.Search

                value={searchState}
                allowClear
                onChange={(ev) => {
                  setSearchState(ev.currentTarget.value);
                  // queryPartner.search = ev.currentTarget.value;
                }}
                onKeyDown={(ev) => {
                  if (ev.code == "Enter") {
                    // fetchPartner();
                  }
                }}
                onSearch={handleSearch}
                size="large"
                className="w-full search-btn"
                enterButton={<SearchIcon onClick={() => {

                }} />}
                style={{ width: '100%' }}
              />
            </div>
            <div className="filter-item flex-1 max-w-[50%] max-w-[295px]">
              <label className="block mb-1">{t("createdAt")}</label>
              <DatePicker.RangePicker
                size="large"
                className="w-full"
                allowClear
                value={[

                  queryPartner.fromAt ? dayjs.unix(queryPartner.fromAt) : null,
                  queryPartner.toAt ? dayjs.unix(queryPartner.toAt) : null,
                ]}
                onChange={(value) => {
                  if (value) {
                    queryPartner.fromAt = dayjs(value[0]).startOf("day").unix();
                    queryPartner.toAt = dayjs(value[1]).endOf("day").unix();
                  } else {
                    delete queryPartner.fromAt;
                    delete queryPartner.toAt;
                  }
                  queryPartner.page = 1;
                  fetchPartner();
                }}
                format="DD/MM/YYYY"
              />
            </div>

            <div className="filter-item" style={{ flex: 1, maxWidth: 175 }}>
              <label htmlFor="">{t("activeStatus")}</label>
              <br />
              <Select
                size="large"
                style={{ width: '100%' }}
                defaultValue="ALL"
                placeholder={t("selectStatus")}
                onChange={(value) => {
                  queryPartner.page = 1;
                  if (value === undefined || value === "ALL") {
                    queryPartner.isBlocked = null;
                  } else {
                    queryPartner.isBlocked = value;
                  }
                  fetchPartner();
                  // fetchSummary();
                }}
              >
                {
                  optionStatus.map((option) =>
                    <Select.Option value={option.value}>{option.label}</Select.Option>
                  )
                }
              </Select>
            </div>
            <div className="filter-item" style={{ flex: 1, maxWidth: 175 }}>
              <label htmlFor="">{t("verifyStatus")}</label>
              <br />
              <Select
                size="large"
                style={{ width: '100%' }}
                defaultValue="ALL"
                placeholder={t("selectStatus")}
                onChange={(value) => {
                  queryPartner.page = 1;
                  if (value === undefined || value === "ALL") {
                    queryPartner.verifyStatus = null;
                  } else {
                    queryPartner.verifyStatus = value;
                  }
                  fetchPartner();
                  // fetchSummary();
                }}
              >
                {Object.values(PartnerVerifyStatusTrans).map(({ value }) => (
                  <Select.Option key={value} value={value}>
                    {t(value)}
                  </Select.Option>
                ))}
              </Select>
            </div>
            {/* <div className="filter-item btn">
              <Button
                onClick={() => fetchPartner()}
                type="primary"
                icon={<SearchOutlined />}
              >
                {t("search")}
              </Button>
            </div>
            <div className="filter-item btn">
              <Button
                onClick={() => {
                  partnerModalRef.current?.handleCreate();
                }}
                icon={<PlusOutlined />}
                type="primary"
              >
                {t("create")}
              </Button>
            </div>
            <div className="filter-item btn">
              <Button
                onClick={() => {
                  importModal.current?.open();
                }}
                type="primary"
                icon={<ImportOutlined />}
              >
                {t("importExcel")}
              </Button>
            </div> */}

            {/* )} */}
            {/* <div className="filter-item ">
                <Button
                  onClick={() => {
                    setOpenImport(true);
                  }}
                  type="primary"
                  icon={<PlusOutlined />}
                >
                  Nhập excel
                </Button>
              </div> */}
            {/* <div className="filter-item ">
                  <Button
                    onClick={() => {
                      importModal.current?.open();
                    }}
                    type="primary"
                    icon={<ImportOutlined />}
                  >
                    Nhập excel
                  </Button>
                </div> */}

            <div className="filter-item" style={{ flex: 'none', width: '150px', minWidth: '150px' }}>
              <Popconfirm
                title={t("exportAsk")}
                onConfirm={() =>
                  handleExport({
                    onProgress(percent) { },
                    exportColumns,
                    fileType: "xlsx",
                    dataField: "partners",
                    query: queryPartner,
                    api: partnerApi.findAll,
                    fileName: t("partnerList"),
                    sheetName: t("partnerList"),
                  })
                }
                okText={t("exportExcel")}
                cancelText={t("cancel")}
              >
                <Button
                  size="large"
                  type="primary"
                  loading={false}
                  icon={<DownloadIcon />}
                  style={{ width: '100%' }}
                >
                  <p className="text-sm !font-[400]">{t("exportExcel")}</p>
                </Button>
              </Popconfirm>
            </div>
          </div>
        </div>

        <PartnerList
          onEdit={(record) => partnerModalRef.current?.handleUpdate(record)}
          onReset={(record) => resetModalRef.current?.handleUpdate(record)}
          dataSource={partners}
          loading={loadingPartner}
          loadingDelete={loadingDelete}
          pagination={{
            total: totalPartner,
            defaultPageSize: queryPartner.limit,
            currentPage: queryPartner.page,
            onChange: ({ page, limit }) => {
              Object.assign(queryPartner, {
                page,
                limit,
              });
              fetchPartner();
            },
          }}
          onDelete={handleDeletePartner}
          onActive={handleActivePartner}
          onInactive={handleInactivePartner}
          onVerify={handleVerifyPartner}
          onUnverify={handleRejectPartner}
          onSubmitOk={fetchPartner}

        // hasDeletePartnerPermission={hasPartnerDeletePermission}
        // hasUpdatePartnerPermission={hasPartnerUpdatePermission}
        />
      </section>

      <PartnerModal
        ref={partnerModalRef}
        onClose={function (): void {
          throw new Error("Function not implemented.");
        }}
        onSubmitOk={fetchPartner}
      />
      <ResetPasswordModal
        ref={resetModalRef}
        onClose={() => { }}
        onSubmitOk={fetchPartner}
      // hasAddIndustryPermission={hasIndustryAddPermission}
      // hasUpdateIndustryPermission={hasIndustryUpdatePermission}
      />
      {useMemo(
        () => (
          <ImportPartner
            guide={[
              t("pleaseDownload"),
              t("notChangeSample"),
              t("forDetailSheet"),
            ]}
            onSuccess={() => {
              fetchPartner();
            }}
            ref={importModal}
            createApi={partnerApi.create}
            onUploaded={(excelData, setData) => {
              handleOnUploadedFile(excelData, setData);
            }}
            okText={`${t("importPartnerNow")}`}
            onDownloadDemoExcel={() => {
              partnerExportDemo(t);
            }}
            t={t}
          />
        ),
        []
      )}
    </div>
    // </Card>
  );
};
