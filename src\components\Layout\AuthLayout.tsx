import "./AuthLayout.scss";
import React, { useState } from "react";
import loginBg from "assets/svgs/login-bg.svg";
import { settings } from "settings";
import { Image, Select, Space } from "antd";
import { useMediaQuery } from "@mui/material";
import { useTranslation } from "react-i18next";
import i18nInstance, { langResources } from "config-translation";
import unitedKingdomIcon from "assets/svgs/united-kingdom.svg";
import { IoLanguageOutline } from "react-icons/io5";
import dayjs from "dayjs";
import { setValueToLocalStorage } from "utils/auth";
import clsx from "clsx";

interface Props {
  children?: React.ReactNode;
}

export const AuthLayout = ({ children }: Props) => {
  const { t } = useTranslation();
  const mdUp = useMediaQuery((theme: any) => theme.breakpoints.up("md"));
  const [language, setLanguage] = useState(localStorage.getItem("lng") || "vi");

  return (
    <div className="h-[100dvh] flex overflow-hidden justify-center">
      {mdUp && (
        <div className="auth-layout-bg flex-1 h-auto flex justify-center items-center">
          <img
            src={settings.logo}
            className="absolute z-20 left-[64px] top-[64px] w-[131px] h-[44px] object-contain"
          />
          <img
            src={loginBg}
            className="w-[545.74px] h-[540px] object-contain z-10"
          />
          <div className="absolute left-[64px] bottom-[48px] text-[#5D6A83] z-10">
            @ 2025 Rivi. All Right Reserved.
          </div>
        </div>
      )}
      <div
        className={clsx(
          "relative w-[588px] h-full bg-white flex flex-col overflow-y-auto",
          mdUp ? "" : ""
        )}
      >
        <div
          className={clsx(
            "flex-1 flex flex-col",
            mdUp ? "p-[64px] justify-between" : "px-[16px] mt-[24px] gap-[64px]"
          )}
        >
          <div className="relative h-[60px] flex-shrink-0 ">
            <div className={clsx(mdUp ? "absolute top-0 right-0" : "w-full")}>
              <Space
                className={clsx(
                  "w-full",
                  mdUp ? "justify-end" : "justify-between"
                )}
              >
                {!mdUp && (
                  <img
                    src={settings.logo}
                    className="w-[119px] h-[440x] object-contain"
                  />
                )}
                {mdUp && (
                  <div className="text-[#5A6A85]">{t("changeLanguage")}</div>
                )}
                <Select
                  className="w-fit !h-[40px] rounded-full !border border-solid !border-[#EAEFF4] py-2 flex items-center justify-center shadow-sm selector-language [&_.ant-select-selector]:border-none"
                  value={language}
                  onChange={(value) => {
                    setLanguage(value);
                    i18nInstance.changeLanguage(value.toLowerCase());
                    dayjs.locale(value.toLowerCase());
                    setValueToLocalStorage("lng", value.toLowerCase());
                  }}
                  dropdownClassName="custom-language-dropdown"
                  bordered={false}
                  options={Object.values(langResources).map((item) => {
                    return {
                      label: (
                        <Space>
                          <Image
                            preview={false}
                            src={item.icon || unitedKingdomIcon}
                            alt=""
                            className="!w-[20px] !h-[20px] cursor-pointer -translate-y-[1.5px]"
                          />
                          <div className="text-[#5A6A85] text-[14px]">
                            {item.text}
                          </div>
                        </Space>
                      ),
                      value: item.value,
                    };
                  })}
                  placeholder={
                    <React.Fragment>
                      <IoLanguageOutline />
                      &nbsp; {t("languagePlaceholder")}
                    </React.Fragment>
                  }
                  size="large"
                ></Select>
              </Space>
            </div>
          </div>
          <div className={clsx("relative ", mdUp ? "" : "")}>{children}</div>
          <div className="relative h-[60px] flex-shrink-0 "></div>
        </div>
      </div>
      <div className="absolute bottom-6 left-[64px] opacity-50 text-center text-sm">
        v{settings.version}
      </div>
    </div>
  );
};
