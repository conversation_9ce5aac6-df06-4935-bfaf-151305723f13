import "./styles/text.scss";
import "./styles/App.scss";
import "./styles/AntDesign.scss";
import "./styles/mui.scss";
import "./styles/AntForm.scss";
// import "antd/dist/antd.less";
import { useRoutes } from "react-router-dom";
import { routes } from "./router";
import { ConfigProvider } from "antd";
import { observer } from "mobx-react";
import viVN from "antd/lib/locale/vi_VN";
import { userStore } from "store/userStore";
import { useEffect } from "react";
import { autorun, toJS } from "mobx";
import { settings } from "settings";
import { useOneSignalContext } from "context/OneSignalContext";
import { initUUID } from "utils/devide";
import { CssBaseline, ThemeProvider } from "@mui/material";
import { ThemeSettings } from "theme/Theme";
import i18nInstance from "config-translation";
import dayjs from "dayjs";
import { viVNLocale } from "utils/locale";
import enUS from "antd/es/locale/en_US";
import { appStore } from "store/appStore";
autorun(() => {
  // const language = !!userStore.token
  //   ? userStore.info?.language?.toLowerCase()
  //   : "vi";
  const language = "vi";
  i18nInstance.changeLanguage(language);
  dayjs.locale(language);
});
const App = observer(() => {
  const account = toJS(userStore);

  const { runOneSignal } = useOneSignalContext();

  // useEffect(() => {
  //   localStorage.setItem("isAuthGG", "");
  //   if (account.token) {
  //     if (!$isDev) {
  //       oneSignal.run();
  //     }
  //   }
  // }, [account.token]);
  useEffect(() => {
    if (userStore.isLoggedIn) {
      appStore.fetchConfiguration();
    }
  }, [userStore.isLoggedIn]);
  useEffect(() => {
    initUUID();
    if (account.token) {
      if (!settings.isDev) {
        runOneSignal?.();
      }
    }
  }, [account.token]);

  const element = useRoutes(routes);
  const theme = ThemeSettings();
  return (
    <ThemeProvider theme={theme}>
      <ConfigProvider
        locale={
          userStore.info.language?.toLowerCase() == "vi" ? viVNLocale : enUS
        }
        // locale={viVN}
        theme={{
          hashed: false,
          components: {
            Card: {
              colorText: "var(--gray-1-color)",
            },
            Tabs: {
              lineHeight: undefined,
            },
          },
          token: {
            colorTextPlaceholder: "var(--gray-3-color)",
            colorPrimary: theme.palette.primary.main,
            fontFamily:
              '"Plus Jakarta Sans", -apple-system, BlinkMacSystemFont,"Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans","Droid Sans", "Helvetica Neue", sans-serif',
          },
        }}
      >
        <CssBaseline></CssBaseline>
        <div className="App">{element}</div>
      </ConfigProvider>
    </ThemeProvider>
  );
});

export default App;
