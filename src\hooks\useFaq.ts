import { faqApi } from "api/faq.api";
import { useState } from "react";
import { Faq } from "types/faq";
import { QueryParam } from "types/query";

export interface FaqQuery extends QueryParam {}

interface UseFaqProps {
  initQuery: FaqQuery;
}

export const useFaq = ({ initQuery }: UseFaqProps) => {
  const [data, setData] = useState<Faq[]>([]);
  const [total, setTotal] = useState(0);
  const [query, setQuery] = useState<FaqQuery>(initQuery);
  const [loading, setLoading] = useState(false);

  const fetchData = async () => {
    setLoading(true);
    try {
      const { data } = await faqApi.findAll(query);

      setData(data.faqs);
      setTotal(data.total);
    } finally {
      setLoading(false);
    }
  };

  return {
    faqs: data,
    totalFaq: total,
    fetchFaq: fetchData,
    loadingFaq: loading,
    setQueryFaq: setQuery,
    queryFaq: query,
  };
};
