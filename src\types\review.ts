import { FileAttach } from "./fileAttach";
import { Partner } from "./partner";
import { Project } from "./project";

export enum ReviewStatus {
  All = "ALL",
  Pending = "PENDING",
  Assigning = "ASSIGNING", // <PERSON><PERSON> người nhận nhiệm vụ này rồi
  Accept = "ACCEPT", // chấp nhận nhiệm vụ
  SystemPending = "SYSTEM_PENDING", // Chờ hệ thống duyệt
  AdminPending = "ADMIN_PENDING", // Ch<PERSON> nhân viên duyệt
  Complete = "COMPLETE",
  Reject = "REJECT",
  Warranty = "WARRANTY", // đang phân phối lại lần 2
  RequestWarranty = "REQUEST_WARRANTY", // yêu c<PERSON>u bảo hành
  RejectWarranty = "REJECT_WARRANTY", // Huỷ yêu cầu bảo hành
  Inactive = "INACTIVE",
  OverTime = "OVER_TIME",
}

export const ReviewStatusTrans2 = {
 [ReviewStatus.Pending]: {
    value: ReviewStatus.Pending,
    color: "yellow",
    note: "pendingStatusNote",
  },
  [ReviewStatus.Warranty]: {
    value: ReviewStatus.Warranty,
    color: "orange",
    note: "warrantyStatusNote",
  },
  [ReviewStatus.RejectWarranty]: {
    value: ReviewStatus.RejectWarranty,
    textColor: "black",
    borderColor: "black",
    color: "#cecece",
    note: "",
  },
  // [ReviewStatus.Assigning]: {
  //   value: ReviewStatus.Assigning,
  //   color: "yellow",
  //   note: "",
  // },
  [ReviewStatus.SystemPending]: {
    value: ReviewStatus.SystemPending,
    color: "blue",
    note: "systemStatusNote",
  },
  [ReviewStatus.AdminPending]: {
    value: ReviewStatus.AdminPending,
    color: "blue",
    note: "adminStatusNote",
  },
  [ReviewStatus.RequestWarranty]: {
    value: ReviewStatus.RequestWarranty,
    color: "red",
    note: "requestWarrantyNote",
  },
  [ReviewStatus.Complete]: {
    value: ReviewStatus.Complete,
    color: "green",
    note: "",
  },
  [ReviewStatus.Reject]: {
    value: ReviewStatus.Reject,
    color: "red",
    note: "",
  },
};

export const ReviewStatusTrans = {
  [ReviewStatus.All]: {
    value: ReviewStatus.All,
    color: "blue",
  },
  [ReviewStatus.Pending]: {
    value: ReviewStatus.Pending,
    color: "yellow",
    textColor: "#FFCD00",
    bgColor: "#FFFDE9",
    borderColor: "#FEF086",
  },
  [ReviewStatus.Warranty]: {
    value: ReviewStatus.Warranty,
    color: "orange",
    textColor: "#EB9113",
    bgColor: "#FDF3E3",
    borderColor: "#FFDFB3",
  },

  [ReviewStatus.SystemPending]: {
    value: ReviewStatus.SystemPending,
    color: "blue",
  },
  [ReviewStatus.AdminPending]: {
    value: ReviewStatus.AdminPending,
    color: "orange",
  },
  [ReviewStatus.RequestWarranty]: {
    value: ReviewStatus.RequestWarranty,
    color: "yellow",
    textColor: "#FFCD00",
    bgColor: "#FFFDE9",
    borderColor: "#FEF086",
  },
  [ReviewStatus.RejectWarranty]: {
    value: ReviewStatus.RejectWarranty,
    color: "red",
    textColor: "#E94134",
    bgColor: "#FFEBEA",
    borderColor: "#FFC8C4",
  },
  [ReviewStatus.Complete]: {
    value: ReviewStatus.Complete,
    color: "green",
    textColor: "#41D664",
    bgColor: "#E9FFEE",
    borderColor: "#A5FFBA",
  },
  [ReviewStatus.Reject]: {
    value: ReviewStatus.Reject,
    color: "red",
    textColor: "#E94134",
    bgColor: "#FFEBEA",
    borderColor: "#FFC8C4",
  },
};

export const MisionListTrans = {
  [ReviewStatus.All]: {
    value: ReviewStatus.All,
    color: "blue",
    label: "withdrawALL",
  },
  [ReviewStatus.Pending]: {
    value: ReviewStatus.Pending,
    color: "yellow",
    label: "ongoing",
  },
  [ReviewStatus.SystemPending]: {
    value: ReviewStatus.SystemPending,
    color: "blue",
    label: "SYSTEM_PENDING",
  },
  [ReviewStatus.Warranty]: {
    value: ReviewStatus.Warranty,
    color: "orange",
    label: "distributed",
  },
  [ReviewStatus.Complete]: {
    value: ReviewStatus.Complete,
    color: "green",
    label: "completed",
  },
  [ReviewStatus.Reject]: {
    value: ReviewStatus.Reject,
    color: "red",
    label: "REJECT",
  },
};

export const MisionWarningTrans = {
  [ReviewStatus.All]: {
    value: ReviewStatus.All,
    color: "blue",
    label: "withdrawALL",
  },
  [ReviewStatus.RequestWarranty]: {
    value: ReviewStatus.RequestWarranty,
    color: "yellow",
    label: "ongoing",
  },
  [ReviewStatus.RejectWarranty]: {
    value: ReviewStatus.RejectWarranty,
    color: "red",
    label: "REJECT",
  },
};

export enum MissionTabType {
  List = "LIST",
  Approve = "APPROVE",
  Warranty = "WARRANTY",
}

export interface Review {
  id: number;
  createdAt: number;
  updatedAt: number;
  expireWarrantyAt: number;
  isDeleted: boolean;
  status: ReviewStatus;
  content: string;
  note: string;
  reviewUrl: string;
  isExistImage: boolean;
  isWarrantyReject: boolean;
  rewardPoint: number; //số điểm nhận được khi hoàn thành
  price: number; //chi phí
  project: Project;
  fileAttaches: FileAttach[];
  partner: Partner;
  profit: number;
  contentChange: string;
  projectLogs: any;
  acceptAt?:  number;
  completeAt?: string;
}

export enum ReviewError {
  ErrorContent = "ERROR_CONTENT",
  ErrorImage = "ERROR_IMAGE",
}

export interface ReviewStatusSummary {
  status: ReviewStatus;
  total: number;
}
