import {
  DownOutlined,
  EditOutlined,
  EyeOutlined,
  InfoCircleOutlined,
  LockOutlined,
  RetweetOutlined,
  UnlockOutlined,
} from "@ant-design/icons";
import {
  Button,
  Divider,
  Image,
  Input,
  message,
  Modal,
  Popconfirm,
  Radio,
  Space,
  Table,
  Tag,
} from "antd";
import Column from "antd/lib/table/Column";
import { reviewApi } from "api/review.api";
import { supportTicketApi } from "api/supportTicket.api";
import { IPagination, Pagination } from "components/Pagination";
import { AriviTable } from "components/Table/AriviTable";
import DropdownCell from "components/Table/DropdownCell";
import { useReason } from "hooks/useReason";
import { useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { GrEdit } from "react-icons/gr";
import { HiOutlineTrash } from "react-icons/hi2";
import { userStore } from "store/userStore";
import {
  MissionTabType,
  Review,
  ReviewError,
  ReviewStatus,
  ReviewStatusTrans,
} from "types/review";
import {
  SupportTicket,
  SupportTicketCreatedBy,
  SupportTicketStatus,
  SupportTicketStatusTrans,
} from "types/supportTicket";
import { formatVND } from "utils";
import { unixToFullDate } from "utils/dateFormat";
import { ReactComponent as LinkIcon } from "assets/svgs/link-icon.svg";
import { Link } from "react-router-dom";
import FileUploadItem from "components/Upload/FileUploadItem";
import { formatDate } from "utils/date";
import { settings } from "settings";
import { MissionStatusComp } from "components/MissionStatus/MissionStatusComp";
import "./styles/TaskList.scss";
import { projectApi } from "api/project.api";
import { ProjectModal, ProjectModalRef } from "views/ProjectListPage/ProjectModal";

interface PropsType {
  dataSource: Review[];
  loading: boolean;
  loadingDelete?: boolean;
  pagination?: IPagination;
  onEdit?: (review: Review) => void;
  onDelete?: (reviewId: number) => void;
  onActive?: (reviewId: number) => void;
  onInactive?: (reviewId: number) => void;
  onRefreshData: (projectId?: number) => void;
  missionDisplay?: string;
  // hasUpdateSupportTicketPermission?: boolean;
}

export const TaskList = ({
  dataSource,
  loading,
  loadingDelete,
  pagination,
  onDelete,
  onEdit,
  onActive,
  onInactive,
  onRefreshData,
  missionDisplay = "list"
}: // hasDeleteSupportTicketPermission,
  // hasUpdateSupportTicketPermission,

  PropsType) => {
  const [loadingStatus, setLoadingStatus] = useState(false);
  const { t } = useTranslation();
  const [loadingAction, setLoadingAction] = useState(false);
  const [isRejectModalOpen, setIsRejectModalOpen] = useState(false);
  const [selectedReview, setSelectedReview] = useState<Review>();
  const [note, setNote] = useState("");
  const [noteEn, setNoteEn] = useState("");
  const [visible, setVisible] = useState(false);
  const [selectedImageUrl, setSelectedImageUrl] = useState<string | null>(null);
  const modalRef = useRef<ProjectModalRef>(null);

  const {
    fetchReason,
    loadingReason,
    queryReason,
    reasons,
    setQueryReason,
    totalReason,
  } = useReason({ initQuery: { page: 1, limit: 100 } });
  const handleComplete = async (reviewId: number) => {
    try {
      setLoadingAction(true);
      const data = await reviewApi.complete(reviewId);
      onRefreshData();
      message.success(t("actionSuccessfully"));
    } catch (error) {
    } finally {
      setLoadingAction(false);
    }
  };
  const handleRejectWarranty = async (reviewId: number, note?: string) => {
    // if (!note.trim()) {
    //   message.warning(t("pleaseEnterNote"));
    //   return;
    // }

    try {
      setLoadingAction(true);
      await reviewApi.rejectWarranty(reviewId, { note: note });
      onRefreshData();
      message.success(t("actionSuccessfully"));
      setIsRejectModalOpen(false);
      setNote("");
      setNoteEn("");
    } catch (error) {
      message.error(t("actionFailed"));
    } finally {
      setLoadingAction(false);
    }
  };

  const handleApproveWarranty = async (reviewId: number) => {
    try {
      setLoadingAction(true);
      const data = await reviewApi.approveWarranty(reviewId);
      onRefreshData();

      message.success(t("actionSuccessfully"));
    } catch (error) {
    } finally {
      setLoadingAction(false);
    }
  };
  const handlePending = async (reviewId: number) => {
    try {
      setLoadingAction(true);
      const data = await reviewApi.pending(reviewId);
      onRefreshData();

      message.success(t("actionSuccessfully"));
    } catch (error) {
    } finally {
      setLoadingAction(false);
    }
  };
  const handleReject = async (
    reviewId: number,
    note: string,
    type: ReviewError
  ) => {
    try {
      setLoadingAction(true);
      const data = await reviewApi.reject(reviewId, { note, type });
      onRefreshData();

      message.success(t("actionSuccessfully"));
    } catch (error) {
    } finally {
      setLoadingAction(false);
    }
  };

  useEffect(() => {
    fetchReason();
  }, []);

  const openDetailProject = async (id: number) => {

    const { data } = await projectApi.findOne(id);
    modalRef.current?.handleUpdate(data, false);

  };

  return (
    <div>
      <AriviTable
        // bordered

        loading={loading}
        pagination={false}
        rowKey={missionDisplay === MissionTabType.List ? "" : "id"}
        dataSource={dataSource}
        className="custom-scrollbar list-mission"
        scroll={{ x: "max-content", y: "calc(100vh - 370px)" }}

      // onChange={}
      >

        {missionDisplay !== MissionTabType.List &&
          < Column
            title={<div className="text-semibold">{t("stt")}</div>}
            key="index"
            width={70}
            render={(_, __, index) => <span className="text-regular">{index + 1}</span>}
          />
        }
        <Column
          title={<div className="text-semibold">{t("idNv")}</div>}
          width={100}
          dataIndex="id"
          key={"id"}
          render={(text, record: Review) => <span className="text-regular">{record.id}</span>}
        />
        <Column
          width={300}
          title={<div className="text-semibold">{t("project")}</div>}
          dataIndex="name"
          key="title"
          render={(address, record: Review) => {
            return (
              <>
                <div className="whitespace-pre-wrap text-regular">
                  <span className="text-bold">{t("code")}</span> : {record.project?.code}
                </div>
                <div className="whitespace-pre-wrap text-regular">
                  <span className="text-bold">{t("projectName")}</span> : {record.project?.name}
                </div>
                <div className="whitespace-pre-wrap flex items-center gap-1  text-regular">
                  <span className="text-bold">{t("location")}: </span>
                  <a
                    href={record.project?.mapUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="h-4"
                  >
                    <LinkIcon />
                  </a>

                </div>

                <div className="whitespace-pre-wrap flex items-center gap-1  text-regular">
                  <span className="text-bold">{t("detail")}: </span>
                  <a
                    // href={record.project?.mapUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    // className="h-4"
                    onClick={() => openDetailProject(record?.project?.id)}
                  >
                    <InfoCircleOutlined />
                  </a>

                </div>

                {/* <Button type="primary" className="mt-1">
                  {t("detail")}
                </Button> */}

              </>
            );
          }}
        />
        <Column
          title={<div className="text-semibold">{t("time")}</div>}
          width={200}
          dataIndex="content"
          key={"content"}
          render={(text, record: Review) => {
            return (
              <>
                <div className="whitespace-pre-wrap text-regular">
                  {t("timeCreate")}<br />
                  {formatDate(record.createdAt, settings.fullDateFormat)}
                </div>
                {record.status !== ReviewStatus.Pending &&  <>
                  <Divider className="!my-2" />
                  <div className="whitespace-pre-wrap text-regular">
                    {t("timeDelivery")}<br />
                    {record.acceptAt !== 0 && record.acceptAt !== undefined ? formatDate(record.acceptAt, settings.fullDateFormat) : "--"}
                  </div>
                </>
                }
              </>
            );
          }}
        />
        <Column
          title={<div className="text-semibold">{t("content")}</div>}
          width={350}
          dataIndex="content"
          key={"content"}
          render={(text, record: Review) => <span className="text-caption-no-caps-regular">{record.content}</span>}
        />
        <Column
          title={<div className="text-semibold">{t("image")}</div>}
          width={100}
          dataIndex="content"
          key={"content"}
          render={(text, record: Review) =>
            <>
              {
                !record.isExistImage && Array.isArray(record.fileAttaches) ? <span className="text-regular">
                  {t("no")}
                </span> :
                  <>
                    {/* Modal ảnh */}
                    <Modal
                      open={!!selectedImageUrl}
                      footer={null}
                      onCancel={() => setSelectedImageUrl(null)}
                      title={<div className="text-semibold">{<span className="text-[18px] font-[400] text-[#000000]">{t("image")}</span>}</div>}
                      centered
                      width="auto"
                      bodyStyle={{ padding: 0 }}
                      style={{ maxWidth: "fit-content" }}
                      className="modal-image"
                      maskStyle={{ backgroundColor: "rgba(0, 0, 0, 0.045)" }}
                    >
                      <img
                        src={selectedImageUrl ?? ""}
                        alt="preview-large"
                        className="max-w-[90vw] max-h-[80vh] object-contain rounded"
                      />
                    </Modal>

                    {/* Ảnh nhỏ + mask */}
                    <div
                      className="relative inline-block cursor-pointer group"
                      onClick={() => setSelectedImageUrl(record.fileAttaches?.[0].url)}
                    >
                      <img
                        src={record.fileAttaches?.[0]?.url}
                        alt="preview"
                        className="w-[[80px]] h-[52px] object-cover rounded"
                      />
                      <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition flex items-center justify-center text-white text-sm gap-2 rounded">
                        <EyeOutlined />
                        <span>{t("see")}</span>
                      </div>
                    </div>
                  </>
              }
            </>
          }
        />
        <Column
          title={<div className="text-semibold">{t("partner")}</div>}
          width={200}
          dataIndex="content"
          key={"content"}
          render={(text, record: Review) => {
            return (
              <>
                {
                  record.partner && record.partner.id ?
                    <>
                      <div className="whitespace-pre-wrap text-regular">
                        <span className="text-bold">{t("code")}</span> : {record.partner?.code}
                      </div>
                      <div className="whitespace-pre-wrap text-regular">
                        <span className="text-bold">{t("name")}</span> : {record.partner?.fullName}
                      </div>
                      <div className="whitespace-pre-wrap text-regular">
                        <span className="text-bold">{t("rank")}</span> : {record.partner?.rank?.name}
                      </div>
                      <div className="whitespace-pre-wrap flex items-center gap-1 text-regular">

                        <a
                          href={record.reviewUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className=" flex items-center"
                        >
                          <span className="text-bold text-[#1A73E8]">{t("linkReview")}: </span>
                          <LinkIcon className="h-4" />
                        </a>

                      </div>
                    </> :
                    <span className="text-regular">{t("missionPending")}</span>
                }
              </>
            );
          }}
        />
        {(missionDisplay === MissionTabType.List || missionDisplay === MissionTabType.Warranty) && <Column
          width={100}
          title={<div className="text-semibold">{t("status")}</div>}
          dataIndex="name"
          align="center"
          key={"name"}
          render={(text, record: Review) => {
            return (
              <>
                <MissionStatusComp status={record.status} />
                {record.status === ReviewStatus.RejectWarranty &&
                  <>
                    <Divider className="!my-2" />
                    <div className="whitespace-pre-wrap text-caption-no-caps-regular text-left">
                      <span className="font-bold">{t("note")}{": "}</span>
                      {record.note}
                    </div>
                  </>
                }
              </>
            )

          }}
        />
        }

        {(missionDisplay == MissionTabType.List || missionDisplay === MissionTabType.Warranty) && <Column
          title={<div className="text-semibold">{t("statistical")}</div>}
          width={200}
          dataIndex="statistical"
          key={"statistical"}
          render={(_, record: Review) => {
            return (
              <>
                <div className="whitespace-pre-wrap text-regular">
                  {t("revenue")}: <span className="status-statistical__info">{formatVND(record.price ?? 0)}₫</span>
                </div>
                <div className="whitespace-pre-wrap text-regular">
                  {t("fee")}: <span className="status-statistical__error">{formatVND(record.rewardPoint ?? 0)}₫</span>
                </div>
                <div className="whitespace-pre-wrap text-regular">
                  {t("profit")} : <span className="status-statistical__success">{formatVND(record.profit ?? 0)}₫</span>
                </div>
              </>
            );
          }}
        />
        }
        {/* 
        <Column
          width={200}
          title={<div className="text-semibold">{t("profit")}
          dataIndex="name"
          align="right"
          key={"name"}
          render={(text, record: Review) => (
            <div>
              {record.status === ReviewStatus.Complete
                ? `${formatVND(record?.profit)} VND`
                : t("missionNotCompleted")}
            </div>
          )}
        /> */}
        {/* <Column
          width={200}
          title={<div className="text-semibold">{t("note")}
          dataIndex="name"
          align="right"
          key={"name"}
          render={(text, record: Review) => (
            <div>
              {record.status === ReviewStatus.SystemPending &&
                record?.projectLogs?.[0]?.description}
            </div>
          )}
        /> */}
        {/* <Column
          title={<div className="text-semibold">{t("createdAt")}
          dataIndex="createdAt"
          key="createdAt"
          render={(createdAt) => {
            return unixToFullDate(createdAt);
          }}
        /> */}
        {(missionDisplay == MissionTabType.Approve || missionDisplay === MissionTabType.Warranty) && <Column
          fixed="right"
          width={120}
          align="center"
          title=""
          key="action"
          dataIndex={""}
          render={(text, record: Review) => {
            const items = [
              {
                label: (
                  <Button
                    type="primary"
                    className="w-full justify-center !flex !items-center gap-2 !font-[400] text-[12px]"
                    onClick={() => window.open(record.reviewUrl, "_blank")}
                  >
                    <span className="action-mission-text"> {t("seeReview")}</span>
                  </Button>
                ),
                key: "seeReview",
                hidden: !(record.reviewUrl && record.status !== ReviewStatus.RequestWarranty &&
                  record.status !== ReviewStatus.AdminPending && record.status !== ReviewStatus.SystemPending
                ),
              },
              {
                label: (
                  <Popconfirm
                    title={<div className="text-semibold">{t("confirm?")}</div>}
                    onConfirm={() =>
                      handleReject(
                        record.id,
                        t("noReview"),
                        ReviewError.ErrorContent
                      )
                    }
                    okText={t("save")}
                    cancelText={t("close")}
                  >
                    <Button
                      loading={loadingAction}
                      type="primary"
                      danger
                      className="w-full justify-center !flex !items-center gap-2 !font-medium"
                    >
                      <span className="action-mission-text">{t("noReview")}</span>
                    </Button>
                  </Popconfirm>
                ),
                key: "noReview",
                hidden: !(
                  record.status === ReviewStatus.AdminPending ||
                  record.status === ReviewStatus.SystemPending
                ),
              },
              {
                label: (
                  <Popconfirm
                    title={<div className="text-semibold">{t("confirm?")}</div>}
                    onConfirm={() =>
                      handleReject(
                        record.id,
                        t("noStarReview"),
                        ReviewError.ErrorContent
                      )
                    }
                    okText={t("save")}
                    cancelText={t("close")}
                  >
                    <Button
                      loading={loadingAction}
                      type="primary"
                      danger
                      className="w-full justify-center !flex !items-center gap-2 !font-medium"
                    >
                      <span className="action-mission-text">{t("noStarReview")}</span>
                    </Button>
                  </Popconfirm>
                ),
                key: "noStarReview",
                hidden: !(
                  record.status === ReviewStatus.AdminPending ||
                  record.status === ReviewStatus.SystemPending
                ),
              },
              {
                label: (
                  <Popconfirm
                    title={<div className="text-semibold">{t("confirm?")}</div>}
                    onConfirm={() =>
                      handleReject(
                        record.id,
                        t("wrongPhoto"),
                        ReviewError.ErrorImage
                      )
                    }
                    okText={t("save")}
                    cancelText={t("close")}
                  >
                    <Button
                      loading={loadingAction}
                      type="primary"
                      danger
                      className="w-full justify-center !flex !items-center gap-2 !font-[400] text-[12px]"
                    >
                      <span className="action-mission-text">  {t("wrongPhoto")}</span>
                    </Button>
                  </Popconfirm>
                ),
                key: "wrongPhoto",
                hidden: !(
                  record.status === ReviewStatus.AdminPending ||
                  record.status === ReviewStatus.SystemPending
                ),
              },
              {
                label: (
                  <Button
                    type="primary"
                    className="w-full"
                    danger
                    onClick={() => {
                      // setIsRejectModalOpen(true);
                      handleRejectWarranty(record.id, t("reviewStill"))
                      // setSelectedReview(record);
                    }}
                  >
                    <span className="action-mission-text">
                      {t("reject")}{": "}{t("reviewStill")}
                    </span>
                  </Button>
                ),
                key: "rejectWarranty",
                hidden: record.status !== ReviewStatus.RequestWarranty,
              },
              {
                label: (
                  <Button
                    type="primary"
                    className="w-full"
                    danger
                    onClick={() => {
                      // setIsRejectModalOpen(true);
                      // setSelectedReview(record);
                      handleRejectWarranty(record.id, t("inappropriateContent"))
                    }}
                  >
                    <span className="action-mission-text">
                      {t("reject")}{": "}{t("inappropriateContent")}
                    </span>
                  </Button>
                ),
                key: "rejectWarranty",
                hidden: record.status !== ReviewStatus.RequestWarranty,
              },
              {
                label: (
                  <Popconfirm
                    title={<div className="text-semibold">{t("confirm?")}</div>}
                    onConfirm={() => handleApproveWarranty(record.id)}
                    okText={t("save")}
                    cancelText={t("close")}
                  >
                    <Button
                      type="primary"
                      className="w-full"
                      loading={loadingAction}
                    >
                      <span className="action-mission-text">
                        {t("warrantyAgree")}
                      </span>
                    </Button>
                  </Popconfirm>
                ),
                key: "approveWarranty",
                hidden: record.status !== ReviewStatus.RequestWarranty,
              },
              {
                label: (
                  <Popconfirm
                    title={<div className="text-semibold">{t("confirm?")}</div>}
                    onConfirm={() => handlePending(record.id)}
                    okText={t("save")}
                    cancelText={t("close")}
                  >
                    <Button
                      type="primary"
                      className="w-full"
                      loading={loadingAction}
                    >
                      <span className="action-mission-text">
                        {t("reviewAgain")}
                      </span>
                    </Button>
                  </Popconfirm>
                ),
                key: "reject",
                hidden: record.status !== ReviewStatus.Reject,
              },
              {
                label: (
                  <Popconfirm
                    title={<div className="text-semibold">{t("confirm?")}</div>}
                    onConfirm={() => handleComplete(record.id)}
                    okText={t("save")}
                    cancelText={t("close")}
                  >
                    <Button
                      loading={loadingAction}
                      type="primary"
                      className="w-full justify-center !flex !items-center gap-2 !font-medium"
                    >
                      <span className="action-mission-text">
                        {t("vaildConfirm")}
                      </span>
                    </Button>
                  </Popconfirm>
                ),
                key: "approve",
                hidden: !(
                  record.status === ReviewStatus.AdminPending ||
                  record.status === ReviewStatus.SystemPending
                ),
              },
            ];

            const visibleItems = items.filter((it) => !it.hidden);

            if (visibleItems.length === 0) {
              return null;
            }

            return (
              //@ts-ignore
              <DropdownCell
                text={t("action")}
                items={visibleItems}
                trigger={["click"]}
              >
                <a onClick={(e) => e.preventDefault()}>
                  <Space>
                    {t("action")}
                    <DownOutlined />
                  </Space>
                </a>
              </DropdownCell>
            );
          }}
        />
        }
      </AriviTable>
      <Modal
        title={<div className="text-semibold">{t("confirm?")}</div>}
        open={isRejectModalOpen}
        onOk={() => {
          if (selectedReview) {
            handleRejectWarranty(selectedReview?.id);
          }
        }}
        onCancel={() => setIsRejectModalOpen(false)}
        okText={t("save")}
        cancelText={t("close")}
        confirmLoading={loadingAction}
      >
        {/* <Input.TextArea
          rows={4}
          placeholder={t("pleaseEnterNote")}
          value={note}
          onChange={(e) => setNote(e.target.value)}
        /> */}
        <Radio.Group
          className="!flex !flex-col gap-2"
          onChange={(e) => {
            const reason = reasons.find((it) => it.name == e.target.value);
            setNote(e.target.value);
            if (reason) {
              setNoteEn(reason.nameEn);
            }
          }}
          value={note}
        >
          {reasons.map((reason) => (
            <Radio key={reason.name} value={reason.name}>
              {userStore.info?.language == "VI" ? reason.name : reason.nameEn}
            </Radio>
          ))}
        </Radio.Group>
      </Modal>
      {(pagination && pagination.defaultPageSize && pagination.total > pagination.defaultPageSize) && <Pagination {...pagination} />}

      <ProjectModal
        onSubmitOk={() => { }}
        onClose={() => { }}
        ref={modalRef}
      />
    </div >
  );
};
