import { request } from "utils/request";
import { AxiosPromise } from "axios";

export const rankApi = {
  findAll: (params?: any, lang?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/rank",
      params,
      headers: {
        lang,
      },
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/rank",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/rank/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/rank/${id}`,
      method: "delete",
    }),
};
