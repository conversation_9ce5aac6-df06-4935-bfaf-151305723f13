import { LineChart } from '@mui/x-charts/LineChart';
import { Card } from 'antd';

type LineChartData = {
  month: string;
  value: number;
};

type LineChartProps = {
  data: LineChartData[];
};

const BasicLineChart: React.FC<LineChartProps> = ({ data }) => {
  const xAxisData = data.map((item) => item.month);
  const seriesData = data.map((item) => item.value);

  return (
    <Card style={{ borderRadius: '8px', border: 'none' }}>
      <LineChart
        width={800}
        height={400}
        series={[
          {
            data: seriesData,
            color: '#1976d2',
            curve: 'linear',
          },
        ]}
        xAxis={[
          {
            scaleType: 'point',
            data: xAxisData,
          },
        ]}
        yAxis={[
          {
            min: 0,
            max: 1000,
          },
        ]}
        grid={{ vertical: true, horizontal: true }}
        margin={{ left: 60, right: 30, top: 30, bottom: 60 }}
      />
    </Card>
  );
};

export default BasicLineChart;
