import { Button, Col, Form, Input, message, Modal, Row } from "antd";
import { Rule } from "antd/lib/form";
import { useForm } from "antd/lib/form/Form";
import FormItem from "antd/lib/form/FormItem";
import { authApi } from "api/auth.api";
import { SingleImageUpload } from "components/Upload/SingleImageUpload";
import { forwardRef, useImperativeHandle, useState } from "react";
import { useTranslation } from "react-i18next";
import { ModalStatus } from "types/modal";
import { Staff } from "types/staff";
import { phoneNumberRule } from "utils/validate-rules";
const rules: Rule[] = [{ required: true }];

export interface UpdateProfileModalProps {
  onClose: () => void;
  onSubmitOk: () => void;
}

export interface UpdateProfileModal {
  handleUpdate: (staff: Partial<Staff>) => void;
  handleCreate: () => void;
}

export const UpdateProfileModal = forwardRef(
  ({ onSubmitOk }: UpdateProfileModalProps, ref) => {
    const [form] = useForm();
    const [loading, setLoading] = useState(false);
    const [visible, setVisible] = useState<boolean>();
    const [selectedStaff, setSelectedStaff] = useState<Partial<Staff>>();
    const [status, setStatus] = useState<ModalStatus>("create");
    const [isView, setIsView] = useState(false);
    const onlinePaymentType = Form.useWatch("onlinePaymentType", form);
    const { t } = useTranslation();

    useImperativeHandle(
      ref,
      () => ({
        handleUpdate,
      }),
      []
    );

    const handleUpdate = (staff: Partial<Staff>) => {
      console.log(staff);
      form.setFieldsValue({
        ...staff,
      });
      setSelectedStaff(staff);
      setStatus("update");
      setVisible(true);
    };

    const handleSubmitForm = async () => {
      await form.validateFields();
      const dataForm = form.getFieldsValue();
      console.log(dataForm);
      console.log(onlinePaymentType);
      const payload = {
        staff: dataForm,
      };

      try {
        setLoading(true);
        switch (status) {
          case "update":
            await authApi.updateProfile(payload);
            message.success("actionSuccessfully");
            break;
        }
        onSubmitOk();
      } finally {
        setLoading(false);
        setVisible(false);
        onSubmitOk();
      }
    };

    // const onChange = (e: RadioChangeEvent) => {
    //   setOnlinePaymentType(e.target.value);
    // };

    return (
      <Modal
        onCancel={() => {
          setVisible(false);
        }}
        visible={visible}
        centered
        title={
          <h1 className="mb-0 text-lg text-primary font-bold">
            {isView ? (
              "Chi tiết đơn vị thanh toán"
            ) : (
              <> {status == "create" ? "Thêm" : t("update")}</>
            )}
          </h1>
        }
        confirmLoading={loading}
        width={700}
        destroyOnClose
        afterClose={() => {
          form.resetFields();
        }}
        maskClosable={false}
        okText={t("save")}
        footer={
          <div className={isView ? "none" : ""}>
            <Button onClick={() => setVisible(false)}>{t("cancel")}</Button>
            <Button
              type="primary"
              onClick={() => handleSubmitForm()}
              className="ml-2"
            >
              {t("save")}
            </Button>
          </div>
        }
      >
        <Form
          disabled={isView}
          form={form}
          layout="vertical"
          initialValues={{ isEnabled: true }}
          validateTrigger={["onBlur", "onChange"]}
        >
          <Row gutter={16}>
            {/* <Col span={12}>
              <Form.Item shouldUpdate={true} style={{ marginBottom: 0 }}>
                {() => {
                  return (
                    <Form.Item
                      rules={rules}
                      style={{ marginBottom: 0 }}
                      label={<div>Ảnh đại diện</div>}
                      name="avatar"
                    >
                      <SingleImageUpload
                        onUploadOk={(path: string) => {
                          form.setFieldsValue({
                            avatar: path,
                          });
                        }}
                        imageUrl={form.getFieldValue("avatar")}
                      />
                    </Form.Item>
                  );
                }}
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item shouldUpdate={true} style={{ marginBottom: 0 }}>
                {() => {
                  return (
                    <Form.Item
                      style={{ marginBottom: 0 }}
                      label={<div>Ảnh profile</div>}
                      name="profileImage"
                    >
                      <SingleImageUpload
                        recommendSize={{ width: 764, height: 1080 }}
                        onUploadOk={(path: string) => {
                          form.setFieldsValue({
                            profileImage: path,
                          });
                        }}
                        imageUrl={form.getFieldValue("profileImage")}
                      />
                    </Form.Item>
                  );
                }}
              </Form.Item>
            </Col> */}
            <Col span={24}>
              <FormItem
                rules={rules}
                required
                label={t("fullName")}
                name={"fullName"}
              >
                <Input />
              </FormItem>
            </Col>

            <Col span={24}>
              <FormItem
                rules={[
                  { required: true },
                  {
                    pattern: /(84|0[3|5|7|8|9])+([0-9]{8})\b/g,
                    message: t("wrongFormatPhone"),
                  },
                  { max: 50 },
                ]}
                required
                label={t("phoneNumber")}
                name={"phone"}
              >
                <Input />
              </FormItem>
            </Col>
            <Col span={24}>
              <FormItem rules={rules} required label="Email" name={"email"}>
                <Input type="email" />
              </FormItem>
            </Col>
          </Row>
        </Form>
      </Modal>
    );
  }
);
