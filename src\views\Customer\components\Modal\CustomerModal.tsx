import { CloseOutlined, LockOutlined, UnlockOutlined } from "@ant-design/icons";
import {
  Button,
  Col,
  Form,
  Input,
  Modal,
  Popconfirm,
  Row,
  Space,
  Spin,
  Tabs,
  Typography,
  message,
} from "antd";
import { TablePaginationConfig } from "antd/es/table";
import { Rule } from "antd/lib/form";
import { useForm } from "antd/lib/form/Form";
import TextArea from "antd/lib/input/TextArea";
import {
  FilterValue,
  SorterResult,
  TableCurrentDataSource,
} from "antd/lib/table/interface";
import { customerApi } from "api/customer.api";
import { InputNumber } from "components/Input/InputNumber";
import { SingleImageUpload } from "components/Upload/SingleImageUpload";
import dayjs from "dayjs";
import { useHandleCustomer } from "hooks/useHandleCustomer";
import { isEqual } from "lodash";
import React, { useImperativeHandle, useState } from "react";
import { useTranslation } from "react-i18next";
import { IoShieldCheckmarkOutline } from "react-icons/io5";
import { PermissionNames } from "router";
import { permissionStore } from "store/permissionStore";
import { Customer, CustomerStatus } from "types/customer";
import { ModalStatus } from "types/modal";
import { OrderStatus } from "types/order";
import { QueryObject } from "types/query";
import { StockOrder } from "types/stockOrder";
import { checkRole } from "utils/auth";
import OrderHistoryTab from "../Tab/OrderHistoryTab";
import CustomerTransactionTab from "../Tab/CustomerTransactionTab";
import AntFormErrorLabel from "components/Common/AntFormErrorLabel";
import {requiredWithoutSpaceRule} from "utils/validate";
import { requiredRule } from "utils/validate-rules";

const rules: Rule[] = [{ required: true }, requiredWithoutSpaceRule];

enum ActiveKey {
  Info = "INFO",
  Order = "ORDER",
  Transaction = "Transaction",
}

export interface Props {
  onClose?: () => void;
  onSubmit: () => void;

  // hasApprovePermission: boolean;
  // hasUpdatePermission: boolean;
}

export interface CustomerModal {
  handleUpdate: (record: Customer) => void;
  handleCreate: () => void;
  handleClose: () => void;
}

export const CustomerModal = React.memo(
  React.forwardRef(({ onClose, onSubmit }: Props, ref) => {
    const { t } = useTranslation();

    const [visible, setVisible] = useState(false);
    const [customerSelected, setCustomerSelected] = useState<Partial<Customer>>(
      {}
    );
    const [activeKey, setActiveKey] = useState<ActiveKey | string>("INFO");
    const [loading, setLoading] = useState(false);
    const [isApproveOrRejectLoading, setIsApproveOrRejectLoading] =
      useState(false);
    const [isLoadingBlockAccount, setIsLoadingBlockAccount] = useState(false);
    const [loadingRemoveCustomerRank, setLoadingRemoveCustomerRank] =
      useState(false);
    const [status, setStatus] = useState<ModalStatus>("create");
    const [form] = useForm<
      Omit<Customer, "dob"> & {
        dob: dayjs.Dayjs;
        refEmployeeName: string;
        companyId: number;
      }
    >();

    const [reason, setReason] = useState<string>("");
    const [reasonOrder, setReasonOrder] = useState<string>("");

    //Order tab
    const [tabActive, setTabActive] = useState<OrderStatus>(OrderStatus.All);
    const [summaryOrderOfStatus, setSummaryOrderOfStatus] = useState();

    const { isFindOneCustomerLoading, fetchOneCustomer } = useHandleCustomer({
      initQuery: {
        limit: 50,
        page: 1,
      },
    });

    const customerBlockPermission = checkRole(
      PermissionNames.customerBlock,
      permissionStore.permissions
    );

    const customerChangeCustomerRankPermission = checkRole(
      PermissionNames.changeCustomerRank,
      permissionStore.permissions
    );

    const customerChangeRefEmployeePermission = checkRole(
      PermissionNames.changeRefEmployee,
      permissionStore.permissions
    );

    const customerApprovePermission = checkRole(
      PermissionNames.customerApprove,
      permissionStore.permissions
    );

    const customerUpdatePermission = checkRole(
      PermissionNames.customerEdit,
      permissionStore.permissions
    );

    const handleTableChange = (
      pagination: TablePaginationConfig,
      filters: Record<string, FilterValue | null>,
      sorter: SorterResult<StockOrder>,
      extra: TableCurrentDataSource<StockOrder>
    ) => {
      let queryObject: QueryObject[] = [];

      const addSorterToQuery = (sort: SorterResult<StockOrder>) => {
        queryObject.push({
          type: "sort",
          field: `${sort.columnKey}`,
          value: sort.order === "ascend" ? "ASC" : "DESC",
        });
      };

      if (Array.isArray(sorter)) {
        sorter.forEach(addSorterToQuery);
      } else {
        addSorterToQuery(sorter);
      }
    };

    useImperativeHandle(
      ref,
      () => ({
        handleUpdate,
        handleCreate,
        handleClose,
      }),
      []
    );

    const getFormData = () => {
      const { id, dob, companyId, ...customer } = form.getFieldsValue();
      return {
        id,
        customer: {
          ...customer,
          dob: dob ? dob.year() : "",
        },
        companyId,
      };
    };

    const handleUpdate = async (customer: Customer) => {
      form.setFieldsValue({
        ...customer,
        // dob: customer?.dob ? dayjs(customer.dob, "YYYY") : "",
        // refEmployeeName: `${customer.refEmployee?.name} (${customer.refEmployee?.refCode})`,
      });
      setCustomerSelected(customer);
      // await handleInit(id);
      setStatus("update");
      setVisible(true);
    };

    const handleCreate = () => {
      form.resetFields();
      setStatus("create");
      setVisible(true);
    };

    const handleClose = () => {
      setVisible(false);
      onClose?.();
      setActiveKey("INFO");
      setCustomerSelected({});
    };

    // const handleInit = async (id: number) => {
    //   const customer = await fetchOneCustomer(id);
    //   form.resetFields();
    //   form.setFieldsValue({
    //     ...customer,
    //     refEmployeeName: customer.refEmployee
    //       ? `${customer.refEmployee?.name} (${customer.refEmployee?.refCode})`
    //       : "",
    //     companyId: customer?.company?.id,
    //   });
    //   setCustomerSelected(customer);
    // };

    // console.log(customerSelected);
    const handleSubmit = async () => {
      await form.validateFields();
      setLoading(true);
      try {
        const { id, ...formData } = getFormData();
        if (status == "create") {
          await customerApi.create(formData);
          onSubmit?.();
        } else {
          await customerApi.update(id, formData);
        }
        message.success(t("actionSuccessfully"));
        await onSubmit?.();
        handleClose();
      } catch (error) {
      } finally {
        setLoading(false);
      }
    };

    const handleApproveCustomer = async (customerId: number) => {
      try {
        setIsApproveOrRejectLoading(true);
        const res = await customerApi.approve(customerId);
        message.success(t("actionSuccessfully"));
        await onSubmit?.();

        handleClose();
      } finally {
        setIsApproveOrRejectLoading(false);
      }
    };

    // const handleRemoveCustomerRank = async (
    //   customerId: number,
    //   customerProductId: number,
    //   productId: number
    // ) => {
    //   try {
    //     setLoadingRemoveCustomerRank(true);
    //     const res = await customerApi.removeRank(customerId, {
    //       customerProductId,
    //       productId,
    //     });
    //     await handleInit(customerSelected?.id!);
    //     onSubmit?.();
    //     message.success("Xóa cấp độ thành công!");
    //   } finally {
    //     setLoadingRemoveCustomerRank(false);
    //   }
    // };

    const handleRejectCustomer = async (customerId: number, reason: string) => {
      try {
        setIsApproveOrRejectLoading(true);
        const res = await customerApi.reject(customerId, { reason });
        message.success(t("actionSuccessfully"));
        await onSubmit?.();
        handleClose();
      } finally {
        setIsApproveOrRejectLoading(false);
      }
    };

    const handleBlockAccount = async (
      customerId: number,
      isBlocked: boolean
    ) => {
      try {
        setIsLoadingBlockAccount(true);
        const res = await customerApi.block(customerId, {
          isBlocked,
        });
        await onSubmit();
        handleClose();
        message.success(t("actionSuccessfully"));
      } catch (error) {
      } finally {
        setIsLoadingBlockAccount(false);
      }
    };

    return (
      <Modal
        centered
        onCancel={handleClose}
        open={visible}
        title={
          <h1 className="text-lg font-bold mb-0 text-primary">
            {status === "create" ? (
              <>{t("create")}</>
            ) : (
              <>
                {t("update")}
                <span className=""> - {customerSelected?.name}</span>
              </>
            )}
          </h1>
        }
        afterClose={() => {
          form.resetFields();
        }}
        style={{ top: 10 }}
        width={activeKey === ActiveKey.Order ? 1600 : 1200}
        confirmLoading={loading}
        destroyOnClose
        onOk={handleSubmit}
        okText={status === "create" ? t("save") : t("update")}
        // okButtonProps={{
        //   style: {
        //     display: activeKey !== ActiveKey.Info ? "none" : "",
        //   },
        // }}
        maskClosable={false}
        footer={
          <>
            <Button onClick={() => setVisible(false)}>{t("close")}</Button>
            {activeKey == ActiveKey.Info ? (
              <Button
                // disabled={selectedSurvey && selectedSurvey?.totalSubmit > 0}
                type="primary"
                onClick={() => handleSubmit()}
                loading={loading}
                // hidden={type === "review"}
              >
                {t("save")}
              </Button>
            ) : null}
          </>
        }
      >
        <Tabs
          defaultActiveKey={activeKey}
          type="line"
          onChange={(activeKey) => setActiveKey(activeKey)}
        >
          <Tabs.TabPane tab={t("basicInformation")} key={ActiveKey.Info}>
            {activeKey == ActiveKey.Info && (
              <>
                <Spin
                  spinning={
                    isFindOneCustomerLoading || loadingRemoveCustomerRank
                  }
                >
                  <Form
                    layout="vertical"
                    form={form}
                    validateTrigger={["onBlur", "onChange"]}
                  >
                    <Form.Item hidden label="id" name="id">
                      <Input />
                    </Form.Item>
                    <Row gutter={24}>
                      <Col
                        span={14}
                        style={{ borderRight: "1px solid #efefef" }}
                      >
                        <Row gutter={16}>
                          <>
                            <Col span={24}>
                              <Form.Item
                                label={t("fullName")}
                                name="name"
                                rules={rules}
                              >
                                <Input placeholder={t("enterFullName")} />
                              </Form.Item>
                            </Col>
                          </>

                          <Col span={12}>
                            <Form.Item
                              label={t("phoneNumber")}
                              name="phone"
                              rules={[
                                { required: true },
                                {
                                  pattern: /(84|0[3|5|7|8|9])+([0-9]{8})\b/g,
                                  message: t("wrongFormatPhone"),
                                },
                                { max: 50 },
                              ]}
                            >
                              <Input placeholder={t("enterPhoneNumber")} />
                            </Form.Item>
                          </Col>

                          <Col span={12}>
                            <Form.Item
                              label={t("email")}
                              name="email"
                              required  
                              rules={[
                                {
                                  required: true,
                                  type: "email",
                                  message: "Vui lòng nhập email hợp lệ!",
                                },
                              ]}
                            >
                              <Input />
                            </Form.Item>
                          </Col>

                          <Col span={24}>
                            <Form.Item
                              label={t("companyName")}
                              name="companyName"
                              rules={rules}
                            >
                              <Input />
                            </Form.Item>
                          </Col>

                          <Col span={12}>
                            <Form.Item
                              label={t("taxCode")}
                              name="taxCode"
                              // required
                              // rules={[requiredRule]}
                              rules={rules}
                            >
                              <Input />
                            </Form.Item>
                          </Col>
                          <Col span={12}>
                            <Form.Item
                              label={t("companyEmail")}
                              name="companyEmail"
                              required
                              rules={[
                                {
                                  required: true,
                                  type: "email",
                                  message: "Vui lòng nhập email hợp lệ!",
                                },
                              ]}
                            >
                              <Input />
                            </Form.Item>
                          </Col>
                          <Col span={24}>
                            <Form.Item
                              label={t("companyAddress")}
                              name="companyAddress"
                              className="mb-4 md:mb-6"
                              // required
                              // rules={[requiredRule]}
                              rules={rules}
                            >
                              <Input />
                            </Form.Item>
                          </Col>

                          {/* <Col span={12}>
                            <Form.Item
                              label={t("password")}
                              name="password"
                              rules={[{ required: true }]} >
                              <Input.Password />
                            </Form.Item>
                          </Col> */}
                          {/* <Col span={12}>
                        <Form.Item label="Năm sinh" name="dob">
                          <DatePicker.YearPicker
                            className="w-full"
                            placeholder="Chọn năm sinh"
                          />
                        </Form.Item>
                      </Col> */}

                          {/* <Col span={12}>
                        <Form.Item
                          initialValue={Gender.Male}
                          label="Giới tính"
                          name="gender"
                        >
                          <Select
                            placeholder="Chọn giới tính"
                            style={{ width: "100%" }}
                          >
                            <Select.Option value={Gender.Male}>
                              Nam
                            </Select.Option>
                            <Select.Option value={Gender.FeMale}>
                              Nữ
                            </Select.Option>
                          </Select>
                        </Form.Item>
                      </Col> */}
                          {/* <Col span={12}>
                        <Form.Item
                          label="Cấp độ thành viên thành viên"
                        >
                          <Input
                            placeholder="Nhập email"
                            value={form.getFieldValue("customerRank.name")}
                          />
                        </Form.Item>
                      </Col>
                      <Col span={12}>
                        <Form.Item
                          label="Công ty chứng khoán"
                          name="refCompanyName"
                        >
                          <Input placeholder="Nhập tên công ty" />
                        </Form.Item>
                      </Col>
                      <Col span={12}>
                        <Form.Item
                          label="Số tài khoản chứng khoán"
                          name="bankAccountNumber"
                        >
                          <Input placeholder="Nhập số tài khoản chứng khoán" />
                        </Form.Item>
                      </Col>
                      <Col span={12}>
                        <Form.Item label="NAV" name="nav">
                          <Input placeholder="Nhập NAV" />
                        </Form.Item>
                      </Col> */}
                        </Row>
                      </Col>
                      <Col span={10}>
                        <Form.Item shouldUpdate={true}>
                          {() => {
                            return (
                              <Form.Item label={t("avatar")} name="avatar">
                                <SingleImageUpload
                                  onUploadOk={(path: string) => {
                                    form.setFieldsValue({
                                      avatar: path,
                                    });
                                  }}
                                  imageUrl={form.getFieldValue("avatar")}
                                />
                              </Form.Item>
                            );
                          }}
                        </Form.Item>
                      </Col>
                    </Row>{" "}
                  </Form>
                </Spin>
              </>
            )}
          </Tabs.TabPane>
          {status === "update" && (
            <>
              {customerSelected?.id && (
                <Tabs.TabPane tab={t("orderHistory")} key={ActiveKey.Order}>
                  <OrderHistoryTab customerId={customerSelected?.id} />
                </Tabs.TabPane>
              )}
              {customerSelected?.id && (
                <Tabs.TabPane
                  tab={t("customerTransaction")}
                  key={ActiveKey.Transaction}
                >
                  <CustomerTransactionTab customerId={customerSelected?.id} />
                </Tabs.TabPane>
              )}
            </>
          )}
        </Tabs>
      </Modal>
    );
  }),
  (prev, next) => isEqual(prev, next)
);
