import {
  CloseOutlined,
  DownOutlined,
  KeyOutlined,
  LockOutlined,
  UnlockOutlined,
} from "@ant-design/icons";
import {
  Avatar,
  Button,
  Descriptions,
  Image,
  Popconfirm,
  Space,
  Table,
  Tag,
  message,
} from "antd";
import { customerApi } from "api/customer.api";
import { IPagination, Pagination } from "components/Pagination";
import DropdownCell from "components/Table/DropdownCell";
import { useRef, useState } from "react";
import { CiEdit } from "react-icons/ci";
import { IoShieldCheckmarkOutline, IoTrashOutline } from "react-icons/io5";
import { Customer, CustomerStatus, CustomerStatusTrans } from "types/customer";
import { unixToDate, unixToFullDate } from "utils/dateFormat";
import { CustomerModal } from "../Modal/CustomerModal";
import dayjs from "dayjs";
import { checkRole } from "utils/auth";
import { PermissionNames } from "router";
import { permissionStore } from "store/permissionStore";
import TextArea from "antd/lib/input/TextArea";
import { formatVND } from "utils";
import { AriviTable } from "components/Table/AriviTable";
import { GrEdit } from "react-icons/gr";
import { HiOutlineTrash } from "react-icons/hi2";
import { useTranslation } from "react-i18next";
const { ColumnGroup, Column } = Table;

export const handleCountRemainDays = (expiredDateUnix: number) => {
  // Parse the future date
  const futureDate = dayjs.unix(expiredDateUnix);

  // Get the current date
  const currentDate = dayjs();

  // Calculate the difference in days
  const remainingDays = futureDate.diff(currentDate, "day");
  return remainingDays;
};

interface PropsType {
  dataSource: Customer[];
  loading: boolean;
  isGetFromStockOrder?: boolean;
  isFollow?: boolean;
  isFavorite?: boolean;
  isClosed?: boolean;
  loadingDelete?: boolean;
  onReset?: (customer: Customer) => void;

  onRefreshData: () => void;
  onActive?: (customerId: number) => void;
  onInactive?: (customerId: number) => void;
  onReject?: (customerId: number, reason: string) => void;
  onDelete?: (customerId: number) => void;
  onEdit: (record: Customer) => void;
  // customerModal: React.MutableRefObject<CustomerModal | undefined>;
  showActionColumn?: boolean;
  showStatusColumn?: boolean;
  rowSelection?: any;
  handleTableChange: (
    pagination: any,
    filters: any,
    sorter: any,
    extra: any
  ) => void;
}

export const CustomerTable = ({
  dataSource,
  loading,
  onEdit,
  onActive,
  onInactive,
  onReject,
  onRefreshData,
  isGetFromStockOrder = false,
  isFollow = false,
  isFavorite = false,
  // customerModal,
  isClosed = false,
  showActionColumn = true,
  showStatusColumn = true,
  rowSelection,
  handleTableChange,
  onDelete,
  loadingDelete,
  onReset,
}: PropsType) => {
  const { t } = useTranslation();

  const [selectedCustomer, setSelectedCustomer] = useState<Customer>();
  const [visibleResetPass, setVisibleResetPass] = useState(false);
  const [reason, setReason] = useState<string>("");
  const customerBlockPermission = checkRole(
    PermissionNames.customerBlock,
    permissionStore.permissions
  );
  const customerResetPwPermission = checkRole(
    PermissionNames.customerResetPw,
    permissionStore.permissions
  );
  const customerApprovePermission = checkRole(
    PermissionNames.customerApprove,
    permissionStore.permissions
  );
  const customerDeletePermission = checkRole(
    PermissionNames.customerDelete,
    permissionStore.permissions
  );

  const handleRenderRemainDayContent = (remainingDays: number) => {
    if (remainingDays <= 0)
      return <span className="font-medium text-red-500">Quá hạn</span>;
    return <span className="font-medium ">({remainingDays} ngày)</span>;
  };

  return (
    <div>
      <AriviTable
        rowSelection={rowSelection ? rowSelection : false}
        loading={loading}
        onChange={handleTableChange}
        pagination={false}
        rowKey="id"
        dataSource={dataSource}
        scroll={{ x: "max-content", y: "calc(100vh - 310px)" }}
        size="small"
      >
        <Column
          align="center"
          width={100}
          title={t("code")}
          dataIndex="code"
          key="customer.code"
          render={(text, record: Customer) => {
            return (
              <span
                onClick={() => {
                  onEdit(record);
                }}
                className="font-semibold cursor-pointer text-primary hover:underline"
              >
                {text || "--"}
              </span>
            );
          }}
        />
        <Column
          width={200}
          title={t("customerName")}
          dataIndex="name"
          key="customer.name"
          render={(text, record: Customer) => {
            return (
              <div className="flex items-center gap-2">
                <Avatar
                  src={
                    record.avatar ||
                    "https://i.pinimg.com/736x/0d/64/98/0d64989794b1a4c9d89bff571d3d5842.jpg"
                  }
                  className="!h-[30px] !w-[30px] object-contain"
                />
                <span
                  onClick={() => {
                    onEdit(record);
                  }}
                  className="font-semibold cursor-pointer text-primary hover:underline"
                >
                  {text || "--"}
                </span>
              </div>
            );
          }}
        />

        <Column
          align="center"
          width={120}
          title={t("phoneNumber")}
          dataIndex="phone"
          key="customer.phone"
        />

        {/* <Column

          width={150}
          title="Số TK CK"
          dataIndex="bankAccountNumber"
          key="customer.bankAccountNumber"
        /> */}
        <Column
          width={120}
          title="Email"
          dataIndex="email"
          key="customer.email"
        />
        <Column
          width={270}
          align="center"
          title={t("projectCreated")}
          dataIndex="projectCreated"
          key="customer.totalProduct"
          render={(text, record: Customer) => {
            return formatVND(record.totalProduct || 0);
          }}
        />
        <Column
          width={270}
          align="center"
          title={t("paidProject")}
          dataIndex="paidProject"
          key="customer.totalProjectPaymentComplete"
          render={(text, record: any) => {
            return formatVND(record.totalProjectPaymentComplete || 0);
          }}
        />
        <Column
          width={120}
          align="center"
          sorter={(a: Customer, b: Customer) =>
            (a.balance || 0) - (b.balance || 0)
          }
          title={t("balance")}
          dataIndex="balance"
          key="customer.balance"
          render={(text, record: Customer) => {
            return formatVND(text || 0);
          }}
        />
        <Column
          width={120}
          align="center"
          title={t("status")}
          dataIndex="balance"
          key="customer.balance"
          render={(text, record: Customer) => {
            return (
              <Tag color={CustomerStatusTrans[record.status]?.color}>
                {t(record.status)}
              </Tag>
            );
          }}
        />
        <Column
          width={150}
          title={t("createdAt")}
          dataIndex="createdAt"
          key="customer.createdAt"
          render={(text, record: Customer) => {
            return unixToFullDate(record.createdAt);
          }}
        />
        <Column
          fixed="right"
          width={120}
          align="center"
          title=""
          key="action"
          dataIndex={""}
          render={(text, record: Customer) => (
            //@ts-ignore
            <DropdownCell
              text={t("action")}
              items={[
                {
                  onClick: () => "",
                  // createOrderModalRef.current?.handleUpdate(record),
                  label: (
                    <Button
                      icon={<GrEdit />}
                      type="primary"
                      className="w-full justify-center !flex !items-center gap-2 !font-medium"
                      onClick={() => onEdit?.(record)}
                    >
                      {t("detail")}
                    </Button>
                  ),
                  key: "update",
                  // hidden: !hasUpdatePartnerPermission,
                },
                {
                  onClick: () => "",
                  // createOrderModalRef.current?.handleUpdate(record),
                  label: (
                    <Button
                      className="w-full"
                      //   block
                      //   ghost
                      onClick={() => onReset?.(record)}
                      icon={<KeyOutlined />}
                    >
                      {t("resetPass")}
                    </Button>
                  ),
                  key: "reset",
                },
                {
                  onClick: () => "",
                  // createOrderModalRef.current?.handleUpdate(record),
                  label: (
                    <Popconfirm
                      title={t("confirm?")}
                      onConfirm={() => {
                        if (record.status === CustomerStatus.Active) {
                          onInactive?.(record.id);
                        } else {
                          onActive?.(record.id);
                        }
                      }}
                      okText={t("yes")}
                      cancelText={t("no")}
                    >
                      <Button
                        icon={
                          record.status === CustomerStatus.Active ? (
                            <LockOutlined />
                          ) : (
                            <UnlockOutlined />
                          )
                        }
                        // type="primary"
                        className={`text-white w-full justify-center !flex !items-center gap-2 !font-medium ${record.status === CustomerStatus.Active
                          ? "bg-red-500"
                          : "bg-green-500"
                          }`}
                      >
                        {record.status === CustomerStatus.Active
                          ? t("lock")
                          : t("unlock")}
                      </Button>
                    </Popconfirm>
                  ),
                  key: "updateStatus",
                  // hidden: !hasUpdatePartnerPermission,
                },
                // {
                //   label: (
                //     <Popconfirm
                //       placement="topLeft"
                //       title={
                //         <div>
                //           <h1 className="text-sm">{t("confirm?")}</h1>
                //         </div>
                //       }
                //       onConfirm={() => onDelete?.(record.id)}
                //       okText={t("yes")}
                //       cancelText={t("no")}
                //     >
                //       <Button
                //         loading={loadingDelete}
                //         icon={<HiOutlineTrash className="text-lg" />}
                //         className={`w-full justify-center !flex !items-center gap-2 !text-red-500 !font-medium`}
                //       >
                //         {t("delete")}
                //       </Button>
                //     </Popconfirm>
                //   ),
                //   key: "delete",
                //   // hidden: !hasDeletePartnerPermission,
                // },
              ]}
              trigger={["click"]}
            >
              <a onClick={(e) => e.preventDefault()}>
                <Space>
                  {t("action")} <DownOutlined />
                </Space>
              </a>
            </DropdownCell>
          )}
        />
      </AriviTable>

      {/* {visibleResetPass && (
        <ResetPasswordModal
          customerId={selectedCustomer?.id || 0}
          visible={visibleResetPass}
          onClose={() => setVisibleResetPass(false)}
        />
      )} */}

      {/* <CustomerModal onSubmit={onRefreshData} ref={customerModalRef} /> */}
    </div>
  );
};
