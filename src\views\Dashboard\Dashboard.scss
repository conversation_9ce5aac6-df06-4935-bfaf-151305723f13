.chart-container {
  background: white;
  border-radius: 5px;
  padding: 16px 24px;

  .chart-label {
    font-size: 18px;
    display: block;
    text-align: center;
    margin-top: 12px;
  }

  .chart-filter {
    margin-bottom: 12px;
    display: flex;
    justify-content: space-between;
  }
}

.card-panel {
  // height: 108px;
  margin-bottom: 10px;
  cursor: pointer;
  font-size: 12px;
  position: relative;
  overflow: hidden;
  color: #666;
  border-radius: 4px;

  &:hover {
    .card-panel-icon-wrapper {
      color: #fff;
    }

    .icon-people {
      background: #40c9c6;
    }

    .icon-message {
      background: #36a3f7;
    }

    .icon-money {
      background: #f4516c;
    }

    .icon-shopping {
      background: #34bfa3;
    }
  }

  .icon-people {
    color: #40c9c6;
  }

  .icon-message {
    color: #36a3f7;
  }

  .icon-money {
    color: #f4516c;
  }

  .icon-shopping {
    color: #34bfa3;
  }

  .card-panel-icon-wrapper {
    float: left;
    margin: 14px 0 0 14px;
    padding: 16px;
    transition: all 0.38s ease-out;
    border-radius: 6px;
  }

  .card-panel-icon {
    float: left;
    font-size: 48px;
  }

  .card-panel-description {
    font-weight: bold;
    // margin: 26px;

    .card-panel-text {
      line-height: 18px;
      color: rgba(0, 0, 0, 0.45);
      font-size: 16px;
      // margin-bottom: 10px;
    }

    .card-panel-num {
      display: block;
      height: auto;
      font-size: 27px;
      line-height: 1;
      margin-left: 5px;
      padding: 0;
    }
    .card-panel-card {
      color: white;
      .ant-card-body {
        padding: 10px;
        min-height: 100px;
        display: flex;
        .ant-space-vertical {
          justify-content: space-between;
        }
      }
    }
  }
}