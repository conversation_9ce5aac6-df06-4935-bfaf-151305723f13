import i18next from "i18next";
import { initReactI18next } from "react-i18next";
import translationEN from "../translations/en.json";
import translationVI from "../translations/vi.json";
// import translationDE from "../translations/de.json";
// import translationFR from "../translations/fr.json";
// import translationNL from "../translations/nl.json";
import { userStore } from "store/userStore";
import unitedKingdomIcon from "../assets/images/united-kingdom.svg";
import vietnamIcon from "../assets/images/vietnam.svg";
import germanyIcon from "../assets/images/germanyFlagIcon.png";
import franceIcon from "../assets/images/franceFlagIcon.png";
import netherlandsIcon from "../assets/images/netherlandsFlagIcon.png";

const i18nInstance = i18next.createInstance();
const resources = {
  en: { translation: translationEN },
  vi: { translation: translationVI },
  // de: { translation: translationDE },
  // fr: { translation: translationFR },
  // nl: { translation: translationNL },
};

export const langResources = {
  en: { text: "ENG", text2: "English", icon: unitedKingdomIcon, value: "en" },
  vi: { text: "VIE", text2: "Tiếng việt", icon: vietnamIcon, value: "vi" },
  // de: { text: "German", icon: germanyIcon, value: "de" },
  // fr: { text: "French", icon: franceIcon, value: "fr" },
  // nl: { text: "Nederlands", icon: netherlandsIcon, value: "nl" },
};

i18nInstance.use(initReactI18next).init({
  lng: !!userStore.token ? userStore.info?.language?.toLowerCase() : "vi",
  debug: true,
  resources,
});

export default i18nInstance;
