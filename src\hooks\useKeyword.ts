import { keywordApi } from "api/keyword.api";
import { useState } from "react";
import { Keyword } from "types/keyword";
import { QueryParam } from "types/query";

export interface KeywordQuery extends QueryParam {}

interface UseKeywordProps {
  initQuery: KeywordQuery;
}

export const useKeyword = ({ initQuery }: UseKeywordProps) => {
  const [data, setData] = useState<Keyword[]>([]);
  const [total, setTotal] = useState(0);
  const [query, setQuery] = useState<KeywordQuery>(initQuery);
  const [loading, setLoading] = useState(false);

  const fetchData = async () => {
    setLoading(true);
    try {
      const { data } = await keywordApi.findAll(query);

      setData(data.keywords);
      setTotal(data.total);
      return data.keywords;
    } finally {
      setLoading(false);
    }
  };

  return {
    keywords: data,
    totalKeyword: total,
    fetchKeyword: fetchData,
    loadingKeyword: loading,
    setQueryKeyword: setQuery,
    queryKeyword: query,
  };
};
