import React, { useEffect, useState } from "react";
import { <PERSON>ack, Avatar, Box, Typography } from "@mui/material";
import { useTranslation } from "react-i18next";
import { observer } from "mobx-react";
import { unixToFullDate } from "utils/dateFormat";

import FileUploadItem, {
  FileCustomProps,
} from "components/Upload/FileUploadItem";

import { SupportTicket, SupportTicketCreatedBy } from "types/supportTicket";

import adminImg from "assets/svgs/admin-avg.svg";
import { settings } from "settings";

import "./styles/SupportTicketReply.scss";
import { Divider, Modal, Space } from "antd";
import { partnerApi } from "api/partner.api";

interface Props {
  supportTickets: SupportTicket[];
  onReply?: () => void;
}

const SupportTicketReply = ({ supportTickets }: Props) => {
  const { t } = useTranslation();

  const [modalInfoUser, setModalInfoUser] = useState(false);
  const [infoUser, setInfoUser] = useState<any>();
  const [isCustomer, setIsCustomer] = useState(false);

  const getReplierInfo = (ticket: SupportTicket) => {
    const { createdBy, staff, customer, partner } = ticket;

    switch (createdBy) {
      case SupportTicketCreatedBy.Admin:
        return {
          avatar: staff?.avatar ?? adminImg,
          name: "ADMIN RIVI",
          isAdmin: true,
        };

      case SupportTicketCreatedBy.Customer:
        return {
          avatar: customer?.avatar,
          name: customer?.name,
          isAdmin: false,
        };

      case SupportTicketCreatedBy.Partner:
        return {
          avatar: partner?.avatar,
          name: partner?.fullName,
          isAdmin: false,
        };

      default:
        return {
          avatar: settings.defaultAvatar,
          name: "",
          isAdmin: false,
        };
    }
  };

  useEffect(() => {
    if (supportTickets) {
      const supportTicket = supportTickets[0];
      if (supportTicket) {
        console.log("supportTicket", supportTicket)
        setInfoUser(supportTicket.customer ?? supportTicket.partner);
        setIsCustomer(!!supportTicket.customer)
      }
    }
  }, [supportTickets])

  return (
    <Stack spacing={2} className="support-ticket-reply">
      {supportTickets.map((ticket) => {
        const { avatar, name, isAdmin } = getReplierInfo(ticket);

        return (
          <Stack
            key={ticket.id}
            direction="row"
            justifyContent={isAdmin ? "flex-end" : "flex-start"} // admin sang phải
            alignItems="flex-start"
            spacing={1}
            className="hover:cursor-pointer"
          >
            {!isAdmin && (
              <Space onClick={() => {
                setModalInfoUser(true);
              }}>
                <div className="avatar-content">
                  <Avatar src={avatar} alt={name} />
                </div>
                <Typography
                  variant="h6"
                  fontWeight="bold"
                  color="primary"
                  className="name-content"
                >
                  {name}
                </Typography>
              </Space>
            )}

            <Box className={`reply-box ${isAdmin ? "admin" : "user"}`}>
              <Typography variant="body1" className="reply-content">
                <div dangerouslySetInnerHTML={{ __html: ticket.content }} />
              </Typography>

              {ticket.fileAttaches?.length > 0 && (
                <>
                  <div className="file-label mb-1">{t("fileAttachLabel")}</div>
                  <div className="file-list">
                    {ticket.fileAttaches.map((file: any) => {
                      const refinedFile: FileCustomProps = {
                        id: String(file.id),
                        fileType: file.mimeType || file.type,
                        fileName: file.name,
                        fileSize: file.size,
                        fileUrl: file.url,
                      };
                      return (
                        <FileUploadItem
                          key={file.id}
                          file={refinedFile}
                          showName
                        />
                      );
                    })}
                  </div>
                </>
              )}
              <div className="reply-time">
                <Typography variant="caption">
                  {unixToFullDate(ticket.createdAt)}
                </Typography>
              </div>
            </Box>

            {isAdmin && (
              <Space>
                <Typography
                  variant="h6"
                  fontWeight="bold"
                  color="primary"
                  className="name-content"
                >
                  {name}
                </Typography>
                <div className="avatar-content-ad">
                  <Avatar src={avatar} alt={name} />
                </div>
              </Space>
            )}
          </Stack>
        );
      })}

      <Modal
        onCancel={() => {
          setModalInfoUser(false);
        }}
        visible={modalInfoUser}
        centered
        title={
          <h1 className="mb-0 text-lg text-primary font-bold">
            {t("info")} {isCustomer ? t("CUSTOMER") : t("PARTNER")}
          </h1>
        }
        destroyOnClose
        width={800}
      >

        <div>
          <Typography variant="body1">
            <b>{t("fullName")}:</b>{" "}
            {infoUser?.fullName ?? infoUser?.name}
          </Typography>
          <Typography variant="body1">
            <b>{t("phoneNumber")}:</b>{" "}
            {infoUser?.phone}
          </Typography>
          <Typography variant="body1">
            <b>{t("email")}:</b>{" "}
            {infoUser?.email}
          </Typography>
          <Divider className="!my-3" />
          {
            isCustomer ?
              (
                <>
                  <h3 className="text-primary">{t("invoiceInfo")}</h3>
                  <Typography variant="body1">
                    <b>{t("companyName")}:</b>{" "}
                    {infoUser?.companyName}
                  </Typography>
                  <Typography variant="body1">
                    <b>{t("taxCode")}:</b>{" "}
                    {infoUser?.taxCode}
                  </Typography>

                  <Typography variant="body1">
                    <b>{t("companyEmail")}:</b>{" "}
                    {infoUser?.companyEmail}
                  </Typography>
                  <Typography variant="body1">
                    <b>{t("companyAddress")}:</b>{" "}
                    {infoUser?.companyAddress}
                  </Typography>
                </>
              ) :
              (
                <>
                  <h3 className="text-primary">{t("rewardInfo")}</h3>
                  {/* <Typography variant="body1">
                    <b>{t("bankName")}:</b>{" "}
                    {infoUser?.bank?.fullName}
                  </Typography> */}
                  <Typography variant="body1">
                    <b>{t("bankAccountNumber")}:</b>{" "}
                    {infoUser?.bankAccountNumber}
                  </Typography>
                  <Typography variant="body1">
                    <b>{t("bankAccountName")}:</b>{" "}
                    {infoUser?.bankAccountName}
                  </Typography>
                </>
              )
          }


        </div>
      </Modal>
    </Stack>
  );
};

export default observer(SupportTicketReply);
