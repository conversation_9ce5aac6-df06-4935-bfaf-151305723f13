import {
  ExportOutlined,
  ImportOutlined,
  PlusOutlined,
  SearchOutlined,
  UserOutlined,
} from "@ant-design/icons";
import {
  <PERSON>ton,
  Card,
  Col,
  Divider,
  Input,
  message,
  Popconfirm,
  Row,
  Space,
} from "antd";
import { projectApi } from "api/project.api";
import { useProject } from "hooks/useProject";
import { useEffect, useMemo, useRef, useState } from "react";
import { PermissionNames } from "router";
import { permissionStore } from "store/permissionStore";
import { Project, ProjectStatus } from "types/project";
import { formatVND, getTitle } from "utils";
import { checkRole } from "utils/auth";
import { unixToFullDate } from "utils/dateFormat";
import { handleExport, MyExcelColumn } from "../../utils/MyExcel";
import { ProjectModal, ProjectModalRef } from "./components/Modal/ProjectModal";
import { ProjectList } from "./components/Table/ProjectList";
import { Box, styled, TextField, Typography } from "@mui/material";
import { useTranslation } from "react-i18next";
import { FaFileContract, FaFileInvoiceDollar } from "react-icons/fa";
import { FaFileCircleCheck, FaFileCirclePlus } from "react-icons/fa6";
import { Grid } from "@mui/system";
const BoxStyled = styled(Box)(() => ({
  padding: "30px",
  transition: "0.1s ease-in",
  cursor: "pointer",
  color: "inherit",
  "&:hover": {
    transform: "scale(1.03)",
  },
}));
export const ProjectPage = ({ title = "" }) => {
  const projectModalRef = useRef<ProjectModalRef>();
  const [openImport, setOpenImport] = useState(false);
  const { t } = useTranslation();

  const [loadingDelete, setLoadingDelete] = useState(false);
  const {
    projects,
    fetchProject,
    loadingProject,
    queryProject,
    totalProject,
    fetchSummary,
    summaryData,
  } = useProject({
    initQuery: {
      page: 1,
      limit: 10,
    },
  });
  // const hasProjectAddPermission = checkRole(
  //   PermissionNames.consumerProjectAdd,
  //   permissionStore.permissions
  // );
  // const hasProjectUpdatePermission = checkRole(
  //   PermissionNames.consumerIndustryEdit,
  //   permissionStore.permissions
  // );
  // const hasIndustryDeletePermission = checkRole(
  //   PermissionNames.consumerIndustryDelete,
  //   permissionStore.permissions
  // );
  useEffect(() => {
    document.title = getTitle(t(title));
    fetchProject();
    fetchSummary();
  }, []);
  const exportColumns: MyExcelColumn<Project>[] = [
    {
      width: 15,
      header: t("code"),
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "code",
      render: (record: Project) => {
        const content = (
          <>
            <div className="whitespace-pre-wrap">
              <strong>{t("code")}</strong> : {record.code}
            </div>
            <div className="whitespace-pre-wrap">
              <strong>{t("projectName")}</strong> : {record.name}
            </div>
            <div className="whitespace-pre-wrap">
              <strong>{t("customer")}</strong>: {record.customer?.name}
            </div>
          </>
        );
        return content;
      },
    },

    {
      width: 30,
      header: t("projectName"),
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "name",
      // style: { font: { color: { argb: "004e47cc" } } },
      render: (record: Project) => {
        return record.name || t("notUpdate");
      },
    },
    {
      width: 20,
      header: t("customerName"),
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "phone",
      render: (record: Project) => {
        return record.customer?.name || t("notUpdate");
      },
    },

    {
      width: 20,
      header: t("product"),
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "product",
      render: (record: Project) => {
        return record.product?.name || t("notUpdate");
      },
    },

    {
      width: 30,
      header: t("status"),
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "status",
      render: (record: Project) => {
        return t(record.status);
      },
    },
    {
      width: 20,
      header: t("createdAt"),
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "createdAt",
      render: (record: Project) => {
        return unixToFullDate(record.createdAt);
      },
    },
  ];
  const handleDeleteProject = async (projectId: number) => {
    try {
      setLoadingDelete(true);
      const res = await projectApi.delete(projectId);
      fetchProject();
      fetchSummary();

      message.success(t("actionSuccessfully"));
    } catch (error) {
    } finally {
      setLoadingDelete(false);
    }
  };
  const handleActiveProject = async (projectId: number) => {
    try {
      setLoadingDelete(true);
      const res = await projectApi.reopen(projectId);
      fetchProject();
      fetchSummary();

      message.success(t("actionSuccessfully"));
    } catch (error) {
    } finally {
      setLoadingDelete(false);
    }
  };

  const handleInactiveProject = async (projectId: number) => {
    try {
      setLoadingDelete(true);
      const res = await projectApi.pause(projectId);
      fetchProject();
      fetchSummary();

      message.success(t("actionSuccessfully"));
    } catch (error) {
    } finally {
      setLoadingDelete(false);
    }
  };

  return (
    <>
      <Grid container spacing={3} textAlign="center">
        <Grid
          size={{
            lg: 3,
            sm: 6,
            xs: 12,
          }}
        >
          <div className="rounded-lg p-[30px] transition ease-in duration-100 cursor-pointer text-inherit hover:scale-[1.03] bg-[#ECF2FF] text-[#5D87FF]">
            <Typography variant="h3">{summaryData?.total}</Typography>
            <Typography variant="h6">{t("totalProject")}</Typography>
          </div>
        </Grid>
        <Grid
          size={{
            lg: 3,
            sm: 6,
            xs: 12,
          }}
        >
          <div className="rounded-lg p-[30px] transition ease-in duration-100 cursor-pointer text-inherit hover:scale-[1.03] bg-[#FEF5E5] text-[#FFAE1F]">
            <Typography variant="h3">{summaryData?.totalProcessing}</Typography>
            <Typography variant="h6">{t("ongoing")}</Typography>
          </div>
        </Grid>
        <Grid
          size={{
            lg: 3,
            sm: 6,
            xs: 12,
          }}
        >
          <div className="rounded-lg p-[30px] transition ease-in duration-100 cursor-pointer text-inherit hover:scale-[1.03] bg-[#FDEDE8] text-[#FA896B]">
            <Typography variant="h3">{summaryData?.totalPaused}</Typography>
            <Typography variant="h6">{t("paused")}</Typography>
          </div>
        </Grid>
        <Grid
          size={{
            lg: 3,
            sm: 6,
            xs: 12,
          }}
        >
          <div className="rounded-lg p-[30px] transition ease-in duration-100 cursor-pointer text-inherit hover:scale-[1.03] bg-[#E6FFFA] text-[#13DEB9]">
            <Typography variant="h3">
              {formatVND(summaryData?.totalPrice || 0)}
            </Typography>
            <Typography variant="h6">{t("spent")}</Typography>
          </div>
        </Grid>
      </Grid>

      <Card
        bodyStyle={{ padding: "8px 20px" }}
        style={{ borderRadius: "8px", marginTop: "16px" }}
      >
        <section className="box">
          <div className="filter-container">
            <Space wrap>
              <div className="filter-item ">
                <label htmlFor="">{t("search")}</label>
                <Input
                  allowClear
                  onChange={(ev) => {
                    if (ev.currentTarget.value) {
                      queryProject.search = ev.currentTarget.value;
                    } else {
                      queryProject.search = undefined;
                    }
                    queryProject.page = 1;
                    fetchProject();
                    fetchSummary();
                  }}
                  onKeyDown={(ev) => {
                    if (ev.code == "Enter") {
                      fetchProject();
                      fetchSummary();
                    }
                  }}
                  size="middle"
                  placeholder={t("projectName")}
                />
              </div>

              <div className="filter-item btn">
                <Button
                  onClick={() => {
                    fetchProject();
                    fetchSummary();
                  }}
                  type="primary"
                  icon={<SearchOutlined />}
                >
                  {t("search")}
                </Button>
              </div>
              {/* {hasProjectAddPermission && ( */}
              <div className="filter-item btn">
                <Button
                  onClick={() => {
                    projectModalRef.current?.handleCreate();
                  }}
                  icon={<PlusOutlined />}
                  type="primary"
                >
                  {t("create")}
                </Button>
              </div>
              {/* )} */}
              {/* <div className="filter-item btn">
                <Button
                  onClick={() => {
                    setOpenImport(true);
                  }}
                  type="primary"
                  icon={<PlusOutlined />}
                >
                  Nhập excel
                </Button>
              </div> */}
              {/* <div className="filter-item btn">
                  <Button
                    onClick={() => {
                      importModal.current?.open();
                    }}
                    type="primary"
                    icon={<ImportOutlined />}
                  >
                    Nhập excel
                  </Button>
                </div> */}

              <div className="filter-item btn">
                <Popconfirm
                  title={t("exportAsk")}
                  onConfirm={() =>
                    handleExport({
                      onProgress(percent) {},
                      exportColumns,
                      fileType: "xlsx",
                      dataField: "projects",
                      query: queryProject,
                      api: projectApi.findAll,
                      fileName: t("projectList"),
                      sheetName: t("projectList"),
                    })
                  }
                  okText={t("exportExcel")}
                  cancelText={t("cancel")}
                >
                  <Button
                    type="primary"
                    loading={false}
                    icon={<ExportOutlined />}
                  >
                    {t("exportExcel")}
                  </Button>
                </Popconfirm>
              </div>
            </Space>
          </div>

          <ProjectList
            onEdit={(record) => projectModalRef.current?.handleUpdate(record)}
            dataSource={projects}
            loading={loadingProject}
            loadingDelete={loadingDelete}
            pagination={{
              total: totalProject,
              defaultPageSize: queryProject.limit,
              currentPage: queryProject.page,
              onChange: ({ page, limit }) => {
                Object.assign(queryProject, {
                  page,
                  limit,
                });
                fetchProject();
                fetchSummary();
              },
            }}
            onDelete={handleDeleteProject}
            onActive={handleActiveProject}
            onInactive={handleInactiveProject}

            // hasDeleteProjectPermission={hasProjectDeletePermission}
            // hasUpdateProjectPermission={hasProjectUpdatePermission}
          />
        </section>

        <ProjectModal
          ref={projectModalRef}
          onClose={function (): void {
            throw new Error("Function not implemented.");
          }}
          onSubmitOk={() => {
            fetchProject();
            fetchSummary();
          }}
          // hasAddIndustryPermission={hasIndustryAddPermission}
          // hasUpdateIndustryPermission={hasIndustryUpdatePermission}
        />
      </Card>
    </>
  );
};
