import { Promotion } from "./../types/promotion";
import { QueryParam } from "./../types/query";
import { promotionApi } from "../api/promotion.api";
import { useState } from "react";

export interface PromotionQuery extends QueryParam {}

interface UsePromotionProps {
  initQuery: PromotionQuery;
}

export const usePromotion = ({ initQuery }: UsePromotionProps) => {
  const [data, setData] = useState<Promotion[]>([]);
  const [total, setTotal] = useState(0);
  const [query, setQuery] = useState<PromotionQuery>(initQuery);
  const [loading, setLoading] = useState(false);

  const fetchData = async () => {
    setLoading(true);
    try {
      const { data } = await promotionApi.findAll(query);

      setData(data.promotions);
      setTotal(data.total);
    } finally {
      setLoading(false);
    }
  };

  return {
    promotions: data,
    totalPromotion: total,
    fetchPromotion: fetchData,
    loadingPromotion: loading,
    setQueryPromotion: setQuery,
    queryPromotion: query,
  };
};
