import { Table } from "antd";
import { ColumnsType } from "antd/es/table";
import { TableProps } from "antd/lib";
import clsx from "clsx";

interface CustomTableProps<T> extends TableProps<T> {
  columns?: ColumnsType<T>;
  dataSource: T[];
}

export const AriviTable = <T extends {}>({
  columns,
  dataSource,
  className,
  children,
  ...props
}: CustomTableProps<T>) => {
  if (columns) {
    return (
      <Table
        className={clsx("arivi-table", className)}
        columns={columns}
        dataSource={dataSource}
        pagination={false}
        {...props}
      />
    );
  }
  return (
    <Table
      className={clsx("arivi-table", className)}
      dataSource={dataSource}
      pagination={false}
      {...props}
    >
      {children}
    </Table>
  );
};
