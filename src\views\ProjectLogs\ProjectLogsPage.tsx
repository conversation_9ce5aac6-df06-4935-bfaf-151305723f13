import {
  DownOutlined,
  ExportOutlined,
  PlusOutlined,
  SearchOutlined,
} from "@ant-design/icons";
import {
  Button,
  Input,
  message,
  Popconfirm,
  Select,
  Space,
  Spin,
  Table,
  Tag,
} from "antd";
import { projectLogApi } from "api/projectLog.api";
import { Pagination } from "components/Pagination";
import React, { useEffect, useState, useRef, useCallback } from "react";
import { ModalStatus } from "types/modal";
import { QueryParam } from "types/query";
import { ProjectLog } from "types/projectLog";
import { formatVND, getTitle } from "utils";
import { $url } from "utils/url";
import { useProjectLog } from "hooks/useProjectLog";
import { useReview } from "hooks/useReview";
import { Review, ReviewStatus, ReviewStatusTrans } from "types/review";
import { AriviTable } from "components/Table/AriviTable";
import { useTranslation } from "react-i18next";
import { unixToFullDate } from "utils/dateFormat";
import DropdownCell from "components/Table/DropdownCell";
import { handleExport, MyExcelColumn } from "utils/MyExcel";
import { reviewApi } from "api/review.api";
import { debounce } from "lodash";
import { useProject } from "hooks/useProject";
// import { ProjectLogModal } from "./components/ProjectLogModal";

const { ColumnGroup, Column } = Table;

export const ProjectLogsPage = ({ title = "" }) => {
  const { t } = useTranslation();
  //   const modalRef = useRef<ProjectLogsPageModal>(null)
  const [selectedProjectId, setSelectedProjectId] = useState();
  const [loadingAction, setLoadingAction] = useState(false);
  useEffect(() => {
    document.title = getTitle(t(title));
  }, []);
  const {
    fetchReview,
    loadingReview,
    queryReview,
    reviews,
    setQueryReview,
    totalReview,
  } = useReview({
    initQuery: {
      page: 1,
      limit: 10,
      isWarranty: true,
    },
  });
  const exportColumns: MyExcelColumn<Review>[] = [
    {
      width: 30,
      header: t("project"),
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "project",
      render: (record: Review) => {
        return `${t(record.project?.name)}-${t(record.project?.code)}`;
      },
    },
    {
      width: 30,
      header: t("content"),
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "content",
      render: (record: Review) => {
        return t(record.content);
      },
    },
    {
      width: 30,
      header: t("partner"),
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "partner",
      render: (record: Review) => {
        return t(record.partner?.fullName);
      },
    },
    {
      width: 30,
      header: t("profit"),
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "profit",
      render: (record: Review) => {
        return record.status === ReviewStatus.Complete
          ? `${formatVND(record?.profit)}`
          : t("missionNotCompleted");
      },
    },
    {
      width: 20,
      header: t("status"),
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "rewardPoint",
      render: (record: Review) => {
        return t(record.status);
      },
    },
    {
      width: 20,
      header: t("createdAt"),
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "createdAt",
      render: (record: Review) => {
        return unixToFullDate(record.createdAt);
      },
    },
  ];
  const {
    fetchProject,
    projects,
    totalProject,
    setQueryProject,
    queryProject,
    loadingProject,
  } = useProject({
    initQuery: {
      page: 1,
      limit: 100,
    },
  });
  const debounceSearchProject = useCallback(
    debounce((keyword) => {
      console.log(keyword);
      (queryProject.limit = 100), (queryProject.page = 1);
      queryProject.search = keyword;
      fetchProject();
    }, 300),
    []
  );
  const handleReject = async (reviewId: number, note: string) => {
    try {
      setLoadingAction(true);
      const data = await reviewApi.reject(reviewId, { note });
      //   onRefreshData();
      fetchReview();
      message.success(t("actionSuccessfully"));
    } catch (error) {
    } finally {
      setLoadingAction(false);
    }
  };
  const handleComplete = async (reviewId: number) => {
    try {
      setLoadingAction(true);
      const data = await reviewApi.complete(reviewId);
      //   onRefreshData();
      fetchReview();
      message.success(t("actionSuccessfully"));
    } catch (error) {
    } finally {
      setLoadingAction(false);
    }
  };
  useEffect(() => {
    fetchReview();
  }, [queryReview]);
  useEffect(() => {
    fetchProject();
  }, []);
  return (
    <div>
      <div className="filter-container">
        <Space>
          {/* <div className="filter-item">
            <label htmlFor="">Tìm kiếm</label>
            <Input
              onKeyDown={(ev) => {
                if (ev.code == "Enter") {
                  queryReview.page = 1;
                  setQueryReview({ ...queryReview });
                }
              }}
              size="middle"
              onChange={(ev) => {
                queryReview.search = ev.currentTarget.value;
              }}
              placeholder="Tìm kiếm"
            />
          </div> */}

          {/* <div className="filter-item">
            <Button
              onClick={() => fetchReview()}
              type="primary"
              icon={<SearchOutlined />}
            >
              Tìm kiếm
            </Button>
          </div> */}
          <div className="filter-item">
            <Select
              showSearch
              value={selectedProjectId}
              defaultValue=""
              onSearch={(value) => {
                console.log("first");
                debounceSearchProject(value);
              }}
              placeholder={t("selectProject")}
              // optionFilterProp="children"
              className="w-[350px]  h-[60px]"
              onChange={(value) => {
                // delete queryReview.status;
                queryReview.projectId = value;
                queryReview.page = 1;

                fetchReview();
                // onSubmitOk?.(value);
              }}
              onClear={() => {
                queryProject.search = undefined;
                queryReview.projectId = undefined;
                // onSubmitOk?.();

                fetchProject();
                // fetchBrand();
              }}
              filterOption={false}
            >
              {[{ name: t("ALL"), id: "", code: "" }, ...projects].map((project) => (
                <Select.Option key={project.id} value={project.id}>
                  <div>{project.name}</div>{" "}
                  <div className="text-primary">{project.code}</div>
                </Select.Option>
              ))}
            </Select>
          </div>
          {/* <div className="filter-item btn">
            <Button
              onClick={() => {
                // modalRef.current?.handleCreate();
              }}
              type="primary"
              icon={<PlusOutlined />}
            >
              Thêm mới
            </Button>
          </div> */}
          <div className={`filter-item`}>
            <Popconfirm
              title={t("exportAsk")}
              onConfirm={() =>
                handleExport({
                  onProgress(percent) { },
                  exportColumns,
                  fileType: "xlsx",
                  dataField: "reviews",
                  query: queryReview,
                  api: reviewApi.findAll,
                  fileName: `${t("reviewUnderWarranty")} `,
                  sheetName: `${t("reviewUnderWarranty")} `,
                })
              }
              okText={t("exportExcel")}
              cancelText={t("cancel")}
            >
              <Button type="primary" loading={false} icon={<ExportOutlined />}>
                {t("exportExcel")}
              </Button>
            </Popconfirm>
          </div>
        </Space>
      </div>
      <div>
        <AriviTable
          // bordered

          loading={loadingReview}
          pagination={false}
          rowKey="id"
          dataSource={reviews}
          className="custom-scrollbar"
          scroll={{ x: "max-content", y: "calc(100vh - 370px)" }}

        // onChange={}
        >
          <Column
            width={300}
            title={t("project")}
            dataIndex="name"
            key="title"
            render={(address, record: Review) => {
              return (
                <>
                  <div className="whitespace-pre-wrap">
                    <strong>{t("code")}</strong> : {record.project?.code}
                  </div>
                  <div className="whitespace-pre-wrap">
                    <strong>{t("projectName")}</strong> : {record.project?.name}
                  </div>
                </>
              );
            }}
          />
          <Column
            title={t("content")}
            width={350}
            dataIndex="content"
            key={"content"}
            render={(text, record: Review) => <span>{record.content}</span>}
          />
          <Column
            title={t("partner")}
            width={150}
            dataIndex="partner"
            key={"partner"}
            render={(text, record: Review) => (
              <span>{record.partner?.fullName}</span>
            )}
          />
          <Column
            width={200}
            title={t("status")}
            dataIndex="name"
            align="center"
            key={"name"}
            render={(text, record: Review) => (
              <Tag
                color={
                  ReviewStatusTrans[
                    record.status as keyof typeof ReviewStatusTrans
                  ]?.color
                }
              >
                {t("review" + record.status)}
              </Tag>
            )}
          />
          <Column
            width={200}
            title={t("profit")}
            dataIndex="name"
            align="right"
            key={"name"}
            render={(text, record: Review) => (
              <div>
                {record.status === ReviewStatus.Complete
                  ? `${formatVND(record?.profit)} VND`
                  : t("missionNotCompleted")}
              </div>
            )}
          />
          <Column
            title={t("createdAt")}
            dataIndex="createdAt"
            key="createdAt"
            width={120}
            render={(createdAt) => {
              return unixToFullDate(createdAt);
            }}
          />
          <Column
            fixed="right"
            width={100}
            align="center"
            title=""
            key="action"
            dataIndex={""}
            render={(text, record: Review) => {
              const items = [
                {
                  label: (
                    <Button
                      type="primary"
                      className="w-full justify-center !flex !items-center gap-2 !font-medium"
                      onClick={() => window.open(record.reviewUrl, "_blank")}
                    >
                      {t("seeReview")}
                    </Button>
                  ),
                  key: "seeReview",
                  hidden: !record.reviewUrl,
                },
                {
                  label: (
                    <Popconfirm
                      title={t("confirm?")}
                      onConfirm={() => handleReject(record.id, t("wrongPhoto"))}
                      okText={t("save")}
                      cancelText={t("close")}
                    >
                      <Button
                        loading={loadingAction}
                        type="primary"
                        danger
                        className="w-full justify-center !flex !items-center gap-2 !font-medium"
                      >
                        {t("wrongPhoto")}
                      </Button>
                    </Popconfirm>
                  ),
                  key: "wrongPhoto",
                  hidden: !(
                    record.status === ReviewStatus.AdminPending ||
                    record.status === ReviewStatus.SystemPending
                  ),
                },
                {
                  label: (
                    <Popconfirm
                      title={t("confirm?")}
                      onConfirm={() => handleReject(record.id, t("noReview"))}
                      okText={t("save")}
                      cancelText={t("close")}
                    >
                      <Button
                        loading={loadingAction}
                        type="primary"
                        danger
                        className="w-full justify-center !flex !items-center gap-2 !font-medium"
                      >
                        {t("noReview")}
                      </Button>
                    </Popconfirm>
                  ),
                  key: "noReview",
                  hidden: !(
                    record.status === ReviewStatus.AdminPending ||
                    record.status === ReviewStatus.SystemPending
                  ),
                },
                {
                  label: (
                    <Popconfirm
                      title={t("confirm?")}
                      onConfirm={() => handleComplete(record.id)}
                      okText={t("save")}
                      cancelText={t("close")}
                    >
                      <Button
                        loading={loadingAction}
                        type="primary"
                        danger
                        className="w-full justify-center !flex !items-center gap-2 !font-medium"
                      >
                        {t("approve")}
                      </Button>
                    </Popconfirm>
                  ),
                  key: "approve",
                  hidden: !(
                    record.status === ReviewStatus.AdminPending ||
                    record.status === ReviewStatus.SystemPending
                  ),
                },
              ];

              const visibleItems = items.filter((it) => !it.hidden);

              if (visibleItems.length === 0) {
                return null;
              }

              return (
                //@ts-ignore
                <DropdownCell
                  text={t("action")}
                  items={visibleItems}
                  trigger={["click"]}
                >
                  <a onClick={(e) => e.preventDefault()}>
                    <Space>
                      {t("action")}
                      <DownOutlined />
                    </Space>
                  </a>
                </DropdownCell>
              );
            }}
          />
        </AriviTable>
        <Pagination
          total={reviews?.length || 0}
          defaultPageSize={queryReview.limit}
          currentPage={queryReview.page}
          onChange={({ page, limit }) => {
            Object.assign(queryReview, {
              page,
              limit,
            });
            fetchReview();
          }}
        />
      </div>

      {/* <ProjectLogModal
        onSubmitOk={fetchData}
        onClose={() => {}}
        ref={modalRef}
      /> */}
    </div>
  );
};
