import dayjs, { Dayjs } from "dayjs";
import { format } from "path";
import { settings } from "settings";

export const unixToFullDate = (unix: number) => {
  if (unix) {
    return dayjs.unix(unix).format(settings.fullDateFormat);
  } else {
    return "--";
  }
};


export const unixToDate = (unix: number) => {
  return dayjs.unix(unix).format(settings.dateFormat);
};

export const onRangeChangeUnix = (dates: null | (Dayjs | null)[]) => {
  if (dates) {
    return [dates[0]?.startOf("day")?.unix(), dates[1]?.endOf("day")?.unix()];
  } else {
    return [undefined, undefined];
  }
};