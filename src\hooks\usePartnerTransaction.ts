import { partnerTransactionApi } from "api/partnerTransaction.api";
import { useState } from "react";
import { PartnerTransaction } from "types/partnerTransaction";
import { QueryParam } from "types/query";

export interface PartnerTransactionQuery extends QueryParam {}

interface UsePartnerTransactionProps {
  initQuery: PartnerTransactionQuery;
}

export const usePartnerTransaction = ({
  initQuery,
}: UsePartnerTransactionProps) => {
  const [data, setData] = useState<PartnerTransaction[]>([]);
  const [total, setTotal] = useState(0);
  const [query, setQuery] = useState<PartnerTransactionQuery>(initQuery);
  const [loading, setLoading] = useState(false);

  const fetchData = async () => {
    setLoading(true);
    try {
      const { data } = await partnerTransactionApi.findAll(query);

      setData(data.partnerTransactions);
      setTotal(data.total);
    } finally {
      setLoading(false);
    }
  };

  return {
    partnerTransactions: data,
    total,
    fetchData,
    loading,
    setQuery,
    query,
  };
};
