import { Customer } from "./customer";
import { Staff } from "./staff";
import { TypeService } from "./type-service";

export enum ContactFormType {
  Contact = "CONTACT",
  PremiumService = "PREMIUM_SERVICE",
}

export enum ContactFormStatus {
  All = "ALL",
  Pending = "PENDING",
  Complete = "COMPLETE",
}

export const ContactFormStatusTrans = {
  [ContactFormStatus.All]: {
    title: "Tất cả",
    color: "purple",
    value: ContactFormStatus.All,
  },
  [ContactFormStatus.Pending]: {
    title: "Chờ xác nhận",
    color: "blue",
    value: ContactFormStatus.Pending,
  },
  [ContactFormStatus.Complete]: {
    title: "Đã hoàn thành",
    color: "green",
    value: ContactFormStatus.Complete,
  },
};

export interface ContactForm {
  id: number;
  createdAt: number;
  updatedAt: number;
  deletedBy: string;
  isDeleted: boolean;
  deletedAt: number;
  fullName: string;
  phone: string;
  email: string;
  content: string;
  status: ContactFormStatus;
  customer: Customer;
  note: string;
  completedAt: number;
  completedStaff: Staff;
  type: ContactFormType;
  typeService: TypeService;
}
