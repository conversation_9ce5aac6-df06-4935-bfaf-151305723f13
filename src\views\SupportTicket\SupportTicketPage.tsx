import { useCallback, useEffect, useRef, useState } from "react";
import { <PERSON><PERSON>, Card, Tabs } from "antd";
import { getTitle } from "utils";
import {
  SupportTicketCreatedBy,
  SupportTicketStatus,
  SupportTicketStatusTrans,
} from "types/supportTicket";
import { supportTicketApi } from "api/supportTicket.api";
import { SupportTicketTab } from "./components/SupportTicketTab";
import { useTranslation } from "react-i18next";
import "./SupportTicket.scss";
import { SupportTicketModal, SupportTicketModalRef } from "./components/Modal/SupportTicketModal";
import { useSupportTicket } from "hooks/useSupportTicket";
import { ModalStatus } from "types/modal";
import { appStore } from "store/appStore";

export const SupportTicketPage = ({
  title = "",
  type = SupportTicketCreatedBy.Customer,
}) => {
  const [tabActive, setTabActive] = useState<SupportTicketStatus>(
    SupportTicketStatus.All
  );
  const [summaryReceiptOfStatus, setSummaryReceiptOfStatus] = useState();
  const [lastUpdate, setLastUpdate] = useState<number>(0);

  console.log({ parentLastUpdate: lastUpdate });
  const { t } = useTranslation();

  const supportTicketModalRef = useRef<SupportTicketModalRef>();
  const [statusShow, setStatusShow] = useState<ModalStatus | "list">("list");
  const [idTicket, setIdTicket] = useState(0);
  const [pageNumber, setPageNumber] = useState(1);

  useEffect(() => {
    document.title = getTitle(t(title));
    fetchSummary();
  }, []);

  const fetchSummary = useCallback(async () => {
    const res = await supportTicketApi.summaryStatus({ createdBy: type });

    if (res.status) {
      setSummaryReceiptOfStatus(() => {
        const summary = res.data.reduce(
          (prev: any, curr: { status: SupportTicketStatus; total: number }) => {
            prev[curr.status] = curr.total;
            prev.ALL = (prev.ALL || 0) + curr.total;
            return prev;
          },
          { ALL: 0 }
        );

        return summary;
      });
    }
  }, []);
  const onChangeTab = useCallback((key: SupportTicketStatus) => {
    setTabActive(key as SupportTicketStatus);
  }, []);

  const onLastUpdateChange = useCallback(() => {
    appStore.setRefreshSticket(!appStore.refreshSticket);
    setLastUpdate((pre) => pre + 1);
  }, []);


  const {
    fetchSupportTicket,

  } = useSupportTicket({
    initQuery: {
      page: 1,
      limit: 20,
      status: status === SupportTicketStatus.All ? undefined : status,
      createdBy:
        type === SupportTicketCreatedBy.Customer
          ? SupportTicketCreatedBy.Customer
          : SupportTicketCreatedBy.Partner,
    },
  });

  return (
    // <Card bodyStyle={{ padding: "8px 20px" }} style={{ borderRadius: "8px" }}>
    <div>

      {statusShow === "update" &&
        <SupportTicketModal
          ref={supportTicketModalRef}
          onClose={function (): void {
            throw new Error("Function not implemented.");
          }}
          idTicket={idTicket}
          setStatus={setStatusShow}
          status={statusShow}
          onSubmitOk={() => {
            fetchSupportTicket();
            onLastUpdateChange();
            fetchSummary();
          }}
        />
      }

      {statusShow === "list" &&
        <Tabs
          activeKey={tabActive}
          onChange={(key) => onChangeTab(key as SupportTicketStatus)}
          type="line"
          animated={{ inkBar: true, tabPane: true, tabPaneMotion: {} }}
        >
          {Object.values(SupportTicketStatusTrans).map((item) => (
            <Tabs.TabPane
              tab={
                <div className="flex items-center gap-2">
                  {t("supportTicket" + item.value)}
                  {summaryReceiptOfStatus && (
                    <Badge
                      key={item.value}
                      color={SupportTicketStatusTrans[item.value as keyof typeof SupportTicketStatusTrans]?.color}
                      count={summaryReceiptOfStatus?.[item.value] || 0}
                    />
                  )}
                </div>
              }
              key={item.value}
              tabKey={item.value}
            >
              <SupportTicketTab
                typeTitle={t(title)}
                title={t(item.value)}
                parentLastUpdate={lastUpdate}
                isFocus={tabActive == item.value}
                status={tabActive}
                onSubmitOk={() => {
                  onLastUpdateChange();
                  fetchSummary();
                }}
                type={type}
                onEditModal={(record) => {
                  setIdTicket(record.id);
                  supportTicketModalRef.current?.handleUpdate(record);
                  setStatusShow("update")
                }}
                setPageNumber={setPageNumber}
                pageNumber={pageNumber}
              />
            </Tabs.TabPane>
          ))}
        </Tabs>
      }
      {/* </Card> */}
    </div>
  );
};
