import { LoadingOutlined, PlusOutlined } from "@ant-design/icons";
import { message, Upload } from "antd";
import { UploadChangeParam } from "antd/lib/upload";
import dayjs from "dayjs";
import React, { useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { getImageSize } from "utils";
import { getToken } from "utils/auth";
import { $url } from "utils/url";

interface SingleImageUploadProps {
  uploadUrl?: string;
  imageUrl: string;
  onUploadOk: (path: string) => void;
  width?: number | string;
  recommendSize?: { width: number; height: number };
  recommendFileSize?: number; //In Byte
  recommendRatio?: string; //In Byte
  height?: number | string;
  disabled?: boolean;
}

export const SingleImageUpload = React.memo(
  ({
    uploadUrl = import.meta.env.VITE_API_URL + "/v1/admin/image/upload",
    imageUrl,
    onUploadOk,
    height = 150,
    width = 150,
    disabled,
    recommendSize = { width: 400, height: 400 },
    recommendRatio,
    recommendFileSize,
  }: SingleImageUploadProps) => {
    const [loading, setLoading] = useState(false);
    const uploadRef = useRef(null);
    const [imageError, setImageError] = useState(false);
    const [singleUploadId, setsingleUploadId] = useState(
      `single-upload-${dayjs().valueOf()}`
    );
    const [imageSize, setImageSize] = useState<{
      width: number;
      height: number;
    }>({ width: 0, height: 0 });
    const { t } = useTranslation();

    const checkSize = async (file: File): Promise<boolean> => {
      return new Promise((resolve, reject) => {
        if (recommendSize) {
          const reader = new FileReader();
          reader.readAsDataURL(file);
          reader.addEventListener("load", (event) => {
            //@ts-ignore
            const _loadedImageUrl = event.target.result;
            const image = document.createElement("img");

            //@ts-ignore
            image.src = _loadedImageUrl;
            // image.addEventListener("load", () => {
            //   const { width, height } = image;
            //   if (
            //     width > recommendSize.width ||
            //     height > recommendSize.height
            //   ) {
            //     message.error(
            //       `Kích thước quá lớn (Đề xuất: ${recommendSize.width}x${recommendSize.height})`
            //     );

            //     return resolve(false);
            //   } else {
            //     resolve(true);
            //   }
            // });
            resolve(true);
          });
        } else {
          return resolve(true);
        }
      });
    };

    const checkFileSize = (size: number) => {
      if (recommendFileSize) {
        if (recommendFileSize < size) {
          message.error(
            `Dung lượng ảnh quá lớn (Tối đa: ${
              recommendFileSize / 1024 / 1024
            }MB)`,
            5
          );
          return false;
        }
        return true;
      }
      return true;
    };

    const beforeUpload = async (file: File) => {
      const notLargeDimension = await checkSize(file);
      const notLargeSize = checkFileSize(file.size);

      const isImg = file.type.includes("image");

      if (!isImg) {
        message.error("Only accept image format");
      }

      return notLargeDimension && notLargeSize && isImg;
    };

    const handleChange = (info: UploadChangeParam<any>) => {
      if (info.file.status === "uploading") {
        setLoading(true);
        return;
      }
      if (info.file.status === "done") {
        if (recommendSize) {
          getImageSize(
            setImageSize,
            import.meta.env.VITE_IMG_URL + info.file.response.data.path
          );
        }
        onUploadOk(import.meta.env.VITE_IMG_URL + info.file.response.data.path);
        setLoading(false);
      }
      if (info.file.status === "error") {
        message.error(info.file.response?.message);
        setLoading(false);
        return;
      }
    };

    const uploadButton = (
      <div>
        {loading ? <LoadingOutlined /> : <PlusOutlined />}
        <div style={{ marginTop: 8 }}>{t("upload")}</div>
      </div>
    );

    useEffect(() => {
      const container = document.querySelector<HTMLDivElement>(
        `.${singleUploadId} .ant-upload-select`
      );
      if (container) {
        container.style.width = typeof width == "number" ? `${width}px` : width;
        container.style.height =
          typeof height == "number" ? `${height}px` : height;
      }
    }, []);

    useEffect(() => {
      if (recommendSize) {
        if (imageUrl) {
          getImageSize(setImageSize, imageUrl);
        } else {
          setImageSize({ height: 0, width: 0 });
        }
      }
    }, [imageUrl]);

    return (
      <>
        {/* {recommendSize &&
          (imageSize.width > recommendSize?.width ||
            imageSize.height > recommendSize.height) && (
            <p style={{ color: "red" }}>
              Kích thước hình ảnh quá lớn cần chỉnh sửa lại
            </p>
          )} */}
        {recommendFileSize && (
          <p className="text-center">
            Dung lượng ảnh tối đa {recommendFileSize / 1024 / 1024}MB
          </p>
        )}
        <Upload
          accept="image/*"
          name="file"
          listType="picture-card"
          className={singleUploadId}
          showUploadList={false}
          action={uploadUrl}
          headers={{
            token: getToken() || "",
          }}
          beforeUpload={beforeUpload}
          onChange={handleChange}
          ref={uploadRef}
          disabled={disabled}
          style={{
            width: 200,
          }}
        >
          {imageUrl ? (
            <img
              src={$url(imageUrl)}
              alt="avatar"
              onError={(e) => {
                //@ts-ignore
                e.target.src = "/default-thumbnail.jpg";
              }}
              style={{ width: "100%", height: "100%", objectFit: "contain" }}
            />
          ) : (
            uploadButton
          )}
        </Upload>
        {/* {imageUrl && (
          <p className="text-center !my-2 font-semibold">
            {t("currentSize")}: {imageSize.width} x {imageSize.height}
          </p>
        )}
        <p className="text-center !my-2 font-semibold">
          {t("recommendSize")}: {recommendSize.width} x {recommendSize.height}{" "}
          {recommendRatio && `(${recommendRatio})`}
        </p> */}
      </>
    );
  }
);
