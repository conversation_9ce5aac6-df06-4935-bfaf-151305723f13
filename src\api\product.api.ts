import { request } from "utils/request";
import { AxiosPromise } from "axios";

export const productApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/product",
      params,
    }),
  findOne: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/product/${id}`,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/product",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/product/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/product/${id}`,
      method: "delete",
    }),
  estimate: (data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/product/estimate`,
      method: "post",
      data,
    }),
};
