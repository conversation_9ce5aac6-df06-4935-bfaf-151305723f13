import { customerWithdrawApi } from "api/customerWithdraw.api";
import { useState } from "react";
import { CustomerWithdraw } from "types/customerWithdraw";
import { QueryParam } from "types/query";

export interface CustomerWithdrawQuery extends QueryParam {}

interface UseCustomerWithdrawProps {
  initQuery: CustomerWithdrawQuery;
}

export const useCustomerWithdraw = ({
  initQuery,
}: UseCustomerWithdrawProps) => {
  const [data, setData] = useState<CustomerWithdraw[]>([]);
  const [total, setTotal] = useState(0);
  const [query, setQuery] = useState<CustomerWithdrawQuery>(initQuery);
  const [loading, setLoading] = useState(false);

  const fetchData = async () => {
    setLoading(true);
    try {
      const { data } = await customerWithdrawApi.findAll(query);

      setData(data.customerWithdraws);
      setTotal(data.total);
    } finally {
      setLoading(false);
    }
  };

  return {
    customerWithdraws: data,
    total,
    fetchData,
    loading,
    setQuery,
    query,
  };
};
