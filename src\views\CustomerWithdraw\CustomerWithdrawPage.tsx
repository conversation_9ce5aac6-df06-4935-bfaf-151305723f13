import { useCallback, useEffect, useState } from "react";
import { Badge, Card, Tabs } from "antd";
import { getTitle } from "utils";
import {
  CustomerWithdrawStatus,
  CustomerWithdrawStatusTrans,
} from "types/customerWithdraw";
import { customerWithdrawApi } from "api/customerWithdraw.api";
import { useTranslation } from "react-i18next";
import { CustomerWithdrawTab } from "./CustomerWithdrawTab";

export const CustomerWithdrawPage = ({ title = "", isAgent = false }) => {
  const [tabActive, setTabActive] = useState<CustomerWithdrawStatus>(
    CustomerWithdrawStatus.All
  );
  const [summaryReceiptOfStatus, setSummaryReceiptOfStatus] = useState();
  const [lastUpdate, setLastUpdate] = useState(0);
  const { t } = useTranslation();

  console.log({ parentLastUpdate: lastUpdate });

  useEffect(() => {
    document.title = getTitle(t(title));
    fetchSummaryReceipt();
  }, []);

  const fetchSummaryReceipt = useCallback(async () => {
    const res = await customerWithdrawApi.getSummary();

    if (res.status) {
      setSummaryReceiptOfStatus(() => {
        const summary = res.data.reduce(
          (
            prev: any,
            curr: { status: CustomerWithdrawStatus; total: number }
          ) => {
            prev[curr.status] = curr.total;
            prev.ALL = (prev.ALL || 0) + curr.total;
            return prev;
          },
          { ALL: 0 }
        );

        return summary;
      });
    }
  }, [isAgent]);
  const onChangeTab = useCallback((key: CustomerWithdrawStatus) => {
    setTabActive(key as CustomerWithdrawStatus);
  }, []);

  const onLastUpdateChange = useCallback(() => {
    console.log("on last update");
    setLastUpdate((pre) => pre + 1);
  }, []);

  return (
    <Card bodyStyle={{ padding: "8px 20px" }} style={{ borderRadius: "8px" }}>
      <Tabs
        activeKey={tabActive}
        onChange={(key) => onChangeTab(key as CustomerWithdrawStatus)}
        type="line"
        animated={{ inkBar: true, tabPane: true, tabPaneMotion: {} }}
      >
        {Object.values(CustomerWithdrawStatusTrans).map((item) => (
          <Tabs.TabPane
            tab={
              <div className="flex items-center gap-2">
                {t(item.value)}
                {summaryReceiptOfStatus && (
                  <Badge
                    key={item.value}
                    color={CustomerWithdrawStatusTrans[item.value]?.color}
                    count={summaryReceiptOfStatus?.[item.value] || 0}
                  />
                )}
              </div>
            }
            key={item.value}
            tabKey={item.value}
          >
            <CustomerWithdrawTab
              parentLastUpdate={lastUpdate}
              isFocus={tabActive == item.value}
              status={tabActive}
              onSubmitOk={() => {
                onLastUpdateChange();
                fetchSummaryReceipt();
              }}
            />
          </Tabs.TabPane>
        ))}
      </Tabs>
    </Card>
  );
};
