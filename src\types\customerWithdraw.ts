import { Customer } from "./customer";
import { CustomerTransaction } from "./customerTransaction";
import { Staff } from "./staff";

export enum CustomerWithdrawStatus {
  All = "ALL",
  Pending = "PENDING",
  Approve = "APPROVE",
  Reject = "REJECT",
}

export const CustomerWithdrawStatusTrans = {
  [CustomerWithdrawStatus.All]: {
    value: CustomerWithdrawStatus.All,
    color: "blue",
  },
  [CustomerWithdrawStatus.Pending]: {
    value: CustomerWithdrawStatus.Pending,
    color: "orange",
  },
  [CustomerWithdrawStatus.Approve]: {
    value: CustomerWithdrawStatus.Approve,
    color: "green",
  },
  [CustomerWithdrawStatus.Reject]: {
    value: CustomerWithdrawStatus.Reject,
    color: "red",
  },
};

export interface CustomerWithdraw {
  id: number;
  createdAt: number;
  updatedAt: number;
  isDeleted: boolean;
  code: string;
  note: string;
  inspecNote: string;
  amount: number;
  status: CustomerWithdrawStatus;
  inspecAt: number;
  customer: Customer;
  createdStaff: Staff;
  inspecStaff: Staff;
  customerTransactions: CustomerTransaction[];
}
