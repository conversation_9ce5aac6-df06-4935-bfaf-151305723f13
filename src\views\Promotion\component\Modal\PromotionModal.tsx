import {
  <PERSON><PERSON>,
  Col,
  DatePicker,
  Form,
  Input,
  message,
  Modal,
  Row,
  Select,
  Tabs,
} from "antd";
import { useForm, useWatch } from "antd/lib/form/Form";
import { promotionApi } from "api/promotion.api";
import { forwardRef, useImperativeHandle, useState } from "react";
import {
  Promotion,
  PromotionDiscountType,
  PromotionDiscountTypeTrans,
} from "types/promotion";
import { ModalStatus } from "types/modal";
import { SurveyCampaign } from "types/survey";
import { requiredRule } from "utils/validate-rules";
import { InputNumber } from "components/Input/InputNumber";
import { useTranslation } from "react-i18next";
import dayjs from "dayjs";
import TextArea from "antd/es/input/TextArea";
import PromotionProjectTab from "../Tab/PromotionProjectTab";
enum PromotionModalTabKeys {
  PromotionInfo = "1",
  PromotionProject = "2",
}
export interface PromotionModalProps {
  onClose: () => void;
  onSubmitOk: () => void;
  // hasAddPromotionPermission?: boolean;
  // hasUpdatePromotionPermission?: boolean;
}

export interface PromotionModalRef {
  handleUpdate: (promotion: Promotion) => void;
  handleCreate: () => void;
}

export const PromotionModal = forwardRef(
  (
    {
      onSubmitOk,
    }: // hasAddPromotionPermission,
    // hasUpdatePromotionPermission,
    PromotionModalProps,
    ref
  ) => {
    const [form] = useForm();
    const [loading, setLoading] = useState(false);
    const [visible, setVisible] = useState<boolean>();

    const [selectedPromotion, setSelectedPromotion] = useState<Promotion>();
    const [status, setStatus] = useState<ModalStatus>("create");
    const { t } = useTranslation();
    const [activeKey, setActiveKey] = useState<PromotionModalTabKeys>(
      PromotionModalTabKeys.PromotionInfo
    );
    const discountType = useWatch("discountType", form);
    const discountValue = useWatch("discountValue", form);
    const minAmount = useWatch("minAmount", form);

    const handleTabChange = (key: string) => {
      setActiveKey(key as PromotionModalTabKeys);
    };

    useImperativeHandle(
      ref,
      () => ({
        handleUpdate,
        handleCreate,
      }),
      []
    );

    const handleUpdate = (promotion: Promotion) => {
      form.setFieldsValue({
        ...promotion,
        date: [dayjs.unix(promotion.startAt), dayjs.unix(promotion.endAt)],
      });
      setSelectedPromotion(promotion);
      setStatus("update");
      setVisible(true);
    };

    const handleCreate = () => {
      setStatus("create");
      setVisible(true);
    };

    const handleSubmitForm = async () => {
      await form.validateFields();
      const { date, ...dataForm } = form.getFieldsValue();
      if (dataForm.name) {
        dataForm.name = dataForm.name.trim();
      }
      const payload = {
        promotion: {
          ...dataForm,
          startAt: date ? dayjs(date[0]).startOf("day").unix() : null,
          endAt: date ? dayjs(date[1]).endOf("day").unix() : null,
        },
      };

      try {
        setLoading(true);
        switch (status) {
          case "update":
            await promotionApi.update(selectedPromotion?.id || 0, payload);
            message.success(t("actionSuccessfully"));
            break;

          //create
          default:
            await promotionApi.create(payload);
            message.success(t("actionSuccessfully"));
            break;
        }
        setVisible(false);
        onSubmitOk();
      } catch (error) {
      } finally {
        setLoading(false);
        // onSubmitOk();
      }
    };

    return (
      <Modal
        onCancel={() => {
          setVisible(false);
          setActiveKey(PromotionModalTabKeys.PromotionInfo);
        }}
        visible={visible}
        centered
        title={
          <h1 className="mb-0 text-lg text-primary font-bold">
            {status == "create" ? t("create") : t("update")}
          </h1>
        }
        confirmLoading={loading}
        destroyOnClose
        width={activeKey == PromotionModalTabKeys.PromotionInfo ? 900 : 1200}
        onOk={handleSubmitForm}
        afterClose={() => {
          form.resetFields();
        }}
        okText={t("save")}
        // okButtonProps={{
        //   hidden:
        //     (!hasAddPromotionPermission && status == "create") ||
        //     (!hasUpdatePromotionPermission && status == "update"),
        // }}
        footer={
          activeKey == PromotionModalTabKeys.PromotionInfo ? (
            <>
              <Button onClick={() => setVisible(false)}>{t("close")}</Button>
              <Button
                // disabled={selectedSurvey && selectedSurvey?.totalSubmit > 0}
                type="primary"
                onClick={() => handleSubmitForm()}
                loading={loading}
                //   hidden={type === "review"}
              >
                {t("save")}
              </Button>
            </>
          ) : null
        }
      >
        <Tabs activeKey={activeKey} onChange={handleTabChange}>
          <Tabs.TabPane
            tab={t("voucherInfo")}
            key={PromotionModalTabKeys.PromotionInfo}
          >
            <Form
              form={form}
              layout="vertical"
              initialValues={{ discountType: PromotionDiscountType.Percent }}
              validateTrigger={["onBlur", "onChange"]}
            >
              <Row gutter={[12, 0]}>
                <Col span={12}>
                  <Form.Item
                    className="!mb-2"
                    name="code"
                    label={t("code")}
                    rules={[requiredRule]}
                  >
                    <Input minLength={3} maxLength={15} />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    className="!mb-2"
                    name="title"
                    label={t("promotionName")}
                    rules={[requiredRule]}
                  >
                    <Input />
                  </Form.Item>
                </Col>
                <Col span={24}>
                  <Form.Item
                    className="!mb-2"
                    name="description"
                    label={t("description")}
                    // rules={[requiredRule]}
                    //   noStyle
                  >
                    <TextArea
                      className="mb-2"
                      size="large"
                      //   value={quantity}
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    className="!mb-2"
                    name="date"
                    label={t("voucherPeriod")}
                    rules={[
                      {
                        required: true,
                      },
                    ]}
                  >
                    <DatePicker.RangePicker
                      size="large"
                      className="!w-full"
                      //   ranges={dateRanges}
                      format="DD/MM/YYYY"
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="discountValue"
                    label={t("discount")}
                    required
                    rules={[requiredRule]}
                    shouldUpdate
                  >
                    <InputNumber
                      addonAfter={
                        <Select
                          className="[&_.ant-select-selector]:!bg-transparent"
                          value={discountType}
                          onChange={(value) => {
                            form.setFieldValue("discountType", value);
                            form.setFieldValue("discountValue", undefined);
                          }}
                          options={Object.values(
                            PromotionDiscountTypeTrans
                          ).map((item) => {
                            return {
                              label: (
                                <div>
                                  <span className="">{item.label}</span>
                                </div>
                              ),
                              value: item.value,
                            };
                          })}
                        >
                          {/* <Option value="%">%</Option>
                          <Option
                            value={userStore.info.merchant?.priceCurrencyCode}
                          >
                            {userStore.info.merchant?.priceCurrencyCode &&
                              PriceCurrencyCodeTypeTrans[
                                userStore.info.merchant?.priceCurrencyCode
                              ].label}
                          </Option> */}
                        </Select>
                      }
                      //   placeholder={t("enterPercent")}
                    />
                  </Form.Item>
                  <Form.Item name="discountType" hidden></Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    className="!mb-2"
                    name="quantity"
                    label={t("promotionQuantity")}
                    rules={[requiredRule]}
                    //   noStyle
                  >
                    <InputNumber
                      className="mb-2"
                      size="large"
                      //   value={quantity}
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    className="!mb-2"
                    name="minAmount"
                    label={t("minAmountOrder")}
                    rules={[
                      requiredRule,
                      ({ getFieldValue }) => ({
                        validator(_, value) {
                          debugger;
                          if (
                            discountType === PromotionDiscountType.Fixed &&
                            +value < +discountValue
                          ) {
                            return Promise.reject(
                              new Error(t("validateMinAmount"))
                            );
                          }
                          return Promise.resolve();
                        },
                      }),
                    ]} //   noStyle
                  >
                    <InputNumber
                      className="mb-2"
                      size="large"
                      //   value={quantity}
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    className="!mb-2"
                    name="usePerOne"
                    label={t("maxPerOne")}
                    rules={[requiredRule]}
                    //   noStyle
                  >
                    <InputNumber
                      className="mb-2"
                      size="large"
                      //   value={quantity}
                    />
                  </Form.Item>
                </Col>
              </Row>
            </Form>
          </Tabs.TabPane>
          {status == "update" && (
            <>
              {selectedPromotion?.id && (
                <Tabs.TabPane
                  tab={t("orderApplied")}
                  key={PromotionModalTabKeys.PromotionProject}
                >
                  <PromotionProjectTab promotionId={selectedPromotion?.id} />
                </Tabs.TabPane>
              )}
            </>
          )}
        </Tabs>
      </Modal>
    );
  }
);
