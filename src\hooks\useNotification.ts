import { notificationApi } from "api/notification.api";
import { useState } from "react";
import { Notification } from "types/notification";
import { QueryParam } from "types/query";

export interface NotificationQuery extends QueryParam {}

interface UseNotificationProps {
  initQuery: NotificationQuery;
}

export const useNotification = ({ initQuery }: UseNotificationProps) => {
  const [data, setData] = useState<Notification[]>([]);
  const [total, setTotal] = useState(0);
  const [query, setQuery] = useState<NotificationQuery>(initQuery);
  const [loading, setLoading] = useState(false);

  const fetchData = async () => {
    setLoading(true);
    try {
      const { data } = await notificationApi.findAll(query);

      setData(data.notifications);
      setTotal(data.total);
    } finally {
      setLoading(false);
    }
  };

  return {
    notifications: data,
    totalNotification: total,
    fetchNotification: fetchData,
    loadingNotification: loading,
    setQueryNotification: setQuery,
    queryNotification: query,
  };
};
