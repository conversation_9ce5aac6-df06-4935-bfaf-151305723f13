import {
  DownOutlined,
  EditOutlined,
  LockOutlined,
  UnlockOutlined,
} from "@ant-design/icons";
import { Button, Popconfirm, Space, Table } from "antd";
import Column from "antd/lib/table/Column";
import { IPagination, Pagination } from "components/Pagination";
import { AriviTable } from "components/Table/AriviTable";
import DropdownCell from "components/Table/DropdownCell";
import { useTranslation } from "react-i18next";
import { GrEdit } from "react-icons/gr";
import { HiOutlineTrash } from "react-icons/hi2";
import { Faq } from "types/faq";
import { formatVND } from "utils";
import { unixToFullDate } from "utils/dateFormat";

interface PropsType {
  dataSource: Faq[];
  loading: boolean;
  loadingDelete?: boolean;
  pagination?: IPagination;
  onEdit?: (faq: Faq) => void;
  onDelete?: (faqId: number) => void;
  onActive?: (faqId: number) => void;
  onInactive?: (faqId: number) => void;

  // hasDeleteFaqPermission?: boolean;
  // hasUpdateFaqPermission?: boolean;
}

export const FaqList = ({
  dataSource,
  loading,
  loadingDelete,
  pagination,
  onDelete,
  onEdit,
  onActive,
  onInactive,
}: // hasDeleteFaqPermission,
  // hasUpdateFaqPermission,
  PropsType) => {
  const { t } = useTranslation();

  return (
    <div>
      <AriviTable
        // bordered
        scroll={{ x: "max-content", y: "calc(100vh - 310px)" }}
        loading={loading}
        pagination={false}
        rowKey="id"
        dataSource={dataSource}
        className="custom-scrollbar"
      // onChange={}
      >
        <Column
          title={t("position")}
          dataIndex="position"
          width={100}
          key={"position"}
          render={(_, record: Faq) => (
            <div>
              {record.position}
            </div>
          )}
        />
        <Column
          title={t("faqName")}
          dataIndex="name"
          width={500}
          key={"name"}
          render={(text, record: Faq) => (
            <div>
              <div>
                <span className="font-bold">{t("vietnamese")}</span>:{" "}
                {record.title}
              </div>
              <div>
                <span className="font-bold">{t("english")}</span>:{" "}
                {record.titleEn}
              </div>
            </div>
          )}
        />
        <Column
          title={t("faqContent")}
          width={800}
          dataIndex="name"
          key={"name"}
          render={(text, record: Faq) => (
            <div>
              <div>
                <span className="font-bold">{t("vietnamese")}</span>:{" "}
                {record.content}
              </div>
              <div>
                <span className="font-bold">{t("english")}</span>:{" "}
                {record.contentEn}
              </div>
            </div>
          )}
        />
        <Column
          title={t("faqCategory")}
          width={300}
          dataIndex="faqCategory"
          key={"faqCategory"}
          render={(text, record: Faq) => (
            <div>
              <div>
                <span className="font-bold">{t("vietnamese")}</span>:{" "}
                {record.faqCategory.name}
              </div>
              <div>
                <span className="font-bold">{t("english")}</span>:{" "}
                {record.faqCategory.nameEn}
              </div>
            </div>
          )}
        />

        <Column
          fixed="right"
          width={120}
          align="center"
          title=""
          key="action"
          dataIndex={""}
          render={(text, record: Faq) => (
            //@ts-ignore
            <DropdownCell
              text={t("action")}
              items={[
                {
                  onClick: () => "",
                  // createOrderModalRef.current?.handleUpdate(record),
                  label: (
                    <Button
                      icon={<GrEdit />}
                      type="primary"
                      className="w-full justify-center !flex !items-center gap-2 !font-medium"
                      onClick={() => onEdit?.(record)}
                    >
                      {t("update")}
                    </Button>
                  ),
                  key: "update",
                  // hidden: !hasUpdateFaqPermission,
                },

                {
                  label: (
                    <Popconfirm
                      placement="topLeft"
                      title={
                        <div>
                          <h1 className="text-sm">{t("confirm?")}</h1>
                        </div>
                      }
                      onConfirm={() => onDelete?.(record.id)}
                      okText={t("yes")}
                      cancelText={t("no")}
                    >
                      <Button
                        loading={loadingDelete}
                        icon={<HiOutlineTrash className="text-lg" />}
                        className={`w-full justify-center !flex !items-center gap-2 !text-red-500 !font-medium`}
                      >
                        {t("delete")}
                      </Button>
                    </Popconfirm>
                  ),
                  key: "delete",
                  // hidden: !hasDeleteFaqPermission,
                },
              ]}
              trigger={["click"]}
            >
              <a onClick={(e) => e.preventDefault()}>
                <Space>
                  {t("action")}
                  <DownOutlined />
                </Space>
              </a>
            </DropdownCell>
          )}
        />
      </AriviTable>
      {pagination && <Pagination {...pagination} />}
    </div>
  );
};
