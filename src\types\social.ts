import { Staff } from "./staff";

export enum SocialType {
  Youtube = "YOUTUBE",
}

export const SocialTypeTrans = {
  [SocialType.Youtube]: {
    label: "Youtube",
    value: SocialType.Youtube,
  },
};

export interface Social {
  createdAt: number;
  id: number;
  updatedAt: number;
  deletedBy: string;
  isDeleted: boolean;
  deletedAt: number;
  name: string;
  url: string;
  image: string;
  type: SocialType;
  isVisible: boolean;
  createdStaff: Staff;
  no: number;
}
