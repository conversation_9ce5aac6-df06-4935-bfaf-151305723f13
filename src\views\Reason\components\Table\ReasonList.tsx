import {
  DownOutlined,
  EditOutlined,
  LockOutlined,
  UnlockOutlined,
} from "@ant-design/icons";
import { Button, Image, Popconfirm, Space, Table } from "antd";
import Column from "antd/lib/table/Column";
import { IPagination, Pagination } from "components/Pagination";
import { AriviTable } from "components/Table/AriviTable";
import DropdownCell from "components/Table/DropdownCell";
import { useTranslation } from "react-i18next";
import { GrEdit } from "react-icons/gr";
import { HiOutlineTrash } from "react-icons/hi2";
import { Reason } from "types/reason";
import { formatVND } from "utils";
import { unixToFullDate } from "utils/dateFormat";

interface PropsType {
  dataSource: Reason[];
  loading: boolean;
  loadingDelete?: boolean;
  pagination?: IPagination;
  onEdit?: (reason: Reason) => void;
  onDelete?: (reasonId: number) => void;
  onActive?: (reasonId: number) => void;
  onInactive?: (reasonId: number) => void;

  // hasDeleteReasonPermission?: boolean;
  // hasUpdateReasonPermission?: boolean;
}

export const ReasonList = ({
  dataSource,
  loading,
  loadingDelete,
  pagination,
  onDelete,
  onEdit,
  onActive,
  onInactive,
}: // hasDeleteReasonPermission,
// hasUpdateReasonPermission,
PropsType) => {
  const { t } = useTranslation();

  return (
    <div>
      <AriviTable
        loading={loading}
        pagination={false}
        rowKey="id"
        dataSource={dataSource}
        size="small"
        className="custom-scrollbar"
        scroll={{ x: "max-content", y: "calc(100vh - 380px)" }}
        // onChange={}
      >
        <Column
          title={t("reason")}
          dataIndex="name"
          key={"name"}
          render={(text, record: Reason) => (
            <div>
              <div>
                <span className="font-bold">{t("vietnamese")}</span>:{" "}
                {record.name}
              </div>
              <div>
                <span className="font-bold">{t("english")}</span>:{" "}
                {record.nameEn}
              </div>
            </div>
          )}
        />

        <Column
          title={t("createdAt")}
          dataIndex="name"
          key={"name"}
          render={(text, record: Reason) => (
            <span>{unixToFullDate(record.createdAt)}</span>
          )}
        />

        <Column
          fixed="right"
          width={120}
          align="center"
          title=""
          key="action"
          dataIndex={""}
          render={(text, record: Reason) => (
            //@ts-ignore
            <DropdownCell
              text={t("action")}
              items={[
                {
                  onClick: () => "",
                  // createOrderModalRef.current?.handleUpdate(record),
                  label: (
                    <Button
                      icon={<GrEdit />}
                      type="primary"
                      className="w-full justify-center !flex !items-center gap-2 !font-medium"
                      onClick={() => onEdit?.(record)}
                    >
                      {t("update")}
                    </Button>
                  ),
                  key: "update",
                  // hidden: !hasUpdateReasonPermission,
                },

                {
                  label: (
                    <Popconfirm
                      placement="topLeft"
                      title={
                        <div>
                          <h1 className="text-sm">{t("confirm?")}</h1>
                        </div>
                      }
                      onConfirm={() => onDelete?.(record.id)}
                      okText={t("yes")}
                      cancelText={t("no")}
                    >
                      <Button
                        loading={loadingDelete}
                        icon={<HiOutlineTrash className="text-lg" />}
                        className={`w-full justify-center !flex !items-center gap-2 !text-red-500 !font-medium`}
                      >
                        {t("delete")}
                      </Button>
                    </Popconfirm>
                  ),
                  key: "delete",
                  // hidden: !hasDeleteReasonPermission,
                },
              ]}
              trigger={["click"]}
            >
              <a onClick={(e) => e.preventDefault()}>
                <Space>
                  {t("action")}
                  <DownOutlined />
                </Space>
              </a>
            </DropdownCell>
          )}
        />
      </AriviTable>
      {pagination && <Pagination {...pagination} />}
    </div>
  );
};
