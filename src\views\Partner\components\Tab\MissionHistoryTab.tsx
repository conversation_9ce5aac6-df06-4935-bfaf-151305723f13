// SurveyActivityLogTab.js
import {
  Button,
  Input,
  message,
  Popconfirm,
  Progress,
  Select,
  Space,
  Table,
  Tag,
} from "antd";
import { FormInstance } from "antd/es/form";
import Column from "antd/lib/table/Column";

import { unixToFullDate } from "utils/dateFormat";
import { useEffect, useRef, useState } from "react";
import { Pagination } from "components/Pagination";
import { useProject } from "hooks/useProject";
import { Project, ProjectStatus, ProjectStatusTrans } from "types/project";
import { useTranslation } from "react-i18next";
import { AriviTable } from "components/Table/AriviTable";
import { PaymentStatus, PaymentStatusTrans } from "types/payment";
import { useHandleCustomer } from "hooks/useHandleCustomer";
import {
  DownOutlined,
  LockOutlined,
  PlusOutlined,
  SearchOutlined,
  StopOutlined,
  UnlockOutlined,
} from "@ant-design/icons";
import { projectApi } from "api/project.api";
import { formatVND } from "utils";
import { useSearchParams } from "react-router-dom";
import {
  ProjectModal,
  ProjectModalRef,
} from "views/ProjectListPage/ProjectModal";
import DropdownCell from "components/Table/DropdownCell";
import { useReview } from "hooks/useReview";
import { Review, ReviewStatusTrans } from "types/review";
import { IconLink } from "@tabler/icons-react";
import { MissionStatusComp } from "components/MissionStatus/MissionStatusComp";

interface PropTypes {
  partnerId?: number;
}
const MissionHistoryTab = ({ partnerId }: PropTypes) => {
  const {
    reviewHistory: reviews,
    fetchReviewHistory: fetchReview,
    loadingReviewHistory: loadingReview,
    queryReviewHistory: queryReview,
    totalReviewHistory: totalReview,
    setQueryReviewHistory: setQueryReview,
  } = useReview({
    initQuery: {
      page: 1,
      limit: 5,
      partnerId: partnerId ? partnerId : undefined,
    },
  });
  useEffect(() => {
    fetchReview();
  }, []);
  const { t } = useTranslation();

  const [loadingPay, setLoadingPay] = useState(false);
  const [loadingDelete, setLoadingDelete] = useState(false);
  const [searchParams, setSearchParams] = useSearchParams();
  const modalRef = useRef<ProjectModalRef>(null);

  // const sortedReviews = reviews.sort((a, b) => {
  //   return new Date(b.assignAt).getTime() - new Date(a.assignAt).getTime();
  // });

  return (
    <div>
      <div className="filter-container">
        <Space wrap>
          {/* <div className="filter-item ml-2 md:ml-0">
              <label htmlFor="">{t("customer")}</label>
              <br />
              <Select
                showSearch
                onSearch={(value) => debounceSearchCustomer(value)}
                style={{ width: 250 }}
                allowClear
                onChange={(value) => {
                  queryProject.page = 1;
                  if (value === undefined) {
                    queryProject.partnerId = null;
                  } else {
                    queryProject.partnerId = value;
                  }
                  fetchProject();
                  fetchSummary();
                }}
                options={customers?.map((item) => {
                  return {
                    label: (
                      <div>
                        <span className="">{item.name}</span>
                      </div>
                    ),
                    value: item.id,
                  };
                })}
                filterOption={false}
                placeholder={t("selectCustomer")}
              />
            </div> */}

          {/* <div className="filter-item btn">
              <Button
                onClick={() => {
                  navigate("/project/project-create");
                }}
                type="primary"
                icon={<PlusOutlined />}
              >
                {t("create")}
              </Button>
            </div> */}
        </Space>
      </div>

      <AriviTable
        pagination={false}
        rowKey="id"
        dataSource={reviews}
        scroll={{ x: "max-content" }}
      >
        <Column
          title={t("completedAt")}
          dataIndex="submittedAt"
          key="submittedAt"
          render={(submittedAt, record: Review) => {
            return (
              <div>
                <div>{unixToFullDate(submittedAt)}</div>
              </div>
            );
          }}
        />
        <Column
          title={t("projectCode")}
          render={(_, record: Review) => (
            <div>
              <div>{record.project.code}</div>
              <div className="mt-2">
                <MissionStatusComp status={record.status} />
                {/* <Tag color={ReviewStatusTrans[record.status as keyof typeof ReviewStatusTrans]?.color}>
                  {t(record.status)}
                </Tag> */}
              </div>
            </div>
          )}
        />
        <Column
          title={t("projectName")}
          width={150}
          render={(_, record: Review) => <div>{record.project?.name}</div>}
        />
        <Column
          title={t("partner")}
          width={200}
          render={(_, record: Review) => <div>{record.partner?.fullName}</div>}
        />
        <Column
          title={t("mapUrl")}
          width={175}
          render={(_, record: Review) => (

            <a
              href={record.project.mapUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-600 hover:underline block "
            >
              <IconLink
                width={22}
                className="flex-shrink-0 cursor-pointer hover:text-primary"
              />
            </a>
          )}
        />

        <Column
          title={t("reviewUrl")}
          width={140}
          render={(_, record: Review) => (
            <a
              href={record.reviewUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-600 hover:underline block "
            >
              <IconLink
                width={22}
                className="flex-shrink-0 cursor-pointer hover:text-primary"
              />
            </a>
          )}
        />

        <Column
          title={t("reviewContent")}
          render={(_, record: Review) => (
            <div className="w-[300px]">{record.content}</div>
          )}
        />
        {/* <Column
              title={t("status")}
              render={(_, record: Review) => (
                <div>
                  <Tag color={ReviewStatusTrans[record.status]?.color}>
                    {t(record.status)}
                  </Tag>
                </div>
              )}
            /> */}
        <Column
          title={t("rewardPoint") + "(vnd)"}
          render={(_, record: Review) => (
            <div>{formatVND(record.rewardPoint)}</div>
          )}
          align="right"
        />
        <Column
          title={t("note")}
          render={(_, record: Review) => <div>{record.note}</div>}
        />
      </AriviTable>


      {
        totalReview > queryReview.limit &&
        <Pagination
          defaultPageSize={queryReview.limit}
          currentPage={queryReview.page}
          total={totalReview}
          onChange={({ limit, page }) => {
            queryReview.page = page;
            queryReview.limit = limit;
            setQueryReview({ ...queryReview });
            fetchReview();
          }}
        />
      }
    </div>
  );
};

export default MissionHistoryTab;
