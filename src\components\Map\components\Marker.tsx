import React, { useMemo } from "react";
import PinIMG from "assets/images/pin.png";
import { Project } from "types/project";
import { Partner } from "types/partner";
import { Button, Popover, Rate } from "antd";
import { Typography } from "@mui/material";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import clsx from "clsx";
import { ReactComponent as CodeIcon } from "assets/svgs/code-icon.svg";
import { ReactComponent as LocationIcon } from "assets/svgs/location-icon.svg";
import PingParnert from "assets/svgs/ping-partner.svg";
import PingCustomer from "assets/svgs/ping-customer.svg";
export const Marker = React.memo(
  ({
    lat,
    lng,
    partner,
    project,
    isCluster,
    pointCount,
    clusterSize,
    clusterClassName,
    viewPartner = false
  }: {
    lat: number;
    lng: number;
    project?: Project;
    partner?: Partner;
    isCluster?: boolean;
    pointCount?: number;
    clusterSize?: { width: number; height: number };
    clusterClassName?: string;
    viewPartner?: boolean
  }) => {
    const { t } = useTranslation();
    const navigate = useNavigate();

    const popoverContent = useMemo(() => {
      if (project) {
        return (
          <div className="w-[206px] ">
            <label className="text-[#2A3547] pb-2 text-[14px] !font-[600]">
              {project.name}
            </label>
            <div className="flex items-center gap-1 mt-2">
              <CodeIcon className="w-[16px] h-[16px]" />
              <label className="text-[12px] !font-[400]">{project.code}</label>
            </div>

            <div className="flex items-start gap-1">
              <LocationIcon className="w-[18px] h-[18px] -ml-[1px] min-w-[18px]" />
              <label className="text-[12px] !font-[400]">{project.address}</label>
            </div>
            <div className="flex gap-2 justify-between">
              <div className="flex gap-1 items-center text-[10px]">
                <Rate
                  value={project.totalStar}
                  className="text-[10px]"
                  allowHalf
                  disabled
                />{" "}
                ({project.totalStar})
              </div>
              <Button size="small" color="primary" variant="solid" onClick={() => {
                window.open(
                  `/project/project-list?projectId=${project.id}`,
                  "_blank"
                );
              }}>
                <label className="text-[10px] !font-[400] text-[#FFFFFF]">{t("viewDetail")}</label>
              </Button>

            </div>
          </div>
        );
      }
      if (partner) {
        return (
          <div className="w-[206px]">
            <label className="text-[#2A3547] pb-2 text-[14px] !font-[600]">
              {partner.fullName}
            </label>
            <div className="flex items-center gap-ư mt-2">
              <CodeIcon className="w-[16px] h-[16px] mr-1" />
              <label className="text-[12px] !font-[400]">{partner.code}</label>
            </div>

            <div className="flex items-start gap-1">
              <LocationIcon className="w-[18px] h-[18px] -ml-[1px] min-w-[18px]" />
              <label className="text-[12px] !font-[400]">{partner.rank.name}</label>
            </div>
            <div className="flex gap-2 justify-between mt-1">
              <Button size="small" color="primary" variant="solid" onClick={() => {
                window.open(
                  `/partner/partner-list?search=${partner.code}`,
                  "_blank"
                );
              }}>
                <label className="text-[10px] !font-[400] text-[#FFFFFF]">{t("viewDetail")}</label>
              </Button>
            </div>
          </div>
        );
      }

      return <></>;
    }, [project, partner]);

    return isCluster ? (
      <div
        className={clsx(
          "flex items-center justify-center font-bold rounded-full p-4 bg-[#FF766B]",
          clusterClassName
        )}
        style={{
          width: `${clusterSize?.width}px`,
          height: `${clusterSize?.height}px`,
        }}
      >
        {pointCount}
      </div>
    ) : project || partner ? (
      <Popover content={popoverContent}>
        <div
          className="test-marker"
          style={{
            position: "absolute",
            top: "100%",
            left: "50%",
            transform: "translate(-50%, -100%)",
            zIndex: 1000,
          }}
        >
          {!viewPartner ? <img src={PingCustomer} style={{ width: 22, height: 29 }} alt="" /> : <img src={PingParnert} style={{ width: 22, height: 29 }} alt="" />}
        </div>
      </Popover>
    ) : (
      <div
        className="test-marker"
        style={{
          position: "absolute",
          top: "100%",
          left: "50%",
          transform: "translate(-50%, -100%)",
          zIndex: 1000,
        }}
      >
        <img src={PinIMG} style={{ width: 40 }} alt="" />
      </div>
    );
  }
);
