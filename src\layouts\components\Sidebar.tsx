import { Box, Drawer, useMediaQuery, useTheme } from "@mui/material";
import Scrollbar from "components/Common/Scrollbar";
import Logo from "components/Logo/Logo";
import { observer } from "mobx-react";
import { appStore } from "store/appStore";
import { SidebarProfile } from "./SidebarProfile";
import SidebarItems from "./SidebarItems";
import { settings } from "settings";

export const Sidebar = observer(() => {
  const lgUp = useMediaQuery((theme: any) => theme.breakpoints.up("lg"));
  const customizer = appStore.customizer;
  const theme = useTheme();
  const toggleWidth =
    customizer.isCollapse && !customizer.isSidebarHover ? 97 : 264;

  const onHoverEnter = () => {
    if (customizer.isCollapse) {
      appStore.hoverSidebar(true);
    }
  };

  const onHoverLeave = () => {
    appStore.hoverSidebar(false);
  };

  if (lgUp) {
    return (
      <Box
        sx={{
          width: toggleWidth,
          flexShrink: 0,
          ...(customizer.isCollapse && {
            position: "absolute",
          }),
        }}
      >
        {/* ------------------------------------------- */}
        {/* Sidebar for desktop */}
        {/* ------------------------------------------- */}
        <Drawer
          anchor="left"
          open
          onMouseEnter={onHoverEnter}
          onMouseLeave={onHoverLeave}
          variant="permanent"
          slotProps={{
            paper: {
              sx: {
                transition: theme.transitions.create("width", {
                  duration: theme.transitions.duration.shortest,
                }),
                width: toggleWidth,
                boxSizing: "border-box",
                minWidth: "97px",
              },
            },
          }}
        >
          {/* ------------------------------------------- */}
          {/* Sidebar Box */}
          {/* ------------------------------------------- */}
          <Box
            sx={{
              height: "100%",
            }}
          >
            {/* ------------------------------------------- */}
            {/* Logo */}
            {/* ------------------------------------------- */}
            <Box px={"24px"} py={"22px"}>
              <Logo className="w-full h-full" />
            </Box>
            <Scrollbar sx={{ height: "calc(100% - 180px)", marginTop: "24px" }}>
              {/* ------------------------------------------- */}
              {/* Sidebar Items */}
              {/* ------------------------------------------- */}
              <SidebarItems />
            </Scrollbar>
            <div className="absolute bottom-0 left-1/2 -translate-x-1/2 opacity-50 text-center text-xs">
              v{settings.version}
            </div>
            <SidebarProfile />
          </Box>
        </Drawer>
      </Box>
    );
  }

  return (
    <Drawer
      anchor="left"
      open={customizer.isMobileSidebar}
      onClose={() => appStore.toggleMobileSidebar()}
      variant="temporary"
      slotProps={{
        paper: {
          sx: {
            width: customizer.SidebarWidth,
            border: "0 !important",
            boxShadow: (theme) => theme.shadows[8],
          },
        },
      }}
    >
      {/* ------------------------------------------- */}
      {/* Logo */}
      {/* ------------------------------------------- */}
      <Box px={"24px"} py={"22px"}>
        <Logo className="w-full h-full" />
      </Box>
      {/* ------------------------------------------- */}
      {/* Sidebar For Mobile */}
      {/* ------------------------------------------- */}
      <SidebarItems />
    </Drawer>
  );
});

// export const Sidebar = observer(({ collapsed }: { collapsed: boolean }) => {
//   const [defaultOpenKeys, setDefaultOpenKeys] = useState<string[]>([]);
//   const location = useLocation();
//   const [isLoaded, setIsLoaded] = useState(false);

//   useEffect(() => {
//     const routes = permissionStore.accessRoutes;
//     let firstRoute = routes.find(
//       (route) => (route.isAccess || !settings.checkPermission) && route.children
//     );
//     if (
//       firstRoute &&
//       firstRoute.path &&
//       location.pathname.includes(firstRoute.path)
//     ) {
//       setDefaultOpenKeys(() => [firstRoute?.path || ""]);
//     }
//     setIsLoaded(true);
//   }, []);

//   // console.log(
//   //   "permissionStore.accessRoutes",
//   //   toJS(permissionStore.accessRoutes)
//   // );

//   return (
//     <Sider trigger={null} collapsible collapsed={collapsed} width={270}>
//       <div className="logo p-2">
//         <img src={logo} alt="" className="w-[50px] h-[50px] object-contain" />
//       </div>

//       {isLoaded && (
//         <Menu
//           className="sidebar custom-scrollbar"
//           theme="light"
//           mode="inline"
//           inlineCollapsed={collapsed}
//           selectedKeys={[location.pathname]}
//           defaultOpenKeys={defaultOpenKeys}
//           forceSubMenuRender
//           // forceSubMenuRender
//         >
//           {permissionStore.accessRoutes
//             .filter((route) => {
//               if (
//                 !route.hidden &&
//                 (route.isAccess || !settings.checkPermission)
//               ) {
//                 if ($isShowGameRouter) {
//                   // console.log($isShowGameRouter);
//                   return true;
//                 } else {
//                   if (route?.isGameRouter) {
//                     return false;
//                   } else {
//                     return true;
//                   }
//                 }
//               }
//             })

//             .map((route) => {
//               if (
//                 route.path == "/telegramLog" &&
//                 userStore.info.username !== "develop"
//               ) {
//                 return null; // Ẩn menu nếu path là telegramLog và userName khác develop
//               }
//               if (route.children?.length) {
//                 const filterElementRoutes = route.children.filter(
//                   (item) => item.element && item.isAccess
//                 );
//                 const routeJs = JSON.parse(JSON.stringify(route));
//                 // if (route.path?.includes("/customer")) debugger;
//                 if (filterElementRoutes.length == 1) {
//                   // debugger;
//                   const element = filterElementRoutes[0];
//                   const path = `${route.path!}/${element.path}`.trim() || "";
//                   return (
//                     <Menu.Item
//                       icon={route.icon}
//                       key={path}
//                       // onClick={() => console.log("path ne", element.path)}
//                     >
//                       <Link to={path}>{route.title}</Link>
//                     </Menu.Item>
//                   );
//                 } else if (!filterElementRoutes.length) return;

//                 return (
//                   <SubMenu
//                     key={route.path}
//                     icon={route.icon}
//                     title={route.title}
//                   >
//                     {route.children
//                       ?.filter(
//                         (child) => child.isAccess || !settings.checkPermission
//                       )
//                       .filter((item) => !item.hidden)
//                       .map((item) => {
//                         return (
//                           <Menu.Item
//                             icon={item.icon}
//                             key={route.path + "/" + item.path}
//                           >
//                             {route.path && item.path && (
//                               <Link to={route.path + "/" + item.path}>
//                                 {item.title}{" "}
//                               </Link>
//                             )}
//                           </Menu.Item>
//                         );
//                       })}
//                   </SubMenu>
//                 );
//               }
//               return (
//                 <Menu.Item
//                   icon={route.icon}
//                   key={route.path}
//                   // onClick={() => console.log("path ne", route.path)}
//                 >
//                   <Link to={route.path || ""}>{route.title}</Link>
//                 </Menu.Item>
//               );
//             })}
//         </Menu>
//       )}
//     </Sider>
//   );
// });
