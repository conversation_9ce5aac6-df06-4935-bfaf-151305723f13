// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
import {
  Avatar,
  IconButton,
  Menu,
  MenuItem,
  Stack,
  Typography,
  useMediaQuery,
} from "@mui/material";
import i18nInstance, { langResources } from "config-translation";
import dayjs from "dayjs";
import { observer } from "mobx-react";
import React, { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import FlagCn from "src/assets/images/flag/icon-flag-cn.svg";
import FlagEn from "src/assets/images/flag/icon-flag-en.svg";
import FlagFr from "src/assets/images/flag/icon-flag-fr.svg";
import FlagSa from "src/assets/images/flag/icon-flag-sa.svg";
import { appStore } from "store/appStore";
import { userStore } from "store/userStore";

const Language = () => {
  const [anchorEl, setAnchorEl] = React.useState(null);
  const open = Boolean(anchorEl);
  const lgDown = useMediaQuery((theme: any) => theme.breakpoints.down("lg"));
  const currentLang = langResources.vi;

  const handleClick = (event: any) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };
  useEffect(() => {
    i18nInstance.changeLanguage(currentLang.value);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleUpdateLanguage = async (language: string) => {
    try {
      // const res = await employeeApi.updateLanguage(
      //   { language: language.toUpperCase() }
      //   // token
      // );
      await userStore.changeLanguage(language);
      i18nInstance.changeLanguage(language);
      dayjs.locale(language);
      localStorage.setItem("lng", language);
      window.location.reload();
      // appStore.refetchNoti = true;
    } catch (error) {}
  };

  return (
    <>
      <IconButton
        aria-label="more"
        id="long-button"
        aria-controls={open ? "long-menu" : undefined}
        aria-expanded={open ? "true" : undefined}
        aria-haspopup="true"
        onClick={handleClick}
        sx={{
          gap: "8px",
          border: "1px solid #EAEFF4",
          borderRadius: "100px",
        }}
      >
        <Avatar
          src={currentLang.icon}
          alt={currentLang.value}
          sx={{
            width: lgDown ? 20 : 24,
            height: lgDown ? 20 : 24,
          }}
        />
        <div className="text-[14px] hidden md:block">
          {langResources[currentLang.value as keyof typeof langResources]?.text}
        </div>
      </IconButton>
      <Menu
        id="long-menu"
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        sx={{
          "& .MuiMenu-paper": {
            width: "200px",
          },
        }}
      >
        {Object.values(langResources).map((option, index) => (
          <MenuItem
            key={index}
            sx={{ py: 2, px: 3 }}
            onClick={() => handleUpdateLanguage(option.value)}
          >
            <Stack direction="row" spacing={1} alignItems="center">
              <Avatar
                src={option.icon}
                alt={option.icon}
                sx={{ width: 20, height: 20 }}
              />
              <div className=""> {option.text}</div>
            </Stack>
          </MenuItem>
        ))}
      </Menu>
    </>
  );
};

export default observer(Language);
