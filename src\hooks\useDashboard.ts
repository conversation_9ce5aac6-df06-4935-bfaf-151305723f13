import { useState } from "react";
import { dashboardApi } from "api/dashboard.api";
import { reviewApi } from "api/review.api";
import { message } from "antd";
import { useTranslation } from "react-i18next";
import { QueryParam } from "types/query";
import { PartnerSummary, ProjectData, RankSummary, ReviewTotalSummary, SummaryProfitItem, SummaryStatistical, TopCustomer, TopProduct } from "types/dashboard";
import { ReviewStatusSummary } from "types/review";

export interface DashboardQuery extends QueryParam { }

export const useDashboard = () => {
  const { t } = useTranslation();

  const [projectPayments, setProjectPayments] = useState<any[]>([]);
  const [total, setTotal] = useState(0);
  const [loadingProjectPayments, setLoadingProjectPayments] = useState(false);

  const [topPartners, setTopPartners] = useState<any[]>([]);
  const [loadingTopPartners, setLoadingTopPartners] = useState(false);

  const [summaryTotal, setSummaryTotal] = useState<ProjectData[]>([]);
  const [loadingSummaryTotal, setLoadingSummaryTotal] = useState(false);

  const [partnerRank, setPartnerRank] = useState<RankSummary[]>([]);
  const [loadingPartnerRank, setLoadingPartnerRank] = useState(false);

  const [summaryReview, setSummaryReview] = useState<ReviewStatusSummary[]>([]);
  const [loadingSummaryReview, setLoadingSummaryReview] = useState(false);

  const [topCustomers, setTopCustomers] = useState<TopCustomer[]>([]);
  const [loadingTopCustomers, setLoadingTopCustomers] = useState(false);

  const [summaryReviewTotal, setSummaryReviewTotal] = useState<ReviewTotalSummary | null>();
  const [loadingSummaryReviewTotal, setLoadingSummaryReviewTotal] = useState(false);

  const [summaryStatistical, setSummaryStatistical] = useState<SummaryStatistical | null>(null);
  const [loadingSummaryStatistical, setLoadingSummaryStatistical] = useState(false);

  const [summaryProfit, setSummaryProfit] = useState<SummaryProfitItem[]>([]);
  const [loadingSummaryProfit, setLoadingSummaryProfit] = useState(false);

  const [topProducts, setTopProducts] = useState<TopProduct[]>([]);
  const [loadingTopProducts, setLoadingTopProducts] = useState(false);

  const [totalStatistical, setTotalStatistical] = useState<any | null>(null);
  const [loadingTotalStatistical, setLoadingTotalSummaryStatistical] = useState(false);

  const [summaryDeposit, setSummaryDeposit] = useState<any[]>([]);
  const [loadingSummaryDeposit, setLoadingSummaryDeposit] = useState(false);

  const [customerTransaction, setCustomerTransaction] = useState<any[]>([]);
  const [loadingCustomerTransaction, setLoadingCustomerTransaction] = useState(false);

  const [customerBalance, setCustomerBalance] = useState<any[]>([]);
  const [loadingCustomerBalance, setLoadingCustomerBalance] = useState(false);

  const [partnerCount, setPartnerCount] = useState<PartnerSummary>({
    total: 0,
    totalVerified: 0
  });
  const [loadingPartnerCount, setLoadingPartnerCount] = useState<boolean>(false);

  const [summaryCost, setSummaryCost] = useState<any[]>([]);
  const [loadingSummaryCost, setLoadingSummaryCost] = useState(false);

  const [summaryProfitSingle, setSummaryProfitSingle] = useState<any[]>([]);
  const [loadingSummaryProfitSingle, setLoadingSummaryProfitSingle] = useState(false);


  const fetchProjectPayments = async (query?: DashboardQuery) => {
    setLoadingProjectPayments(true);
    try {
      const { data } = await dashboardApi.moneyProjectPayment(query);
      setProjectPayments(data?.items || []);
      setTotal(data?.total || 0);
    } catch (error) {
      message.error(t("fetchFailed") || "Fetch failed");
    } finally {
      setLoadingProjectPayments(false);
    }
  };


  const fetchTopPartners = async () => {
    setLoadingTopPartners(true);
    try {
      const { data } = await dashboardApi.topPartner();
      setTopPartners(data || []);
    } catch (error) {
      message.error(t("fetchFailed") || "Fetch failed");
    } finally {
      setLoadingTopPartners(false);
    }
  };

  const fetchSummaryTotal = async (query?: any) => {
    setLoadingSummaryTotal(true);
    try {
      const { data } = await dashboardApi.summaryTotal(query);
      setSummaryTotal(data || null);
    } catch (error) {
      message.error(t("fetchFailed") || "Fetch failed");
    } finally {
      setLoadingSummaryTotal(false);
    }
  };

  const fetchPartnerRank = async (query?: any) => {
    setLoadingPartnerRank(true);
    try {
      const { data } = await dashboardApi.partnerRank(query);
      setPartnerRank(data || []);
    } catch (error) {
      message.error(t("fetchFailed") || "Fetch failed");
    } finally {
      setLoadingPartnerRank(false);
    }
  };

  const fetchSummaryReview = async (query?: any) => {
    setLoadingSummaryReview(true);
    try {
      const { data } = await reviewApi.summaryStatus(query);
      console.log("setSummaryReview", data)
      setSummaryReview(data || []);
    } catch (error) {
      message.error(t("fetchFailed") || "Fetch failed");
    } finally {
      setLoadingSummaryReview(false);
    }
  };

  const fetchPartnerCount = async (query?: any) => {
    setLoadingPartnerCount(true);
    try {
      const { data } = await dashboardApi.partnerCount(query);
      if (data) setPartnerCount(data);

    } catch (error) {
      message.error(t("fetchFailed") || "Fetch failed");
    } finally {
      setLoadingPartnerCount(false);
    }
  };

  const fetchTopCustomers = async () => {
    setLoadingTopCustomers(true);
    try {
      const { data } = await dashboardApi.topCustomer();
      setTopCustomers(data || []);
    } catch (error) {
      message.error(t("fetchFailed") || "Fetch failed");
    } finally {
      setLoadingTopCustomers(false);
    }
  };

  const fetchSummaryReviewTotal = async (query?: any) => {
    setLoadingSummaryReviewTotal(true);
    try {
      const { data } = await dashboardApi.summaryReview(query);
      setSummaryReviewTotal(data || null);
    } catch (error) {
      message.error(t("fetchFailed") || "Fetch failed");
    } finally {
      setLoadingSummaryReviewTotal(false);
    }
  };

  const fetchSummaryStatistical = async (query?: any) => {
    setLoadingSummaryStatistical(true);
    try {
      const { data } = await dashboardApi.summaryStatistical(query);
      setSummaryStatistical(data || null);
    } catch (error) {
      message.error(t("fetchFailed") || "Fetch failed");
    } finally {
      setLoadingSummaryStatistical(false);
    }
  };

  const fetchSummaryProfit = async (query?: any) => {
    setLoadingSummaryProfit(true);
    try {
      const { data } = await dashboardApi.summaryProfit(query);
      setSummaryProfit(data || []);
    } catch (error) {
      message.error(t("fetchFailed") || "Fetch failed");
    } finally {
      setLoadingSummaryProfit(false);
    }
  };

  const fetchTopProducts = async (query?: any) => {
    setLoadingTopProducts(true);
    try {
      const { data } = await dashboardApi.topProduct(query);
      setTopProducts(data || []);
    } catch (error) {
      message.error(t("fetchFailed") || "Fetch failed");
    } finally {
      setLoadingTopProducts(false);
    }
  };


  const fetchMoneyPartnerWithdraw = async (query?: any) => {
    setLoadingTotalSummaryStatistical(true);
    try {
      const { data } = await dashboardApi.totalMoneyPartnerWithdraw(query);
      setTotalStatistical(data || null);
    } catch (error) {
      message.error(t("fetchFailed") || "Fetch failed");
    } finally {
      setLoadingTotalSummaryStatistical(false);
    }
  };

  const fetchSummaryDeposit = async (query?: any) => {
    setLoadingSummaryDeposit(true);
    try {
      const { data } = await dashboardApi.summaryDeposit(query);
      setSummaryDeposit(data || []);
    } catch (error) {
      message.error(t("fetchFailed") || "Fetch failed");
    } finally {
      setLoadingSummaryDeposit(false);
    }
  };

  const fetchCustomerTransaction = async (query?: any) => {
    setLoadingCustomerTransaction(true);
    try {
      const { data } = await dashboardApi.summaryCustomerTransaction(query);
      setCustomerTransaction(data || []);
    } catch (error) {
      message.error(t("fetchFailed") || "Fetch failed");
    } finally {
      setLoadingCustomerTransaction(false);
    }
  };

  const fetchCustomerBalance = async (query?: any) => {
    setLoadingCustomerBalance(true);
    try {
      const { data } = await dashboardApi.summaryCustomerBalance(query);
      setCustomerBalance(data || []);
    } catch (error) {
      message.error(t("fetchFailed") || "Fetch failed");
    } finally {
      setLoadingCustomerBalance(false);
    }
  };

  const fetchSummaryCost = async (query?: any) => {
    setLoadingSummaryCost(true);
    try {
      const { data } = await dashboardApi.summaryProfit(query);
      setSummaryCost(data.map((item: any) => ({ date: item.date, total: item.price })) || []);
    } catch (error) {
      message.error(t("fetchFailed") || "Fetch failed");
    } finally {
      setLoadingSummaryCost(false);
    }
  };

  const fetchSummaryProfitSingle = async (query?: any) => {
    setLoadingSummaryProfitSingle(true);
    try {
      const { data } = await dashboardApi.summaryProfit(query);
      setSummaryProfitSingle(data.map((item: any) => ({ date: item.date, total: item.profit })) || []);
    } catch (error) {
      message.error(t("fetchFailed") || "Fetch failed");
    } finally {
      setLoadingSummaryProfitSingle(false);
    }
  };

  return {
    projectPayments,
    totalProjectPayments: total,
    fetchProjectPayments,
    loadingProjectPayments,

    topPartners,
    fetchTopPartners,
    loadingTopPartners,

    summaryTotal,
    fetchSummaryTotal,
    loadingSummaryTotal,

    partnerRank,
    fetchPartnerRank,
    loadingPartnerRank,

    summaryReview,
    fetchSummaryReview,
    loadingSummaryReview,

    partnerCount,
    fetchPartnerCount,
    loadingPartnerCount,

    topCustomers,
    fetchTopCustomers,
    loadingTopCustomers,

    summaryReviewTotal,
    fetchSummaryReviewTotal,
    loadingSummaryReviewTotal,

    summaryStatistical,
    fetchSummaryStatistical,
    loadingSummaryStatistical,

    summaryProfit,
    fetchSummaryProfit,
    loadingSummaryProfit,

    topProducts,
    fetchTopProducts,
    loadingTopProducts,

    totalStatistical,
    fetchMoneyPartnerWithdraw,

    summaryDeposit,
    fetchSummaryDeposit,
    loadingSummaryDeposit,

    customerTransaction,
    fetchCustomerTransaction,
    loadingCustomerTransaction,

    customerBalance,
    fetchCustomerBalance,
    loadingCustomerBalance,

    summaryCost,
    fetchSummaryCost,
    loadingSummaryCost,

    summaryProfitSingle,
    fetchSummaryProfitSingle,
    loadingSummaryProfitSingle
  };
};
