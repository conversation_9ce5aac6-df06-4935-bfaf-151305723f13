import { Customer } from "./customer";

export interface SurveyCampaign {
  id: number;
  createdAt: number;
  updatedAt: number;
  isDeleted: boolean;
  isDefault: boolean;
  name: string; //tên chiến dịch khảo sát
  slug: string; //slug path dẫn tới trang khảo sát
  phoneColName: string; //tên cột sđt trong google sheet để get data
  point: number; //số điểm sau khi hòa tất khảo sát
  googleFormLink: string;
  googleFormId: string;
  googleSheetId: string;
  sheetName: string;
  totalResponse: number; //tổng số câu trả lời
  //custom
  surveyHistories: SurveyHistory[];
}

export interface SurveyHistory {
  id: number;
  createdAt: number;
  updatedAt: number;
  isDeleted: boolean;
  phone: string; //sđt lấy từ google sheet
  customer: Customer;
  surveyCampaign: SurveyCampaign;
  point: number;
}
