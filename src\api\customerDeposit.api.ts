import { request } from "utils/request";
import { AxiosPromise } from "axios";

export const customerDepositApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/customerDeposit",
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/customerDeposit",
      data,
      method: "post",
    }),
  getSummary: (): AxiosPromise<any> =>
    request({
      url: "/v1/admin/customerDeposit/summary/status",

      method: "get",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/customerDeposit/${id}`,
      method: "patch",
      data,
    }),
  approve: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/customerDeposit/${id}/approve`,
      method: "patch",
      data,
    }),
  reject: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/customerDeposit/${id}/reject`,
      method: "delete",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/customerDeposit/${id}`,
      method: "delete",
    }),
};
