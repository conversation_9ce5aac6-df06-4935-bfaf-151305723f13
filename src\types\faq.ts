import { Language } from "./customer";

export enum FaqCategoryType {
  Customer = "CUSTOMER",
  Partner = "PARTNER",
}

export interface FaqCategory {
  id: number;
  createdAt: number;
  updatedAt: number;
  isDeleted: boolean;
  name: string;
  nameEn: string;
  type: FaqCategoryType;
  position?: number | string;
}

export enum MultipleLangType {
  Faq = "FAQ",
  FaqCategory = "FAQ_CATEGORY",
}

export interface MultipleLang {
  id: number;
  createdAt: number;
  updatedAt: number;
  isDeleted: boolean;
  lang: Language;
  name: string;
  shortContent: string;
  type: MultipleLangType;
  title: string;
  body: string;
  content: string;
  description: string;
  faq: Faq;
  faqCategory: FaqCategory;
}

export interface Faq {
  id: number;
  createdAt: number;
  updatedAt: number;
  isDeleted: boolean;
  title: string;
  content: string;
  titleEn: string;
  contentEn: string;
  faqCategory: FaqCategory;
  multipleLangs: MultipleLang[];
  position?: string | number;
}
