import {
  ExportOutlined,
  ImportOutlined,
  PlusOutlined,
  SearchOutlined,
} from "@ant-design/icons";
import { Button, Card, Input, message, Popconfirm, Space } from "antd";
import { productApi } from "api/product.api";
import { useProduct } from "hooks/useProduct";
import { useEffect, useMemo, useRef, useState } from "react";
import { PermissionNames } from "router";
import { permissionStore } from "store/permissionStore";
import { Product, ProductStatus } from "types/product";
import { formatVND, getTitle } from "utils";
import { checkRole } from "utils/auth";
import { unixToFullDate } from "utils/dateFormat";
import { handleExport, MyExcelColumn } from "../../utils/MyExcel";
import { ProductModal, ProductModalRef } from "./components/Modal/ProductModal";
import { ProductList } from "./components/Table/ProductList";
import { TextField } from "@mui/material";
import { useTranslation } from "react-i18next";
import { ReactComponent as DownloadIcon } from "assets/svgs/download.svg";
import { ReactComponent as SearchIcon } from "assets/svgs/search-icon.svg";

export const ProductPage = ({ title = "" }) => {
  const productModalRef = useRef<ProductModalRef>();
  const [openImport, setOpenImport] = useState(false);
  const { t } = useTranslation();
  const exportColumns: MyExcelColumn<Product>[] = [
    {
      header: t("code"),
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "code",
      columnKey: "code",
      render: (record) => record.code,
    },
    {
      header: t("productName"),
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "name",
      columnKey: "name",
      render: (record) => record.name,
    },
    {
      header: t("status"),
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "status",
      columnKey: "status",
      render: (record) => t(record.status),
    },
    {
      header: t("price"),
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "price",
      columnKey: "price",
      render: (record) => formatVND(record.price),
    },
    {
      header: t("reviewQuantity"),
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "numReview",
      columnKey: "numReview",
      render: (record) => formatVND(record.numReview),
    },
    {
      header: t("estimatedDay"),
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "estimatedDay",
      columnKey: "estimatedDay",
      render: (record) => formatVND(record.estimatedDay),
    },
    {
      header: t("slowMinQuantity"),
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "slowMinQuantity",
      columnKey: "slowMinQuantity",
      render: (record) => formatVND(record.slowMinQuantity),
    },
    {
      header: t("maxLengthCharReview"),
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "maxLengthCharReview",
      columnKey: "maxLengthCharReview",
      render: (record) => formatVND(record.maxLengthCharReview),
    },
  ];
  const [loadingDelete, setLoadingDelete] = useState(false);
  const { products, fetchProduct, loadingProduct, queryProduct, totalProduct } =
    useProduct({
      initQuery: {
        page: 1,
        limit: 10,
      },
    });
  // const hasProductAddPermission = checkRole(
  //   PermissionNames.consumerProductAdd,
  //   permissionStore.permissions
  // );
  // const hasProductUpdatePermission = checkRole(
  //   PermissionNames.consumerIndustryEdit,
  //   permissionStore.permissions
  // );
  // const hasIndustryDeletePermission = checkRole(
  //   PermissionNames.consumerIndustryDelete,
  //   permissionStore.permissions
  // );
  useEffect(() => {
    document.title = getTitle(t(title));
    fetchProduct();
  }, []);

  const handleDeleteProduct = async (productId: number) => {
    try {
      setLoadingDelete(true);
      const res = await productApi.delete(productId);
      fetchProduct();
      message.success(t("actionSuccessfully"));
    } catch (error) {
    } finally {
      setLoadingDelete(false);
    }
  };
  const handleActiveProduct = async (productId: number) => {
    try {
      setLoadingDelete(true);
      const res = await productApi.update(productId, {
        product: { status: ProductStatus.Active },
      });
      fetchProduct();
      message.success(t("actionSuccessfully"));
    } catch (error) {
    } finally {
      setLoadingDelete(false);
    }
  };

  const handleInactiveProduct = async (productId: number) => {
    try {
      setLoadingDelete(true);
      const res = await productApi.update(productId, {
        product: { status: ProductStatus.Inactive },
      });
      fetchProduct();
      message.success(t("actionSuccessfully"));
    } catch (error) {
    } finally {
      setLoadingDelete(false);
    }
  };

  const handleSearch = (search: string) => {
    queryProduct.search = search;
    queryProduct.page = 1;
    fetchProduct();
  };

  return (
    // <Card bodyStyle={{ padding: "8px 20px" }} style={{ borderRadius: "8px" }}>
    <div>
      <section className="box">
        <div className="filter-container">
          <Space wrap>
            <div className="filter-item">
              {/* <label htmlFor="">Tìm kiếm</label>
              <br /> */}
              <label htmlFor="">{t("search")}</label>
              <Input.Search
                allowClear
                onChange={(ev) => {
                  if (ev.currentTarget.value) {
                    queryProduct.search = ev.currentTarget.value;
                  } else {
                    queryProduct.search = undefined;
                  }
                  queryProduct.page = 1;


                }}
                onKeyDown={(ev) => {
                  if (ev.code == "Enter") {
                    // fetchProduct();
                  }
                }}
                size="large"
                className="w-full search-btn mt-1"
                // placeholder={t("productName")}
                enterButton={< SearchIcon />}
                onSearch={handleSearch}
              />
            </div>

            {/* <div className="filter-item btn">
              <Button
                onClick={() => fetchProduct()}
                type="primary"
                icon={<SearchOutlined />}
              >
                {t("search")}
              </Button>
            </div> */}
            {/* {hasProductAddPermission && ( */}
            <div className="filter-item btn">
              <Button
                size="large"
                onClick={() => {
                  productModalRef.current?.handleCreate();
                }}
                icon={<PlusOutlined />}
                type="primary"
              >
                {t("create")}
              </Button>
            </div>
            {/* )} */}
            {/* <div className="filter-item btn">
              <Button
                onClick={() => {
                  setOpenImport(true);
                }}
                type="primary"
                icon={<PlusOutlined />}
              >
                Nhập excel
              </Button>
            </div> */}
            {/* <div className="filter-item btn">
                <Button
                  onClick={() => {
                    importModal.current?.open();
                  }}
                  type="primary"
                  icon={<ImportOutlined />}
                >
                  Nhập excel
                </Button>
              </div> */}

            <div className="filter-item btn">
              <Popconfirm
                title={t("exportAsk")}
                onConfirm={() =>
                  handleExport({
                    onProgress(percent) { },
                    exportColumns,
                    fileType: "xlsx",
                    dataField: "products",
                    query: queryProduct,
                    api: productApi.findAll,
                    fileName: t("productList"),
                    sheetName: t("productList"),
                  })
                }
                okText={t("exportExcel")}
                cancelText={t("cancel")}
              >
                <Button
                  size="large"
                  type="primary"
                  loading={false}
                  icon={<DownloadIcon />}
                >
                  {t("exportExcel")}
                </Button>
              </Popconfirm>
            </div>
          </Space>
        </div>
        <div className="b-container">
          <ProductList
            onEdit={(record) => productModalRef.current?.handleUpdate(record)}
            dataSource={[...products]}
            loading={loadingProduct}
            loadingDelete={loadingDelete}
            pagination={{
              total: totalProduct,
              defaultPageSize: queryProduct.limit,
              currentPage: queryProduct.page,
              onChange: ({ page, limit }) => {
                Object.assign(queryProduct, {
                  page,
                  limit,
                });
                fetchProduct();
              },
            }}
            onDelete={handleDeleteProduct}
            onActive={handleActiveProduct}
            onInactive={handleInactiveProduct}

          // hasDeleteProductPermission={hasProductDeletePermission}
          // hasUpdateProductPermission={hasProductUpdatePermission}
          />{" "}
        </div>
      </section>

      <ProductModal
        ref={productModalRef}
        onClose={function (): void {
          throw new Error("Function not implemented.");
        }}
        onSubmitOk={fetchProduct}
      // hasAddIndustryPermission={hasIndustryAddPermission}
      // hasUpdateIndustryPermission={hasIndustryUpdatePermission}
      />
    </div>
  );
};
