import { LockOutlined, UserOutlined } from "@ant-design/icons";
import { Button, Form, Input, message } from "antd";
import logo from "assets/images/logo.png";
import { useEffect, useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import { adminRoutes } from "router";
import { settings } from "settings";
import { permissionStore } from "store/permissionStore";
import { userStore } from "store/userStore";
import { getTitle } from "utils";
import "./styles/LoginPage.scss";
import { RiLockPasswordLine } from "react-icons/ri";
const { Item: FormItem } = Form;
import { Grid2 as Grid, Box, Stack, Typography } from "@mui/material";
import img1 from "assets/svgs/login-bg.svg";
import AuthLogin from "components/Auth/AuthLogin";
import Logo from "components/Logo/Logo";
import { useTranslation } from "react-i18next";
import { observer } from "mobx-react";
import { AuthLayout } from "components/Layout/AuthLayout";

const LoginPage = ({ title = "" }) => {
  const { t } = useTranslation();
  return (
    <AuthLayout>
      <AuthLogin title={t("welcomeArivi")} />
    </AuthLayout>
  );
};

export default observer(LoginPage);
