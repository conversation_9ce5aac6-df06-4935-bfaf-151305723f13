import { DownloadOutlined, InboxOutlined } from "@ant-design/icons";
import {
  <PERSON><PERSON>,
  Button,
  Modal,
  Select,
  Space,
  Spin,
  Table,
  Upload,
  message,
} from "antd";
import { Rule } from "antd/es/form";
import FormItem from "antd/es/form/FormItem";
import { partnerApi } from "api/partner.api";
import dayjs from "dayjs";
import { chunk } from "lodash";
import { forwardRef, useEffect, useImperativeHandle, useState } from "react";
import { Link } from "react-router-dom";
import { Partner } from "types/partner";
import { readerData2 } from "utils/excel2";
const rules: Rule[] = [{ required: true }];

const { Dragger } = Upload;

export interface ImportPartnerModal {
  open: () => void;
  close: () => void;
}

export interface IValidate {
  index: number;
  message: string;
}

interface DataImportReturn {
  msg: string;
  rowNum: number;
  status: string;
}

export interface PartnerImport extends Partner {
  rowNum: number;
}

interface IProps {
  onSuccess?: () => void;
  createApi?: (data: any) => any;
  onUploaded?: (excelData: any, setData: (data: any) => any) => void;
  demoExcel?: string;
  guide?: React.ReactNode[];
  uploadText?: string;
  okText?: string;
  onClose?: () => void;
  titleText?: string;
  validateMessage?: IValidate[];
  onDownloadDemoExcel?: () => void;
  t: (key: string, options?: any) => string;
}

const ImportPartner = forwardRef(
  (
    {
      onSuccess,
      createApi,
      onUploaded,
      onClose,
      validateMessage,
      guide,
      demoExcel,
      uploadText,
      okText,
      titleText,
      onDownloadDemoExcel,
      t,
    }: IProps,
    ref
  ) => {
    const [errorsLog, setErrorsLog] = useState<any[]>([]);
    const [dataPosts, setDataPosts] = useState<PartnerImport[]>([]);
    const [visible, setVisible] = useState<boolean>(false);
    const [loading, setLoading] = useState(false);
    const [dataReturn, setDataReturn] = useState<{
      data: DataImportReturn[];
      successCount: number;
      errorCount: number;
    }>();
    const [selectedCompanyId, setSelectedCompanyId] = useState<number>();
    useEffect(() => {
      if (validateMessage?.length) {
        setErrorsLog([]);
      }
    }, [validateMessage]);

    const handleOnImport = async () => {
      if (!dataPosts.length) return;
      let errors: any = [];
      //   if (selectedCompanyId && userStore.info.company) {
      //     message.error("Hãy chọn nhà máy để nhập excel");
      //     return;
      //   }
      //   let companyId;
      //   if (selectedCompanyId) {
      //     companyId = selectedCompanyId;
      //   } else {
      //     companyId = userStore.info.company?.id;
      //   }

      try {
        // const dataTest = dataPosts.map((dataPost) => {
        //   console.log(dataPost.timeMachineAt);
        //   console.log(dataPost.periodType);
        //   console.log(dataPost.periodStartAt);
        // });
        const { data } = await partnerApi.import({
          partners: dataPosts.map((dataPost) => ({
            ...dataPost,
          })),
          //   companyId,
        });
        if (data.length) {
          const successCount = data.reduce(
            (acc: number, item: DataImportReturn) => {
              if (item.status == "ok") return acc + 1;
              return acc;
            },
            0
          );
          const errorCount = data.reduce(
            (acc: number, item: DataImportReturn) => {
              if (item.status == "error") return acc + 1;
              return acc;
            },
            0
          );
          setDataReturn({ data, successCount, errorCount });
          onSuccess?.();
          setDataPosts([]);
        }
      } catch (err) {
        console.log({ err });
      } finally {
        setLoading(false);
      }
    };

    const handleOnCancel = () => {
      setVisible(false);
      onClose?.();
    };
    useImperativeHandle(
      ref,
      () => ({
        open: () => setVisible(true),
        close: () => setVisible(false),
      }),
      []
    );

    return (
      <Modal
        maskClosable={false}
        width={1000}
        style={{ top: 50 }}
        visible={visible}
        onCancel={handleOnCancel}
        destroyOnClose={true}
        afterClose={() => {
          setDataPosts([]);
          setErrorsLog([]);
          setDataReturn(undefined);
        }}
        title={titleText || t("importExcel")}
        footer={[
          <Button
            type="primary"
            disabled={!dataPosts.length}
            onClick={() => {
              handleOnImport();
            }}
            style={{ color: "#fff" }}
          >
            {okText}
          </Button>,
          <Button
            onClick={() => {
              handleOnCancel();
            }}
          >
            {t("close")}
          </Button>,
        ]}
      >
        <Spin spinning={false}>
          {guide && (
            <Alert
              style={{ padding: "10px", marginBottom: "10px" }}
              message={<b>{t("note")}</b>}
              type="warning"
              description={
                <ul>
                  {guide.map((text, index) => (
                    <li key={index}>
                      <p>{text}</p>
                    </li>
                  ))}
                </ul>
              }
            />
          )}
          {demoExcel && (
            <Link to={demoExcel} target="_blank" download>
              <Space>
                <DownloadOutlined />
                {t("downloadFileSample")}
              </Space>
            </Link>
          )}
          {onDownloadDemoExcel && (
            <a>
              <Space onClick={() => onDownloadDemoExcel()}>
                <DownloadOutlined />
                {t("downloadFileSample")}
              </Space>
            </a>
          )}
          {/* {!userStore.info.company ? (
            <div className="flex items-center gap-3 mt-2 w">
              <div style={{ width: 80 }}>Nhà máy:</div>
              <Select
                onChange={(value) => {
                  setSelectedCompanyId(value);
                }}
                title="Chọn nhà máy"
                style={{ width: "100%" }}
                options={companies?.map((company) => ({
                  label: (
                    <div>
                      {company.name}{" "}
                    </div>
                  ),
                  value: company.id,
                }))}
              ></Select>
            </div>
          ) : (
            <div className="flex items-center gap-3 mt-2">
              <div style={{ width: 80 }}>Nhà máy:</div>
              <Select
                disabled
                title="Chọn nhà máy"
                value={userStore.info.company.id}
                style={{ width: "100%" }}
                options={[
                  {
                    label: userStore.info.company.name,
                    value: userStore.info.company.id,
                  },
                ]}
              ></Select>
            </div>
          )} */}

          <Dragger
            style={{ marginTop: "0.5em" }}
            maxCount={1}
            multiple={false}
            beforeUpload={async (file) => {
              //Check file type
              const isCSVFile = file.name.includes("xlsx");
              if (isCSVFile === false) {
                message.error(t("uploadOnlyExcel"));
                return Upload.LIST_IGNORE;
              }
              debugger;
              const excelData = await readerData2(file, 0);
              setDataReturn(undefined);
              onUploaded?.(excelData, setDataPosts);

              return false;
            }}
            onChange={(info) => {
              //reset data
              if (info.fileList.length == 0) {
                setErrorsLog([]);
                setDataPosts([]);
              }
            }}
          >
            <p className="ant-upload-drag-icon">
              <InboxOutlined />
            </p>
            <p className="ant-upload-text">{uploadText || t("dragOrDrop")}</p>
          </Dragger>

          {dataReturn && (
            <Alert
              className="p-3 mt-2"
              type="warning"
              description={
                <div>
                  <div className="text-blue-600 font-bold">
                    {t("totalImportLine")}: {dataReturn.data.length}
                  </div>
                  <div className="text-green-500">
                    {t("totalImportLineSuccess")}: {dataReturn.successCount}
                  </div>
                  <div className="text-red-500">
                    {t("totalImportLineFail")}: {dataReturn.errorCount}
                  </div>
                  <div className="font-bold">{t("listFail")}</div>
                  <div className="border-[1px] border-red-300 border-solid rounded-md overflow-hidden">
                    <Table
                      columns={[
                        { title: t("line"), dataIndex: "rowNum" },
                        { title: t("error"), dataIndex: "msg" },
                      ]}
                      dataSource={dataReturn.data.filter(
                        (it) => it.status == "error"
                      )}
                      pagination={false}
                    />
                  </div>
                </div>
              }
            ></Alert>
          )}
        </Spin>
        <Space
          style={{ width: "100%", justifyContent: "end", marginTop: "1em" }}
        ></Space>
      </Modal>
    );
  }
);

export default ImportPartner;
