import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>rid<PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>ield,
  Typo<PERSON>,
  useMediaQuery,
} from "@mui/material";
import {
  Collapse,
  Form,
  Image,
  Input,
  Menu,
  message,
  Modal,
  Popover,
  Progress,
  Space,
  Tabs,
  Tag,
} from "antd";
import Column from "antd/es/table/Column";
import { Rule } from "antd/lib/form";
import { projectApi } from "api/project.api";
import { reviewApi } from "api/review.api";
import { productApi } from "api/product.api";
import { supportTicketApi } from "api/supportTicket.api";
import ParentCard from "components/Common/ParentCard";
import { Pagination } from "components/Pagination";
import { AriviTable } from "components/Table/AriviTable";
import { useReview } from "hooks/useReview";
import { cloneDeep, debounce } from "lodash";
import React, {
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from "react";
import { useTranslation } from "react-i18next";
import { ModalStatus } from "types/modal";
import { Project, ProjectStatus, ProjectStatusTrans } from "types/project";
import { Review, ReviewStatus, ReviewStatusTrans, ReviewStatusTrans2 } from "types/review";
import {
  ProjectPaymentModal,
  ProjectPaymentModalRef,
} from "./ProjectPaymentModal";
import { formatVND } from "utils";
import { geminiApi } from "api/gemini.api";
import { PaymentStatus, PaymentStatusTrans } from "types/payment";
import { appStore } from "store/appStore";
import { ConfigurationParam } from "types/configuration";
import { EyeOutlined, InfoCircleOutlined } from "@ant-design/icons";
import { unixToDate, unixToFullDate } from "utils/dateFormat";
import ProjectActivityLogTab from "./ProjectActivityLogTab";
import { TbClockSearch } from "react-icons/tb";
import { IconLink } from "@tabler/icons-react";
import { ReviewLogModal } from "./ReviewLogModal";
import dayjs from "dayjs";
import { ProjectStatusComp } from "components/ProjectStatus/ProjectStatusComp";
import { PaymentStatusComp } from "components/PaymentStatus/PaymentStatusComp";
import { ReviewStatusComp } from "components/ReviewStatus/ReviewStatusComp";

const rules: Rule[] = [{ required: true }];
enum ProjectModalTabKeys {
  ProjectInfo = "1",

  ActivityLog = "2",
}
export interface ProjectModalRef {
  handleCreate: () => void;
  handleUpdate: (project: Project, isOpen?: boolean) => void;
}
interface ProjectModalProps {
  onClose: () => void;
  onSubmitOk: () => void;
}

export const ProjectModal = React.forwardRef(
  ({ onClose, onSubmitOk }: ProjectModalProps, ref) => {
    const { t } = useTranslation();
    const mdUp = useMediaQuery((theme: any) => theme.breakpoints.up("md"));
    const mdDown = useMediaQuery((theme: any) => theme.breakpoints.down("sm"));

    const {
      fetchReview,
      loadingReview,
      queryReview,
      reviews: originReviews,
      setQueryReview: setReviewQuery,
      totalReview,
      totalContentEmpty,
    } = useReview({ initQuery: { limit: 10, page: 1 } });

    const [form] = Form.useForm<Project>();
    const [selectedProject, setSelectedProject] = useState<Project>();
    const [loadingUpdateReview, setLoadingUpdateReview] = useState<{
      loading: boolean;
      id: number;
    }>({ loading: false, id: -1 });
    const [visible, setVisible] = useState(false);
    const [status, setStatus] = useState<ModalStatus>("create");
    const [reviews, setReviews] = useState<Review[]>([]);
    const [isDoneGenerate, setIsDoneGenerate] = useState(false);
    const [activeKey, setActiveKey] = useState<ProjectModalTabKeys>(
      ProjectModalTabKeys.ProjectInfo
    );
    const [loadingWarrantyReview, setLoadingWarrantyReview] = useState(false);
    const [open, setOpen] = useState("1");

    const projectPaymentRef = useRef<ProjectPaymentModalRef>();
    const reviewLogModalRef = useRef<ReviewLogModal>();

    const reviewInterval = useRef<NodeJS.Timeout>();
    const handleTabChange = (key: string) => {
      setActiveKey(key as ProjectModalTabKeys);
    };
    useImperativeHandle<any, ProjectModalRef>(
      ref,
      () => ({
        handleCreate() {
          form.resetFields();
          setVisible(true);
          setStatus("create");
        },
        async handleUpdate(project: Project, isOpen?: boolean) {
          form.setFieldsValue({ ...project });
          setSelectedProject(cloneDeep(project));
          setVisible(true);
          setStatus("update");
          setReviewQuery({
            ...queryReview,
            page: 1,
            projectId: project.id,
          });
          fetchReview({ ...queryReview, page: 1, projectId: project.id });

          await projectApi.findOne(project.id);
          if (isOpen == true) {
            setOpen("1");
          } else {
            setOpen("2");
          }
        },
      }),
      [queryReview]
    );

    useEffect(() => {
      setReviews(
        originReviews.map((r) => ({ ...r, contentChange: r.content }))
      );
    }, [originReviews]);

    useEffect(() => {
      const total = selectedProject?.status === ProjectStatus.New ?
        totalReview > (selectedProject?.quantityReview ?? 0) ? 0 : totalReview
        : totalReview;

      if (selectedProject) {
        if (
          queryReview.status ||
          total >= (selectedProject.quantityReview || 0)
        ) {
          setIsDoneGenerate(true);
          clearInterval(reviewInterval.current);
          reviewInterval.current = undefined;
        } else if (!reviewInterval.current) {
          setIsDoneGenerate(false);
          reviewInterval.current = setInterval(() => {
            fetchReview();
          }, 5000);
        }
      }

      return () => {
        if (reviewInterval.current) {
          clearInterval(reviewInterval.current);
          reviewInterval.current = undefined;
        }
      };
    }, [totalReview, selectedProject, queryReview]);

    const handleClose = () => {
      setVisible(false);
      form.resetFields();
      onClose?.();
    };

    const handleUpdateReview = async (review: Review) => {
      if (loadingUpdateReview.loading) return;
      try {
        setLoadingUpdateReview({ loading: true, id: review.id });
        await reviewApi.update(review.id, {
          review: { content: review.contentChange },
        });
        message.success(t("operationSuccess"));
        fetchReview();
      } catch (error) {
        console.log({ error });
      } finally {
        setLoadingUpdateReview({ loading: false, id: -1 });
      }
    };
    const handleRegenerateReview = async (review: Review) => {
      if (loadingUpdateReview.loading) return;
      try {
        setLoadingUpdateReview({ loading: true, id: review.id });
        const { data } = await geminiApi.create({
          projectId: selectedProject?.id,
          numberReviews: 1,
        });
        review.contentChange = data[0].content;
        await handleUpdateReview(review);
      } catch (error) {
        console.log({ error });
      } finally {
        setLoadingUpdateReview({ loading: false, id: -1 });
      }
    };

    const fetchProject = async (projectId: number) => {
      try {
        const { data } = await projectApi.findOne(projectId);
        setSelectedProject(data);
        setQueryReview: setReviewQuery({
          ...queryReview,
          page: 1,
          projectId: data.id,
        });
        fetchReview({ ...queryReview, page: 1, projectId: data.id });
      } catch (e) {
        console.log({ e });
      } finally {
      }
    };

    const { currentProduct, estimateDayForSlowDrop } = useMemo(() => {
      const currProduct = selectedProject?.product;
      let estimateDayForSlowDrop = 0;
      if (currProduct) {
        estimateDayForSlowDrop =
          currProduct.numReview / (selectedProject?.dropSlowNumber || 1);
      }

      return {
        currentProduct: currProduct,
        estimateDayForSlowDrop,
      };
    }, [selectedProject]);

    const handleWarrantyReview = async (id: number) => {
      try {
        setLoadingWarrantyReview(true);
        await reviewApi.warranty(id);
      } catch (e) {
        console.log({ e });
      } finally {
        fetchReview();
        setLoadingWarrantyReview(false);
      }
    };

    const progressCompletionText = (data: Project): string => {
      if (data.paymentStatus == PaymentStatus.Complete) {
        return `${data.currentCompleteReview}/${data.quantityReview}`;
      }

      return `${data.quantityReview - totalContentEmpty}/${data.quantityReview
        }`;
    };

    const progressCompletionWarranty = (data: Project) => {
      if (!!!data) return 0;
      return Number(
        ((data.currentCompleteReview / data.quantityReview) * 100).toFixed(1)
      );
    };

    const progressCompletion = (data: Project) => {
      if (!!!data) return 0;
      return Number(
        (
          ((data.quantityReview - totalContentEmpty) / data.quantityReview) *
          100
        ).toFixed(1)
      );
    };

    const colorProcess = (selectedProject: Project) => {
      if (selectedProject.paymentStatus === PaymentStatus.Complete)
        return "#2A3547";
      return progressCompletion(selectedProject) === 100 ? "green" : "#ED2012";
    };

    const handleFilterOfTable = (
      pagination: any,
      filters: any,
      sorter: any
    ) => {
      const filterQuery = filters.status?.length
        ? filters.status[0]
        : undefined;
      setReviewQuery({ ...queryReview, status: filterQuery });
      // fetchReview();
    };

    const getSettingStatus = (record: Review) => {
      if (record.isWarrantyReject) {
        return {
          color: "orange",
        };
      }
      return (
        (ReviewStatusTrans2[
          record?.status as keyof typeof ReviewStatusTrans2
        ] as any) ?? ""
      );
    };

    const getNote = (record: Review, selectedProject: Project) => {
      const rawExpireTime = record?.expireWarrantyAt;

      const isComplete = record.status === ReviewStatus.Complete;
      if (
        record.status === ReviewStatus.RejectWarranty ||
        (record.completeAt && record.isWarrantyReject)
      ) {
        return <>{record.note}</>;
      }
      if (!isComplete) {
        // Xử lý cho ReviewStatus.Pending
        if (record.status === ReviewStatus.Pending) {
          return <>{t("reviewWAIT_FOR_PENDING")}</>;
        }
        return (
          <>
            {t(

              ReviewStatusTrans[record.status as keyof typeof ReviewStatusTrans]
                // @ts-ignore
                ?.note
            )}
          </>
        );
      }

      if (isComplete && record.isWarrantyReject) {
        return <>{record.note}</>;
      }
      if (
        record.expireWarrantyAt > dayjs().unix() ||
        record.expireWarrantyAt == 0
      ) {
        return (
          <>
            {t("remainingDays")}:{" "}
            {dayjs.unix(record?.expireWarrantyAt).diff(dayjs(), "day") >= 0
              ? `${dayjs
                .unix(record?.expireWarrantyAt)
                .diff(dayjs(), "day")} ${t("days")}`
              : `${dayjs
                .unix(record?.expireWarrantyAt)
                .diff(dayjs(), "hour")} ${t("hours")}`}
          </>
        );
      } else {
        return <>{t("completeStatusNote")}</>;
      }
    };

    useEffect(() => {
      fetchReview();
    }, [queryReview.status]);

    return (
      <Modal
        onCancel={() => {
          handleClose();

          setVisible(false);
          setActiveKey(ProjectModalTabKeys.ProjectInfo);
        }}
        open={visible}
        title={t("projectDetail") + " - " + selectedProject?.code}
        style={{ top: 5 }}
        width={1400}
        onOk={() => {
          if (selectedProject) {
            projectPaymentRef.current?.handleOpen(selectedProject);
          }
        }}
        okButtonProps={{
          className:
            selectedProject?.paymentStatus == PaymentStatus.Complete
              ? // || selectedProject?.status == ProjectStatus.Cancel
              "hidden"
              : "",
          disabled: progressCompletion(selectedProject as Project) < 100,
        }}
        okText={t("payment")}
        cancelText={t("close")}
      // footer={
      //   <Space>
      //     <Button
      //       onClick={() => {
      //         handleClose();

      //         setVisible(false);
      //         setActiveKey(ProjectModalTabKeys.ProjectInfo);
      //       }}
      //     >
      //       {t("close")}
      //     </Button>
      //     {activeKey == ProjectModalTabKeys.ProjectInfo &&
      //     selectedProject?.paymentStatus === PaymentStatus.Pending ? (
      //       <Button
      //         variant="contained"
      //         onClick={() => {
      //           if (selectedProject) {
      //             projectPaymentRef.current?.handleOpen(selectedProject);
      //           }
      //         }}
      //       >
      //         {t("payment")}
      //       </Button>
      //     ) : null}
      //   </Space>
      // }
      >
        <Tabs activeKey={activeKey} onChange={handleTabChange}>
          <Tabs.TabPane
            tab={t("basicInfo")}
            key={ProjectModalTabKeys.ProjectInfo}
          >
            {selectedProject && (
              <Grid2 container spacing={1}>
                <Grid2 size={{ xs: 12, md: 3 }}>
                  {mdDown ? (
                    <Collapse defaultActiveKey={["1"]}>
                      <Collapse.Panel header={selectedProject.name} key={open}>
                        <Typography variant="body1">
                          <b>{t("status")}:</b>{" "}
                          <ProjectStatusComp status={selectedProject.status} />
                        </Typography>
                        <Typography variant="body1" my={1}>
                          <b>{t("paymentStatus")}:</b>{" "}
                          <PaymentStatusComp
                            status={selectedProject.paymentStatus}
                          />
                          {/* {selectedProject.paymentStatus == PaymentStatus.Complete && ( */}
                          {/* )} */}
                        </Typography>
                        {/* {selectedProject.paymentStatus == PaymentStatus.Complete && ( */}
                        <>
                          <Typography
                            variant="body1"
                          // className="whitespace-pre-wrap flex justify-between"
                          >
                            <b>{t("fee")}: </b>
                            <b>{formatVND(selectedProject.moneyFinal)} ₫</b>
                            <Popover
                              content={
                                <>
                                  <Typography
                                    variant="caption"
                                    className="whitespace-pre-wrap flex justify-between"
                                  >
                                    <div className="w-[150px]">
                                      {t("estimatePackage")} (x
                                      {selectedProject.product?.numReview})
                                    </div>
                                    <div>
                                      {formatVND(
                                        selectedProject.product?.price || 0
                                      )}{" "}
                                      ₫
                                    </div>
                                  </Typography>
                                  <Typography
                                    variant="caption"
                                    className="whitespace-pre-wrap flex justify-between"
                                  >
                                    <div>{t("estimateSlowDrop")}</div>
                                    <div>
                                      {formatVND(selectedProject.moneyDrop)} ₫
                                    </div>
                                  </Typography>
                                  <Typography
                                    variant="caption"
                                    className="whitespace-pre-wrap flex justify-between"
                                  >
                                    <div>
                                      {t("estimateImage")} (x
                                      {selectedProject.fileAttaches.length})
                                    </div>
                                    <div>
                                      {formatVND(selectedProject.moneyImage)} ₫
                                    </div>
                                  </Typography>
                                  {selectedProject.moneyDiscount ? (
                                    <Typography
                                      variant="caption"
                                      className="whitespace-pre-wrap flex justify-between"
                                    >
                                      <div>{t("promotionPrice")}</div>
                                      <div>
                                        -
                                        {formatVND(
                                          selectedProject.moneyDiscount
                                        )}{" "}
                                        ₫
                                      </div>
                                    </Typography>
                                  ) : null}
                                  <Typography
                                    variant="caption"
                                    className="whitespace-pre-wrap flex justify-between"
                                  >
                                    <div>{t("estimatePrice")}</div>
                                    <div>
                                      {formatVND(selectedProject.moneyTotal)} ₫
                                    </div>
                                  </Typography>
                                  <Typography
                                    variant="caption"
                                    className="whitespace-pre-wrap flex justify-between"
                                  >
                                    <div>
                                      {t("estimateTax")} (
                                      {appStore.getOneConfiguration(
                                        ConfigurationParam.TaxPercent
                                      )}
                                      %)
                                    </div>
                                    <div>
                                      {formatVND(selectedProject.moneyTax)} ₫
                                    </div>
                                  </Typography>
                                  <Typography
                                    variant="caption"
                                    className="whitespace-pre-wrap flex justify-between"
                                  >
                                    <b>{t("total")}</b>
                                    <b>
                                      {formatVND(selectedProject.moneyFinal)} ₫
                                    </b>
                                  </Typography>
                                </>
                              }
                            >
                              <InfoCircleOutlined className="ml-1" />
                            </Popover>
                          </Typography>
                        </>
                        {/* )} */}
                        <Divider className="!my-2" />
                        <Typography variant="body1">
                          <b>{t("currentAverageRating")}:</b>{" "}
                          {selectedProject.totalStar}
                        </Typography>
                        <Divider className="!my-2" />
                        <Typography variant="body1">
                          <b>{t("address")}:</b> {selectedProject.address}
                        </Typography>
                        <Divider className="!my-2" />
                        <Typography variant="body1">
                          <b>{t("linkGGMap")}:</b>{" "}
                          <a
                            href={selectedProject.mapUrl}
                            target="_blank"
                            rel="noopener noreferrer"
                          >
                            {selectedProject.mapUrl}
                          </a>
                        </Typography>

                        <Divider className="!my-2" />
                        <Typography variant="body1">
                          <b>{t("projectDescription")}:</b>{" "}
                          {selectedProject.description}
                        </Typography>
                        <Divider className="!my-2" />
                        <Typography variant="body1" className="!leading-7">
                          <b>{t("keyword")}:</b>
                          {selectedProject.keywords.map((keyword, i) => (
                            <Tag key={i}>{keyword.name}</Tag>
                          ))}
                        </Typography>
                        <Divider className="!my-2" />
                        <Typography variant="body1">
                          <b>{t("package")}:</b> {selectedProject.product?.name}{" "}
                          (x
                          {selectedProject.product?.numReview || 0})
                        </Typography>
                        <Divider className="!my-2" />
                        <Typography variant="body1">
                          <b>{t("slowDrop")}:</b>{" "}
                          {selectedProject.isDropSlow ? t("yes") : t("no")}
                        </Typography>
                        {selectedProject.isDropSlow && (
                          <>
                            <Typography variant="body1">
                              <b>{t("slowDropPerDay")}:</b>{" "}
                              {formatVND(selectedProject.dropSlowNumber)}
                            </Typography>
                            <Typography variant="body1">
                              {t("slowDropEstimate", {
                                number: Math.ceil(estimateDayForSlowDrop),
                              })}
                            </Typography>
                          </>
                        )}
                        <Divider className="!my-2" />
                        <Typography variant="body1">
                          <b>{t("image")}:</b>{" "}
                          {selectedProject.fileAttaches.length > 0
                            ? ""
                            : t("no")}
                        </Typography>
                        {selectedProject.fileAttaches.length > 0 && (
                          <div className="grid grid-cols-3 gap-2 mt-2">
                            {selectedProject.fileAttaches.map((file, index) => (
                              <Image
                                key={index}
                                src={file.url}
                                className="!w-full !h-[60px] rounded-md"
                                preview={{
                                  mask: (
                                    <EyeOutlined style={{ fontSize: 24 }} />
                                  ),
                                }}
                              ></Image>
                            ))}
                          </div>
                        )}
                      </Collapse.Panel>
                    </Collapse>
                  ) : (
                    <ParentCard
                      title={selectedProject.name}
                      className="h-full"
                      titleClassName="!py-[6px]"
                      contentClassName="max-h-[1100px] overflow-auto"
                    >
                      <Typography variant="body1">
                        <b>{t("status")}:</b>{" "}
                        <ProjectStatusComp status={selectedProject.status} />
                      </Typography>
                      <Typography variant="body1" my={1}>
                        <b>{t("paymentStatus")}:</b>{" "}
                        <PaymentStatusComp
                          status={selectedProject.paymentStatus}
                        />
                        {/* {selectedProject.paymentStatus == PaymentStatus.Complete && ( */}
                        {/* )} */}
                      </Typography>
                      {/* {selectedProject.paymentStatus == PaymentStatus.Complete && ( */}
                      <>
                        <Typography
                          variant="body1"
                        // className="whitespace-pre-wrap flex justify-between"
                        >
                          <b>{t("fee")}: </b>
                          <b>{formatVND(selectedProject.moneyFinal)} ₫</b>
                          <Popover
                            content={
                              <>
                                <Typography
                                  variant="caption"
                                  className="whitespace-pre-wrap flex justify-between"
                                >
                                  <div className="w-[150px]">
                                    {t("estimatePackage")} (x
                                    {selectedProject.product?.numReview})
                                  </div>
                                  <div>
                                    {formatVND(
                                      selectedProject.product?.price || 0
                                    )}{" "}
                                    ₫
                                  </div>
                                </Typography>
                                <Typography
                                  variant="caption"
                                  className="whitespace-pre-wrap flex justify-between"
                                >
                                  <div>{t("estimateSlowDrop")}</div>
                                  <div>
                                    {formatVND(selectedProject.moneyDrop)} ₫
                                  </div>
                                </Typography>
                                <Typography
                                  variant="caption"
                                  className="whitespace-pre-wrap flex justify-between"
                                >
                                  <div>
                                    {t("estimateImage")} (x
                                    {selectedProject.fileAttaches.length})
                                  </div>
                                  <div>
                                    {formatVND(selectedProject.moneyImage)} ₫
                                  </div>
                                </Typography>
                                {selectedProject.moneyDiscount ? (
                                  <Typography
                                    variant="caption"
                                    className="whitespace-pre-wrap flex justify-between"
                                  >
                                    <div>{t("promotionPrice")}</div>
                                    <div>
                                      -
                                      {formatVND(selectedProject.moneyDiscount)}{" "}
                                      ₫
                                    </div>
                                  </Typography>
                                ) : null}
                                <Typography
                                  variant="caption"
                                  className="whitespace-pre-wrap flex justify-between"
                                >
                                  <div>{t("estimatePrice")}</div>
                                  <div>
                                    {formatVND(selectedProject.moneyTotal)} ₫
                                  </div>
                                </Typography>
                                <Typography
                                  variant="caption"
                                  className="whitespace-pre-wrap flex justify-between"
                                >
                                  <div>
                                    {t("estimateTax")} (
                                    {appStore.getOneConfiguration(
                                      ConfigurationParam.TaxPercent
                                    )}
                                    %)
                                  </div>
                                  <div>
                                    {formatVND(selectedProject.moneyTax)} ₫
                                  </div>
                                </Typography>
                                <Typography
                                  variant="caption"
                                  className="whitespace-pre-wrap flex justify-between"
                                >
                                  <b>{t("total")}</b>
                                  <b>
                                    {formatVND(selectedProject.moneyFinal)} ₫
                                  </b>
                                </Typography>
                              </>
                            }
                          >
                            <InfoCircleOutlined className="ml-1" />
                          </Popover>
                        </Typography>
                      </>
                      {/* )} */}
                      <Divider className="!my-2" />
                      <Typography variant="body1">
                        <b>{t("currentAverageRating")}:</b>{" "}
                        {selectedProject.totalStar}
                      </Typography>
                      <Divider className="!my-2" />
                      <Typography variant="body1">
                        <b>{t("address")}:</b> {selectedProject.address}
                      </Typography>
                      <Divider className="!my-2" />
                      <Typography variant="body1">
                        <b>{t("linkGGMap")}:</b>{" "}
                        <a
                          href={selectedProject.mapUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          {selectedProject.mapUrl}
                        </a>
                      </Typography>

                      <Divider className="!my-2" />
                      <Typography variant="body1">
                        <b>{t("projectDescription")}:</b>{" "}
                        {selectedProject.description}
                      </Typography>
                      <Divider className="!my-2" />
                      <Typography variant="body1" className="!leading-7">
                        <b>{t("keyword")}:{" "}</b>
                        {selectedProject.keywords.map((keyword, i) => (
                          <span className="mr-1">
                            <Tag key={i}>{keyword.name}</Tag>
                          </span>

                        ))}
                      </Typography>
                      <Divider className="!my-2" />
                      <Typography variant="body1">
                        <b>{t("package")}:</b>  {selectedProject.product
                          ? `${selectedProject.product.code} - ${formatVND(
                            selectedProject.product.unitPrice
                          )}₫/${t("reviews").toLowerCase()} - ${formatVND(
                            selectedProject.product.numReview
                          )} ${t("reviewTurn").toLowerCase()}`
                          : ""}
                      </Typography>
                      <Divider className="!my-2" />
                      <Typography variant="body1">
                        <b>{t("slowDrop")}:</b>{" "}
                        {selectedProject.isDropSlow ? t("yes") : t("no")}
                      </Typography>
                      {selectedProject.isDropSlow && (
                        <>
                          <Typography variant="body1">
                            <b>{t("slowDropPerDay")}:</b>{" "}
                            {formatVND(selectedProject.dropSlowNumber)}
                          </Typography>
                          <Typography variant="body1">
                            {t("slowDropEstimate", {
                              number: Math.ceil(estimateDayForSlowDrop),
                            })}
                          </Typography>
                        </>
                      )}
                      <Divider className="!my-2" />
                      <Typography variant="body1">
                        <b>{t("image")}:</b>{" "}
                        {selectedProject.fileAttaches.length > 0 ? "" : t("no")}
                      </Typography>
                      {selectedProject.fileAttaches.length > 0 && (
                        <div className="grid grid-cols-3 gap-2 mt-2">
                          {selectedProject.fileAttaches.map((file, index) => (
                            <Image
                              key={index}
                              src={file.url}
                              className="!w-full !h-[60px] rounded-md"
                              preview={{
                                mask: <EyeOutlined style={{ fontSize: 24 }} />,
                              }}
                            ></Image>
                          ))}
                        </div>
                      )}
                    </ParentCard>
                  )}
                  {/* <ParentCard
                    title={selectedProject.name}
                    className="h-full"
                    titleClassName="!py-[6px]"
                    contentClassName="max-h-[1100px] overflow-auto"
                  >
                    <Typography variant="body1">
                      <b>{t("status")}:</b>{" "}
                      <Tag
                        color={
                          ProjectStatusTrans[selectedProject.status]?.color
                        }
                      >
                        {t("project" + selectedProject.status)}
                      </Tag>
                    </Typography>
                    <Typography variant="body1" mt={1} mb={1}>
                      <b>{t("paymentStatus")}:</b>{" "}
                      <Tag
                        color={
                          PaymentStatusTrans[selectedProject.paymentStatus]
                            ?.color
                        }
                      >
                        {t(selectedProject.paymentStatus)}
                      </Tag>
                      <Popover
                        content={
                          <>
                            <Typography
                              variant="caption"
                              className="whitespace-pre-wrap flex justify-between"
                            >
                              <div className="w-[150px]">
                                {t("estimatePackage")} (x
                                {selectedProject.product?.numReview})
                              </div>
                              <div>
                                {formatVND(selectedProject.product?.price || 0)}{" "}
                                VND
                              </div>
                            </Typography>
                            <Typography
                              variant="caption"
                              className="whitespace-pre-wrap flex justify-between"
                            >
                              <div>{t("estimateSlowDrop")}</div>
                              <div>
                                {formatVND(selectedProject.moneyDrop)} VND
                              </div>
                            </Typography>
                            <Typography
                              variant="caption"
                              className="whitespace-pre-wrap flex justify-between"
                            >
                              <div>
                                {t("estimateImage")} (x
                                {selectedProject.fileAttaches.length})
                              </div>
                              <div>
                                {formatVND(selectedProject.moneyImage)} VND
                              </div>
                            </Typography>
                            {selectedProject.moneyDiscount ? (
                              <Typography
                                variant="caption"
                                className="whitespace-pre-wrap flex justify-between"
                              >
                                <div>{t("promotionPrice")}</div>
                                <div>
                                  -{formatVND(selectedProject.moneyDiscount)}{" "}
                                  VND
                                </div>
                              </Typography>
                            ) : null}
                            <Typography
                              variant="caption"
                              className="whitespace-pre-wrap flex justify-between"
                            >
                              <div>{t("estimatePrice")}</div>
                              <div>
                                {formatVND(selectedProject.moneyTotal)} VND
                              </div>
                            </Typography>
                            <Typography
                              variant="caption"
                              className="whitespace-pre-wrap flex justify-between"
                            >
                              <div>
                                {t("estimateTax")} (
                                {appStore.getOneConfiguration(
                                  ConfigurationParam.TaxPercent
                                )}
                                %)
                              </div>
                              <div>
                                {formatVND(selectedProject.moneyTax)} VND
                              </div>
                            </Typography>
                            <Typography
                              variant="caption"
                              className="whitespace-pre-wrap flex justify-between"
                            >
                              <b>{t("totalPrice")}</b>
                              <b>{formatVND(selectedProject.moneyFinal)} VND</b>
                            </Typography>
                          </>
                        }
                      >
                        <InfoCircleOutlined className="translate-y-[2px]" />
                      </Popover>
                    </Typography>
                    <>
                      <Typography
                        variant="body1"
                      >
                        <b>{t("totalPrice")}: </b>
                        <b>{formatVND(selectedProject.moneyFinal)} VND</b>
                      </Typography>
                    </>
                    <Divider className="!my-2" />
                    <Typography variant="body1">
                      <b>{t("totalStar")}:</b> {selectedProject.totalStar}
                    </Typography>
                    <Divider className="!my-2" />
                    <Typography variant="body1">
                      <b>{t("customer")}:</b>{" "}
                      <span>{selectedProject.customer?.code}</span>
                      {" - "}
                      {selectedProject.customer?.name}
                      {" - "}
                      {selectedProject.customer?.phone}
                    </Typography>
                    <Divider className="!my-2" />
                    <Typography variant="body1">
                      <b>{t("address")}:</b> {selectedProject.address}
                    </Typography>
                    <Divider className="!my-2" />
                    <Typography variant="body1">
                      <b>{t("projectDescription")}:</b>{" "}
                      {selectedProject.description}
                    </Typography>
                    <Divider className="!my-2" />
                    <Typography variant="body1" className="!leading-7">
                      <b>{t("keyword")}:</b>
                      {selectedProject.keywords.map((keyword, i) => (
                        <Tag key={i}>{keyword.name}</Tag>
                      ))}
                    </Typography>
                    <Divider className="!my-2" />
                    <Typography variant="body1">
                      <b>{t("package")}:</b> {selectedProject.product?.name} (x
                      {selectedProject.quantityReview})
                    </Typography>
                    <Divider className="!my-2" />
                    <Typography variant="body1">
                      <b>{t("slowDrop")}:</b>{" "}
                      {selectedProject.isDropSlow ? t("on") : t("no")}
                    </Typography>
                    {selectedProject.isDropSlow && (
                      <>
                        <Typography variant="body1">
                          <b>{t("slowDropPerDay")}:</b>{" "}
                          {formatVND(selectedProject.dropSlowNumber)}
                        </Typography>
                        <Typography variant="body1">
                          {t("slowDropEstimate", {
                            number: Math.ceil(estimateDayForSlowDrop),
                          })}
                        </Typography>
                      </>
                    )}
                    <Divider className="!my-2" />
                    <Typography variant="body1">
                      <b>{t("image")}:</b>{" "}
                      {selectedProject.fileAttaches.length > 0 ? "" : t("no")}
                    </Typography>
                    {selectedProject.fileAttaches.length > 0 && (
                      <div className="grid grid-cols-3 gap-2 mt-2">
                        {selectedProject.fileAttaches.map((file, index) => (
                          <Image
                            key={index}
                            src={file.url}
                            className="!w-full !h-[60px] rounded-md"
                          ></Image>
                        ))}
                      </div>
                    )}
                  </ParentCard> */}
                </Grid2>
                <Grid2 size={{ xs: 12, md: 9 }}>
                  <ParentCard
                    titleClassName="!py-[6px]"
                    title={t("reviewList")}
                    className="h-full"
                  >
                    {!isDoneGenerate ? (
                      <>
                        <Typography>
                          {t("generatingReview")}... (
                          {`${totalReview > (selectedProject?.quantityReview ?? 0) ? 0 : totalReview}/${selectedProject.quantityReview}`})
                        </Typography>
                        <Progress
                          percent={Number(
                            (
                              (totalReview > (selectedProject?.quantityReview ?? 0) ? 0 : totalReview /
                                (selectedProject.product?.numReview || 1)) *
                              100
                            ).toFixed(1)
                          )}
                        />
                      </>
                    ) : (
                      <>
                        <Typography color={colorProcess(selectedProject)}>
                          {selectedProject.paymentStatus ===
                            PaymentStatus.Complete
                            ? t("progressComplete")
                            : t("content")}
                          : {progressCompletionText(selectedProject)}
                        </Typography>

                        {selectedProject.paymentStatus ===
                          PaymentStatus.Pending ? (
                          <Progress
                            percent={progressCompletion(selectedProject)}
                          />
                        ) : (
                          <Progress
                            percent={progressCompletionWarranty(
                              selectedProject
                            )}
                          />
                        )}
                      </>
                    )}
                    <>
                      <AriviTable
                        size="small"
                        dataSource={reviews}
                        loading={loadingReview || !isDoneGenerate}
                        scroll={{ x: "max-content" }}
                        onChange={handleFilterOfTable}
                      >
                        {mdUp && (
                          <>
                            <Column
                              width={50}
                              title={t("stt")}
                              dataIndex={"stt"}
                              align="center"
                              render={(_, __, index) => {
                                if (queryReview.page == 1) {
                                  return index + 1;
                                }
                                return (
                                  (queryReview.page - 1) * queryReview.limit +
                                  index +
                                  1
                                );
                              }}
                            />
                            <Column
                              width={100}
                              title={t("updateAt")}
                              dataIndex={"updatedAt"}
                              align="center"
                              render={(updatedAt, record: Review, index) => {
                                return (
                                  <div className="flex flex-col items-center">
                                    <span>{unixToFullDate(updatedAt)}</span>
                                    <TbClockSearch
                                      className="w-fit text-gray-600 cursor-pointer hover:text-primary"
                                      size={20}
                                      onClick={() => {
                                        reviewLogModalRef.current?.handleOpen(
                                          record
                                        );
                                      }}
                                    />
                                  </div>
                                );
                              }}
                            />
                          </>
                        )}
                        <Column
                          // width={500}
                          align="center"
                          title={t("content")}
                          dataIndex={"contentChange"}
                          render={(contentChange, record: Review, index) => {
                            if (!mdUp) {
                              const stt =
                                queryReview.page == 1
                                  ? index + 1
                                  : (queryReview.page - 1) * queryReview.limit +
                                  index +
                                  1;
                              return (
                                <div className="flex gap-1">
                                  <span>{stt}.</span>
                                  <div className="flex-1">
                                    <Input.TextArea
                                      autoSize={{ minRows: 3, maxRows: 5 }}
                                      rows={3}
                                      disabled={
                                        selectedProject.paymentStatus !=
                                        PaymentStatus.Pending
                                      }
                                      value={contentChange}
                                      onChange={(e) => {
                                        record.contentChange = e.target.value;
                                        setReviews(cloneDeep(reviews));
                                      }}
                                      className="!text-gray-1"
                                    />
                                    <div className="flex gap-2 items-center">
                                      <b>{t("updateAt")}:</b>
                                      <div>
                                        {unixToFullDate(record.updatedAt)}
                                      </div>
                                    </div>
                                    {selectedProject.paymentStatus ===
                                      PaymentStatus.Complete && (
                                        <div className="flex gap-2 items-center">
                                          <b>{t("status")}:</b>
                                          <Tag
                                            className="text-xs text-center mr-0 translate-y-[1px]"
                                            color={
                                              getSettingStatus(record)?.color
                                            }
                                            style={{
                                              borderWidth: "1px",
                                              borderStyle: "solid",
                                              borderColor:
                                                getSettingStatus(record)
                                                  ?.borderColor,
                                              color:
                                                getSettingStatus(record)
                                                  ?.textColor,
                                            }}
                                          >
                                            {t(
                                              `review${record.isWarrantyReject
                                                ? ReviewStatus.RejectWarranty
                                                : record.status
                                              }`
                                            )}
                                          </Tag>
                                          {record.reviewUrl &&
                                            ![
                                              ReviewStatus.OverTime,
                                              ReviewStatus.Accept,
                                            ].includes(record.status) &&
                                            record.status !==
                                            ReviewStatus.Pending &&
                                            !(record as any).isWarranty && (
                                              <IconLink
                                                width={16}
                                                className="flex-shrink-0 cursor-pointer hover:text-primary"
                                                onClick={() => {
                                                  window.open(
                                                    record.reviewUrl,
                                                    "_blank"
                                                  );
                                                }}
                                              />
                                            )}
                                        </div>
                                      )}
                                    {selectedProject.paymentStatus ==
                                      PaymentStatus.Complete && (
                                        <>
                                          {record.status ==
                                            ReviewStatus.Complete &&
                                            !record.isWarrantyReject &&
                                            (record.expireWarrantyAt >
                                              dayjs().unix() ||
                                              record.expireWarrantyAt ==
                                              0) ? null : (
                                            <div className="flex gap-2 items-center">
                                              <b>{t("note")}:</b>
                                              {getNote(record, selectedProject)}
                                            </div>
                                          )}
                                        </>
                                      )}
                                    {selectedProject.paymentStatus ==
                                      PaymentStatus.Pending && (
                                        <Stack direction={"row"} gap={1} mt={1}>
                                          <Button
                                            fullWidth
                                            size="small"
                                            disabled={
                                              selectedProject.paymentStatus !=
                                              PaymentStatus.Pending
                                            }
                                            loading={
                                              loadingUpdateReview.loading &&
                                              record.id == loadingUpdateReview.id
                                            }
                                            onClick={() => {
                                              handleRegenerateReview(record);
                                            }}
                                          >
                                            {t("regenerate")}
                                          </Button>
                                          <Button
                                            size="small"
                                            fullWidth
                                            disabled={
                                              selectedProject.paymentStatus !=
                                              PaymentStatus.Pending ||
                                              record.content ==
                                              record.contentChange
                                            }
                                            loading={
                                              loadingUpdateReview.loading &&
                                              record.id == loadingUpdateReview.id
                                            }
                                            onClick={() => {
                                              handleUpdateReview(record);
                                            }}
                                          >
                                            {t("update")}
                                          </Button>
                                        </Stack>
                                      )}
                                  </div>
                                </div>
                              );
                            }
                            return (
                              <Input.TextArea
                                autoSize={{ minRows: 3, maxRows: 5 }}
                                rows={3}
                                disabled={
                                  selectedProject.paymentStatus !=
                                  PaymentStatus.Pending
                                }
                                value={contentChange}
                                onChange={(e) => {
                                  record.contentChange = e.target.value;
                                  setReviews(cloneDeep(reviews));
                                }}
                              />
                            );
                          }}
                        />
                        {mdUp &&
                          selectedProject.paymentStatus !==
                          PaymentStatus.Pending && (
                            <Column
                              width={70}
                              align="center"
                              title={t("link")}
                              dataIndex={"reviewUrl"}
                              render={(reviewUrl, record: Review) => {
                                return (
                                  reviewUrl &&
                                  ![
                                    ReviewStatus.OverTime,
                                    ReviewStatus.Accept,
                                  ].includes(record.status) &&
                                  record.status !== ReviewStatus.Pending &&
                                  !(record as any).isWarranty && (
                                    <IconLink
                                      width={25}
                                      className="flex-shrink-0 cursor-pointer hover:text-primary"
                                      onClick={() => {
                                        window.open(reviewUrl, "_blank");
                                      }}
                                    />
                                  )
                                );
                              }}
                            />
                          )}
                        {mdUp &&
                          selectedProject.paymentStatus !==
                          PaymentStatus.Pending && (
                            <Column
                              width={160}
                              align="center"
                              title={t("status")}
                              dataIndex={"status"}
                              render={(status, record: Review) => {
                                let displayStatus = status;
                                if (
                                  status === ReviewStatus.Complete &&
                                  record.isWarrantyReject
                                ) {
                                  displayStatus = ReviewStatus.RejectWarranty;
                                }

                                const statusData = ReviewStatusTrans2[
                                  status as keyof typeof ReviewStatusTrans2
                                ] as any;

                                return (
                                  <>
                                    <ReviewStatusComp status={displayStatus} />

                                    {/* <Tag
                                      className="text-xs text-center mr-0"
                                      color={
                                        ReviewStatusTrans2[
                                          status as keyof typeof ReviewStatusTrans2
                                        ]?.color
                                      }
                                      style={{
                                        borderWidth: "1px",
                                        borderStyle: "solid",
                                        borderColor:
                                          statusData &&
                                          "borderColor" in statusData
                                            ? statusData.borderColor
                                            : undefined,
                                        color:
                                          statusData?.textColor || undefined,
                                      }}
                                    >
                                      {t("review" + status)}
                                    </Tag> */}
                                  </>
                                );
                              }}
                              filterDropdown={({ confirm }) => {
                                const itemAll = [{ key: "", label: t("ALL") }];

                                const items = [
                                  ...itemAll,
                                  ...Object.values(ReviewStatusTrans2).map(
                                    (item) => ({
                                      key: item.value,
                                      label: t(`review${item.value}`),
                                    })
                                  ),
                                ];

                                return (
                                  <Menu
                                    items={items}
                                    onClick={(selected) => {
                                      setReviewQuery({
                                        ...queryReview,
                                        status: selected.key,
                                        page: 1,
                                      });
                                      fetchReview({
                                        ...queryReview,
                                        status: selected.key,
                                        page: 1,
                                      });
                                      confirm({ closeDropdown: true });
                                    }}
                                  />
                                );
                              }}
                            />
                          )}
                        {mdUp &&
                          selectedProject.paymentStatus !=
                          PaymentStatus.Complete && (
                            <Column
                              width={120}
                              align="center"
                              title={t("operation")}
                              dataIndex={"operation"}
                              render={(_, record: Review) => {
                                return (
                                  <Stack direction={"column"} gap={1}>
                                    <Button
                                      size="small"
                                      disabled={
                                        selectedProject.paymentStatus !=
                                        PaymentStatus.Pending
                                      }
                                      loading={
                                        loadingUpdateReview.loading &&
                                        record.id == loadingUpdateReview.id
                                      }
                                      onClick={() => {
                                        handleRegenerateReview(record);
                                      }}
                                    >
                                      {t("regenerate")}
                                    </Button>
                                    <Button
                                      size="small"
                                      disabled={
                                        selectedProject.paymentStatus !=
                                        PaymentStatus.Pending ||
                                        record.content == record.contentChange
                                      }
                                      loading={
                                        loadingUpdateReview.loading &&
                                        record.id == loadingUpdateReview.id
                                      }
                                      onClick={() => {
                                        handleUpdateReview(record);
                                      }}
                                    >
                                      {t("update")}
                                    </Button>
                                  </Stack>
                                );
                              }}
                            />
                          )}
                        {mdUp &&
                          selectedProject.paymentStatus ==
                          PaymentStatus.Complete && (
                            <Column
                              align="center"
                              width={140}
                              title={t("note")}
                              dataIndex={"note"}
                              render={(note, record: Review) => {
                                // if (
                                //   record.status == ReviewStatus.Complete &&
                                //   !record.isWarrantyReject
                                // ) {
                                //   return (
                                //     <Button
                                //       size="small"
                                //       loading={loadingWarrantyReview}
                                //       onClick={() => {
                                //         handleWarrantyReview(record.id);
                                //       }}
                                //     >
                                //       {t("onNeedWarranty")}
                                //     </Button>
                                //   );
                                // }

                                const expireTime =
                                  record?.expireWarrantyAt < 1e12
                                    ? new Date(
                                      record?.expireWarrantyAt * 1000
                                    ).getTime()
                                    : new Date(
                                      record?.expireWarrantyAt
                                    ).getTime();
                                return (
                                  <>
                                    {record.status != ReviewStatus.Complete &&
                                      t(
                                        ReviewStatusTrans2[
                                          record.status as keyof typeof ReviewStatusTrans2
                                        ]?.note
                                      )}
                                    {record.status === ReviewStatus.Complete &&
                                      record?.expireWarrantyAt > 0 &&
                                      expireTime > Date.now() && (
                                        <>
                                          {t("remainingDays")}:{" "}
                                          {dayjs
                                            .unix(record?.expireWarrantyAt)
                                            .diff(dayjs(), "day")}{" "}
                                          {t("days")}
                                        </>
                                      )}
                                    {record.status == ReviewStatus.Complete &&
                                      record.expireWarrantyAt != 0 &&
                                      expireTime < Date.now() && (
                                        <>{t("completeStatusNote")}</>
                                      )}
                                    {record.status ==
                                      ReviewStatus.RejectWarranty && (
                                        <>
                                          <div>{record.note}</div>
                                        </>
                                      )}
                                  </>
                                );
                              }}
                            />
                          )}
                        {mdUp &&
                          selectedProject.paymentStatus ==
                          PaymentStatus.Complete && (
                            <Column
                              align="center"
                              width={140}
                              title={t("revenue")}
                              dataIndex={"price"}
                              render={(price, record: Review) => {
                                return (
                                  <Typography variant="body2">
                                    {formatVND(price)} ₫
                                  </Typography>
                                );
                              }}
                            />
                          )}
                      </AriviTable>
                      <Pagination
                        currentPage={queryReview.page}
                        defaultPageSize={queryReview.limit}
                        total={totalReview}
                        onChange={({ limit, page }) => {
                          setReviewQuery({ ...queryReview, limit, page });
                          fetchReview({ ...queryReview, limit, page });
                        }}
                      />
                    </>
                  </ParentCard>
                  {/* <ParentCard
                    titleClassName="!py-[6px]"
                    title={t("reviewList")}
                    className="h-full"
                  >
                    {!isDoneGenerate ? (
                      <>
                        <Typography>{t("generatingReview")}...</Typography>
                        <Progress
                          percent={Number(
                            (
                              (totalReview /
                                (selectedProject.product?.numReview || 1)) *
                              100
                            ).toFixed(0)
                          )}
                        />
                      </>
                    ) : (
                      <>
                        <Typography>
                          {t("progress")}:{" "}
                          {selectedProject.currentCompleteReview}/
                          {selectedProject.quantityReview}
                        </Typography>
                        <Progress
                          percent={Number(selectedProject.completePercent)}
                        />
                      </>
                    )}
                    <>
                      <AriviTable
                        size="small"
                        dataSource={reviews}
                        loading={loadingReview || !isDoneGenerate}
                        scroll={{ x: "max-content" }}
                      >
                        {mdUp && (
                          <>
                            <Column
                              width={50}
                              title={t("stt")}
                              dataIndex={"stt"}
                              align="center"
                              render={(_, __, index) => {
                                if (queryReview.page == 1) {
                                  return index + 1;
                                }
                                return (
                                  (queryReview.page - 1) * queryReview.limit +
                                  index +
                                  1
                                );
                              }}
                            />
                            <Column
                              width={100}
                              title={t("updateAt")}
                              dataIndex={"updatedAt"}
                              align="center"
                              render={(updatedAt, __, index) => {
                                return unixToFullDate(updatedAt);
                              }}
                            />
                          </>
                        )}
                        <Column
                          // width={500}
                          title={t("content")}
                          dataIndex={"contentChange"}
                          render={(contentChange, record: Review, index) => {
                            if (!mdUp) {
                              const stt =
                                queryReview.page == 1
                                  ? index + 1
                                  : (queryReview.page - 1) * queryReview.limit +
                                    index +
                                    1;
                              return (
                                <div className="flex gap-1">
                                  <span>{stt}.</span>
                                  <div className="flex-1">
                                    <Input.TextArea
                                      autoSize={{ minRows: 3, maxRows: 5 }}
                                      rows={3}
                                      disabled={
                                        selectedProject.paymentStatus !=
                                        PaymentStatus.Pending
                                      }
                                      value={contentChange}
                                      onChange={(e) => {
                                        record.contentChange = e.target.value;
                                        setReviews(cloneDeep(reviews));
                                      }}
                                    />
                                    <div className="flex gap-2 items-center">
                                      <b>{t("updateAt")}:</b>
                                      <div>
                                        {unixToFullDate(record.updatedAt)}
                                      </div>
                                      {record.reviewUrl ? (
                                        <IconLink
                                          width={16}
                                          className="flex-shrink-0 cursor-pointer hover:text-primary"
                                          onClick={() => {
                                            window.open(
                                              record.reviewUrl,
                                              "_blank"
                                            );
                                          }}
                                        />
                                      ) : (
                                        <></>
                                      )}
                                    </div>
                                    <div className="flex gap-2 items-center">
                                      <b>{t("status")}:</b>
                                      <Tag
                                        className="text-xs text-center mr-0 translate-y-[1px]"
                                        color={
                                          record.isWarrantyReject
                                            ? undefined
                                            : ReviewStatusTrans[
                                                record.status as keyof typeof ReviewStatusTrans
                                              ]?.color
                                        }
                                      >
                                        {t(
                                          `review${
                                            record.isWarrantyReject
                                              ? ReviewStatus.RejectWarranty
                                              : record.status
                                          }`
                                        )}
                                      </Tag>
                                      {record.reviewUrl ? (
                                        <IconLink
                                          width={16}
                                          className="flex-shrink-0 cursor-pointer hover:text-primary"
                                          onClick={() => {
                                            window.open(
                                              record.reviewUrl,
                                              "_blank"
                                            );
                                          }}
                                        />
                                      ) : (
                                        <></>
                                      )}
                                    </div>
                                    {selectedProject.paymentStatus ==
                                      PaymentStatus.Complete && (
                                      <>
                                        {record.status ==
                                          ReviewStatus.Complete &&
                                        !record.isWarrantyReject ? (
                                          <Button size="small">
                                            {t("onNeedWarranty")}
                                          </Button>
                                        ) : (
                                          <div className="flex gap-2 items-center">
                                            <b>{t("note")}:</b>
                                            <span>{record.note}</span>
                                          </div>
                                        )}
                                      </>
                                    )}
                                    {selectedProject.paymentStatus ==
                                      PaymentStatus.Pending && (
                                      <Stack direction={"row"} gap={1} mt={1}>
                                        <Button
                                          fullWidth
                                          size="small"
                                          disabled={
                                            selectedProject.paymentStatus !=
                                            PaymentStatus.Pending
                                          }
                                          loading={
                                            loadingUpdateReview.loading &&
                                            record.id == loadingUpdateReview.id
                                          }
                                          onClick={() => {
                                            handleRegenerateReview(record);
                                          }}
                                        >
                                          {t("regenerate")}
                                        </Button>
                                        <Button
                                          size="small"
                                          fullWidth
                                          disabled={
                                            selectedProject.paymentStatus !=
                                              PaymentStatus.Pending ||
                                            record.content ==
                                              record.contentChange
                                          }
                                          loading={
                                            loadingUpdateReview.loading &&
                                            record.id == loadingUpdateReview.id
                                          }
                                          onClick={() => {
                                            handleUpdateReview(record);
                                          }}
                                        >
                                          {t("update")}
                                        </Button>
                                      </Stack>
                                    )}
                                  </div>
                                </div>
                              );
                            }
                            return (
                              <Input.TextArea
                                autoSize={{ minRows: 3, maxRows: 5 }}
                                rows={3}
                                disabled={
                                  selectedProject.paymentStatus !=
                                  PaymentStatus.Pending
                                }
                                value={contentChange}
                                onChange={(e) => {
                                  record.contentChange = e.target.value;
                                  setReviews(cloneDeep(reviews));
                                }}
                              />
                            );
                          }}
                        />
                        {mdUp && (
                          <Column
                            width={70}
                            align="center"
                            title={t("link")}
                            dataIndex={"reviewUrl"}
                            render={(reviewUrl, record: Review) => {
                              return reviewUrl ? (
                                <IconLink
                                  width={25}
                                  className="flex-shrink-0 cursor-pointer hover:text-primary"
                                  onClick={() => {
                                    window.open(reviewUrl, "_blank");
                                  }}
                                />
                              ) : (
                                <></>
                              );
                            }}
                          />
                        )}
                        {mdUp && (
                          <Column
                            width={100}
                            align="center"
                            title={t("status")}
                            dataIndex={"status"}
                            render={(status, record: Review) => {
                              if (record.isWarrantyReject) {
                                return (
                                  <Tag className="text-xs text-center mr-0">
                                    {t("review" + ReviewStatus.RejectWarranty)}
                                  </Tag>
                                );
                              }
                              return (
                                <Tag
                                  className="text-xs text-center mr-0"
                                  color={
                                    ReviewStatusTrans[
                                      status as keyof typeof ReviewStatusTrans
                                    ]?.color
                                  }
                                >
                                  {t("review" + status)}
                                </Tag>
                              );
                            }}
                          />
                        )}
                        {mdUp &&
                          selectedProject.paymentStatus !=
                            PaymentStatus.Complete && (
                            <Column
                              width={120}
                              title={t("operation")}
                              dataIndex={"operation"}
                              render={(_, record: Review) => {
                                return (
                                  <Stack direction={"column"} gap={1}>
                                    <Button
                                      size="small"
                                      disabled={
                                        selectedProject.paymentStatus !=
                                        PaymentStatus.Pending
                                      }
                                      loading={
                                        loadingUpdateReview.loading &&
                                        record.id == loadingUpdateReview.id
                                      }
                                      onClick={() => {
                                        handleRegenerateReview(record);
                                      }}
                                    >
                                      {t("regenerate")}
                                    </Button>
                                    <Button
                                      size="small"
                                      disabled={
                                        selectedProject.paymentStatus !=
                                          PaymentStatus.Pending ||
                                        record.content == record.contentChange
                                      }
                                      loading={
                                        loadingUpdateReview.loading &&
                                        record.id == loadingUpdateReview.id
                                      }
                                      onClick={() => {
                                        handleUpdateReview(record);
                                      }}
                                    >
                                      {t("update")}
                                    </Button>
                                  </Stack>
                                );
                              }}
                            />
                          )}
                        {mdUp &&
                          selectedProject.paymentStatus ==
                            PaymentStatus.Complete && (
                            <Column
                              width={120}
                              title={t("note")}
                              dataIndex={"note"}
                              render={(note, record: Review) => {
                                if (
                                  record.status == ReviewStatus.Complete &&
                                  !record.isWarrantyReject
                                ) {
                                  return (
                                    <Button
                                      size="small"
                                      loading={loadingWarrantyReview}
                                      onClick={() => {
                                        handleWarrantyReview(record.id);
                                      }}
                                    >
                                      {t("onNeedWarranty")}
                                    </Button>
                                  );
                                }
                                return note;
                              }}
                            />
                          )}
                      </AriviTable>
                      <Pagination
                        currentPage={queryReview.page}
                        defaultPageSize={queryReview.limit}
                        total={totalReview}
                        onChange={({ limit, page }) => {
                          setReviewQuery({ ...queryReview, limit, page });
                          fetchReview({ ...queryReview, limit, page });
                        }}
                      />
                    </>
                  </ParentCard> */}
                </Grid2>
              </Grid2>
            )}
          </Tabs.TabPane>
          {selectedProject?.id && (
            <Tabs.TabPane tab="Logs" key={ProjectModalTabKeys.ActivityLog}>
              <ProjectActivityLogTab projectId={selectedProject?.id} />
            </Tabs.TabPane>
          )}
        </Tabs>

        <ReviewLogModal
          ref={reviewLogModalRef}
          onClose={() => { }}
          onSubmitOk={() => { }}
        />
        <ProjectPaymentModal
          onClose={() => { }}
          onSubmitOk={() => {
            if (selectedProject) {
              fetchProject(selectedProject.id);
              onSubmitOk();
            }
          }}
          ref={projectPaymentRef}
        />
      </Modal>
    );
  }
);
