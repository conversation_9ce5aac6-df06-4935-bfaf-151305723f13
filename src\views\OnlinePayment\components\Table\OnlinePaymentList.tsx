import { DeleteOutlined, DownOutlined, EditFilled } from "@ant-design/icons";
import { Button, Image, Popconfirm, Space, Table, Tag } from "antd";
import Column from "antd/lib/table/Column";
import { IPagination, Pagination } from "components/Pagination";
import DropdownCell from "components/Table/DropdownCell";
import React from "react";
import { EOnlinePaymentTrans, OnlinePayment } from "types/online-payment";
import { StockCode } from "types/stockCode";
import { unixToFullDate } from "utils/dateFormat";
import { OnlinePaymentModal } from "../Modal/OnlinePaymentModal";
import { useTranslation } from "react-i18next";

interface PropsType {
  dataSource: OnlinePayment[];
  loading: boolean;
  pagination?: IPagination;
  onlinePaymentModalRef?: React.MutableRefObject<
    OnlinePaymentModal | undefined
  >;
  onDelete: (onlinePaymentId: number) => void;
  onRefreshData: () => void;
  showActionColumn?: boolean;
}

export const OnlinePaymentList = ({
  dataSource,
  loading,
  pagination,
  onlinePaymentModalRef,
  onRefreshData,
  onDelete,
  showActionColumn = true,
}: PropsType) => {
  const { t } = useTranslation();

  return (
    <div>
      <Table
        bordered
        scroll={{ x: "max-content", y: "calc(100vh - 380px)" }}
        loading={loading}
        pagination={false}
        rowKey="id"
        dataSource={dataSource}
        size="small"
        className="custom-scrollbar"
        // onChange={}
      >
        <Column
          align="center"
          width={100}
          title="Logo"
          dataIndex="icon"
          key={"icon"}
          render={(text, record: OnlinePayment) => (
            <Image
              src={record.icon}
              preview={false}
              className="!h-[35px] object-contain"
            />
          )}
        />
        <Column
          width={250}
          title={t("onlinePaymentName")}
          dataIndex="code"
          key={"code"}
          render={(text, record: OnlinePayment) => (
            <span
              className="font-medium text-primary cursor-pointer"
              onClick={() => {
                onlinePaymentModalRef?.current?.handleUpdate(record);
              }}
            >
              {record?.name}
            </span>
          )}
        />
        <Column
          width={200}
          title={t("stk")}
          dataIndex="code"
          key={"code"}
          render={(text, record: OnlinePayment) => (
            <span className="">{record?.bankNumber}</span>
          )}
        />
        <Column
          width={250}
          title={t("ownerName")}
          dataIndex="code"
          key={"code"}
          render={(text, record: OnlinePayment) => (
            <span className="font-medium ">{record?.ownerName}</span>
          )}
        />

        <Column
          align="center"
          width={150}
          title={t("type")}
          dataIndex="code"
          key={"code"}
          render={(text, record: OnlinePayment) => (
            <span className="font-medium ">{t(record.type)}</span>
          )}
        />

        {/* <Column
          width={150}
          align="center"
          title="Trạng thái kích hoạt"
          dataIndex="isEnabled"
          key={"onlinePayment.isEnabled"}
          render={(text, record: OnlinePayment) =>
            record?.isEnabled ? (
              <>
                <Tag color="blue">Đã kích hoạt</Tag>
              </>
            ) : (
              <>
                <Tag color="red">Chưa kích hoạt</Tag>
              </>
            )
          }
        /> */}

        <Column
          align="center"
          width={150}
          title={t("createdDate")}
          dataIndex="createdAt"
          key={"createdAt"}
          render={(text, record: StockCode) => (
            <span className="">{unixToFullDate(record?.createdAt)}</span>
          )}
        />

        {showActionColumn && (
          <Column
            fixed="right"
            width={120}
            align="center"
            title=""
            key="moneyCommission"
            dataIndex={"moneyCommission"}
            render={(text, record: OnlinePayment) => (
              //@ts-ignore
              <DropdownCell
                text={t("action")}
                items={[
                  {
                    onClick: () => "",
                    // createOrderModalRef.current?.handleUpdate(record),
                    label: (
                      <Button
                        icon={<EditFilled />}
                        type="primary"
                        className="w-full !font-medium"
                        onClick={() => {
                          onlinePaymentModalRef?.current?.handleUpdate(record);
                        }}
                      >
                        {t("update")}
                      </Button>
                    ),
                    key: "update",
                  },

                  {
                    label: (
                      <Popconfirm
                        placement="topLeft"
                        title={t("confirm?")}
                        onConfirm={() => onDelete(record.id)}
                        okText={t("save")}
                        cancelText={t("close")}
                      >
                        <Button
                          icon={<DeleteOutlined />}
                          className="w-full !text-white !bg-red-500 !font-medium"
                        >
                          {t("delete")}
                        </Button>
                      </Popconfirm>
                    ),
                    key: "delete",
                  },
                ]}
                trigger={["click"]}
              >
                <a onClick={(e) => e.preventDefault()}>
                  <Space>
                    {t("action")}
                    <DownOutlined />
                  </Space>
                </a>
              </DropdownCell>
              // <div className="flex flex-col gap-2">

              // </div>
            )}
          />
        )}
      </Table>
      {pagination && <Pagination {...pagination} />}
    </div>
  );
};
