import { staffApi } from "api/staff.api";
import { useState } from "react";
import { Staff } from "types/staff";
import { QueryParam } from "types/query";
import { message } from "antd";
import { useTranslation } from "react-i18next";

export interface StaffQuery extends QueryParam {}

export const useStaff = () => {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<Staff[]>([]);
  const [total, setTotal] = useState(0);
  const { t } = useTranslation();

  const fetchStaff = async (query: StaffQuery) => {
    setLoading(true);
    const { data } = await staffApi.findAll(query);

    setData(data.staffs);
    setTotal(data.total);
    setLoading(false);
  };

  const deleteStaff = async (id: number) => {
    setLoading(true);
    try {
      await staffApi.delete(id);
      message.success(t("actionSuccessfully"));
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };

  return {
    staffs: data,
    totalStaffs: total,
    fetchStaff,
    loadingStaff: loading,
    deleteStaff,
  };
};
