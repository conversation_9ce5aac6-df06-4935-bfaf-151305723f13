
import { Tag } from 'antd'
import { useTranslation } from 'react-i18next';
import { PaymentStatus, PaymentStatusTrans } from 'types/payment';
import "./styles/PaymentStatus.scss";
import clsx from 'clsx';

interface Props {
  status: PaymentStatus
}

export const PaymentStatusComp = ({ status }: Props) => {
  const { t } = useTranslation();

  return (
    <Tag
      className={clsx("payment-status", status)}
      color={PaymentStatusTrans[status as keyof typeof PaymentStatusTrans]?.color}
    >
      {t(`payment${status}`)}
    </Tag>
  )
}
