import { Libraries, StandaloneSearchBox } from "@react-google-maps/api";
import { $googleApiKey } from "constant";
import GoogleMapReact from "google-map-react";
import { cloneDeep, debounce } from "lodash";
import React, {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from "react";
import { useTranslation } from "react-i18next";
import { Marker } from "./components/Marker";
import { CoordAddress } from "types/address";
import { Select } from "antd";
import { googleMapsApi } from "api/googleMap.api";
import { addBoldToStringAtIndex } from "utils/data";
import { Project } from "types/project";
import { Partner } from "types/partner";
import useSupercluster from "use-supercluster";

export interface MapWithAutocompleteRef {
  fitBounds: () => void;
}

interface MapWithAutocompleteProps {
  defaultCenter?: {
    lat: number;
    lng: number;
  };
  defaultZoom?: number;
  coords?: (GoogleMapReact.Coords & { project?: Project; partner?: Partner })[];
  address?: string;
  noInput?: boolean;
  draggable?: boolean;
  noAutoCenterByCoord?: boolean;
  enableCluster?: boolean;
  onPlaceSelected?: (coordAddress: CoordAddress) => void;
  onMapChange?: (value: GoogleMapReact.ChangeEventValue) => void;
  onLoaded?: () => void;
  viewPartner?: boolean;
  colorCluster?: string;
  style?: React.CSSProperties;
}

interface MarkerProps {
  lat: number;
  lng: number;
}

interface PlaceOptionMatchedSubString {
  length: number;
  offset: number;
}

interface PlaceOption {
  description: string;
  matched_substrings: PlaceOptionMatchedSubString[];
  place_id: string;
  reference: string;
  structured_formatting: {
    main_text: string;
    main_text_matched_substrings: PlaceOptionMatchedSubString[];
    secondary_text: string;
  };
  terms: { offset: number; value: string }[];
  types: string[];
}

// //@ts-ignore
// if (typeof HTMLDialogElement === "undefined") {
//   //@ts-ignore
//   window.HTMLDialogElement = class {}; // Fake class to prevent errors
// }

const libraries: Libraries = ["places"];
export const DEFAULT_LAT = 10.7964726;
export const DEFAULT_LNG = 106.6486002;
export const DEFAULT_NE = { lat: 10.804819310750915, lng: 106.67997131663822 };
export const DEFAULT_NW = { lat: 10.804819310750915, lng: 106.6174007447388 };
export const DEFAULT_SE = { lat: 10.787957029877433, lng: 106.67997131663822 };
export const DEFAULT_SW = { lat: 10.787957029877433, lng: 106.6174007447388 };

const MapWithAutocomplete = forwardRef(
  (
    {
      defaultCenter = { lat: DEFAULT_LAT, lng: DEFAULT_LNG },
      defaultZoom = 12,
      coords = [],
      address,
      noInput = false,
      draggable = true,
      noAutoCenterByCoord = false,
      enableCluster,
      onPlaceSelected,
      onMapChange,
      onLoaded,
      viewPartner,
      colorCluster,
      style = { height: "400px", width: "100%" },
    }: MapWithAutocompleteProps,
    ref
  ) => {
    const { t } = useTranslation();
    const [center, setCenter] = useState(defaultCenter);
    const [zoom, setZoom] = useState(defaultZoom);
    const [bounds, setBounds] = useState<any>();
    // const [marker, setMarker] = useState<MarkerProps>({
    //   lat: DEFAULT_LAT,
    //   lng: DEFAULT_LNG,
    // });
    const [marker, setMarker] = useState<MarkerProps>();
    const [searchBox, setSearchBox] =
      useState<google.maps.places.SearchBox | null>(null);
    const [placeOptions, setPlaceOptions] = useState<PlaceOption[]>([]);
    const [currentPlaceId, setCurrentPlaceId] = useState("");

    const inputRef = useRef<HTMLInputElement>(null);
    const mapRef = useRef<google.maps.Map>();
    const mapsRef = useRef<typeof google.maps>();

    useImperativeHandle<any, MapWithAutocompleteRef>(
      ref,
      () => {
        return {
          fitBounds() {
            // const [marker1, marker2] = findFarthestMarkers(
            //   coords.map((it) => ({ lat: it.lat, lng: it.lng }))
            // );

            const maps = mapsRef.current;
            const map = mapRef.current;

            if (!maps || !map) return;

            const bounds = new maps.LatLngBounds();
            // bounds.extend(new maps.LatLng(marker1.lat, marker1.lng));
            // bounds.extend(new maps.LatLng(marker2.lat, marker2.lng));
            coords.forEach(({ lat, lng }) => {
              bounds.extend(new maps.LatLng(lat, lng));
            });
            console.log({ bounds });
            map.fitBounds(bounds);
            const sw = bounds.getSouthWest();
            const ne = bounds.getNorthEast();
            const nw = new google.maps.LatLng(ne.lat(), sw.lng());
            const se = new google.maps.LatLng(sw.lat(), ne.lng());
            // setBounds({
            //   nw: { lat: nw.lat(), lng: nw.lng() },
            //   ne: { lat: ne.lat(), lng: ne.lng() },
            //   sw: { lat: sw.lat(), lng: sw.lng() },
            //   se: { lat: se.lat(), lng: se.lng() },
            // });
            setBounds([nw.lng(), se.lat(), se.lng(), nw.lat()]);
          },
        };
      },
      [coords]
    );

    const haversineDistance = (
      lat1: number,
      lon1: number,
      lat2: number,
      lon2: number
    ) => {
      const toRad = (deg: number) => (deg * Math.PI) / 180;
      const R = 6371; // Earth radius in km

      const dLat = toRad(lat2 - lat1);
      const dLon = toRad(lon2 - lon1);
      const a =
        Math.sin(dLat / 2) * Math.sin(dLat / 2) +
        Math.cos(toRad(lat1)) *
          Math.cos(toRad(lat2)) *
          Math.sin(dLon / 2) *
          Math.sin(dLon / 2);
      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
      return R * c;
    };

    const findFarthestMarkers = (markers: { lat: number; lng: number }[]) => {
      let maxDistance = 0;
      let pair = [markers[0], markers[0]];

      for (let i = 0; i < markers.length; i++) {
        for (let j = i + 1; j < markers.length; j++) {
          const dist = haversineDistance(
            markers[i].lat,
            markers[i].lng,
            markers[j].lat,
            markers[j].lng
          );
          if (dist > maxDistance) {
            maxDistance = dist;
            pair = [markers[i], markers[j]];
          }
        }
      }

      return pair;
    };

    const handleSearchBoxLoad = (ref: google.maps.places.SearchBox | null) => {
      setSearchBox(ref);
    };

    const handlePlacesChanged = async ({ _place }: { _place?: any }) => {
      console.log({ _place });
      if (_place) {
        // const places = searchBox.getPlaces();
        const place = _place;
        const location = place.geometry?.location;
        if (location) {
          console.log("zo location nè", { location });
          setMarker({ lat: location.lat, lng: location.lng });
          setCenter({ lat: location.lat, lng: location.lng });
          // debugger;
          onPlaceSelected?.({
            address: inputRef.current?.value || "",
            lat: location.lat,
            lng: location.lng,
            placeId: place.place_id,
          });
          if (inputRef.current) {
            inputRef.current.value =
              place.formatted_address || place.name || "";
          }
        }
      }
    };

    const handleMapClick = async ({
      lat,
      lng,
    }: {
      lat: number;
      lng: number;
    }) => {
      setMarker({ lat, lng });
      setCenter({ lat, lng });

      // onPlaceSelected?.({ lat, lng });
      const { address, result } = await getGeocode(lat, lng);

      console.log({ result });
      // if (inputRef.current) {
      //   inputRef.current.value = address;
      // }
    };

    const getPlaceDetailFromMap = (
      address_components: any[],
      formatted_address: string
    ) => {
      const tmpCity = getCityFromGeocode(address_components);
      const tmpDistrict = getDistrictFromGeocode(address_components);
      const tmpWard = getWardFromFullAddress(
        formatted_address,
        address_components
      );
      const tmpStreet = getStreetFromGeocode(address_components);
      const tmpCountry = getCountryFromGeocode(address_components);
      const tmpPostalCode = getPostalCodeFromGeocode(address_components);
      return {
        tmpCountry,
        tmpCity,
        tmpDistrict,
        tmpWard,
        tmpStreet,
        tmpPostalCode,
      };
    };

    const getPostalCodeFromGeocode = (array: any[]): string => {
      const _array = cloneDeep(array);
      const result = _array
        .reverse()
        .filter((o: { types: string | string[] }) => {
          return o.types.includes("postal_code");
        });

      if (result.length > 0) {
        return result[0].short_name;
      }

      return "";
    };
    const getCountryFromGeocode = (array: any[]): string => {
      const _array = cloneDeep(array);
      const result = _array
        .reverse()
        .filter((o: { types: string | string[] }) => {
          return o.types.includes("country");
        });

      if (result.length > 0) {
        return result[0].short_name;
      }

      return "";
    };
    const getCityFromGeocode = (array: any[]): string => {
      const _array = cloneDeep(array);
      const result = _array
        .reverse()
        .filter((o: { types: string | string[] }) => {
          return o.types.includes("administrative_area_level_1");
        });

      if (result.length > 0) {
        return result[0].long_name;
      }

      return "";
    };

    const getDistrictFromGeocode = (array: any[]): string => {
      const _array = cloneDeep(array);
      const result = _array
        .reverse()
        .filter((o: { types: string | string[] }) => {
          return (
            o.types.includes("administrative_area_level_2") ||
            o.types.includes("locality")
          );
        });

      if (result.length > 0) {
        return result[0].long_name;
      }

      return "";
    };
    const getWardFromFullAddress = (address: string, array: any[]) => {
      const _array = cloneDeep(array);

      const result = _array
        .reverse()
        .filter((o: { types: string | string[] }) => {
          return o.types.includes("administrative_area_level_3");
        });
      const split = address.split(", ");
      let ward = "";

      const regex = /(phường|xã|thị trấn).+/gm;
      const regexName = /(phường|xã|thị trấn)/gm;
      for (const item of split) {
        if (regex.test(item.toLowerCase())) {
          ward = item.trim();
        }
      }
      if (!ward) {
        if (result.length > 0) {
          ward = result[0].long_name;
        } else {
          ward = split[1] || "";
        }
      }

      return ward;
    };
    const getStreetFromGeocode = (array: any[]): string => {
      let tmpStreet = "";
      const route = array.filter((o: { types: string | string[] }) => {
        return o.types.includes("route");
      });
      const streetNum = array.filter((o: { types: string | string[] }) => {
        return o.types.includes("street_number");
      });

      if (streetNum.length > 0) {
        tmpStreet += streetNum[0].long_name;
      }

      if (route.length > 0) {
        tmpStreet += " " + route[0].long_name;
      }

      return tmpStreet;
    };

    const getGeocode = async (lat: number, lng: number) => {
      const response = await fetch(
        `https://maps.googleapis.com/maps/api/geocode/json?latlng=${lat},${lng}&key=${$googleApiKey}`
      );
      const data = await response.json();
      if (data.status === "OK") {
        return {
          address: data.results[0]?.formatted_address || "",
          result: data.results[0],
        };
      } else {
        return { address: "", result: undefined };
      }
    };

    useEffect(() => {
      //Kiểm tra tọa độ hợp lệ, đảm bảo phải có lat, lng
      // const coordsValid = coords?.every((item) => item.lat > 0 && item.lng > 0);
      // debugger;
      if (coords && !noAutoCenterByCoord) {
        coords.map((item) => {
          setMarker({ lat: item.lat, lng: item.lng });
          setCenter({ lat: item.lat, lng: item.lng });
        });
      }
    }, [coords, noAutoCenterByCoord]);

    useEffect(() => {
      setZoom(defaultZoom);
    }, []);

    // useEffect(() => {
    //   console.log({ marker });
    // }, [marker]);

    const debounceSearch = useCallback(
      debounce(async (search) => {
        if (search) {
          const { data } = await googleMapsApi.searchPlace({ search });
          setPlaceOptions(data.predictions || []);
        }
      }, 300),
      []
    );

    const handleMapChangedWithCluster = ({
      bounds,
      zoom,
    }: GoogleMapReact.ChangeEventValue) => {
      setBounds([bounds.nw.lng, bounds.se.lat, bounds.se.lng, bounds.nw.lat]);
      setZoom(zoom);
    };

    const points = coords.map((coord) => ({
      type: "Feature",
      properties: {
        cluster: false,
        project: coord.project,
        partner: coord.partner,
      },
      geometry: {
        type: "Point",
        coordinates: [coord.lng, coord.lat],
      },
    }));

    const { clusters } = useSupercluster({
      points,
      bounds,
      zoom,
      options: { radius: 75, maxZoom: 20 },
    });

    const listOfMarkers = useMemo(() => {
      if (enableCluster) {
        return clusters.map((cluster, i) => {
          const [lng, lat] = cluster.geometry.coordinates;
          const {
            cluster: isCluster,
            point_count: pointCount,
            project,
            partner,
          } = cluster.properties;
          return (
            <Marker
              key={i}
              lat={lat}
              lng={lng}
              project={project}
              partner={partner}
              isCluster={isCluster}
              pointCount={pointCount}
              clusterSize={{
                width: 10 + (pointCount / points.length) * 50,
                height: 10 + (pointCount / points.length) * 50,
              }}
              clusterClassName={
                !!!viewPartner
                  ? "!bg-[#FF766B] text-white"
                  : "!bg-[#559FFF] text-white"
              }
              viewPartner={viewPartner}
            />
          );
        });
      } else {
        return coords.map((coord, i) => (
          <Marker
            key={i}
            lat={coord.lat}
            lng={coord.lng}
            project={coord.project}
            partner={coord.partner}
          />
        ));
      }
    }, [clusters, coords, enableCluster]);

    return (
      <>
        {!noInput && (
          // <StandaloneSearchBox
          //   onLoad={handleSearchBoxLoad}
          //   onPlacesChanged={handlePlacesChanged}
          // >
          //   <input
          //     ref={inputRef}
          //     type="text"
          //     placeholder={t("search")}
          //     style={{
          //       boxSizing: "border-box",
          //       border: "1px solid #ccc",
          //       width: "100%",
          //       height: "40px",
          //       padding: "0 12px",
          //       borderRadius: "8px",
          //       fontSize: "14px",
          //       outline: "none",
          //       marginBottom: 10,
          //     }}
          //   />
          // </StandaloneSearchBox>
          <div className="mb-2">
            <Select
              value={currentPlaceId}
              className="w-full"
              showSearch
              onSearch={debounceSearch}
              filterOption={false}
              onChange={async (value, option) => {
                const { data } = await googleMapsApi.getPlaceDetail({
                  placeId: value,
                });
                setCurrentPlaceId(value);
                // handleMapClick({
                //   lat: data.geometry?.location?.lat,
                //   lng: data.geometry?.location?.lng,
                // });
                handlePlacesChanged({ _place: data });
              }}
              options={placeOptions.map((p) => ({
                label: addBoldToStringAtIndex({
                  str: p.description,
                  matchs: p.matched_substrings,
                }),
                value: p.place_id,
                place: p,
                key: p.place_id,
              }))}
            />
          </div>
        )}
        <div style={style}>
          <GoogleMapReact
            yesIWantToUseGoogleMapApiInternals
            onGoogleApiLoaded={({ map, maps, ref }) => {
              mapRef.current = map;
              mapsRef.current = maps;
              onLoaded?.();
            }}
            draggable={draggable}
            center={center}
            zoom={zoom}
            bootstrapURLKeys={{
              key: $googleApiKey || "",
            }}
            onChange={(value) => {
              onMapChange?.(value);
              if (enableCluster) {
                handleMapChangedWithCluster(value);
              }
            }}
            // onClick={handleMapClick}
            // onGoogleApiLoaded={({ map, maps }) => {}}
          >
            {marker && <Marker lat={marker.lat} lng={marker.lng} />}
            {listOfMarkers}
          </GoogleMapReact>
        </div>
      </>
    );
  }
);

export default MapWithAutocomplete;
