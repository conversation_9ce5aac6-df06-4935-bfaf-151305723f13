import { InfoCircleOutlined } from "@ant-design/icons";
import { Card, Tooltip, Typography } from "@mui/material";
import { Spin } from "antd";
import MapWithAutocomplete, {
  DEFAULT_LAT,
  DEFAULT_LNG,
  DEFAULT_NW,
  DEFAULT_SE,
  MapWithAutocompleteRef,
} from "components/Map/MapWithAutocomplete";
import googleMapReact from "google-map-react";
import { usePartner } from "hooks/usePartner";
import { useProject } from "hooks/useProject";
import { debounce, truncate } from "lodash";
import { observer } from "mobx-react";
import { useCallback, useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";

const DashboardMap = () => {
  const { t } = useTranslation();

  const {
    fetchProject,
    projects,
    setQueryProject,
    queryProject,
    loadingProject,
  } = useProject({
    initQuery: {
      limit: 0,
      page: 1,
      // lat: DEFAULT_LAT,
      // long: DEFAULT_LNG,
      // topLeftLat: DEFAULT_NW.lat,
      // topLeftLong: DEFAULT_NW.lng,
      // bottomRightLat: DEFAULT_SE.lat,
      // bottomRightLong: DEFAULT_SE.lng,
    },
  });

  const {
    fetchPartner,
    queryPartner,
    partners,
    setQueryPartner,
    loadingPartner,
  } = usePartner({
    initQuery: {
      limit: 0,
      page: 1,
      isHavePosition: true,
      // lat: DEFAULT_LAT,
      // long: DEFAULT_LNG,
      // topLeftLat: DEFAULT_NW.lat,
      // topLeftLong: DEFAULT_NW.lng,
      // bottomRightLat: DEFAULT_SE.lat,
      // bottomRightLong: DEFAULT_SE.lng,
    },
  });

  const projectMapRef = useRef<MapWithAutocompleteRef>();
  const partnerMapRef = useRef<MapWithAutocompleteRef>();

  const [projectMapLoaded, setProjectMapLoaded] = useState(false);
  const [partnerMapLoaded, setPartnerMapLoaded] = useState(false);

  useEffect(() => {
    fetchProject();
  }, [queryProject]);
  useEffect(() => {
    fetchPartner();
  }, [queryPartner]);

  useEffect(() => {
    if (projects.length > 0 && projectMapLoaded) {
      projectMapRef.current?.fitBounds();
    }
  }, [projects, projectMapLoaded]);
  useEffect(() => {
    if (partners.length > 0 && partnerMapLoaded) {
      partnerMapRef.current?.fitBounds();
    }
  }, [partners, partnerMapLoaded]);

  const debounceProjectMapChange = useCallback(
    debounce((value: googleMapReact.ChangeEventValue) => {
      setQueryProject({
        ...queryProject,
        topLeftLat: value.bounds.nw.lat,
        topLeftLong: value.bounds.nw.lng,
        bottomRightLat: value.bounds.se.lat,
        bottomRightLong: value.bounds.se.lng,
      });
    }, 300),
    [queryProject]
  );
  const debouncePartnerMapChange = useCallback(
    debounce((value: googleMapReact.ChangeEventValue) => {
      setQueryPartner({
        ...queryPartner,
        topLeftLat: value.bounds.nw.lat,
        topLeftLong: value.bounds.nw.lng,
        bottomRightLat: value.bounds.se.lat,
        bottomRightLong: value.bounds.se.lng,
      });
    }, 300),
    [queryPartner]
  );

  return (
    <>
      <Card className="mt-4">
        <Typography variant="h4" textAlign={"center"} mb={2}>
          {t("customerDashboard")}
        </Typography>
        <MapWithAutocomplete
          enableCluster
          ref={projectMapRef}
          key={"mapDashboardCustomer"}
          coords={projects.map((p) => ({
            lat: p.lat,
            lng: p.long,
            project: p,
          }))}
          noInput
          // onMapChange={(value) => {
          //   console.log({ value });
          //   debounceProjectMapChange(value);
          // }}
          onLoaded={() => {
            setProjectMapLoaded(true);
          }}
          noAutoCenterByCoord
        />
      </Card>
      <Card className="mt-4">
        <Typography variant="h4" textAlign={"center"} mb={2}>
          {t("partnerDashboard")}{" "}
          <Tooltip title={t("onlyShowActivePartnerDashboard")}>
            <InfoCircleOutlined className="text-[18px]" />
          </Tooltip>
        </Typography>
        <MapWithAutocomplete
          enableCluster
          ref={partnerMapRef}
          key={"mapDashboardPartner"}
          coords={partners.map((p) => ({
            lat: p.lat,
            lng: p.long,
            partner: p,
          }))}
          // defaultZoom={15}
          noInput
          // onMapChange={(value) => {
          //   console.log({ value });
          //   debouncePartnerMapChange(value);
          // }}
          onLoaded={() => {
            setPartnerMapLoaded(true);
          }}
          noAutoCenterByCoord
        />
      </Card>
    </>
  );
};

export default observer(DashboardMap);
