import React from 'react';
import { Card, Col, Row } from 'antd';

export interface StatItem {
  label: string;
  value: string | number;
  icon?: React.ReactNode;
}

interface DashboardStatsProps {
  stats: StatItem[];
  col?: boolean;
  height?: number | string;
  gutter?: number;
  endText?: string
}

const DashboardStats: React.FC<DashboardStatsProps> = ({ stats, col = false, height, gutter = 16, endText }) => {
  return (
    <Row gutter={[gutter, gutter]}>
      {stats.map((stat, index) => (
        <Col xs={24} sm={col ? 24 : 12} md={col ? 24 : 8} key={index}>
          <Card
            bordered={false}
            style={{
              borderRadius: 12,
              boxShadow: '0 4px 12px rgba(0, 0, 0, 0.05)',
              height: height && height,
              display: "flex",
              alignItems: "center"
            }}
          >
            <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
              <div>{stat.icon}</div>
              <div>
                <div className="text-regular">{stat.label}</div>
                <h3 className="semibold">{stat.value}{endText}</h3>
              </div>
            </div>
          </Card>
        </Col>
      ))}
    </Row>
  );
};

export default DashboardStats;
