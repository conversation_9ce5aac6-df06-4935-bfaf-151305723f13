export enum EPaymentType {
  Balance = "BALANCE",
  Online = "ONLINE",
  COD = "COD",
}

export const PaymentTypeTrans = {
  [EPaymentType.Balance]: {
    title: "Thanh toán bằng điểm",
    color: "magenta",
    value: EPaymentType.Balance,
  },

  [EPaymentType.Online]: {
    title: "Thanh toán online",
    color: "orange",
    value: EPaymentType.Online,
  },

  [EPaymentType.COD]: {
    title: "Thanh toán khi nhận hàng",
    color: "blue",
    value: EPaymentType.COD,
  },
};

export interface PaymentType {
  id: number;

  name: string;
  type: EPaymentType;
  isVisible: boolean;
}
