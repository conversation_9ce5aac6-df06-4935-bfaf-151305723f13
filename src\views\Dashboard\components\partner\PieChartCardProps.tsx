import React, { useEffect, useRef } from 'react';
import * as echarts from 'echarts';
import { Card, Col, Row } from 'antd';
import { formatVND } from 'utils';

interface PieChartCardProps {
  title?: string;
  data: { name: string; value: number | string }[];
  colors?: string[];
  showLabel?: boolean;
  height?: number;
  maxWidth?: number | string;
  formatMoney?: boolean
}

const PieChartCard: React.FC<PieChartCardProps> = ({ title, data, colors, showLabel = false, height = 320, maxWidth = "100%", formatMoney = false }) => {
  const chartRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!chartRef.current) return;
    const chart = echarts.init(chartRef.current);

    const isMobile = window.innerWidth <= 768;

    const chartWidth = chartRef.current.offsetWidth;
    const maxLegendDistance = 85;
    const maxLegendTextLength = Math.max(...data.map(d => d.name.length));
    const legendWidth = Math.min(160, maxLegendTextLength * 12);

    // const legendRight = Math.min(10, Math.max(10, chartWidth - maxLegendDistance - legendWidth));
    const legendRight = 10; // hoặc 16/20px nếu bạn muốn spacing đẹp hơn

    chart.setOption({
      tooltip: {
        trigger: 'item',
        formatter: (params: any) => {
          return `
            <div style="
              display: flex;
              align-items: center;
              gap: 8px;
            ">
              <span style="
                display: inline-block;
                width: 12px;
                height: 12px;
                border-radius: 50%;
                background-color: ${params.color};
              "></span>
              <span style="font-size: 12px; color: #2A3547;">
                ${params.name}: ${formatMoney ? formatVND(params.value ?? 0) + "₫" : params.value}
              </span>
            </div>
          `;
        },
        backgroundColor: 'white',
        borderWidth: 0,
        extraCssText: `
          border-radius: 8px;
          padding: 5px 8px;
          height: 26px;
          display: flex;
        `,
      },
      legend: {
        orient: isMobile ? 'horizontal' : 'vertical',
        right: isMobile ? 'center' : 0,
        bottom: isMobile ? 5 : 'auto',
        top: isMobile ? 'auto' : 'center',
        left: isMobile ? 'center' : 'auto',
        textStyle: {
          fontFamily: '"Plus Jakarta Sans", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif',
          fontSize: 14,
          overflow: 'break',
          lineHeight: 18,
        },
        icon: 'circle',
        itemWidth: 8,
        itemHeight: 8,
        itemGap: isMobile ? 8 : 12,
        width: 'auto',
      },

      color: colors || ['#3C82F4', '#F5A623', '#D8D8D8', '#FFD600', '#5470C6', '#91CC75', '#EE6666'],
      series: [
        {
          type: 'pie',
          radius: ['0%', isMobile ? '70%' : '80%'],
          center: isMobile ? ['50%', '40%'] : [`${45 - (maxLegendDistance + legendWidth / 2) / chartWidth * 50}%`, '45%'],
          data,
          label: {
            show: showLabel,
            position: 'inside',
            formatter: '{d}%',
            fontSize: 14,
            color: '#fff',
            fontWeight: 500,
          },
          labelLine: {
            show: false,
          },
          animationType: 'scale',
          animationEasing: 'elasticOut',
          animationDelay: function (idx: number) {
            return Math.random() * 200;
          }
        },
      ],
    });

    const resize = () => {
      const isMobileResize = window.innerWidth <= 768;
      const currentChartWidth = chartRef.current?.offsetWidth || chartWidth;
      const currentLegendRight = Math.min(10, Math.max(10, currentChartWidth - maxLegendDistance - legendWidth));

      chart.setOption({
        legend: {
          orient: isMobileResize ? 'horizontal' : 'vertical',
          right: isMobileResize ? 'center' : currentLegendRight,
          bottom: isMobileResize ? 5 : 'auto',
          top: isMobileResize ? 'auto' : 'center',
          left: isMobileResize ? 'center' : 'auto',
          textStyle: {
            fontSize: 12,
          },
          itemGap: isMobileResize ? 8 : 12,
          width: 'auto',
        },
        series: [{
          radius: ['0%', isMobileResize ? '60%' : '70%'],
          center: isMobileResize ? ['50%', '40%'] : [`${50 - (maxLegendDistance + legendWidth / 2) / currentChartWidth * 50}%`, '50%'],
          label: {
            fontSize: 12,
          }
        }]
      });
      chart.resize();
    };

    if (window.innerWidth < 1440) {
      resize();
    }

    window.addEventListener('resize', resize);

    return () => {
      chart.dispose();
      window.removeEventListener('resize', resize);
    };
  }, [data, colors, showLabel, height]);

  return (
    <Card
      className="shadow-md rounded-lg"
      bodyStyle={{ padding: 20 }}
      bordered={false}
    >
      <Row>
        <Col span={24}>
          <h5 className="semibold">{title}</h5>
        </Col>
        <Col span={24}>
          <div ref={chartRef} style={{ width: '100%', height: height, maxWidth: maxWidth, margin: "auto" }} />
        </Col>
      </Row>
    </Card>
  );
};

export default PieChartCard;