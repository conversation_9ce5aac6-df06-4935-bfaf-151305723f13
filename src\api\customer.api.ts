import { request } from "utils/request";
import { AxiosPromise } from "axios";

export const customerApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/customer",
      params,
    }),
  findOne: (id: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/customer/${id}`,
    }),
  summaryStatus: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/customer/summary/status",
      params,
    }),

  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/customer/${id}`,
      method: "patch",
      data,
    }),
  resetPass: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/customer/${id}/password`,
      method: "patch",
      data,
    }),
  changeEmployee: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/customer/${id}/employee`,
      method: "patch",
      data,
    }),
  changeCustomerRank: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/customer/${id}/changeRank`,
      method: "patch",
      data,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/customer`,
      method: "post",
      data,
    }),
  verifyApproveCode: (data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/customer/verify`,
      method: "post",
      data,
    }),
  resetPassword: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/customer/${id}/password`,
      method: "patch",
      data,
    }),
  downgrade: (data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/customer/downgrade/free`,
      method: "patch",
      data,
    }),
  verify: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/customer/${id}/verify`,
      method: "post",
    }),

  unVerify: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/customer/${id}/un-verify`,
      method: "delete",
    }),
  removeRank: (id: number, data?: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/customer/${id}/removeRank`,
      method: "delete",
      data,
    }),

  delete: (id: number, data?: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/customer/${id}`,
      method: "delete",
      data,
    }),

  block: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/customer/${id}/block`,
      method: "patch",
      data,
    }),
  blockAccount: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/customer/${id}/block/account`,
      method: "patch",
      data,
    }),
  blockScan: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/customer/${id}/block/scan`,
      method: "patch",
      data,
    }),
  approve: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/customer/${id}/approve`,
      method: "patch",
    }),
  reject: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/customer/${id}/reject`,
      method: "delete",
      data,
    }),
};
