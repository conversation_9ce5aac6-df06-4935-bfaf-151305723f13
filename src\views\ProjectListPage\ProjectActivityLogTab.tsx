// SurveyActivityLogTab.js
import { Table } from "antd";
import { FormInstance } from "antd/es/form";
import Column from "antd/lib/table/Column";

import { unixToFullDate } from "utils/dateFormat";
import { useEffect } from "react";
import { Pagination } from "components/Pagination";
import { useProjectLog } from "hooks/useProjectLog";
import { ProjectLog } from "types/projectLog";
import { useTranslation } from "react-i18next";

interface PropTypes {
  projectId?: number;
}
const ProjectActivityLogTab = ({ projectId }: PropTypes) => {
  const { t } = useTranslation();

  const {
    projectLogs,
    fetchProjectLog,
    loadingProjectLog,
    queryProjectLog,
    totalProjectLog,
  } = useProjectLog({
    initQuery: {
      page: 1,
      limit: 50,
      projectId,
    },
  });

  useEffect(() => {
    if (projectId) {
      queryProjectLog.projectId = projectId;
      queryProjectLog.page = 1;
      fetchProjectLog();
    }
  }, [projectId]);

  const getDisplayName = (record: ProjectLog) => {
    if (record.partner) return `${record.partner?.phone} - ${record.partner?.fullName}`
    if (record.customer) return `${record.customer?.phone} - ${record.customer?.name}`
  }

  return (
    <div>
      <Table
        bordered
        scroll={{
          x: "max-content",
          y: "400px",
        }}
        loading={loadingProjectLog}
        pagination={false}
        rowKey="id"
        dataSource={projectLogs}
        size="small"
        className="custom-scrollbar"
      // onChange={}
      >
        {/* <Column
          width={150}
          title="Người thực hiện"
          dataIndex="name"
          key={"name"}
          render={(text, record: ActivityLog) => (
            <span className="">{record.staff.name}</span>
          )}
        /> */}
        <Column
          width={150}
          title={t("partner")}
          dataIndex="staff"
          key={"staff"}
          render={(text, record: ProjectLog) => (
            <>
              <div>
                <span className="">{getDisplayName(record)}</span>
              </div>
            </>
          )}
        />
        <Column
          width={200}
          title={t("description")}
          dataIndex="description"
          key={"description"}
          render={(text, record: ProjectLog) => (
            <>
              <span className="">{record.description}</span>
            </>
          )}
        />

        <Column
          align="center"
          width={150}
          title={t("createdAt")}
          dataIndex="createdAt"
          key={"createdAt"}
          render={(text, record: ProjectLog) => (
            <span className="">{unixToFullDate(record?.createdAt)}</span>
          )}
        />
      </Table>
      <Pagination
        total={totalProjectLog}
        defaultPageSize={queryProjectLog.limit}
        currentPage={queryProjectLog.page}
        onChange={({ page, limit }) => {
          Object.assign(queryProjectLog, {
            page,
            limit,
          });
          fetchProjectLog();
        }}
      />
    </div>
  );
};

export default ProjectActivityLogTab;
